import React from 'react';
import { 
  Search, 
  Filter, 
  SortAsc, 
  Grid, 
  List, 
  Calendar,
  DollarSign,
  User,
  Package,
  RefreshCw,
  Download
} from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';

interface OrderFiltersProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  sortBy: string;
  setSortBy: (sort: string) => void;
  viewMode: 'grid' | 'list';
  setViewMode: (mode: 'grid' | 'list') => void;
  onRefresh: () => void;
  onExport?: () => void;
  totalOrders: number;
  filteredOrders: number;
}

const OrderFilters: React.FC<OrderFiltersProps> = ({
  searchTerm,
  setSearchTerm,
  filterStatus,
  setFilterStatus,
  sortBy,
  setSortBy,
  viewMode,
  setViewMode,
  onRefresh,
  onExport,
  totalOrders,
  filteredOrders
}) => {
  const { language } = useTranslation();

  const statusOptions = [
    { value: 'all', label: language === 'ar' ? 'جميع الحالات' : 'All Status' },
    { value: 'pending', label: language === 'ar' ? 'معلق' : 'Pending' },
    { value: 'processing', label: language === 'ar' ? 'قيد المعالجة' : 'Processing' },
    { value: 'completed', label: language === 'ar' ? 'مكتمل' : 'Completed' },
    { value: 'cancelled', label: language === 'ar' ? 'ملغي' : 'Cancelled' }
  ];

  const sortOptions = [
    { value: 'date', label: language === 'ar' ? 'التاريخ' : 'Date', icon: Calendar },
    { value: 'amount', label: language === 'ar' ? 'المبلغ' : 'Amount', icon: DollarSign },
    { value: 'status', label: language === 'ar' ? 'الحالة' : 'Status', icon: Package },
    { value: 'customer', label: language === 'ar' ? 'العميل' : 'Customer', icon: User }
  ];

  return (
    <div className="bg-gradient-to-r from-gray-900/80 to-gray-800/40 border border-gray-700/50 rounded-xl p-6 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-bold text-white flex items-center">
            <Filter className="w-5 h-5 mr-2 text-secondary" />
            {language === 'ar' ? 'فلاتر البحث' : 'Search Filters'}
          </h2>
          <p className="text-sm text-gray-400 mt-1">
            {language === 'ar' 
              ? `عرض ${filteredOrders} من أصل ${totalOrders} طلب`
              : `Showing ${filteredOrders} of ${totalOrders} orders`
            }
          </p>
        </div>

        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          {/* View Mode Toggle */}
          <div className="flex items-center bg-gray-800/50 border border-gray-600/50 rounded-lg p-1">
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-all duration-200 ${
                viewMode === 'list' 
                  ? 'bg-secondary text-white shadow-lg' 
                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
              }`}
              title={language === 'ar' ? 'عرض قائمة' : 'List View'}
            >
              <List className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-all duration-200 ${
                viewMode === 'grid' 
                  ? 'bg-secondary text-white shadow-lg' 
                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
              }`}
              title={language === 'ar' ? 'عرض شبكة' : 'Grid View'}
            >
              <Grid className="w-4 h-4" />
            </button>
          </div>

          {/* Action Buttons */}
          <button
            onClick={onRefresh}
            className="p-2 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded-lg hover:bg-blue-500/30 transition-colors"
            title={language === 'ar' ? 'تحديث' : 'Refresh'}
          >
            <RefreshCw className="w-4 h-4" />
          </button>

          {onExport && (
            <button
              onClick={onExport}
              className="p-2 bg-green-500/20 text-green-400 border border-green-500/30 rounded-lg hover:bg-green-500/30 transition-colors"
              title={language === 'ar' ? 'تصدير' : 'Export'}
            >
              <Download className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Filters Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Search Input */}
        <div className="lg:col-span-2">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            {language === 'ar' ? 'البحث' : 'Search'}
          </label>
          <div className="relative">
            <Search className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder={language === 'ar' ? 'البحث في الطلبات، العملاء، أو الخدمات...' : 'Search orders, customers, or services...'}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 rtl:pr-10 rtl:pl-4 pr-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:border-secondary focus:ring-1 focus:ring-secondary focus:outline-none transition-colors"
            />
          </div>
        </div>

        {/* Status Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            {language === 'ar' ? 'الحالة' : 'Status'}
          </label>
          <div className="relative">
            <Package className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full pl-10 rtl:pr-10 rtl:pl-4 pr-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:border-secondary focus:ring-1 focus:ring-secondary focus:outline-none transition-colors appearance-none"
            >
              {statusOptions.map(option => (
                <option key={option.value} value={option.value} className="bg-gray-800">
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Sort By */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            {language === 'ar' ? 'ترتيب حسب' : 'Sort By'}
          </label>
          <div className="relative">
            <SortAsc className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full pl-10 rtl:pr-10 rtl:pl-4 pr-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:border-secondary focus:ring-1 focus:ring-secondary focus:outline-none transition-colors appearance-none"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value} className="bg-gray-800">
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Quick Filter Chips */}
      <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-gray-700/50">
        <span className="text-sm text-gray-400 mr-2">
          {language === 'ar' ? 'فلاتر سريعة:' : 'Quick filters:'}
        </span>
        
        {statusOptions.slice(1).map(option => (
          <button
            key={option.value}
            onClick={() => setFilterStatus(option.value)}
            className={`px-3 py-1 rounded-full text-xs font-medium border transition-colors ${
              filterStatus === option.value
                ? 'bg-secondary/20 text-secondary border-secondary/30'
                : 'bg-gray-800/50 text-gray-400 border-gray-600/50 hover:border-gray-500/50 hover:text-gray-300'
            }`}
          >
            {option.label}
          </button>
        ))}
        
        {filterStatus !== 'all' && (
          <button
            onClick={() => setFilterStatus('all')}
            className="px-3 py-1 rounded-full text-xs font-medium bg-red-500/20 text-red-400 border border-red-500/30 hover:bg-red-500/30 transition-colors"
          >
            {language === 'ar' ? 'مسح الفلاتر' : 'Clear Filters'}
          </button>
        )}
      </div>
    </div>
  );
};

export default OrderFilters;
