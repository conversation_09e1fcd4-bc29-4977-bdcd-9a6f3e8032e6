#!/usr/bin/env node

const mysql = require('mysql2/promise');

async function fixMissingData() {
  console.log('🔧 Fixing Missing Data in Database...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '123456',
      database: 'khan<PERSON><PERSON><PERSON>_db'
    });

    console.log('✅ Connected to MySQL database\n');

    // Fix Technical Services Data
    console.log('1️⃣ Fixing Technical Services Data...');
    
    const technicalServicesUpdates = [
      {
        id: 'freebsd',
        features_ar: [
          'إعداد نظام FreeBSD محسن للأداء',
          'تكوين جدار حماية متقدم',
          'تحسين إعدادات الشبكة',
          'تثبيت أدوات المراقبة',
          'إعداد النسخ الاحتياطي التلقائي'
        ],
        features_en: [
          'Optimized FreeBSD system setup',
          'Advanced firewall configuration',
          'Network settings optimization',
          'Monitoring tools installation',
          'Automatic backup setup'
        ],
        tech_specs_ar: [
          'دعم FreeBSD 13.x أو أحدث',
          'ذاكرة وصول عشوائي 4GB كحد أدنى',
          'مساحة تخزين 50GB',
          'اتصال إنترنت مستقر'
        ],
        tech_specs_en: [
          'FreeBSD 13.x or newer support',
          'Minimum 4GB RAM',
          '50GB storage space',
          'Stable internet connection'
        ],
        image_url: 'https://images.pexels.com/photos/1181675/pexels-photo-1181675.jpeg',
        video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        delivery_time_ar: '3-5 أيام عمل',
        delivery_time_en: '3-5 business days'
      },
      {
        id: 'monthlyMaintenance',
        features_ar: [
          'مراقبة الخادم على مدار الساعة',
          'تحديثات أمنية شهرية',
          'تنظيف ملفات النظام',
          'فحص الأداء والتحسين',
          'تقارير شهرية مفصلة'
        ],
        features_en: [
          '24/7 server monitoring',
          'Monthly security updates',
          'System file cleanup',
          'Performance check and optimization',
          'Detailed monthly reports'
        ],
        tech_specs_ar: [
          'متوافق مع جميع أنظمة Linux',
          'وصول SSH مطلوب',
          'صلاحيات root أو sudo',
          'اتصال إنترنت مستمر'
        ],
        tech_specs_en: [
          'Compatible with all Linux systems',
          'SSH access required',
          'Root or sudo privileges',
          'Continuous internet connection'
        ],
        image_url: 'https://images.pexels.com/photos/1181677/pexels-photo-1181677.jpeg',
        video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        delivery_time_ar: 'خدمة مستمرة',
        delivery_time_en: 'Continuous service'
      },
      {
        id: 'yearlySupport',
        features_ar: [
          'دعم فني متقدم على مدار السنة',
          'أولوية في الاستجابة',
          'حلول مخصصة للمشاكل',
          'استشارات تقنية مجانية',
          'دعم عبر الهاتف والبريد'
        ],
        features_en: [
          'Advanced technical support all year',
          'Priority response',
          'Custom problem solutions',
          'Free technical consultations',
          'Phone and email support'
        ],
        tech_specs_ar: [
          'تغطية جميع الخدمات المشتراة',
          'وقت استجابة أقل من 4 ساعات',
          'دعم باللغتين العربية والإنجليزية',
          'تقارير دورية عن الحالة'
        ],
        tech_specs_en: [
          'Coverage for all purchased services',
          'Response time under 4 hours',
          'Support in Arabic and English',
          'Regular status reports'
        ],
        image_url: 'https://images.pexels.com/photos/1181679/pexels-photo-1181679.jpeg',
        video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        delivery_time_ar: 'فوري',
        delivery_time_en: 'Immediate'
      }
    ];

    for (const service of technicalServicesUpdates) {
      console.log(`   Updating: ${service.id}`);
      
      await connection.execute(`
        UPDATE technical_services 
        SET 
          features_ar = ?,
          features_en = ?,
          tech_specs_ar = ?,
          tech_specs_en = ?,
          image_url = ?,
          video_url = ?,
          delivery_time_ar = ?,
          delivery_time_en = ?,
          updated_at = NOW()
        WHERE id = ?
      `, [
        JSON.stringify(service.features_ar),
        JSON.stringify(service.features_en),
        JSON.stringify(service.tech_specs_ar),
        JSON.stringify(service.tech_specs_en),
        service.image_url,
        service.video_url,
        service.delivery_time_ar,
        service.delivery_time_en,
        service.id
      ]);
      
      console.log(`     ✅ Updated: ${service.id}`);
    }

    // Fix System Services Data
    console.log('\n2️⃣ Fixing System Services Data...');
    
    const systemServicesUpdates = [
      {
        id: 'guildWar',
        features_ar: [
          'نظام حروب الروابط التلقائي',
          'واجهة سهلة الاستخدام',
          'إحصائيات مفصلة',
          'نظام مكافآت متقدم',
          'دعم متعدد الخوادم'
        ],
        features_en: [
          'Automatic guild war system',
          'Easy-to-use interface',
          'Detailed statistics',
          'Advanced reward system',
          'Multi-server support'
        ],
        tech_specs_ar: [
          'متوافق مع Metin2',
          'قاعدة بيانات MySQL',
          'PHP 7.4 أو أحدث',
          'خادم Linux مُوصى به'
        ],
        tech_specs_en: [
          'Compatible with Metin2',
          'MySQL database',
          'PHP 7.4 or newer',
          'Linux server recommended'
        ],
        image_url: 'https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg',
        video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
      },
      {
        id: 'petLevel',
        features_ar: [
          'نظام رفيق متطور',
          'مستويات متعددة للرفيق',
          'مهارات خاصة',
          'نظام تطوير تلقائي',
          'واجهة تحكم متقدمة'
        ],
        features_en: [
          'Advanced pet system',
          'Multiple pet levels',
          'Special skills',
          'Automatic development system',
          'Advanced control interface'
        ],
        tech_specs_ar: [
          'متوافق مع Metin2',
          'قاعدة بيانات MySQL',
          'C++ للخادم',
          'Python للأدوات'
        ],
        tech_specs_en: [
          'Compatible with Metin2',
          'MySQL database',
          'C++ for server',
          'Python for tools'
        ],
        image_url: 'https://images.pexels.com/photos/163036/mario-luigi-yoschi-figures-163036.jpeg',
        video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
      }
    ];

    for (const system of systemServicesUpdates) {
      console.log(`   Updating: ${system.id}`);
      
      await connection.execute(`
        UPDATE system_services 
        SET 
          features_ar = ?,
          features_en = ?,
          tech_specs_ar = ?,
          tech_specs_en = ?,
          image_url = ?,
          video_url = ?,
          updated_at = NOW()
        WHERE id = ?
      `, [
        JSON.stringify(system.features_ar),
        JSON.stringify(system.features_en),
        JSON.stringify(system.tech_specs_ar),
        JSON.stringify(system.tech_specs_en),
        system.image_url,
        system.video_url,
        system.id
      ]);
      
      console.log(`     ✅ Updated: ${system.id}`);
    }

    // Test the updates
    console.log('\n3️⃣ Testing updates...');
    
    const [testServices] = await connection.execute(`
      SELECT id, name_ar, features_ar, image_url, video_url 
      FROM technical_services 
      WHERE id IN ('freebsd', 'monthlyMaintenance')
    `);
    
    console.log('Technical services test:');
    testServices.forEach(service => {
      try {
        const features = JSON.parse(service.features_ar);
        console.log(`   ✅ ${service.name_ar}: ${features.length} features, image: ${!!service.image_url}, video: ${!!service.video_url}`);
      } catch (error) {
        console.log(`   ❌ ${service.name_ar}: JSON parse failed`);
      }
    });

    const [testSystems] = await connection.execute(`
      SELECT id, name_ar, features_ar, image_url, video_url 
      FROM system_services 
      WHERE id IN ('guildWar', 'petLevel')
    `);
    
    console.log('\nSystem services test:');
    testSystems.forEach(system => {
      try {
        const features = JSON.parse(system.features_ar);
        console.log(`   ✅ ${system.name_ar}: ${features.length} features, image: ${!!system.image_url}, video: ${!!system.video_url}`);
      } catch (error) {
        console.log(`   ❌ ${system.name_ar}: JSON parse failed`);
      }
    });

    await connection.end();

    console.log('\n✅ Missing data fixes completed!');
    console.log('\n🔄 Next steps:');
    console.log('1. Refresh frontend to see updated data');
    console.log('2. Check that images and videos display correctly');
    console.log('3. Verify features and tech specs show properly');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

fixMissingData();
