/**
 * Simple Orders Migration Script
 * Updates existing orders table with essential new fields
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function migrateOrders() {
  let connection;
  
  try {
    // Connect to database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '123456',
      database: process.env.DB_NAME || 'khanfashariya_db',
      charset: 'utf8mb4'
    });
    
    console.log('✅ Connected to database');
    
    // Backup existing orders
    console.log('📦 Creating backup...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS orders_backup_${Date.now()} AS 
      SELECT * FROM orders
    `);
    console.log('✅ Backup created');
    
    // Add new columns to existing orders table
    console.log('🔧 Adding new columns...');
    
    const alterStatements = [
      // Order classification
      `ALTER TABLE orders ADD COLUMN order_category ENUM('standard','premium_base','premium_custom','subscription','maintenance') DEFAULT 'standard' AFTER order_type`,
      
      // Enhanced pricing
      `ALTER TABLE orders ADD COLUMN base_price DECIMAL(10,2) DEFAULT 0.00 AFTER unit_price`,
      `ALTER TABLE orders ADD COLUMN addons_price DECIMAL(10,2) DEFAULT 0.00 AFTER base_price`,
      `ALTER TABLE orders ADD COLUMN subscription_price DECIMAL(10,2) DEFAULT 0.00 AFTER addons_price`,
      `ALTER TABLE orders ADD COLUMN maintenance_price DECIMAL(10,2) DEFAULT 0.00 AFTER subscription_price`,
      `ALTER TABLE orders ADD COLUMN tax_amount DECIMAL(10,2) DEFAULT 0.00 AFTER discount_amount`,
      
      // Enhanced status management
      `ALTER TABLE orders MODIFY COLUMN status ENUM('pending','confirmed','in_progress','testing','completed','cancelled','refunded','on_hold','expired') DEFAULT 'pending'`,
      `ALTER TABLE orders ADD COLUMN priority ENUM('low','medium','high','urgent') DEFAULT 'medium' AFTER status`,
      `ALTER TABLE orders ADD COLUMN progress_percentage INT(3) DEFAULT 0 AFTER priority`,
      
      // Enhanced payment
      `ALTER TABLE orders MODIFY COLUMN payment_status ENUM('pending','paid','partial','failed','refunded','disputed') DEFAULT 'pending'`,
      `ALTER TABLE orders ADD COLUMN payment_gateway VARCHAR(50) AFTER payment_reference`,
      
      // Subscription management
      `ALTER TABLE orders ADD COLUMN subscription_type ENUM('none','monthly','quarterly','yearly','lifetime') DEFAULT 'none' AFTER payment_gateway`,
      `ALTER TABLE orders ADD COLUMN subscription_duration INT(11) NULL AFTER subscription_type`,
      `ALTER TABLE orders ADD COLUMN subscription_start_date TIMESTAMP NULL AFTER subscription_duration`,
      `ALTER TABLE orders ADD COLUMN subscription_end_date TIMESTAMP NULL AFTER subscription_start_date`,
      `ALTER TABLE orders ADD COLUMN auto_renewal TINYINT(1) DEFAULT 0 AFTER subscription_end_date`,
      
      // Service details
      `ALTER TABLE orders ADD COLUMN maintenance_included TINYINT(1) DEFAULT 0 AFTER auto_renewal`,
      `ALTER TABLE orders ADD COLUMN installation_included TINYINT(1) DEFAULT 0 AFTER maintenance_included`,
      `ALTER TABLE orders ADD COLUMN support_level ENUM('basic','standard','premium','enterprise') DEFAULT 'basic' AFTER installation_included`,
      
      // Complex order details
      `ALTER TABLE orders ADD COLUMN order_details JSON NULL AFTER support_level`,
      `ALTER TABLE orders ADD COLUMN selected_addons JSON NULL AFTER order_details`,
      `ALTER TABLE orders ADD COLUMN customization_options JSON NULL AFTER selected_addons`,
      
      // Enhanced timeline
      `ALTER TABLE orders ADD COLUMN confirmed_at TIMESTAMP NULL AFTER updated_at`,
      `ALTER TABLE orders ADD COLUMN started_at TIMESTAMP NULL AFTER confirmed_at`,
      `ALTER TABLE orders ADD COLUMN estimated_completion TIMESTAMP NULL AFTER started_at`,
      `ALTER TABLE orders ADD COLUMN completed_at TIMESTAMP NULL AFTER estimated_completion`,
      
      // Additional notes
      `ALTER TABLE orders ADD COLUMN customer_requirements TEXT NULL AFTER admin_notes`,
      `ALTER TABLE orders ADD COLUMN attached_files JSON NULL AFTER customer_requirements`,
      
      // Audit trail
      `ALTER TABLE orders ADD COLUMN created_by VARCHAR(36) NULL AFTER attached_files`,
      `ALTER TABLE orders ADD COLUMN last_modified_by VARCHAR(36) NULL AFTER created_by`
    ];
    
    for (const statement of alterStatements) {
      try {
        await connection.execute(statement);
        console.log('✅ Added column successfully');
      } catch (error) {
        if (error.code === 'ER_DUP_FIELDNAME') {
          console.log('ℹ️ Column already exists, skipping...');
        } else {
          console.warn(`⚠️ Warning: ${error.message}`);
        }
      }
    }
    
    // Update existing data with calculated values
    console.log('🔄 Updating existing data...');
    
    await connection.execute(`
      UPDATE orders SET 
        base_price = COALESCE(unit_price, final_price, 0),
        order_category = CASE 
          WHEN order_type IN ('premium_content', 'premium_package') THEN 'premium_base'
          ELSE 'standard'
        END,
        progress_percentage = CASE 
          WHEN status = 'completed' THEN 100
          WHEN status = 'cancelled' THEN 0
          WHEN status = 'pending' THEN 0
          ELSE 50
        END,
        completed_at = CASE 
          WHEN status = 'completed' AND completion_date IS NOT NULL THEN completion_date
          ELSE NULL
        END
      WHERE base_price = 0 OR order_category = 'standard'
    `);
    
    // Create additional indexes
    console.log('📊 Creating indexes...');
    
    const indexes = [
      'CREATE INDEX idx_orders_category ON orders (order_category)',
      'CREATE INDEX idx_orders_priority ON orders (priority)',
      'CREATE INDEX idx_orders_subscription ON orders (subscription_type)',
      'CREATE INDEX idx_orders_progress ON orders (progress_percentage)',
      'CREATE INDEX idx_orders_estimated ON orders (estimated_completion)'
    ];
    
    for (const indexSql of indexes) {
      try {
        await connection.execute(indexSql);
        console.log('✅ Index created');
      } catch (error) {
        if (!error.message.includes('Duplicate key name')) {
          console.warn(`⚠️ Index warning: ${error.message}`);
        }
      }
    }
    
    // Validate migration
    console.log('🔍 Validating migration...');
    const [result] = await connection.execute('SELECT COUNT(*) as count FROM orders');
    console.log(`📊 Total orders: ${result[0].count}`);
    
    // Test new columns
    const [testResult] = await connection.execute(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN order_category IS NOT NULL THEN 1 ELSE 0 END) as with_category,
        SUM(CASE WHEN priority IS NOT NULL THEN 1 ELSE 0 END) as with_priority
      FROM orders
    `);
    
    console.log(`📊 Orders with category: ${testResult[0].with_category}/${testResult[0].total}`);
    console.log(`📊 Orders with priority: ${testResult[0].with_priority}/${testResult[0].total}`);
    
    console.log('🎉 Orders migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run migration
migrateOrders().catch(console.error);
