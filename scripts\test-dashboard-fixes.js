/**
 * Test Dashboard Fixes
 * Tests all the fixes applied to the user dashboard
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';
const TEST_USER_CREDS = { email: '<EMAIL>', password: '123456' };

async function testDashboardFixes() {
  console.log('🔧 Testing Dashboard Fixes...');
  
  try {
    // Login
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, TEST_USER_CREDS);
    
    if (!loginResponse.data.success) {
      console.log('❌ Login failed');
      return;
    }
    
    const userAuth = {
      token: loginResponse.data.data.tokens.accessToken,
      user: loginResponse.data.data.user,
      headers: { Authorization: `Bearer ${loginResponse.data.data.tokens.accessToken}` }
    };
    
    console.log(`✅ Login successful: ${userAuth.user.email}`);
    
    // Test all dashboard data endpoints
    console.log('\n📊 Testing Dashboard Data...');
    
    // 1. Orders
    const ordersResponse = await axios.get(`${API_BASE}/orders`, { headers: userAuth.headers });
    console.log(`Orders: ${ordersResponse.data.success ? '✅' : '❌'} - ${ordersResponse.data.data?.orders?.length || 0} orders`);
    
    // 2. Subscriptions
    const subscriptionsResponse = await axios.get(`${API_BASE}/users/${userAuth.user.id}/subscriptions`, { headers: userAuth.headers });
    console.log(`Subscriptions: ${subscriptionsResponse.data.success ? '✅' : '❌'} - ${subscriptionsResponse.data.data?.subscriptions?.length || 0} subscriptions`);
    
    // 3. Messages
    const messagesResponse = await axios.get(`${API_BASE}/users/${userAuth.user.id}/messages`, { headers: userAuth.headers });
    console.log(`Messages: ${messagesResponse.data.success ? '✅' : '❌'} - ${messagesResponse.data.data?.messages?.length || 0} messages`);
    
    // 4. Systems (for shop)
    const systemsResponse = await axios.get(`${API_BASE}/systems`);
    console.log(`Systems: ${systemsResponse.data.success ? '✅' : '❌'} - ${systemsResponse.data.data?.systems?.length || 0} systems`);
    
    // 5. Services (for shop)
    const servicesResponse = await axios.get(`${API_BASE}/services/technical`);
    console.log(`Services: ${servicesResponse.data.success ? '✅' : '❌'} - ${servicesResponse.data.data?.services?.length || 0} services`);
    
    // Test shop purchase functionality
    console.log('\n🛍️ Testing Shop Purchase...');
    
    if (systemsResponse.data.success && systemsResponse.data.data.systems.length > 0) {
      const testSystem = systemsResponse.data.data.systems[0];
      
      const orderData = {
        order_type: 'system_service',
        item_id: testSystem.id,
        quantity: 1,
        notes_ar: 'طلب من لوحة التحكم - اختبار',
        notes_en: 'Dashboard purchase - test'
      };
      
      const purchaseResponse = await axios.post(`${API_BASE}/orders`, orderData, { headers: userAuth.headers });
      
      if (purchaseResponse.data.success) {
        console.log(`✅ Shop purchase successful: ${purchaseResponse.data.data.order.order_number}`);
      } else {
        console.log('❌ Shop purchase failed');
      }
    }
    
    console.log('\n🎉 Dashboard fixes testing completed!');
    console.log('✅ All dashboard functionality should now work correctly');
    console.log('✅ Shop tab should display products without errors');
    console.log('✅ Messages tab should display messages properly');
    console.log('✅ Orders tab should show order history');
    console.log('✅ Logout button removed from dashboard');
    
  } catch (error) {
    console.log('❌ Error during testing:', error.response?.data || error.message);
  }
}

testDashboardFixes();
