# دليل إعداد TestSprite الشامل - مجاني 100%

## الخطوة 1: تثبيت ngrok (مجاني)

### تحميل ngrok:
1. اذهب إلى: https://ngrok.com/download
2. اختر Windows
3. ح<PERSON><PERSON> الملف المضغوط
4. فك الضغط في مجلد (مثل: C:\ngrok)
5. أض<PERSON> المجلد إلى PATH أو استخدم المسار الكامل

### أو التثبيت عبر npm:
```bash
npm install -g ngrok
```

## الخطوة 2: تجهيز المشروع

### تأكد من تشغيل قاعدة البيانات:
1. شغل WAMP/XAMPP
2. تأكد من عمل MySQL على المنفذ 3306

### تشغيل المشروع:
```bash
# في مجلد المشروع
npm run dev:full
```

## الخطوة 3: تشغيل ngrok للباك اند والفرونت اند

### تحتاج نافذتين terminal منفصلتين:

#### Terminal 1 - للباك اند (API):
```bash
ngrok http 3001
```

#### Terminal 2 - للفرونت اند (الموقع):
```bash
ngrok http 5173
```

### ستحصل على رابطين:
```
# من Terminal 1 (Backend)
Forwarding https://api-1234.ngrok.io -> http://localhost:3001

# من Terminal 2 (Frontend)  
Forwarding https://web-5678.ngrok.io -> http://localhost:5173
```

## الخطوة 4: البيانات لـ TestSprite

### يمكنك إنشاء اختبارين منفصلين:

## 🔧 الاختبار الأول: Backend API

#### API Name:
```
Khanfashariya Backend API
```

#### API endpoint / URL:
```
https://api-1234.ngrok.io
```
**⚠️ استبدل هذا بالرابط الفعلي للباك اند من ngrok**

#### Authentication Type:
```
None - No authentication required
```

#### Extra testing information:
```
Khanfashariya Metin2 Services Platform - Backend API

🔧 API Endpoints:
- GET /health - Health check
- GET /api/status - API status  
- POST /api/auth/login - User login
- POST /api/auth/register - User registration
- GET /api/users/profile - User profile (requires auth)
- GET /api/systems - System services
- GET /api/services - Technical services
- GET /api/orders - Orders (requires auth)
- GET /api/admin/dashboard - Admin panel (requires auth)

🔑 Test Credentials:
Email: <EMAIL>
Password: admin123

📝 Sample Requests:

Login:
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "admin123"
}

Registration:
POST /api/auth/register
{
  "username": "testuser",
  "email": "<EMAIL>", 
  "password": "password123",
  "confirmPassword": "password123"
}

🗄️ Database: MySQL (localhost:3306)
📊 Features: JWT Auth, Rate Limiting, File Upload, Admin Panel
```

## 🌐 الاختبار الثاني: Frontend Website

#### Website Name:
```
Khanfashariya Frontend
```

#### Website URL:
```
https://web-5678.ngrok.io
```
**⚠️ استبدل هذا بالرابط الفعلي للفرونت اند من ngrok**

#### Description:
```
Khanfashariya Metin2 Services Platform - Frontend Application

🌐 Full Stack React Application with:
- User Authentication & Registration
- Service Catalog & Ordering
- Admin Dashboard
- File Upload & Management
- Responsive Design
- Real-time Updates

🔗 Connected to Backend API

📱 Pages to Test:
- / - Homepage
- /login - Login page
- /register - Registration page
- /services - Services catalog
- /profile - User profile (requires login)
- /admin - Admin dashboard (requires admin login)

🧪 Test Scenarios:
1. User Registration Flow
2. Login/Logout Process
3. Service Browsing & Ordering
4. Profile Management
5. Admin Operations
6. Responsive Design Testing
7. Error Handling
8. Performance Testing

🔑 Test Credentials:
Email: <EMAIL>
Password: admin123
```

## الخطوة 5: التحقق من العمل

### اختبر الروابط في المتصفح:

#### للباك اند API:
1. افتح رابط الباك اند من ngrok
2. يجب أن ترى رسالة API
3. جرب: `[رابط الباك اند]/health` - يجب أن ترى حالة صحية
4. جرب: `[رابط الباك اند]/api/status` - يجب أن ترى معلومات API

#### للفرونت اند:
1. افتح رابط الفرونت اند من ngrok
2. يجب أن ترى الموقع الرئيسي
3. جرب التنقل بين الصفحات
4. جرب تسجيل الدخول بالبيانات المذكورة

## الخطوة 6: رفع ملف API Documentation (اختياري)

يمكنك رفع ملف `api-documentation.json` في TestSprite لمساعدته في فهم API بشكل أفضل.

## ملاحظات مهمة:

### مجاني تماماً:
- ngrok مجاني للاستخدام الأساسي
- TestSprite يعطيك 400 كريدت
- لا تحتاج دفع أي شيء

### الحدود المجانية:
- ngrok: رابط واحد، ينتهي عند إغلاق البرنامج
- الرابط يتغير كل مرة تعيد تشغيل ngrok
- سرعة محدودة ولكن كافية للاختبار

### نصائح:
- اتركngrok يعمل أثناء اختبار TestSprite
- لا تغلق المشروع أثناء الاختبار
- راقب logs الخادم لرؤية طلبات TestSprite