/**
 * Test API Endpoints
 * 
 * Simple script to test if API endpoints are working
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testEndpoints() {
  console.log('🧪 Testing API Endpoints...\n');
  
  const endpoints = [
    '/api/systems',
    '/api/services/technical',
    '/api/services/premium'
  ];
  
  for (const endpoint of endpoints) {
    try {
      console.log(`Testing: ${endpoint}`);
      const response = await axios.get(`${BASE_URL}${endpoint}`);
      
      if (response.status === 200) {
        console.log(`✅ ${endpoint} - Status: ${response.status}`);
        
        // Check data structure
        if (response.data && response.data.success) {
          console.log(`   Data: ${response.data.success ? 'Valid' : 'Invalid'}`);
          
          if (endpoint === '/api/systems' && response.data.data && response.data.data.systems) {
            console.log(`   Systems count: ${response.data.data.systems.length}`);
          }
          
          if (endpoint === '/api/services/technical' && response.data.data && response.data.data.services) {
            console.log(`   Services count: ${response.data.data.services.length}`);
          }
          
          if (endpoint === '/api/services/premium' && response.data.data && response.data.data.premiumContent) {
            console.log(`   Premium content count: ${response.data.data.premiumContent.length}`);
          }
        } else {
          console.log(`   ⚠️  Unexpected data format`);
        }
      } else {
        console.log(`❌ ${endpoint} - Status: ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - Error: ${error.message}`);
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Data: ${JSON.stringify(error.response.data).substring(0, 100)}...`);
      }
    }
    console.log('');
  }
  
  console.log('🏁 API endpoint testing completed');
}

testEndpoints();