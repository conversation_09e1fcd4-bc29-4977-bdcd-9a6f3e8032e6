#!/usr/bin/env node

/**
 * Complete LocalStorage to MySQL Migration Script
 * 
 * This script migrates all data from localStorage to MySQL database
 * ensuring no data is lost during the transition.
 */

const mysql = require('mysql2/promise');
const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

async function extractLocalStorageData() {
  console.log('📤 Extracting data from localStorage...\n');
  
  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });
    
    const data = await page.evaluate(() => {
      const result = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('khanfashariya_')) {
          try {
            const value = localStorage.getItem(key);
            result[key] = JSON.parse(value);
          } catch {
            result[key] = value;
          }
        }
      }
      return result;
    });
    
    console.log('✅ Data extracted successfully');
    return data;
    
  } catch (error) {
    console.error('❌ Error extracting localStorage data:', error.message);
    return {};
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

async function connectToMySQL() {
  console.log('🔌 Connecting to MySQL...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '123456',
      database: 'khanfashariya_db',
      charset: 'utf8mb4'
    });
    
    console.log('✅ Connected to MySQL database');
    return connection;
    
  } catch (error) {
    console.error('❌ MySQL connection failed:', error.message);
    throw error;
  }
}

async function migrateUsers(connection, users) {
  if (!users || users.length === 0) {
    console.log('⚠️ No users to migrate');
    return;
  }
  
  console.log(`👥 Migrating ${users.length} users...`);
  
  for (const user of users) {
    try {
      await connection.execute(`
        INSERT INTO users (
          id, email, username, full_name, role, password_hash, 
          status, created_at, updated_at, phone, last_login, login_count
        ) VALUES (?, ?, ?, ?, ?, ?, 'active', ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          full_name = VALUES(full_name),
          role = VALUES(role),
          updated_at = VALUES(updated_at),
          phone = VALUES(phone),
          last_login = VALUES(last_login),
          login_count = VALUES(login_count)
      `, [
        user.id,
        user.email,
        user.username,
        user.full_name,
        user.role,
        user.password_hash,
        user.created_at,
        user.updated_at,
        user.phone || null,
        user.last_login || null,
        user.login_count || 0
      ]);
      
      console.log(`   ✓ Migrated user: ${user.email}`);
    } catch (error) {
      console.error(`   ❌ Failed to migrate user ${user.email}:`, error.message);
    }
  }
}

async function migrateSystemServices(connection, services) {
  if (!services || services.length === 0) {
    console.log('⚠️ No system services to migrate');
    return;
  }
  
  console.log(`🖥️ Migrating ${services.length} system services...`);
  
  for (const service of services) {
    try {
      await connection.execute(`
        INSERT INTO system_services (
          id, name_ar, name_en, description_ar, description_en, price, category, type,
          features_ar, features_en, tech_specs_ar, tech_specs_en, video_url, image_url,
          gallery_images, version, file_size, status, featured, sort_order,
          download_count, rating, rating_count, created_at, updated_at, is_premium_addon
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          name_ar = VALUES(name_ar),
          name_en = VALUES(name_en),
          description_ar = VALUES(description_ar),
          description_en = VALUES(description_en),
          price = VALUES(price),
          updated_at = VALUES(updated_at)
      `, [
        service.id,
        service.name?.ar || service.name_ar,
        service.name?.en || service.name_en,
        service.description?.ar || service.description_ar,
        service.description?.en || service.description_en,
        service.price,
        service.category,
        service.type || 'regular',
        JSON.stringify(service.features?.ar || service.features_ar || []),
        JSON.stringify(service.features?.en || service.features_en || []),
        JSON.stringify(service.tech_specs?.ar || service.tech_specs_ar || []),
        JSON.stringify(service.tech_specs?.en || service.tech_specs_en || []),
        service.video_url,
        service.image_url,
        JSON.stringify(service.gallery_images || []),
        service.version,
        service.file_size,
        service.status || 'active',
        service.featured ? 1 : 0,
        service.sort_order || 0,
        service.download_count || 0,
        service.rating || '0.00',
        service.rating_count || 0,
        service.created_at,
        service.updated_at,
        service.isPremiumAddon ? 1 : 0
      ]);
      
      console.log(`   ✓ Migrated system service: ${service.name?.en || service.name_en}`);
    } catch (error) {
      console.error(`   ❌ Failed to migrate system service:`, error.message);
    }
  }
}

async function migrateTechnicalServices(connection, services) {
  if (!services || services.length === 0) {
    console.log('⚠️ No technical services to migrate');
    return;
  }
  
  console.log(`🛠️ Migrating ${services.length} technical services...`);
  
  for (const service of services) {
    try {
      await connection.execute(`
        INSERT INTO technical_services (
          id, name_ar, name_en, description_ar, description_en, price, category,
          service_type, features_ar, features_en, delivery_time_ar, delivery_time_en,
          video_url, image_url, gallery_images, status, featured, sort_order,
          order_count, rating, rating_count, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          name_ar = VALUES(name_ar),
          name_en = VALUES(name_en),
          description_ar = VALUES(description_ar),
          description_en = VALUES(description_en),
          price = VALUES(price),
          updated_at = VALUES(updated_at)
      `, [
        service.id,
        service.name?.ar || service.name_ar,
        service.name?.en || service.name_en,
        service.description?.ar || service.description_ar,
        service.description?.en || service.description_en,
        service.price,
        service.category,
        service.service_type || 'development',
        JSON.stringify(service.features?.ar || service.features_ar || []),
        JSON.stringify(service.features?.en || service.features_en || []),
        service.delivery_time?.ar || service.delivery_time_ar || '',
        service.delivery_time?.en || service.delivery_time_en || '',
        service.video_url,
        service.image_url,
        JSON.stringify(service.gallery_images || []),
        service.status || 'active',
        service.featured ? 1 : 0,
        service.sort_order || 0,
        service.order_count || 0,
        service.rating || '0.00',
        service.rating_count || 0,
        service.created_at,
        service.updated_at
      ]);
      
      console.log(`   ✓ Migrated technical service: ${service.name?.en || service.name_en}`);
    } catch (error) {
      console.error(`   ❌ Failed to migrate technical service:`, error.message);
    }
  }
}

async function migrateOrders(connection, orders) {
  if (!orders || orders.length === 0) {
    console.log('⚠️ No orders to migrate');
    return;
  }
  
  console.log(`📋 Migrating ${orders.length} orders...`);
  
  for (const order of orders) {
    try {
      await connection.execute(`
        INSERT INTO orders (
          id, user_id, service_name, service_type, service_id, price, status,
          payment_method, payment_status, notes, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          status = VALUES(status),
          payment_status = VALUES(payment_status),
          updated_at = VALUES(updated_at)
      `, [
        order.id,
        order.user_id,
        order.service_name,
        order.service_type,
        order.service_id,
        order.price,
        order.status || 'pending',
        order.payment_method || 'pending',
        order.payment_status || 'pending',
        order.notes || '',
        order.created_at,
        order.updated_at
      ]);
      
      console.log(`   ✓ Migrated order: ${order.id}`);
    } catch (error) {
      console.error(`   ❌ Failed to migrate order:`, error.message);
    }
  }
}

async function migratePremiumContent(connection, content) {
  if (!content || content.length === 0) {
    console.log('⚠️ No premium content to migrate');
    return;
  }
  
  console.log(`👑 Migrating ${content.length} premium content items...`);
  
  for (const item of content) {
    try {
      await connection.execute(`
        INSERT INTO premium_content (
          id, name_ar, name_en, description_ar, description_en, price, category,
          features_ar, features_en, video_url, image_url, gallery_images,
          is_available, sort_order, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          name_ar = VALUES(name_ar),
          name_en = VALUES(name_en),
          description_ar = VALUES(description_ar),
          description_en = VALUES(description_en),
          price = VALUES(price),
          updated_at = VALUES(updated_at)
      `, [
        item.id,
        item.name?.ar || item.name_ar,
        item.name?.en || item.name_en,
        item.description?.ar || item.description_ar,
        item.description?.en || item.description_en,
        item.price,
        item.category,
        JSON.stringify(item.features?.ar || item.features_ar || []),
        JSON.stringify(item.features?.en || item.features_en || []),
        item.video_url,
        item.image_url,
        JSON.stringify(item.gallery_images || []),
        item.is_available ? 1 : 0,
        item.sort_order || 0,
        item.created_at,
        item.updated_at
      ]);
      
      console.log(`   ✓ Migrated premium content: ${item.name?.en || item.name_en}`);
    } catch (error) {
      console.error(`   ❌ Failed to migrate premium content:`, error.message);
    }
  }
}

async function main() {
  console.log('🚀 Starting LocalStorage to MySQL Migration');
  console.log('===========================================\n');
  
  let connection;
  
  try {
    // Extract localStorage data
    const localStorageData = await extractLocalStorageData();
    
    if (!localStorageData.khanfashariya_db) {
      console.log('⚠️ No khanfashariya_db found in localStorage');
      console.log('✅ Migration may not be needed');
      return;
    }
    
    const dbData = localStorageData.khanfashariya_db;
    
    // Connect to MySQL
    connection = await connectToMySQL();
    
    // Migrate each data type
    await migrateUsers(connection, dbData.users);
    await migrateSystemServices(connection, dbData.systemServices);
    await migrateTechnicalServices(connection, dbData.technicalServices);
    await migrateOrders(connection, dbData.orders);
    await migratePremiumContent(connection, dbData.premiumContent);
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('✅ All data has been migrated to MySQL');
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Migration error:', error);
    process.exit(1);
  });
}

module.exports = { main };
