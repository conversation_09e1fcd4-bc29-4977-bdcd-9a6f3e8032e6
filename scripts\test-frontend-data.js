const axios = require('axios');

async function testFrontendDataDisplay() {
  try {
    console.log('🌐 Testing Frontend Data Display...\n');
    
    // Test public endpoints that frontend uses
    console.log('1️⃣ Testing public systems endpoint...');
    const systemsResponse = await axios.get('http://localhost:3001/api/systems');
    console.log('✅ Systems API response structure:', Object.keys(systemsResponse.data));
    
    if (systemsResponse.data.success && systemsResponse.data.data) {
      const systems = systemsResponse.data.data.systems || systemsResponse.data.data;
      console.log(`   📊 Found ${systems.length} systems`);
      
      if (systems.length > 0) {
        const firstSystem = systems[0];
        console.log('   📋 First system structure:', Object.keys(firstSystem));
        console.log(`   📝 Sample: ${firstSystem.name_ar} - $${firstSystem.price}`);
      }
    }
    
    console.log('\n2️⃣ Testing public technical services endpoint...');
    const servicesResponse = await axios.get('http://localhost:3001/api/services/technical');
    console.log('✅ Services API response structure:', Object.keys(servicesResponse.data));
    
    if (servicesResponse.data.success && servicesResponse.data.data) {
      const services = servicesResponse.data.data.services || servicesResponse.data.data;
      console.log(`   📊 Found ${services.length} services`);
      
      if (services.length > 0) {
        const firstService = services[0];
        console.log('   📋 First service structure:', Object.keys(firstService));
        console.log(`   📝 Sample: ${firstService.name_ar} - $${firstService.price}`);
      }
    }
    
    console.log('\n3️⃣ Testing premium content endpoint...');
    const premiumResponse = await axios.get('http://localhost:3001/api/services/premium');
    console.log('✅ Premium API response structure:', Object.keys(premiumResponse.data));
    
    if (premiumResponse.data.success && premiumResponse.data.data) {
      const premium = premiumResponse.data.data;
      console.log(`   📊 Found ${premium.length} premium items`);
      
      if (premium.length > 0) {
        const firstPremium = premium[0];
        console.log('   📋 First premium structure:', Object.keys(firstPremium));
        console.log(`   📝 Sample: ${firstPremium.title_ar} - $${firstPremium.price}`);
      }
    }
    
    console.log('\n✅ Frontend data testing completed!');
    console.log('🌐 Visit http://localhost:5173 to see the data displayed');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testFrontendDataDisplay();
