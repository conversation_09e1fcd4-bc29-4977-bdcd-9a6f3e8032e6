import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotification } from '../../hooks/useNotification';
import { useActivityLogger } from '../../hooks/useActivityLogger';
import {
  getPremiumContent,
  createPremiumContent,
  updatePremiumContent,
  deletePremiumContent,
  setActivePremiumEdition,
  updateSystemPremiumPricing,
  updateServicePremiumPricing
} from '../../lib/apiServices';
import { PremiumContent, SystemService, TechnicalService } from '../../lib/database';
import {
  Crown,
  Star,
  X,
  Plus,
  Edit,
  Trash2,
  Eye,
  Settings,
  CheckCircle,
  XCircle,
  DollarSign,
  Package,
  Wrench,
  Save,
  ArrowLeft,
  Filter,
  Search,
  Grid,
  List,
  Power,
  PowerOff
} from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Modal from '../ui/Modal';
import Card from '../ui/Card';

interface PremiumContentManagerProps {
  onBack?: () => void;
}

const PremiumContentManager: React.FC<PremiumContentManagerProps> = ({ onBack }) => {
  const { t, language } = useTranslation();
  const { showNotification } = useNotification();
  const { logActivity } = useActivityLogger();

  // State management
  const [loading, setLoading] = useState(true);
  const [premiumContent, setPremiumContent] = useState<PremiumContent[]>([]);
  const [availableSystems, setAvailableSystems] = useState<SystemService[]>([]);
  const [availableServices, setAvailableServices] = useState<TechnicalService[]>([]);
  
  // UI state
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive' | 'draft'>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [selectedContent, setSelectedContent] = useState<PremiumContent | null>(null);
  const [selectedSystem, setSelectedSystem] = useState<SystemService | null>(null);
  const [selectedService, setSelectedService] = useState<TechnicalService | null>(null);

  // Form state
  const [formData, setFormData] = useState<Partial<PremiumContent>>({
    title_ar: '',
    title_en: '',
    description_ar: '',
    description_en: '',
    detailed_description_ar: '',
    detailed_description_en: '',
    price: 0,
    original_price: 0,
    discount_percentage: 0,
    category: 'complete_package',
    features_ar: [],
    features_en: [],
    tech_specs_ar: [],
    tech_specs_en: [],
    video_url: '',
    image_url: '',
    gallery_images: [],
    included_systems: [],
    included_services: [],
    installation_guide_ar: '',
    installation_guide_en: '',
    support_info_ar: '',
    support_info_en: '',
    status: 'active',
    is_active_edition: false,
    featured: true,
    sort_order: 0
  });

  // Pricing form state
  const [pricingData, setPricingData] = useState({
    premium_price: 0,
    installation_included: false,
    maintenance_included: false,
    is_available_for_premium: true,
    subscription_discount_percentage: 0,
    description_ar: '',
    description_en: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const { data, error } = await getPremiumContent();
      if (error) {
        console.error('Error loading premium content:', error);
        showNotification({ type: 'error', message: t('notifications.loadError') });
      } else if (data) {
        setPremiumContent(data.premiumContent || []);
        setAvailableSystems(data.availableSystems || []);
        setAvailableServices(data.availableServices || []);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      showNotification({ type: 'error', message: t('notifications.loadError') });
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setFormData({
      title_ar: '',
      title_en: '',
      description_ar: '',
      description_en: '',
      detailed_description_ar: '',
      detailed_description_en: '',
      price: 0,
      original_price: 0,
      discount_percentage: 0,
      category: 'complete_package',
      features_ar: [],
      features_en: [],
      tech_specs_ar: [],
      tech_specs_en: [],
      video_url: '',
      image_url: '',
      gallery_images: [],
      included_systems: [],
      included_services: [],
      installation_guide_ar: '',
      installation_guide_en: '',
      support_info_ar: '',
      support_info_en: '',
      status: 'active',
      is_active_edition: false,
      featured: true,
      sort_order: 0
    });
    setShowCreateModal(true);
  };

  const handleEdit = (content: PremiumContent) => {
    setSelectedContent(content);
    setFormData(content);
    setShowEditModal(true);
  };

  const handleSetActive = async (content: PremiumContent) => {
    try {
      const { error } = await setActivePremiumEdition(content.id);
      if (error) {
        showNotification({ type: 'error', message: t('notifications.updateError') });
      } else {
        showNotification({ 
          type: 'success', 
          message: language === 'ar' 
            ? `تم تفعيل النسخة المميزة: ${content.title_ar}` 
            : `Premium edition activated: ${content.title_en}` 
        });
        await loadData();
        logActivity('premium_edition_activated', { contentId: content.id, title: content.title_en });
      }
    } catch (error) {
      console.error('Error setting active edition:', error);
      showNotification({ type: 'error', message: t('notifications.updateError') });
    }
  };

  const handleDelete = (content: PremiumContent) => {
    showNotification({
      type: 'confirm',
      message: language === 'ar' 
        ? `هل أنت متأكد من حذف النسخة المميزة: ${content.title_ar}؟`
        : `Are you sure you want to delete premium edition: ${content.title_en}?`,
      onConfirm: async () => {
        try {
          const { error } = await deletePremiumContent(content.id);
          if (error) {
            showNotification({ type: 'error', message: t('notifications.deleteError') });
          } else {
            showNotification({ type: 'success', message: t('notifications.deleteSuccess') });
            await loadData();
            logActivity('premium_content_deleted', { contentId: content.id, title: content.title_en });
          }
        } catch (error) {
          console.error('Error deleting premium content:', error);
          showNotification({ type: 'error', message: t('notifications.deleteError') });
        }
      }
    });
  };

  const handleSave = async () => {
    try {
      if (selectedContent) {
        // Update existing content
        const { error } = await updatePremiumContent(selectedContent.id, formData);
        if (error) {
          showNotification({ type: 'error', message: t('notifications.updateError') });
        } else {
          showNotification({ type: 'success', message: t('notifications.updateSuccess') });
          setShowEditModal(false);
          await loadData();
          logActivity('premium_content_updated', { contentId: selectedContent.id, title: formData.title_en });
        }
      } else {
        // Create new content
        const { error } = await createPremiumContent(formData as Omit<PremiumContent, 'id' | 'created_at' | 'updated_at'>);
        if (error) {
          showNotification({ type: 'error', message: t('notifications.createError') });
        } else {
          showNotification({ type: 'success', message: t('notifications.createSuccess') });
          setShowCreateModal(false);
          await loadData();
          logActivity('premium_content_created', { title: formData.title_en });
        }
      }
    } catch (error) {
      console.error('Error saving premium content:', error);
      showNotification({ type: 'error', message: t('notifications.saveError') });
    }
  };

  const handleSystemPricing = (system: SystemService) => {
    setSelectedSystem(system);
    setPricingData({
      premium_price: (system as any).premium_price || system.price,
      installation_included: (system as any).installation_included || false,
      maintenance_included: (system as any).maintenance_included || false,
      is_available_for_premium: (system as any).is_available_for_premium !== false,
      subscription_discount_percentage: 0,
      description_ar: `سعر مخصص للنسخة المميزة: ${system.name_ar}`,
      description_en: `Premium edition special price: ${system.name_en}`
    });
    setShowPricingModal(true);
  };

  const handleServicePricing = (service: TechnicalService) => {
    setSelectedService(service);
    setPricingData({
      premium_price: (service as any).premium_price || service.price,
      installation_included: (service as any).installation_included || false,
      maintenance_included: (service as any).maintenance_included || false,
      is_available_for_premium: (service as any).is_available_for_premium !== false,
      subscription_discount_percentage: (service as any).subscription_discount_percentage || 0,
      description_ar: `سعر مخصص للنسخة المميزة: ${service.name_ar}`,
      description_en: `Premium edition special price: ${service.name_en}`
    });
    setShowPricingModal(true);
  };

  const handleSavePricing = async () => {
    try {
      if (selectedSystem) {
        const { error } = await updateSystemPremiumPricing(selectedSystem.id, pricingData);
        if (error) {
          showNotification({ type: 'error', message: t('notifications.updateError') });
        } else {
          showNotification({ type: 'success', message: t('notifications.updateSuccess') });
          setShowPricingModal(false);
          await loadData();
          logActivity('system_premium_pricing_updated', { systemId: selectedSystem.id, title: selectedSystem.name_en });
        }
      } else if (selectedService) {
        const { error } = await updateServicePremiumPricing(selectedService.id, pricingData);
        if (error) {
          showNotification({ type: 'error', message: t('notifications.updateError') });
        } else {
          showNotification({ type: 'success', message: t('notifications.updateSuccess') });
          setShowPricingModal(false);
          await loadData();
          logActivity('service_premium_pricing_updated', { serviceId: selectedService.id, title: selectedService.name_en });
        }
      }
    } catch (error) {
      console.error('Error saving pricing:', error);
      showNotification({ type: 'error', message: t('notifications.saveError') });
    }
  };

  // Filter content based on search and status
  const filteredContent = premiumContent.filter(content => {
    const matchesSearch = searchTerm === '' || 
      content.title_ar.toLowerCase().includes(searchTerm.toLowerCase()) ||
      content.title_en.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || content.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-secondary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">{language === 'ar' ? 'جاري التحميل...' : 'Loading...'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          {onBack && (
            <Button variant="outline" onClick={onBack} size="md">
              <ArrowLeft className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'رجوع' : 'Back'}
            </Button>
          )}
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <Crown className="w-8 h-8 text-yellow-400" />
            <h1 className="text-2xl font-bold text-white">
              {language === 'ar' ? 'إدارة المحتوى المميز' : 'Premium Content Management'}
            </h1>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Button variant="outline" onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
            {viewMode === 'grid' ? <List className="w-4 h-4 mr-2" /> : <Grid className="w-4 h-4 mr-2" />}
            {viewMode === 'grid' ? 'List' : 'Grid'}
          </Button>
          <Button variant="primary" onClick={handleCreate}>
            <Plus className="w-4 h-4 mr-2" />
            {language === 'ar' ? 'إضافة نسخة مميزة' : 'Add Premium Edition'}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            type="text"
            placeholder={language === 'ar' ? 'البحث...' : 'Search...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value as any)}
          className="bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none"
        >
          <option value="all">{language === 'ar' ? 'جميع الحالات' : 'All Status'}</option>
          <option value="active">{language === 'ar' ? 'نشط' : 'Active'}</option>
          <option value="inactive">{language === 'ar' ? 'غير نشط' : 'Inactive'}</option>
          <option value="draft">{language === 'ar' ? 'مسودة' : 'Draft'}</option>
        </select>

        <div className="flex items-center justify-end">
          <span className="text-gray-400 text-sm">
            {language === 'ar' 
              ? `${filteredContent.length} من ${premiumContent.length} نسخة`
              : `${filteredContent.length} of ${premiumContent.length} editions`
            }
          </span>
        </div>
      </div>

      {/* Premium Content Grid/List */}
      {filteredContent.length === 0 ? (
        <div className="text-center py-12">
          <Crown className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-400 mb-2">
            {language === 'ar' ? 'لا توجد نسخ مميزة' : 'No Premium Editions'}
          </h3>
          <p className="text-gray-500 mb-4">
            {language === 'ar' ? 'ابدأ بإنشاء نسخة مميزة جديدة' : 'Start by creating a new premium edition'}
          </p>
          <Button variant="primary" onClick={handleCreate}>
            <Plus className="w-4 h-4 mr-2" />
            {language === 'ar' ? 'إضافة نسخة مميزة' : 'Add Premium Edition'}
          </Button>
        </div>
      ) : (
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
          {filteredContent.map((content) => (
            <Card key={content.id} className="relative">
              {/* Active Edition Badge */}
              {content.is_active_edition && (
                <div className="absolute top-4 right-4 z-10">
                  <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
                    <Power className="w-3 h-3 mr-1" />
                    {language === 'ar' ? 'نشط' : 'Active'}
                  </div>
                </div>
              )}

              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-white mb-1">
                      {language === 'ar' ? content.title_ar : content.title_en}
                    </h3>
                    <p className="text-gray-400 text-sm line-clamp-2">
                      {language === 'ar' ? content.description_ar : content.description_en}
                    </p>
                  </div>
                </div>

                {/* Price */}
                <div className="mb-4">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <span className="text-2xl font-bold text-secondary">${content.price}</span>
                    {content.original_price && content.original_price > content.price && (
                      <>
                        <span className="text-gray-400 line-through text-sm">${content.original_price}</span>
                        <span className="bg-red-500 text-white px-2 py-1 rounded text-xs">
                          -{content.discount_percentage}%
                        </span>
                      </>
                    )}
                  </div>
                </div>

                {/* Features */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-1">
                    {(language === 'ar' ? content.features_ar : content.features_en)?.slice(0, 3).map((feature, index) => (
                      <span key={index} className="bg-secondary/20 text-secondary px-2 py-1 rounded text-xs">
                        {feature}
                      </span>
                    ))}
                    {(language === 'ar' ? content.features_ar : content.features_en)?.length > 3 && (
                      <span className="text-gray-400 text-xs px-2 py-1">
                        +{(language === 'ar' ? content.features_ar : content.features_en).length - 3} {language === 'ar' ? 'المزيد' : 'more'}
                      </span>
                    )}
                  </div>
                </div>

                {/* Status */}
                <div className="mb-4">
                  <span className={`px-2 py-1 rounded text-xs ${
                    content.status === 'active'
                      ? 'bg-green-500/20 text-green-400'
                      : content.status === 'inactive'
                      ? 'bg-red-500/20 text-red-400'
                      : 'bg-yellow-500/20 text-yellow-400'
                  }`}>
                    {content.status === 'active'
                      ? (language === 'ar' ? 'نشط' : 'Active')
                      : content.status === 'inactive'
                      ? (language === 'ar' ? 'غير نشط' : 'Inactive')
                      : (language === 'ar' ? 'مسودة' : 'Draft')
                    }
                  </span>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-700">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(content)}
                      title={language === 'ar' ? 'تعديل' : 'Edit'}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>

                    {!content.is_active_edition && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetActive(content)}
                        title={language === 'ar' ? 'تفعيل' : 'Activate'}
                      >
                        <Power className="w-4 h-4" />
                      </Button>
                    )}

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(content)}
                      title={language === 'ar' ? 'حذف' : 'Delete'}
                      className="text-red-400 hover:text-red-300"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>

                  <div className="text-xs text-gray-500">
                    {language === 'ar' ? 'المبيعات:' : 'Sales:'} {content.purchase_count}
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Systems & Services Pricing Section */}
      <div className="mt-12">
        <h2 className="text-xl font-bold text-white mb-6 flex items-center">
          <Settings className="w-6 h-6 mr-3" />
          {language === 'ar' ? 'إدارة الأسعار المخصصة' : 'Premium Pricing Management'}
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Systems Pricing */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Package className="w-5 h-5 mr-2" />
              {language === 'ar' ? 'أسعار الأنظمة التقنية' : 'Technical Systems Pricing'}
            </h3>
            <div className="space-y-3">
              {availableSystems.slice(0, 5).map((system) => (
                <div key={system.id} className="bg-background rounded-lg p-4 border border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-white">
                        {language === 'ar' ? system.name_ar : system.name_en}
                      </h4>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
                        <span className="text-gray-400 text-sm">
                          {language === 'ar' ? 'السعر العادي:' : 'Regular:'} ${system.price}
                        </span>
                        <span className="text-secondary text-sm">
                          {language === 'ar' ? 'المميز:' : 'Premium:'} ${(system as any).premium_price || system.price}
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSystemPricing(system)}
                    >
                      <DollarSign className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Services Pricing */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Wrench className="w-5 h-5 mr-2" />
              {language === 'ar' ? 'أسعار الخدمات التقنية' : 'Technical Services Pricing'}
            </h3>
            <div className="space-y-3">
              {availableServices.slice(0, 5).map((service) => (
                <div key={service.id} className="bg-background rounded-lg p-4 border border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-white">
                        {language === 'ar' ? service.name_ar : service.name_en}
                      </h4>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
                        <span className="text-gray-400 text-sm">
                          {language === 'ar' ? 'السعر العادي:' : 'Regular:'} ${service.price}
                        </span>
                        <span className="text-secondary text-sm">
                          {language === 'ar' ? 'المميز:' : 'Premium:'} ${(service as any).premium_price || service.price}
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleServicePricing(service)}
                    >
                      <DollarSign className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Create/Edit Modal */}
      {(showCreateModal || showEditModal) && (
        <Modal
          isOpen={showCreateModal || showEditModal}
          onClose={() => {
            setShowCreateModal(false);
            setShowEditModal(false);
            setSelectedContent(null);
          }}
          title={selectedContent
            ? (language === 'ar' ? 'تعديل النسخة المميزة' : 'Edit Premium Edition')
            : (language === 'ar' ? 'إضافة نسخة مميزة جديدة' : 'Add New Premium Edition')
          }
          size="xl"
        >
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label={language === 'ar' ? 'العنوان (عربي)' : 'Title (Arabic)'}
                value={formData.title_ar || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, title_ar: e.target.value }))}
                required
              />
              <Input
                label={language === 'ar' ? 'العنوان (إنجليزي)' : 'Title (English)'}
                value={formData.title_en || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, title_en: e.target.value }))}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-white font-medium mb-2">
                  {language === 'ar' ? 'الوصف (عربي)' : 'Description (Arabic)'}
                </label>
                <textarea
                  value={formData.description_ar || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, description_ar: e.target.value }))}
                  rows={3}
                  className="w-full bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none resize-none"
                />
              </div>
              <div>
                <label className="block text-white font-medium mb-2">
                  {language === 'ar' ? 'الوصف (إنجليزي)' : 'Description (English)'}
                </label>
                <textarea
                  value={formData.description_en || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, description_en: e.target.value }))}
                  rows={3}
                  className="w-full bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none resize-none"
                />
              </div>
            </div>

            {/* Pricing */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                label={language === 'ar' ? 'السعر ($)' : 'Price ($)'}
                type="number"
                value={formData.price || 0}
                onChange={(e) => setFormData(prev => ({ ...prev, price: Number(e.target.value) }))}
                leftIcon={<DollarSign />}
                required
              />
              <Input
                label={language === 'ar' ? 'السعر الأصلي ($)' : 'Original Price ($)'}
                type="number"
                value={formData.original_price || 0}
                onChange={(e) => setFormData(prev => ({ ...prev, original_price: Number(e.target.value) }))}
                leftIcon={<DollarSign />}
              />
              <Input
                label={language === 'ar' ? 'نسبة الخصم (%)' : 'Discount (%)'}
                type="number"
                value={formData.discount_percentage || 0}
                onChange={(e) => setFormData(prev => ({ ...prev, discount_percentage: Number(e.target.value) }))}
                max={100}
              />
            </div>

            {/* Media */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label={language === 'ar' ? 'رابط الفيديو' : 'Video URL'}
                value={formData.video_url || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, video_url: e.target.value }))}
                placeholder="https://youtube.com/watch?v=..."
              />
              <Input
                label={language === 'ar' ? 'رابط الصورة' : 'Image URL'}
                value={formData.image_url || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, image_url: e.target.value }))}
                placeholder="https://example.com/image.jpg"
              />
            </div>

            {/* Gallery Images */}
            <div>
              <label className="block text-white font-medium mb-2">
                {language === 'ar' ? 'معرض الصور (حتى 10 صور) - رابط واحد لكل سطر' : 'Image Gallery (up to 10 images) - One URL per line'}
              </label>
              <textarea
                value={formData.gallery_images?.join('\n') || ''}
                onChange={(e) => {
                  const urls = e.target.value.split('\n').filter(url => url.trim()).slice(0, 10);
                  setFormData(prev => ({ ...prev, gallery_images: urls }));
                }}
                rows={6}
                className="w-full bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none resize-none"
                placeholder={language === 'ar'
                  ? 'https://example.com/image1.jpg\nhttps://example.com/image2.jpg\n...'
                  : 'https://example.com/image1.jpg\nhttps://example.com/image2.jpg\n...'
                }
              />
              <p className="text-gray-400 text-sm mt-1">
                {language === 'ar'
                  ? `${formData.gallery_images?.length || 0}/10 صور مضافة`
                  : `${formData.gallery_images?.length || 0}/10 images added`
                }
              </p>

              {/* Gallery Preview */}
              {formData.gallery_images && formData.gallery_images.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-white font-medium mb-2">
                    {language === 'ar' ? 'معاينة المعرض' : 'Gallery Preview'}
                  </h4>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                    {formData.gallery_images.map((imageUrl, index) => (
                      <div key={index} className="relative aspect-square rounded-lg overflow-hidden group">
                        <img
                          src={imageUrl}
                          alt={`Gallery ${index + 1}`}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkVycm9yPC90ZXh0Pjwvc3ZnPg==';
                          }}
                        />
                        <button
                          type="button"
                          onClick={() => {
                            const newImages = [...(formData.gallery_images || [])];
                            newImages.splice(index, 1);
                            setFormData(prev => ({ ...prev, gallery_images: newImages }));
                          }}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                          title={language === 'ar' ? 'حذف الصورة' : 'Remove image'}
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-white font-medium mb-2">
                  {language === 'ar' ? 'المميزات (عربي) - سطر واحد لكل ميزة' : 'Features (Arabic) - One per line'}
                </label>
                <textarea
                  value={formData.features_ar?.join('\n') || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    features_ar: e.target.value.split('\n').filter(f => f.trim())
                  }))}
                  rows={4}
                  className="w-full bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none resize-none"
                />
              </div>
              <div>
                <label className="block text-white font-medium mb-2">
                  {language === 'ar' ? 'المميزات (إنجليزي) - سطر واحد لكل ميزة' : 'Features (English) - One per line'}
                </label>
                <textarea
                  value={formData.features_en?.join('\n') || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    features_en: e.target.value.split('\n').filter(f => f.trim())
                  }))}
                  rows={4}
                  className="w-full bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none resize-none"
                />
              </div>
            </div>

            {/* Settings */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-white font-medium mb-2">
                  {language === 'ar' ? 'الحالة' : 'Status'}
                </label>
                <select
                  value={formData.status || 'active'}
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                  className="w-full bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none"
                >
                  <option value="active">{language === 'ar' ? 'نشط' : 'Active'}</option>
                  <option value="inactive">{language === 'ar' ? 'غير نشط' : 'Inactive'}</option>
                  <option value="draft">{language === 'ar' ? 'مسودة' : 'Draft'}</option>
                </select>
              </div>

              <div className="flex items-center space-x-3 rtl:space-x-reverse pt-8">
                <input
                  type="checkbox"
                  id="is_active_edition"
                  checked={formData.is_active_edition || false}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_active_edition: e.target.checked }))}
                  className="text-secondary focus:ring-secondary"
                />
                <label htmlFor="is_active_edition" className="text-white">
                  {language === 'ar' ? 'النسخة النشطة' : 'Active Edition'}
                </label>
              </div>

              <div className="flex items-center space-x-3 rtl:space-x-reverse pt-8">
                <input
                  type="checkbox"
                  id="featured"
                  checked={formData.featured || false}
                  onChange={(e) => setFormData(prev => ({ ...prev, featured: e.target.checked }))}
                  className="text-secondary focus:ring-secondary"
                />
                <label htmlFor="featured" className="text-white">
                  {language === 'ar' ? 'مميز' : 'Featured'}
                </label>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-end space-x-4 rtl:space-x-reverse pt-6 border-t border-gray-700">
              <Button
                variant="outline"
                onClick={() => {
                  setShowCreateModal(false);
                  setShowEditModal(false);
                  setSelectedContent(null);
                }}
              >
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button variant="primary" onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                {language === 'ar' ? 'حفظ' : 'Save'}
              </Button>
            </div>
          </div>
        </Modal>
      )}

      {/* Pricing Modal */}
      {showPricingModal && (selectedSystem || selectedService) && (
        <Modal
          isOpen={showPricingModal}
          onClose={() => {
            setShowPricingModal(false);
            setSelectedSystem(null);
            setSelectedService(null);
          }}
          title={language === 'ar' ? 'إدارة الأسعار المخصصة' : 'Premium Pricing Management'}
          size="lg"
        >
          <div className="space-y-6">
            <div className="bg-background rounded-lg p-4 border border-gray-700">
              <h3 className="font-semibold text-white mb-2">
                {selectedSystem
                  ? (language === 'ar' ? selectedSystem.name_ar : selectedSystem.name_en)
                  : selectedService
                  ? (language === 'ar' ? selectedService.name_ar : selectedService.name_en)
                  : ''
                }
              </h3>
              <p className="text-gray-400 text-sm">
                {language === 'ar' ? 'السعر العادي:' : 'Regular Price:'} ${selectedSystem?.price || selectedService?.price}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label={language === 'ar' ? 'السعر المخصص ($)' : 'Premium Price ($)'}
                type="number"
                value={pricingData.premium_price}
                onChange={(e) => setPricingData(prev => ({ ...prev, premium_price: Number(e.target.value) }))}
                leftIcon={<DollarSign />}
                required
              />

              {selectedService && (
                <Input
                  label={language === 'ar' ? 'خصم الاشتراك (%)' : 'Subscription Discount (%)'}
                  type="number"
                  value={pricingData.subscription_discount_percentage}
                  onChange={(e) => setPricingData(prev => ({ ...prev, subscription_discount_percentage: Number(e.target.value) }))}
                  max={100}
                />
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <input
                  type="checkbox"
                  id="installation_included"
                  checked={pricingData.installation_included}
                  onChange={(e) => setPricingData(prev => ({ ...prev, installation_included: e.target.checked }))}
                  className="text-secondary focus:ring-secondary"
                />
                <label htmlFor="installation_included" className="text-white">
                  {language === 'ar' ? 'شامل التنصيب' : 'Installation Included'}
                </label>
              </div>

              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <input
                  type="checkbox"
                  id="maintenance_included"
                  checked={pricingData.maintenance_included}
                  onChange={(e) => setPricingData(prev => ({ ...prev, maintenance_included: e.target.checked }))}
                  className="text-secondary focus:ring-secondary"
                />
                <label htmlFor="maintenance_included" className="text-white">
                  {language === 'ar' ? 'شامل الصيانة' : 'Maintenance Included'}
                </label>
              </div>
            </div>

            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <input
                type="checkbox"
                id="is_available_for_premium"
                checked={pricingData.is_available_for_premium}
                onChange={(e) => setPricingData(prev => ({ ...prev, is_available_for_premium: e.target.checked }))}
                className="text-secondary focus:ring-secondary"
              />
              <label htmlFor="is_available_for_premium" className="text-white">
                {language === 'ar' ? 'متاح للنسخة المميزة' : 'Available for Premium Edition'}
              </label>
            </div>

            <div className="flex items-center justify-end space-x-4 rtl:space-x-reverse pt-6 border-t border-gray-700">
              <Button
                variant="outline"
                onClick={() => {
                  setShowPricingModal(false);
                  setSelectedSystem(null);
                  setSelectedService(null);
                }}
              >
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button variant="primary" onClick={handleSavePricing}>
                <Save className="w-4 h-4 mr-2" />
                {language === 'ar' ? 'حفظ الأسعار' : 'Save Pricing'}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default PremiumContentManager;
