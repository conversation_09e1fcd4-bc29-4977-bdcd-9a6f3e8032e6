const express = require('express');
const router = express.Router();
const { executeQuery } = require('../config/database');
const { verifyToken, requireAdmin } = require('../middleware/auth');
const crypto = require('crypto');

// Generate UUID v4 using crypto
function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Enhanced Premium Content Management Routes
 *
 * Handles:
 * - Premium content CRUD operations with new schema
 * - Integration with premium systems and services pricing
 * - Active edition management (only one active at a time)
 * - Premium pricing and availability management
 * - System and service add-ons with custom pricing
 */

// Safe JSON parsing utility
const safeJsonParse = (jsonData, defaultValue = []) => {
  if (!jsonData) return defaultValue;
  if (typeof jsonData === 'object') return jsonData;
  try {
    return JSON.parse(jsonData);
  } catch (error) {
    console.warn('JSON parse error for:', jsonData.substring(0, 50));
    return defaultValue;
  }
};

// Get active premium edition (public)
router.get('/', async (req, res) => {
  try {
    console.log('Fetching active premium edition...');

    const { rows: premiumContent } = await executeQuery(`
      SELECT
        id, title_ar, title_en, description_ar, description_en,
        detailed_description_ar, detailed_description_en,
        price, original_price, discount_percentage, category,
        COALESCE(features_ar, '[]') as features_ar,
        COALESCE(features_en, '[]') as features_en,
        COALESCE(tech_specs_ar, '[]') as tech_specs_ar,
        COALESCE(tech_specs_en, '[]') as tech_specs_en,
        video_url, image_url,
        COALESCE(gallery_images, '[]') as gallery_images,
        COALESCE(included_systems, '[]') as included_systems,
        COALESCE(included_services, '[]') as included_services,
        status, is_active_edition, featured, sort_order,
        purchase_count, rating, rating_count,
        installation_guide_ar, installation_guide_en,
        support_info_ar, support_info_en,
        created_at, updated_at
      FROM premium_content
      WHERE status = 'active' AND is_active_edition = 1
      LIMIT 1
    `);

    if (premiumContent.length === 0) {
      return res.json({
        success: true,
        data: { premiumContent: null, message: 'No active premium edition found' }
      });
    }

    const content = premiumContent[0];

    // Process the premium content
    const processedContent = {
      ...content,
      features_ar: safeJsonParse(content.features_ar, []),
      features_en: safeJsonParse(content.features_en, []),
      tech_specs_ar: safeJsonParse(content.tech_specs_ar, []),
      tech_specs_en: safeJsonParse(content.tech_specs_en, []),
      gallery_images: safeJsonParse(content.gallery_images, []),
      included_systems: safeJsonParse(content.included_systems, []),
      included_services: safeJsonParse(content.included_services, [])
    };

    console.log(`Found active premium edition: ${content.title_en}`);

    res.json({
      success: true,
      data: { premiumContent: processedContent }
    });
  } catch (error) {
    console.error('Error fetching premium content:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching premium content',
      error: error.message
    });
  }
});

// Get premium add-ons (systems and services with premium pricing)
router.get('/addons', async (req, res) => {
  try {
    console.log('Fetching premium add-ons...');

    // Get systems with premium pricing
    const { rows: premiumSystems } = await executeQuery(`
      SELECT
        s.id, s.name_ar, s.name_en, s.description_ar, s.description_en,
        s.price as original_price, s.category, s.type,
        COALESCE(s.features_ar, '[]') as features_ar,
        COALESCE(s.features_en, '[]') as features_en,
        COALESCE(s.tech_specs_ar, '[]') as tech_specs_ar,
        COALESCE(s.tech_specs_en, '[]') as tech_specs_en,
        s.video_url, s.image_url,
        COALESCE(s.gallery_images, '[]') as gallery_images,
        s.status,
        p.premium_price, p.installation_included, p.maintenance_included,
        p.is_available_for_premium, p.description_ar as premium_description_ar,
        p.description_en as premium_description_en
      FROM system_services s
      LEFT JOIN premium_system_pricing p ON s.id = p.system_id
      WHERE s.status = 'active' AND s.is_premium_addon = 1
      AND (p.is_available_for_premium = 1 OR p.is_available_for_premium IS NULL)
      ORDER BY s.name_ar ASC
    `);

    // Get services with premium pricing
    const { rows: premiumServices } = await executeQuery(`
      SELECT
        s.id, s.name_ar, s.name_en, s.description_ar, s.description_en,
        s.price as original_price, s.category, s.service_type, s.subscription_type,
        COALESCE(s.features_ar, '[]') as features_ar,
        COALESCE(s.features_en, '[]') as features_en,
        s.video_url, s.image_url,
        s.status,
        p.premium_price, p.installation_included, p.maintenance_included,
        p.is_available_for_premium, p.subscription_discount_percentage,
        p.description_ar as premium_description_ar,
        p.description_en as premium_description_en
      FROM technical_services s
      LEFT JOIN premium_service_pricing p ON s.id = p.service_id
      WHERE s.status = 'active' AND s.is_premium_addon = 1
      AND (p.is_available_for_premium = 1 OR p.is_available_for_premium IS NULL)
      ORDER BY s.name_ar ASC
    `);

    // Process systems
    const processedSystems = premiumSystems.map(system => {
      return {
        ...system,
        name: { ar: system.name_ar, en: system.name_en },
        description: { ar: system.description_ar, en: system.description_en },
        features: { ar: safeJsonParse(system.features_ar, []), en: safeJsonParse(system.features_en, []) },
        tech_specs: { ar: safeJsonParse(system.tech_specs_ar, []), en: safeJsonParse(system.tech_specs_en, []) },
        gallery_images: safeJsonParse(system.gallery_images, []),
        premium_price: system.premium_price || system.original_price,
        price: system.premium_price || system.original_price,
        installation_included: Boolean(system.installation_included),
        maintenance_included: Boolean(system.maintenance_included)
      };
    });

    // Process services
    const processedServices = premiumServices.map(service => {
      return {
        ...service,
        name: { ar: service.name_ar, en: service.name_en },
        description: { ar: service.description_ar, en: service.description_en },
        features: { ar: safeJsonParse(service.features_ar, []), en: safeJsonParse(service.features_en, []) },
        premium_price: service.premium_price || service.original_price,
        price: service.premium_price || service.original_price,
        premiumPrice: service.premium_price || service.original_price,
        subscriptionType: service.subscription_type || 'none',
        installation_included: Boolean(service.installation_included),
        maintenance_included: Boolean(service.maintenance_included),
        subscription_discount_percentage: service.subscription_discount_percentage || 0
      };
    });

    console.log(`Found ${processedSystems.length} premium systems and ${processedServices.length} premium services`);

    res.json({
      success: true,
      data: {
        systems: processedSystems,
        services: processedServices
      }
    });
  } catch (error) {
    console.error('Error fetching premium add-ons:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching premium add-ons',
      error: error.message
    });
  }
});

// Get premium content with available systems (for admin)
router.get('/admin', verifyToken, requireAdmin, async (req, res) => {
  try {
    // Get all premium content with enhanced fields
    const { rows: premiumContent } = await executeQuery(`
      SELECT
        id, title_ar, title_en, description_ar, description_en,
        detailed_description_ar, detailed_description_en,
        price, original_price, discount_percentage, category,
        features_ar, features_en, tech_specs_ar, tech_specs_en,
        video_url, image_url, gallery_images,
        included_systems, included_services, status, is_active_edition,
        featured, sort_order, purchase_count, rating, rating_count,
        installation_guide_ar, installation_guide_en,
        support_info_ar, support_info_en,
        created_at, updated_at
      FROM premium_content
      ORDER BY is_active_edition DESC, created_at DESC
    `);

    // Get available premium systems with pricing
    const { rows: availableSystems } = await executeQuery(`
      SELECT
        s.id, s.name_ar, s.name_en, s.description_ar, s.description_en,
        s.price, s.category, s.type, s.features_ar, s.features_en,
        s.tech_specs_ar, s.tech_specs_en, s.video_url, s.image_url,
        s.gallery_images, s.status, s.is_premium_addon,
        p.premium_price, p.installation_included, p.maintenance_included,
        p.is_available_for_premium
      FROM system_services s
      LEFT JOIN premium_system_pricing p ON s.id = p.system_id
      WHERE s.is_premium_addon = 1
      ORDER BY s.name_ar ASC
    `);

    // Get available technical services with pricing
    const { rows: availableServices } = await executeQuery(`
      SELECT
        s.id, s.name_ar, s.name_en, s.description_ar, s.description_en,
        s.price, s.category, s.service_type, s.subscription_type,
        s.features_ar, s.features_en, s.status, s.is_premium_addon,
        p.premium_price, p.installation_included, p.maintenance_included,
        p.is_available_for_premium, p.subscription_discount_percentage
      FROM technical_services s
      LEFT JOIN premium_service_pricing p ON s.id = p.service_id
      WHERE s.is_premium_addon = 1
      ORDER BY s.name_ar ASC
    `);

    // Process JSON fields safely
    const processedContent = premiumContent.map(content => ({
      ...content,
      features_ar: safeJsonParse(content.features_ar, []),
      features_en: safeJsonParse(content.features_en, []),
      tech_specs_ar: safeJsonParse(content.tech_specs_ar, []),
      tech_specs_en: safeJsonParse(content.tech_specs_en, []),
      gallery_images: safeJsonParse(content.gallery_images, []),
      included_systems: safeJsonParse(content.included_systems, []),
      included_services: safeJsonParse(content.included_services, [])
    }));

    const processedSystems = availableSystems.map(system => ({
      ...system,
      features_ar: safeJsonParse(system.features_ar, []),
      features_en: safeJsonParse(system.features_en, []),
      tech_specs_ar: safeJsonParse(system.tech_specs_ar, []),
      tech_specs_en: safeJsonParse(system.tech_specs_en, []),
      gallery_images: safeJsonParse(system.gallery_images, []),
      premium_price: system.premium_price || system.price,
      installation_included: Boolean(system.installation_included),
      maintenance_included: Boolean(system.maintenance_included),
      is_available_for_premium: system.is_available_for_premium !== 0
    }));

    const processedServices = availableServices.map(service => ({
      ...service,
      features_ar: safeJsonParse(service.features_ar, []),
      features_en: safeJsonParse(service.features_en, []),
      premium_price: service.premium_price || service.price,
      installation_included: Boolean(service.installation_included),
      maintenance_included: Boolean(service.maintenance_included),
      is_available_for_premium: service.is_available_for_premium !== 0,
      subscription_discount_percentage: service.subscription_discount_percentage || 0
    }));

    res.json({
      success: true,
      data: {
        premiumContent: processedContent,
        availableSystems: processedSystems,
        availableServices: processedServices
      }
    });
  } catch (error) {
    console.error('Error fetching admin premium data:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching admin premium data',
      error: error.message
    });
  }
});

// Set active premium edition
router.post('/set-active/:id', verifyToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if the premium content exists
    const { rows: existingContent } = await executeQuery(`
      SELECT id, title_ar, title_en FROM premium_content WHERE id = ?
    `, [id]);

    if (existingContent.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Premium content not found'
      });
    }

    // Set all premium content to inactive
    await executeQuery(`UPDATE premium_content SET is_active_edition = 0`);

    // Set the selected one as active
    await executeQuery(`
      UPDATE premium_content SET is_active_edition = 1 WHERE id = ?
    `, [id]);

    res.json({
      success: true,
      message: `Premium edition "${existingContent[0].title_en}" is now active`
    });
  } catch (error) {
    console.error('Error setting active premium edition:', error);
    res.status(500).json({
      success: false,
      message: 'Error setting active premium edition',
      error: error.message
    });
  }
});

// Update system premium pricing
router.post('/pricing/system/:systemId', verifyToken, requireAdmin, async (req, res) => {
  try {
    const { systemId } = req.params;
    const {
      premium_price,
      installation_included = false,
      maintenance_included = false,
      is_available_for_premium = true,
      description_ar = '',
      description_en = ''
    } = req.body;

    // Check if system exists
    const { rows: existingSystem } = await executeQuery(`
      SELECT id, name_ar, name_en FROM system_services WHERE id = ?
    `, [systemId]);

    if (existingSystem.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'System not found'
      });
    }

    // Insert or update premium pricing
    await executeQuery(`
      INSERT INTO premium_system_pricing (
        id, system_id, premium_price, installation_included,
        maintenance_included, is_available_for_premium,
        description_ar, description_en, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      ON DUPLICATE KEY UPDATE
        premium_price = VALUES(premium_price),
        installation_included = VALUES(installation_included),
        maintenance_included = VALUES(maintenance_included),
        is_available_for_premium = VALUES(is_available_for_premium),
        description_ar = VALUES(description_ar),
        description_en = VALUES(description_en),
        updated_at = NOW()
    `, [
      uuidv4(), systemId, premium_price, installation_included,
      maintenance_included, is_available_for_premium,
      description_ar, description_en
    ]);

    res.json({
      success: true,
      message: `Premium pricing updated for system "${existingSystem[0].name_en}"`
    });
  } catch (error) {
    console.error('Error updating system premium pricing:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating system premium pricing',
      error: error.message
    });
  }
});

// Update service premium pricing
router.post('/pricing/service/:serviceId', verifyToken, requireAdmin, async (req, res) => {
  try {
    const { serviceId } = req.params;
    const {
      premium_price,
      installation_included = false,
      maintenance_included = false,
      is_available_for_premium = true,
      subscription_discount_percentage = 0,
      description_ar = '',
      description_en = ''
    } = req.body;

    // Check if service exists
    const { rows: existingService } = await executeQuery(`
      SELECT id, name_ar, name_en FROM technical_services WHERE id = ?
    `, [serviceId]);

    if (existingService.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    // Insert or update premium pricing
    await executeQuery(`
      INSERT INTO premium_service_pricing (
        id, service_id, premium_price, installation_included,
        maintenance_included, is_available_for_premium,
        subscription_discount_percentage, description_ar, description_en,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      ON DUPLICATE KEY UPDATE
        premium_price = VALUES(premium_price),
        installation_included = VALUES(installation_included),
        maintenance_included = VALUES(maintenance_included),
        is_available_for_premium = VALUES(is_available_for_premium),
        subscription_discount_percentage = VALUES(subscription_discount_percentage),
        description_ar = VALUES(description_ar),
        description_en = VALUES(description_en),
        updated_at = NOW()
    `, [
      uuidv4(), serviceId, premium_price, installation_included,
      maintenance_included, is_available_for_premium,
      subscription_discount_percentage, description_ar, description_en
    ]);

    res.json({
      success: true,
      message: `Premium pricing updated for service "${existingService[0].name_en}"`
    });
  } catch (error) {
    console.error('Error updating service premium pricing:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating service premium pricing',
      error: error.message
    });
  }
});

// Create premium content
router.post('/', verifyToken, requireAdmin, async (req, res) => {
  try {
    const {
      title_ar, title_en, description_ar, description_en,
      detailed_description_ar = '', detailed_description_en = '',
      price, original_price = null, discount_percentage = 0, category,
      features_ar = [], features_en = [], tech_specs_ar = [], tech_specs_en = [],
      video_url = '', image_url = '', gallery_images = [],
      included_systems = [], included_services = [],
      installation_guide_ar = '', installation_guide_en = '',
      support_info_ar = '', support_info_en = '',
      status = 'active', is_active_edition = false, featured = false, sort_order = 0
    } = req.body;

    const contentId = uuidv4();

    // If setting as active edition, deactivate others first
    if (is_active_edition) {
      await executeQuery(`UPDATE premium_content SET is_active_edition = 0`);
    }

    await executeQuery(`
      INSERT INTO premium_content (
        id, title_ar, title_en, description_ar, description_en,
        detailed_description_ar, detailed_description_en,
        price, original_price, discount_percentage, category,
        features_ar, features_en, tech_specs_ar, tech_specs_en,
        video_url, image_url, gallery_images,
        included_systems, included_services,
        installation_guide_ar, installation_guide_en,
        support_info_ar, support_info_en,
        status, is_active_edition, featured, sort_order,
        purchase_count, rating, rating_count, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, 0.00, 0, NOW(), NOW())
    `, [
      contentId, title_ar, title_en, description_ar, description_en,
      detailed_description_ar, detailed_description_en,
      price, original_price, discount_percentage, category,
      JSON.stringify(features_ar), JSON.stringify(features_en),
      JSON.stringify(tech_specs_ar), JSON.stringify(tech_specs_en),
      video_url, image_url, JSON.stringify(gallery_images),
      JSON.stringify(included_systems), JSON.stringify(included_services),
      installation_guide_ar, installation_guide_en,
      support_info_ar, support_info_en,
      status, is_active_edition, featured, sort_order
    ]);

    // Get the created content
    const { rows: createdContent } = await executeQuery(`
      SELECT * FROM premium_content WHERE id = ?
    `, [contentId]);

    const processedContent = {
      ...createdContent[0],
      features_ar: safeJsonParse(createdContent[0].features_ar, []),
      features_en: safeJsonParse(createdContent[0].features_en, []),
      tech_specs_ar: safeJsonParse(createdContent[0].tech_specs_ar, []),
      tech_specs_en: safeJsonParse(createdContent[0].tech_specs_en, []),
      gallery_images: safeJsonParse(createdContent[0].gallery_images, []),
      included_systems: safeJsonParse(createdContent[0].included_systems, []),
      included_services: safeJsonParse(createdContent[0].included_services, [])
    };

    res.json({
      success: true,
      message: 'Premium content created successfully',
      data: processedContent
    });
  } catch (error) {
    console.error('Error creating premium content:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating premium content',
      error: error.message
    });
  }
});

// Update premium content
router.put('/:id', verifyToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Build dynamic update query
    const updateFields = [];
    const updateValues = [];

    const allowedFields = [
      'title_ar', 'title_en', 'description_ar', 'description_en',
      'detailed_description_ar', 'detailed_description_en',
      'price', 'original_price', 'discount_percentage', 'category',
      'features_ar', 'features_en', 'tech_specs_ar', 'tech_specs_en',
      'video_url', 'image_url', 'gallery_images',
      'included_systems', 'included_services',
      'installation_guide_ar', 'installation_guide_en',
      'support_info_ar', 'support_info_en',
      'status', 'is_active_edition', 'featured', 'sort_order'
    ];

    // Handle is_active_edition special case
    if (updateData.hasOwnProperty('is_active_edition') && updateData.is_active_edition) {
      // If setting as active, deactivate others first
      await executeQuery(`UPDATE premium_content SET is_active_edition = 0 WHERE id != ?`, [id]);
    }

    allowedFields.forEach(field => {
      if (updateData.hasOwnProperty(field)) {
        updateFields.push(`${field} = ?`);

        // Handle JSON fields
        if (['features_ar', 'features_en', 'tech_specs_ar', 'tech_specs_en', 'gallery_images', 'included_systems', 'included_services'].includes(field)) {
          updateValues.push(JSON.stringify(updateData[field]));
        } else {
          updateValues.push(updateData[field]);
        }
      }
    });

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No valid fields to update'
      });
    }

    updateFields.push('updated_at = NOW()');
    updateValues.push(id);

    await executeQuery(`
      UPDATE premium_content 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `, updateValues);

    // Get updated content
    const { rows: updatedContent } = await executeQuery(`
      SELECT * FROM premium_content WHERE id = ?
    `, [id]);

    if (updatedContent.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Premium content not found'
      });
    }

    const processedContent = {
      ...updatedContent[0],
      features_ar: safeJsonParse(updatedContent[0].features_ar, []),
      features_en: safeJsonParse(updatedContent[0].features_en, []),
      tech_specs_ar: safeJsonParse(updatedContent[0].tech_specs_ar, []),
      tech_specs_en: safeJsonParse(updatedContent[0].tech_specs_en, []),
      gallery_images: safeJsonParse(updatedContent[0].gallery_images, []),
      included_systems: safeJsonParse(updatedContent[0].included_systems, []),
      included_services: safeJsonParse(updatedContent[0].included_services, [])
    };

    res.json({
      success: true,
      message: 'Premium content updated successfully',
      data: processedContent
    });
  } catch (error) {
    console.error('Error updating premium content:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating premium content',
      error: error.message
    });
  }
});

// Delete premium content
router.delete('/:id', verifyToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const { rows: existingContent } = await executeQuery(`
      SELECT id FROM premium_content WHERE id = ?
    `, [id]);

    if (existingContent.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Premium content not found'
      });
    }

    await executeQuery(`
      DELETE FROM premium_content WHERE id = ?
    `, [id]);

    res.json({
      success: true,
      message: 'Premium content deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting premium content:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting premium content',
      error: error.message
    });
  }
});

module.exports = router;
