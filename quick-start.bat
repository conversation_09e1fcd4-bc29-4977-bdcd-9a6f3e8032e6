@echo off
chcp 65001 >nul
title 🚀 Khanfashariya - تشغيل سريع

echo.
echo ==========================================
echo    🚀 تشغيل سريع لجميع خدمات المشروع
echo ==========================================
echo.

:: إيقاف العمليات السابقة
echo 🛑 تنظيف العمليات السابقة...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im ngrok.exe >nul 2>&1
timeout /t 2 >nul

:: تشغيل الخوادم
echo.
echo 🔧 تشغيل الخادم الخلفي...
start "Backend" cmd /k "title 🔧 Backend-3001 && npm run start"

echo 🎨 تشغيل الخادم الأمامي...
start "Frontend" cmd /k "title 🎨 Frontend-5173 && npm run dev"

:: انتظار تشغيل الخوادم
echo ⏳ انتظار تشغيل الخوادم...
timeout /t 8

:: تشغيل ngrok
echo.
echo 🌐 تشغيل ngrok للخادم الخلفي...
start "Backend-ngrok" cmd /k "title 🌐 Backend-ngrok && ngrok http 3001"

echo 🌐 تشغيل ngrok للخادم الأمامي...
start "Frontend-ngrok" cmd /k "title 🌐 Frontend-ngrok && ngrok http 5173 --web-addr=localhost:4041"

:: انتظار تشغيل ngrok
echo ⏳ انتظار تشغيل ngrok...
timeout /t 10

:: الحصول على الروابط
echo.
echo 🔗 الحصول على الروابط...
node scripts/get-ngrok-urls.js

echo.
echo ========================================
echo           ✅ تم التشغيل بنجاح!
echo ========================================
echo.
echo 📋 الخدمات:
echo   🔧 Backend: http://localhost:3001
echo   🎨 Frontend: http://localhost:5173
echo   🌐 Ngrok: تحقق من current-ngrok-urls.json
echo.
echo 📁 الروابط محفوظة في: current-ngrok-urls.json
echo.
echo ⚠️ احتفظ بالنوافذ مفتوحة للحفاظ على الخدمات
echo.
pause
