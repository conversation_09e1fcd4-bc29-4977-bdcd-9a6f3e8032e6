<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    
    <link rel="icon" type="image/png" href="/favicon.png" />

    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Security-Policy" content="frame-src 'self' https://www.youtube-nocookie.com https://www.youtube.com; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com;" />
    <title>الخنفشارية للأنظمة - تقنيات المستقبل للحروب الرقمية</title>
    <meta name="description" content="نطور أنظمة حربية متقدمة وحلول تقنية مستقبلية لخوادم Metin2 بتقنيات 2050" />
    <meta name="keywords" content="Metin2, أنظمة, خوادم, تطوير, برمجة, العربية, الخنفشارية, حرب, تقنية" />
    <meta name="author" content="الخنفشارية للأنظمة" />
    
    <meta property="og:title" content="الخنفشارية للأنظمة - تقنيات المستقبل للحروب الرقمية" />
    <meta property="og:description" content="نطور أنظمة حربية متقدمة وحلول تقنية مستقبلية لخوادم Metin2 بتقنيات 2050" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://khanfashariya.com" />
    
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="الخنفشارية للأنظمة - تقنيات المستقبل للحروب الرقمية" />
    <meta name="twitter:description" content="نطور أنظمة حربية متقدمة وحلول تقنية مستقبلية لخوادم Metin2 بتقنيات 2050" />
    
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">

    <!-- Fallback for fonts if preload fails -->
    <noscript>
      <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap">
      <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    </noscript>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>