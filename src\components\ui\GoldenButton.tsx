import React from 'react';
import { LucideIcon } from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';

interface GoldenButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success' | 'warning' | 'info';
  size?: 'sm' | 'md' | 'lg';
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  fullWidth?: boolean;
}

/**
 * Golden Ratio Button Component
 * Unified button with consistent styling and variants
 */
const GoldenButton: React.FC<GoldenButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  icon: Icon,
  iconPosition = 'left',
  disabled = false,
  loading = false,
  className = '',
  type = 'button',
  fullWidth = false
}) => {
  const { language } = useTranslation();

  const baseClasses = `
    inline-flex items-center justify-center font-semibold transition-all duration-300 
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800
    disabled:opacity-50 disabled:cursor-not-allowed
    ${fullWidth ? 'w-full' : ''}
  `;

  const sizeClasses = {
    sm: 'golden-btn-sm',
    md: 'golden-btn-md', 
    lg: 'golden-btn-lg'
  };

  const variantClasses = {
    primary: `
      bg-gradient-to-r from-secondary to-accent hover:from-secondary/80 hover:to-accent/80 
      text-white shadow-lg hover:shadow-xl focus:ring-secondary
    `,
    secondary: `
      bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 
      text-white shadow-lg hover:shadow-xl focus:ring-purple-500
    `,
    outline: `
      border-2 border-gray-500 bg-gray-800/50 hover:bg-gray-700/70 hover:border-gray-400
      text-gray-200 hover:text-white focus:ring-gray-400/50 backdrop-blur-sm
      font-medium tracking-wide transition-all duration-200
    `,
    ghost: `
      bg-transparent hover:bg-gray-700/50
      text-gray-300 hover:text-white focus:ring-gray-500/50
      font-medium tracking-wide transition-all duration-200
    `,
    danger: `
      bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 
      text-white shadow-lg hover:shadow-xl focus:ring-red-500
    `,
    success: `
      bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800
      text-white shadow-lg hover:shadow-xl focus:ring-emerald-500/50 border border-emerald-500/20
      font-semibold tracking-wide
    `,
    warning: `
      bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800
      text-black shadow-lg hover:shadow-xl focus:ring-yellow-500/50 border border-yellow-500/20
      font-semibold tracking-wide
    `,
    info: `
      bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-700 hover:to-blue-700
      text-white shadow-lg hover:shadow-xl focus:ring-cyan-500/50 border border-cyan-500/20
      font-semibold tracking-wide
    `
  };

  const iconSize = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  const iconSpacing = language === 'ar' 
    ? (iconPosition === 'left' ? 'ml-2' : 'mr-2')
    : (iconPosition === 'left' ? 'mr-2' : 'ml-2');

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={`
        ${baseClasses}
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${className}
      `}
    >
      {loading ? (
        <div className={`animate-spin rounded-full border-2 border-white border-t-transparent ${iconSize[size]}`} />
      ) : (
        <>
          {Icon && iconPosition === 'left' && (
            <Icon className={`${iconSize[size]} ${iconSpacing}`} />
          )}
          {children}
          {Icon && iconPosition === 'right' && (
            <Icon className={`${iconSize[size]} ${iconSpacing}`} />
          )}
        </>
      )}
    </button>
  );
};

export default GoldenButton;
