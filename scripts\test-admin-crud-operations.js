const axios = require('axios');

async function testAdminCRUDOperations() {
  console.log('🔧 اختبار وظائف CRUD في لوحة التحكم\n');
  
  let token;
  let testSystemId;
  let testServiceId;
  
  try {
    // 1. تسجيل الدخول كإداري
    console.log('1️⃣ تسجيل الدخول كإداري...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    token = loginResponse.data.data.tokens.accessToken;
    const headers = { Authorization: `Bearer ${token}` };
    console.log('   ✅ تم تسجيل الدخول بنجاح');
    
    // 2. اختبار إضافة نظام جديد (CREATE)
    console.log('\n2️⃣ اختبار إضافة نظام جديد...');
    const newSystemData = {
      name_ar: 'نظام اختبار جديد',
      name_en: 'New Test System',
      description_ar: 'وصف النظام الجديد للاختبار',
      description_en: 'New test system description',
      price: 99.99,
      category: 'general',
      type: 'regular',
      features_ar: ['ميزة 1', 'ميزة 2'],
      features_en: ['Feature 1', 'Feature 2'],
      tech_specs_ar: ['مواصفة 1', 'مواصفة 2'],
      tech_specs_en: ['Spec 1', 'Spec 2'],
      video_url: '',
      image_url: '',
      gallery_images: [],
      status: 'active'
    };
    
    try {
      const createSystemResponse = await axios.post('http://localhost:3001/api/admin/systems', newSystemData, { headers });
      testSystemId = createSystemResponse.data.id;
      console.log('   ✅ تم إنشاء النظام بنجاح - ID:', testSystemId);
    } catch (createError) {
      console.error('   ❌ فشل في إنشاء النظام:', createError.response?.status, createError.response?.data);
    }
    
    // 3. اختبار قراءة النظام (READ)
    console.log('\n3️⃣ اختبار قراءة النظام...');
    if (testSystemId) {
      try {
        const readSystemResponse = await axios.get(`http://localhost:3001/api/systems/${testSystemId}`);
        console.log('   ✅ تم قراءة النظام بنجاح');
        console.log('   📋 اسم النظام:', readSystemResponse.data.name_ar);
      } catch (readError) {
        console.error('   ❌ فشل في قراءة النظام:', readError.response?.status);
      }
    }
    
    // 4. اختبار تحديث النظام (UPDATE)
    console.log('\n4️⃣ اختبار تحديث النظام...');
    if (testSystemId) {
      const updateData = {
        name_ar: 'نظام اختبار محدث',
        name_en: 'Updated Test System',
        price: 149.99
      };
      
      try {
        const updateSystemResponse = await axios.put(`http://localhost:3001/api/admin/systems/${testSystemId}`, updateData, { headers });
        console.log('   ✅ تم تحديث النظام بنجاح');
      } catch (updateError) {
        console.error('   ❌ فشل في تحديث النظام:', updateError.response?.status, updateError.response?.data);
      }
    }
    
    // 5. اختبار إضافة خدمة تقنية جديدة
    console.log('\n5️⃣ اختبار إضافة خدمة تقنية جديدة...');
    const newServiceData = {
      name_ar: 'خدمة اختبار جديدة',
      name_en: 'New Test Service',
      description_ar: 'وصف الخدمة الجديدة للاختبار',
      description_en: 'New test service description',
      price: 199.99,
      category: 'general',
      service_type: 'development',
      features_ar: ['ميزة خدمة 1', 'ميزة خدمة 2'],
      features_en: ['Service Feature 1', 'Service Feature 2'],
      tech_specs_ar: ['مواصفة خدمة 1'],
      tech_specs_en: ['Service Spec 1'],
      is_premium_addon: true,
      premium_price: 299.99,
      subscription_type: 'monthly',
      delivery_time_ar: '7 أيام',
      delivery_time_en: '7 days',
      video_url: '',
      image_url: '',
      gallery_images: [],
      status: 'active'
    };
    
    try {
      const createServiceResponse = await axios.post('http://localhost:3001/api/admin/technical-services', newServiceData, { headers });
      testServiceId = createServiceResponse.data.id;
      console.log('   ✅ تم إنشاء الخدمة بنجاح - ID:', testServiceId);
    } catch (createServiceError) {
      console.error('   ❌ فشل في إنشاء الخدمة:', createServiceError.response?.status, createServiceError.response?.data);
    }
    
    // 6. اختبار قراءة جميع البيانات من قاعدة البيانات
    console.log('\n6️⃣ اختبار قراءة البيانات من قاعدة البيانات...');
    
    const systemsResponse = await axios.get('http://localhost:3001/api/systems/admin', { headers });
    const servicesResponse = await axios.get('http://localhost:3001/api/services/admin/technical', { headers });
    
    console.log('   📊 عدد الأنظمة في قاعدة البيانات:', systemsResponse.data.length);
    console.log('   📊 عدد الخدمات في قاعدة البيانات:', servicesResponse.data.length);
    
    // التحقق من وجود البيانات الجديدة
    const newSystem = systemsResponse.data.find(s => s.id === testSystemId);
    const newService = servicesResponse.data.find(s => s.id === testServiceId);
    
    console.log('   🔍 النظام الجديد موجود في قاعدة البيانات:', newSystem ? '✅ نعم' : '❌ لا');
    console.log('   🔍 الخدمة الجديدة موجودة في قاعدة البيانات:', newService ? '✅ نعم' : '❌ لا');
    
    // 7. اختبار حذف البيانات التجريبية (CLEANUP)
    console.log('\n7️⃣ تنظيف البيانات التجريبية...');
    
    if (testSystemId) {
      try {
        await axios.delete(`http://localhost:3001/api/admin/systems/${testSystemId}`, { headers });
        console.log('   ✅ تم حذف النظام التجريبي');
      } catch (deleteError) {
        console.error('   ⚠️ فشل في حذف النظام التجريبي:', deleteError.response?.status);
      }
    }
    
    if (testServiceId) {
      try {
        await axios.delete(`http://localhost:3001/api/admin/technical-services/${testServiceId}`, { headers });
        console.log('   ✅ تم حذف الخدمة التجريبية');
      } catch (deleteServiceError) {
        console.error('   ⚠️ فشل في حذف الخدمة التجريبية:', deleteServiceError.response?.status);
      }
    }
    
    console.log('\n🎉 اكتمل اختبار وظائف CRUD بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في اختبار CRUD:', error.message);
  }
}

testAdminCRUDOperations();
