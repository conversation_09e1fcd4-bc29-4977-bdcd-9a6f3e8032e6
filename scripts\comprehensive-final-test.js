const axios = require('axios');
const mysql = require('mysql2/promise');

/**
 * Comprehensive Final Test Suite
 * 
 * Tests all aspects of the technical systems management:
 * 1. Database connectivity and schema validation
 * 2. API endpoints functionality
 * 3. CRUD operations
 * 4. Data integrity and JSON parsing
 * 5. Frontend-backend integration
 * 6. Authentication and authorization
 * 7. Error handling and edge cases
 */

class ComprehensiveFinalTest {
  constructor() {
    this.connection = null;
    this.testResults = {
      database: { passed: 0, failed: 0, tests: [] },
      api: { passed: 0, failed: 0, tests: [] },
      crud: { passed: 0, failed: 0, tests: [] },
      integration: { passed: 0, failed: 0, tests: [] },
      auth: { passed: 0, failed: 0, tests: [] }
    };
    this.adminToken = null;
    this.userToken = null;
    this.testSystemId = null;
    this.testServiceId = null;
  }

  async runAllTests() {
    console.log('🚀 Starting Comprehensive Final Test Suite');
    console.log('=' .repeat(70));
    console.log(`📅 Test Date: ${new Date().toISOString()}`);
    console.log(`🎯 Objective: Validate complete MySQL integration\n`);

    try {
      // 1. Database Tests
      await this.runDatabaseTests();
      
      // 2. Authentication Tests
      await this.runAuthenticationTests();
      
      // 3. API Tests
      await this.runApiTests();
      
      // 4. CRUD Tests
      await this.runCrudTests();
      
      // 5. Integration Tests
      await this.runIntegrationTests();
      
      // 6. Generate Final Report
      this.generateFinalReport();
      
    } catch (error) {
      console.error('💥 Test suite failed:', error.message);
    } finally {
      await this.cleanup();
    }
  }

  async runDatabaseTests() {
    console.log('🗄️ Running Database Tests...\n');
    
    // Test 1: Database Connection
    await this.test('database', 'Database Connection', async () => {
      this.connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '123456',
        database: 'khanfashariya_db'
      });
      return true;
    });

    // Test 2: Table Structure Validation
    await this.test('database', 'Table Structure Validation', async () => {
      const [tables] = await this.connection.execute("SHOW TABLES");
      const requiredTables = ['system_services', 'technical_services', 'users', 'orders'];
      
      for (const table of requiredTables) {
        const tableExists = tables.some(t => Object.values(t)[0] === table);
        if (!tableExists) throw new Error(`Table ${table} not found`);
      }
      return true;
    });

    // Test 3: Data Integrity Check
    await this.test('database', 'Data Integrity Check', async () => {
      const [systems] = await this.connection.execute('SELECT COUNT(*) as count FROM system_services WHERE status = "active"');
      const [services] = await this.connection.execute('SELECT COUNT(*) as count FROM technical_services WHERE status = "active"');
      
      const systemCount = systems[0].count;
      const serviceCount = services[0].count;
      
      if (systemCount === 0) throw new Error('No active systems found');
      if (serviceCount === 0) throw new Error('No active services found');
      
      console.log(`   📊 Found ${systemCount} active systems, ${serviceCount} active services`);
      return true;
    });

    // Test 4: JSON Fields Validation
    await this.test('database', 'JSON Fields Validation', async () => {
      const [systems] = await this.connection.execute('SELECT features_ar, gallery_images FROM system_services LIMIT 1');
      
      if (systems.length === 0) throw new Error('No systems to test');
      
      const system = systems[0];
      
      // Test JSON parsing
      if (typeof system.features_ar === 'string') {
        JSON.parse(system.features_ar);
      }
      if (typeof system.gallery_images === 'string') {
        JSON.parse(system.gallery_images);
      }
      
      return true;
    });
  }

  async runAuthenticationTests() {
    console.log('\n🔐 Running Authentication Tests...\n');

    // Test 1: Admin Login
    await this.test('auth', 'Admin Login', async () => {
      const response = await axios.post('http://localhost:3001/api/auth/login', {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      if (response.status !== 200) throw new Error('Login failed');
      if (!response.data.data.tokens.accessToken) throw new Error('No access token received');
      
      this.adminToken = response.data.data.tokens.accessToken;
      console.log('   ✅ Admin token acquired');
      return true;
    });

    // Test 2: User Login
    await this.test('auth', 'User Login', async () => {
      const response = await axios.post('http://localhost:3001/api/auth/login', {
        email: '<EMAIL>',
        password: '123456'
      });
      
      if (response.status !== 200) throw new Error('User login failed');
      
      this.userToken = response.data.data.tokens.accessToken;
      console.log('   ✅ User token acquired');
      return true;
    });

    // Test 3: Token Validation
    await this.test('auth', 'Token Validation', async () => {
      const headers = { Authorization: `Bearer ${this.adminToken}` };
      const response = await axios.get('http://localhost:3001/api/systems/admin', { headers });
      
      if (response.status !== 200) throw new Error('Token validation failed');
      return true;
    });
  }

  async runApiTests() {
    console.log('\n🌐 Running API Tests...\n');

    // Test 1: Public Systems API
    await this.test('api', 'Public Systems API', async () => {
      const response = await axios.get('http://localhost:3001/api/systems');
      
      if (response.status !== 200) throw new Error('API call failed');
      if (!response.data.data.systems) throw new Error('No systems data');
      if (!Array.isArray(response.data.data.systems)) throw new Error('Systems data is not array');
      
      const systems = response.data.data.systems;
      console.log(`   📊 Retrieved ${systems.length} systems`);
      
      // Test JSON parsing
      if (systems.length > 0) {
        const firstSystem = systems[0];
        if (!Array.isArray(firstSystem.features_ar)) throw new Error('Features not parsed as array');
        if (!Array.isArray(firstSystem.gallery_images)) throw new Error('Gallery images not parsed as array');
      }
      
      return true;
    });

    // Test 2: Public Services API
    await this.test('api', 'Public Services API', async () => {
      const response = await axios.get('http://localhost:3001/api/services/technical');
      
      if (response.status !== 200) throw new Error('Services API call failed');
      if (!response.data.data.services) throw new Error('No services data');
      
      const services = response.data.data.services;
      console.log(`   📊 Retrieved ${services.length} services`);
      return true;
    });

    // Test 3: Admin Systems API
    await this.test('api', 'Admin Systems API', async () => {
      const headers = { Authorization: `Bearer ${this.adminToken}` };
      const response = await axios.get('http://localhost:3001/api/systems/admin', { headers });
      
      if (response.status !== 200) throw new Error('Admin API call failed');
      if (!Array.isArray(response.data)) throw new Error('Admin API response not array');
      
      console.log(`   📊 Admin retrieved ${response.data.length} systems`);
      return true;
    });
  }

  async runCrudTests() {
    console.log('\n🔧 Running CRUD Tests...\n');

    // Test 1: Create System
    await this.test('crud', 'Create System', async () => {
      const headers = { Authorization: `Bearer ${this.adminToken}` };
      const systemData = {
        name_ar: 'نظام اختبار شامل',
        name_en: 'Comprehensive Test System',
        description_ar: 'نظام للاختبار الشامل',
        description_en: 'System for comprehensive testing',
        price: 99.99,
        category: 'test',
        type: 'regular',
        is_premium_addon: true,
        features_ar: ['ميزة اختبار 1', 'ميزة اختبار 2'],
        features_en: ['Test feature 1', 'Test feature 2'],
        tech_specs_ar: ['مواصفة 1', 'مواصفة 2'],
        tech_specs_en: ['Spec 1', 'Spec 2'],
        gallery_images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
        status: 'active'
      };
      
      const response = await axios.post('http://localhost:3001/api/admin/systems', systemData, { headers });
      
      if (response.status !== 200) throw new Error('Create failed');
      if (!response.data.data.id) throw new Error('No ID returned');
      
      this.testSystemId = response.data.data.id;
      console.log(`   ✅ Created system with ID: ${this.testSystemId}`);
      return true;
    });

    // Test 2: Read System
    await this.test('crud', 'Read System', async () => {
      const response = await axios.get(`http://localhost:3001/api/systems/${this.testSystemId}`);
      
      if (response.status !== 200) throw new Error('Read failed');
      
      const system = response.data.data.system;
      if (system.name_ar !== 'نظام اختبار شامل') throw new Error('Data mismatch');
      if (!Array.isArray(system.features_ar)) throw new Error('Features not array');
      if (system.features_ar.length !== 2) throw new Error('Features count mismatch');
      
      console.log('   ✅ System data verified');
      return true;
    });

    // Test 3: Update System
    await this.test('crud', 'Update System', async () => {
      const headers = { Authorization: `Bearer ${this.adminToken}` };
      const updateData = {
        name_ar: 'نظام اختبار شامل محدث',
        price: 149.99,
        features_ar: ['ميزة محدثة 1', 'ميزة محدثة 2', 'ميزة جديدة']
      };
      
      const response = await axios.put(`http://localhost:3001/api/admin/systems/${this.testSystemId}`, updateData, { headers });
      
      if (response.status !== 200) throw new Error('Update failed');
      if (!response.data.data) throw new Error('No updated data returned');
      
      const updatedSystem = response.data.data;
      if (updatedSystem.name_ar !== 'نظام اختبار شامل محدث') throw new Error('Update not applied');
      if (parseFloat(updatedSystem.price) !== 149.99) throw new Error('Price not updated');
      
      console.log('   ✅ System updated successfully');
      return true;
    });

    // Test 4: Delete System
    await this.test('crud', 'Delete System', async () => {
      const headers = { Authorization: `Bearer ${this.adminToken}` };
      const response = await axios.delete(`http://localhost:3001/api/admin/systems/${this.testSystemId}`, { headers });
      
      if (response.status !== 200) throw new Error('Delete failed');
      
      // Verify deletion
      try {
        await axios.get(`http://localhost:3001/api/systems/${this.testSystemId}`);
        throw new Error('System still exists after deletion');
      } catch (error) {
        if (error.response && error.response.status === 404) {
          console.log('   ✅ System deleted successfully');
          return true;
        }
        throw error;
      }
    });
  }

  async runIntegrationTests() {
    console.log('\n🔗 Running Integration Tests...\n');

    // Test 1: Data Consistency
    await this.test('integration', 'Data Consistency', async () => {
      const publicResponse = await axios.get('http://localhost:3001/api/systems');
      const adminHeaders = { Authorization: `Bearer ${this.adminToken}` };
      const adminResponse = await axios.get('http://localhost:3001/api/systems/admin', { adminHeaders });
      
      const publicCount = publicResponse.data.data.systems.length;
      const adminCount = adminResponse.data.length;
      
      // Admin should see all systems, public should see only active ones
      if (adminCount < publicCount) throw new Error('Admin sees fewer systems than public');
      
      console.log(`   📊 Public: ${publicCount}, Admin: ${adminCount} systems`);
      return true;
    });

    // Test 2: JSON Parsing Consistency
    await this.test('integration', 'JSON Parsing Consistency', async () => {
      const response = await axios.get('http://localhost:3001/api/systems?limit=3');
      const systems = response.data.data.systems;
      
      for (const system of systems) {
        if (!Array.isArray(system.features_ar)) throw new Error(`System ${system.id} features_ar not array`);
        if (!Array.isArray(system.gallery_images)) throw new Error(`System ${system.id} gallery_images not array`);
        if (!Array.isArray(system.tech_specs_ar)) throw new Error(`System ${system.id} tech_specs_ar not array`);
      }
      
      console.log('   ✅ All JSON fields properly parsed');
      return true;
    });

    // Test 3: Performance Test
    await this.test('integration', 'Performance Test', async () => {
      const startTime = Date.now();
      
      // Make multiple concurrent requests
      const promises = [
        axios.get('http://localhost:3001/api/systems'),
        axios.get('http://localhost:3001/api/services/technical'),
        axios.get('http://localhost:3001/api/systems?category=utility'),
        axios.get('http://localhost:3001/api/systems?featured=true')
      ];
      
      await Promise.all(promises);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (duration > 5000) throw new Error(`Performance too slow: ${duration}ms`);
      
      console.log(`   ⚡ All requests completed in ${duration}ms`);
      return true;
    });
  }

  async test(category, name, testFunction) {
    try {
      console.log(`   🧪 ${name}...`);
      await testFunction();
      this.testResults[category].passed++;
      this.testResults[category].tests.push({ name, status: 'PASSED' });
      console.log(`   ✅ ${name} - PASSED`);
    } catch (error) {
      this.testResults[category].failed++;
      this.testResults[category].tests.push({ name, status: 'FAILED', error: error.message });
      console.log(`   ❌ ${name} - FAILED: ${error.message}`);
    }
  }

  generateFinalReport() {
    console.log('\n' + '=' .repeat(70));
    console.log('📊 COMPREHENSIVE TEST RESULTS');
    console.log('=' .repeat(70));

    let totalPassed = 0;
    let totalFailed = 0;

    for (const [category, results] of Object.entries(this.testResults)) {
      const categoryPassed = results.passed;
      const categoryFailed = results.failed;
      const categoryTotal = categoryPassed + categoryFailed;
      
      totalPassed += categoryPassed;
      totalFailed += categoryFailed;
      
      console.log(`\n📋 ${category.toUpperCase()} TESTS:`);
      console.log(`   ✅ Passed: ${categoryPassed}/${categoryTotal}`);
      console.log(`   ❌ Failed: ${categoryFailed}/${categoryTotal}`);
      console.log(`   📈 Success Rate: ${categoryTotal > 0 ? Math.round((categoryPassed / categoryTotal) * 100) : 0}%`);
    }

    const totalTests = totalPassed + totalFailed;
    const overallSuccessRate = totalTests > 0 ? Math.round((totalPassed / totalTests) * 100) : 0;

    console.log('\n' + '=' .repeat(70));
    console.log('🎯 OVERALL RESULTS:');
    console.log(`   📊 Total Tests: ${totalTests}`);
    console.log(`   ✅ Passed: ${totalPassed}`);
    console.log(`   ❌ Failed: ${totalFailed}`);
    console.log(`   📈 Success Rate: ${overallSuccessRate}%`);

    if (overallSuccessRate >= 95) {
      console.log('\n🎉 EXCELLENT! MySQL integration is working perfectly!');
      console.log('✅ All critical systems are operational');
      console.log('🚀 Ready for production use');
    } else if (overallSuccessRate >= 85) {
      console.log('\n✅ GOOD! Most systems are working correctly');
      console.log('⚠️ Some minor issues need attention');
    } else {
      console.log('\n⚠️ NEEDS ATTENTION! Several issues found');
      console.log('❌ Review failed tests and fix issues');
    }

    console.log('\n📅 Test completed at:', new Date().toISOString());
    console.log('=' .repeat(70));
  }

  async cleanup() {
    if (this.connection) {
      await this.connection.end();
    }
    
    // Clean up any test data
    if (this.testSystemId && this.adminToken) {
      try {
        const headers = { Authorization: `Bearer ${this.adminToken}` };
        await axios.delete(`http://localhost:3001/api/admin/systems/${this.testSystemId}`, { headers });
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  }
}

// Main execution
async function main() {
  const testSuite = new ComprehensiveFinalTest();
  await testSuite.runAllTests();
}

// Export for use in other scripts
module.exports = ComprehensiveFinalTest;

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
