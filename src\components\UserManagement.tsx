import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuth } from '../hooks/useAuth';
import { useNotification } from '../hooks/useNotification';
import {
  getAllUsers,
  updateUserRole,
  signUp as createUser,
  getAllOrders,
  updateOrderStatus,
} from '../lib/apiServices';
import {
  User,
  UserService,
} from '../lib/database';
import {
  Users,
  Shield,
  UserCheck,
  UserX,
  Settings,
  Search,
  Filter,
  Edit3,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  Star,
  Crown,
  Eye,
  MoreVertical,
  Plus,
  Save,
  X as XIcon,
} from 'lucide-react';
import Pagination from './ui/Pagination';

interface UserManagementProps {
  onClose: () => void;
  isEmbedded?: boolean;
}

const UserManagement: React.FC<UserManagementProps> = ({ onClose, isEmbedded = false }) => {
  const { t, language } = useTranslation();
  const { userProfile } = useAuth();
  const { showNotification } = useNotification();
  const [users, setUsers] = useState<User[]>([]);
  const [services, setServices] = useState<UserService[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<'all' | 'admin' | 'user'>('all');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(30);

  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [showAddUser, setShowAddUser] = useState(false);
  const [newUserData, setNewUserData] = useState({
    email: '',
    username: '',
    full_name: '',
    password: '',
    role: 'user' as 'user' | 'admin',
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const usersResult = await getAllUsers();
      const ordersResult = await getAllOrders(); // Get orders instead of services

      if (usersResult.data) {
        setUsers(usersResult.data);
      } else if (usersResult.error) {
        console.error('Error loading users:', usersResult.error);
      }

      // Convert orders to services format for compatibility
      if (ordersResult.data) {
        const servicesFromOrders = ordersResult.data.map(order => ({
          id: order.id,
          user_id: order.user_id,
          service_name: order.service_name,
          service_type: order.service_type,
          status: order.status,
          created_at: order.created_at,
          updated_at: order.updated_at,
          price: order.price
        }));
        setServices(servicesFromOrders);
      } else if (ordersResult.error) {
        console.error('Error loading orders:', ordersResult.error);
        setServices([]); // Set empty array if no orders
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRoleChange = async (userId: string, newRole: 'user' | 'admin') => {
    try {
      const { error } = await updateUserRole(userId, newRole);
      if (error) {
        showNotification({ type: 'error', message: t('notifications.roleUpdateError') });
      } else {
        showNotification({ type: 'success', message: t('notifications.roleUpdateSuccess') });
        await loadData();
      }
    } catch (error) {
      console.error('Error updating user role:', error);
      showNotification({ type: 'error', message: t('notifications.roleUpdateError') });
    }
  };

  const handleAddUser = async () => {
    if (!newUserData.email || !newUserData.username || !newUserData.full_name || !newUserData.password) {
      showNotification({type: 'error', message: t('notifications.fillAllFields')});
      return;
    }

    try {
      const result = await createUser(newUserData.email, newUserData.password, {
        username: newUserData.username,
        full_name: newUserData.full_name,
      });

      if (result.data?.user) {
        if (newUserData.role === 'admin') {
          await updateUserRole(result.data.user.id, 'admin');
        }
        setNewUserData({ email: '', username: '', full_name: '', password: '', role: 'user' });
        setShowAddUser(false);
        await loadData();
        showNotification({type: 'success', message: t('notifications.userAddSuccess')});
      } else {
        showNotification({ type: 'error', message: result.error?.message || 'Error adding user' });
      }
    } catch (error: any) {
      console.error('Error adding user:', error);
      showNotification({ type: 'error', message: error.message || 'Error adding user' });
    }
  };

  const handleServiceStatusChange = async (serviceId: string, status: UserService['status']) => {
    try {
      const { error } = await updateOrderStatus(serviceId, status);
      if (error) {
        showNotification({ type: 'error', message: t('notifications.orderStatusUpdateError') });
      } else {
        showNotification({ type: 'success', message: t('notifications.orderStatusUpdateSuccess') });
        await loadData();
      }
    } catch (error) {
      console.error('Error updating service status:', error);
    }
  };

  const filteredUsers = users.filter(user => {
    const searchTermLower = searchTerm.toLowerCase();
    return (
      user.full_name.toLowerCase().includes(searchTermLower) ||
      user.email.toLowerCase().includes(searchTermLower) ||
      user.username.toLowerCase().includes(searchTermLower)
    ) && (roleFilter === 'all' || user.role === roleFilter);
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, roleFilter]);

  const getUserServices = (userId: string) => {
    return services.filter(service => service.user_id === userId);
  };
  
  const getStatusIcon = (status: UserService['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'active': return <Clock className="w-4 h-4 text-blue-500" />;
      case 'pending': return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'cancelled': return <XCircle className="w-4 h-4 text-red-500" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: UserService['status']) => {
    const statusMap: Record<UserService['status'], string> = {
      completed: language === 'ar' ? 'مكتمل' : 'Completed',
      active: language === 'ar' ? 'نشط' : 'Active',
      pending: language === 'ar' ? 'في الانتظار' : 'Pending',
      cancelled: language === 'ar' ? 'ملغي' : 'Cancelled',
      confirmed: language === 'ar' ? 'مؤكد' : 'Confirmed',
      in_progress: language === 'ar' ? 'قيد التنفيذ' : 'In Progress',
      on_hold: language === 'ar' ? 'معلق' : 'On Hold',
      refunded: language === 'ar' ? 'مسترد' : 'Refunded',
      testing: language === 'ar' ? 'قيد الاختبار' : 'Testing'
    };
    return statusMap[status] || status;
  };

  if (!userProfile || userProfile.role !== 'admin') {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-primary rounded-lg p-6 max-w-md w-full mx-4 text-center">
            <Shield className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-white mb-2">{language === 'ar' ? 'غير مصرح' : 'Unauthorized'}</h3>
            <p className="text-gray-300 mb-4">{language === 'ar' ? 'ليس لديك صلاحية للوصول إلى هذه الصفحة' : 'You do not have permission to access this page'}</p>
            <button onClick={onClose} className="bg-secondary text-primary px-4 py-2 rounded-lg hover:bg-secondary/80 transition-colors">
            {language === 'ar' ? 'إغلاق' : 'Close'}
            </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Users className="w-8 h-8 text-secondary" />
          <h2 className="text-2xl font-bold text-white">{language === 'ar' ? 'إدارة المستخدمين' : 'User Management'}</h2>
        </div>
        <button
            onClick={() => setShowAddUser(true)}
            className="flex items-center space-x-2 rtl:space-x-reverse bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors"
        >
            <Plus className="w-4 h-4" />
            <span>{language === 'ar' ? 'إضافة عضو' : 'Add User'}</span>
        </button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1 relative">
          <Search className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder={language === 'ar' ? 'البحث عن المستخدمين...' : 'Search users...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-background border border-gray-600 rounded-lg pl-10 rtl:pr-10 rtl:pl-4 pr-4 py-2 text-white placeholder-gray-400 focus:border-secondary focus:outline-none"
          />
        </div>
        <div className="relative">
          <Filter className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value as 'all' | 'admin' | 'user')}
            className="bg-background border border-gray-600 rounded-lg pl-10 rtl:pr-10 rtl:pl-4 pr-8 py-2 text-white focus:border-secondary focus:outline-none appearance-none"
          >
            <option value="all">{language === 'ar' ? 'جميع الرتب' : 'All Roles'}</option>
            <option value="admin">{language === 'ar' ? 'مدير' : 'Admin'}</option>
            <option value="user">{language === 'ar' ? 'مستخدم' : 'User'}</option>
          </select>
        </div>
      </div>
      
      <div className="space-y-4">
        {paginatedUsers.map((user) => (
          <div key={user.id} className="bg-background rounded-lg border border-gray-700 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <div className="w-12 h-12 bg-gradient-to-r from-secondary to-accent rounded-full flex items-center justify-center">
                  {user.role === 'admin' ? <Crown className="w-6 h-6 text-white" /> : <UserCheck className="w-6 h-6 text-white" />}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">{user.full_name}</h3>
                  <p className="text-gray-400">@{user.username}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${user.role === 'admin' ? 'bg-red-500/20 text-red-400' : 'bg-blue-500/20 text-blue-400'}`}>
                  {user.role === 'admin' ? (language === 'ar' ? 'مدير' : 'Admin') : (language === 'ar' ? 'مستخدم' : 'User')}
                </span>
                {user.id !== userProfile?.id && (
                    <select value={user.role} onChange={(e) => handleRoleChange(user.id, e.target.value as 'user' | 'admin')} className="bg-primary border border-gray-700 rounded-lg px-3 py-2 text-white">
                        <option value="user">{language === 'ar' ? 'مستخدم' : 'User'}</option>
                        <option value="admin">{language === 'ar' ? 'مدير' : 'Admin'}</option>
                    </select>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {filteredUsers.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={filteredUsers.length}
          itemsPerPage={itemsPerPage}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          itemsPerPageOptions={[10, 20, 30, 50]}
          className="mt-6"
        />
      )}

       {showAddUser && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-60 p-4">
            <div className="bg-primary rounded-xl p-6 w-full max-w-md">
                <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold text-white">{language === 'ar' ? 'إضافة عضو جديد' : 'Add New User'}</h3>
                    <button onClick={() => setShowAddUser(false)} className="text-gray-500 hover:text-white"><XIcon className="w-5 h-5" /></button>
                </div>
                <div className="space-y-4">
                    <input type="email" value={newUserData.email} onChange={(e) => setNewUserData({...newUserData, email: e.target.value})} className="w-full px-3 py-2 bg-background border border-gray-600 rounded-lg text-white" placeholder={language === 'ar' ? 'البريد الإلكتروني' : 'Email'} />
                    <input type="text" value={newUserData.username} onChange={(e) => setNewUserData({...newUserData, username: e.target.value})} className="w-full px-3 py-2 bg-background border border-gray-600 rounded-lg text-white" placeholder={language === 'ar' ? 'اسم المستخدم' : 'Username'} />
                    <input type="text" value={newUserData.full_name} onChange={(e) => setNewUserData({...newUserData, full_name: e.target.value})} className="w-full px-3 py-2 bg-background border border-gray-600 rounded-lg text-white" placeholder={language === 'ar' ? 'الاسم الكامل' : 'Full Name'} />
                    <input type="password" value={newUserData.password} onChange={(e) => setNewUserData({...newUserData, password: e.target.value})} className="w-full px-3 py-2 bg-background border border-gray-600 rounded-lg text-white" placeholder={language === 'ar' ? 'كلمة المرور' : 'Password'} />
                    <select value={newUserData.role} onChange={(e) => setNewUserData({...newUserData, role: e.target.value as 'user' | 'admin'})} className="w-full px-3 py-2 bg-background border border-gray-600 rounded-lg text-white">
                        <option value="user">{language === 'ar' ? 'مستخدم' : 'User'}</option>
                        <option value="admin">{language === 'ar' ? 'مدير' : 'Admin'}</option>
                    </select>
                    <div className="flex space-x-3 rtl:space-x-reverse pt-4">
                        <button onClick={handleAddUser} className="flex-1 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center justify-center space-x-2">
                            <Save className="w-4 h-4" /><span>{language === 'ar' ? 'إضافة' : 'Add User'}</span>
                        </button>
                        <button onClick={() => setShowAddUser(false)} className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">{language === 'ar' ? 'إلغاء' : 'Cancel'}</button>
                    </div>
                </div>
            </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;