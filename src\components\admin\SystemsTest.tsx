import React, { useState, useEffect } from 'react';

const SystemsTest: React.FC = () => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testAPI = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('khanfashariya_access_token');
      console.log('Token exists:', !!token);
      
      const response = await fetch('/api/systems/admin', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Response status:', response.status);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      console.log('API Result:', result);
      setData(result);
      
    } catch (err: any) {
      console.error('API Error:', err);
      setError(err.message);
    }
    
    setLoading(false);
  };

  useEffect(() => {
    testAPI();
  }, []);

  return (
    <div className="p-6 bg-gray-900 min-h-screen text-white">
      <h1 className="text-2xl font-bold mb-4">Systems API Test</h1>
      
      <div className="mb-4">
        <button 
          onClick={testAPI}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          disabled={loading}
        >
          {loading ? 'Testing...' : 'Test API'}
        </button>
      </div>

      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-2">Authentication Status:</h3>
        <p>Token exists: {!!localStorage.getItem('khanfashariya_access_token') ? 'Yes' : 'No'}</p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded">
          <h3 className="text-lg font-semibold mb-2 text-red-400">Error:</h3>
          <p>{error}</p>
        </div>
      )}

      {data && (
        <div className="mb-4 p-4 bg-green-500/20 border border-green-500/30 rounded">
          <h3 className="text-lg font-semibold mb-2 text-green-400">Success:</h3>
          <p>Data type: {typeof data}</p>
          <p>Is array: {Array.isArray(data) ? 'Yes' : 'No'}</p>
          <p>Length: {Array.isArray(data) ? data.length : 'N/A'}</p>
          
          <details className="mt-4">
            <summary className="cursor-pointer">Raw Data:</summary>
            <pre className="mt-2 text-xs overflow-auto bg-gray-800 p-2 rounded">
              {JSON.stringify(data, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  );
};

export default SystemsTest;
