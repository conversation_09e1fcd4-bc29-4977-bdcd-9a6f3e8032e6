#!/usr/bin/env node

const { exec } = require('child_process');
const fs = require('fs');

async function testNgrokTunnel() {
  console.log('🔍 Testing ngrok tunnel...');
  
  try {
    // Get current tunnels
    const tunnels = await new Promise((resolve, reject) => {
      exec('curl -s http://127.0.0.1:4040/api/tunnels', (error, stdout) => {
        if (error) {
          reject(error);
          return;
        }
        
        try {
          const data = JSON.parse(stdout);
          resolve(data.tunnels || []);
        } catch (parseError) {
          reject(parseError);
        }
      });
    });

    if (tunnels.length === 0) {
      console.log('❌ No tunnels found');
      return;
    }

    const tunnel = tunnels[0];
    const baseUrl = tunnel.public_url;
    
    console.log(`🔗 Testing tunnel: ${baseUrl}`);

    // Test health endpoint
    const testUrl = `${baseUrl}/health`;
    console.log(`📡 Testing: ${testUrl}`);

    // Use curl with proper headers
    const curlCommand = `curl -s -H "ngrok-skip-browser-warning: true" -H "User-Agent: TestScript/1.0" -w "\\n%{http_code}" "${testUrl}"`;
    
    const result = await new Promise((resolve) => {
      exec(curlCommand, (error, stdout, stderr) => {
        if (error) {
          resolve({ success: false, error: error.message });
          return;
        }
        
        const lines = stdout.trim().split('\n');
        const statusCode = lines[lines.length - 1];
        const response = lines.slice(0, -1).join('\n');
        
        resolve({
          success: true,
          statusCode: parseInt(statusCode),
          response: response
        });
      });
    });

    if (result.success) {
      console.log(`✅ Status: ${result.statusCode}`);
      console.log(`📄 Response: ${result.response.substring(0, 100)}...`);
      
      if (result.statusCode === 200) {
        console.log('🎉 Tunnel is working perfectly!');
        
        // Save working tunnel info
        const tunnelInfo = {
          timestamp: new Date().toISOString(),
          status: 'working',
          baseUrl: baseUrl,
          testResult: result,
          ready_for_testsprite: true
        };
        
        fs.writeFileSync('working-tunnel.json', JSON.stringify(tunnelInfo, null, 2));
        console.log('💾 Working tunnel info saved to working-tunnel.json');
        
        // Update TestSprite config
        const testSpriteConfig = {
          name: "Khanfashariya API Tests - Working Tunnel",
          baseUrl: baseUrl,
          headers: {
            "ngrok-skip-browser-warning": "true",
            "User-Agent": "TestSprite/1.0",
            "Content-Type": "application/json"
          },
          endpoints: [
            {
              path: "/health",
              method: "GET",
              description: "Health check",
              testCases: [{ name: "Health Check", expectedStatus: 200 }]
            },
            {
              path: "/api/auth/login",
              method: "POST",
              description: "Authentication",
              testCases: [
                {
                  name: "Valid Login",
                  body: {
                    email: "<EMAIL>",
                    password: "admin123"
                  },
                  expectedStatus: 200
                }
              ]
            },
            {
              path: "/api/systems",
              method: "GET",
              description: "Get systems",
              testCases: [{ name: "Get Systems", expectedStatus: 200 }]
            }
          ]
        };
        
        fs.writeFileSync('testsprite-working.json', JSON.stringify(testSpriteConfig, null, 2));
        console.log('📋 TestSprite config saved to testsprite-working.json');
        
        console.log('\n🎯 TestSprite Instructions:');
        console.log(`1. Use Base URL: ${baseUrl}`);
        console.log('2. Add header: ngrok-skip-browser-warning: true');
        console.log('3. Import config from: testsprite-working.json');
        console.log('4. Run tests!');
        
      } else {
        console.log(`⚠️ Unexpected status code: ${result.statusCode}`);
      }
    } else {
      console.log(`❌ Test failed: ${result.error}`);
    }

  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
}

testNgrokTunnel();
