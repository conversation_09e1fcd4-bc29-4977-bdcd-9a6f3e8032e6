{"name": "Khanfashariya API - Verified Ready", "baseUrl": "https://09e0719da445.ngrok-free.app", "timestamp": "2025-07-21T23:17:51.469Z", "readinessScore": 100, "headers": {"ngrok-skip-browser-warning": "true", "User-Agent": "TestSprite/1.0", "Content-Type": "application/json", "Accept": "application/json"}, "endpoints": [{"path": "/health", "method": "GET", "description": "Health check endpoint", "testCases": [{"name": "Health Check", "expectedStatus": 200, "description": "Verify server is running"}]}, {"path": "/api/auth/login", "method": "POST", "description": "User authentication", "testCases": [{"name": "<PERSON><PERSON>", "body": {"email": "<EMAIL>", "password": "admin123"}, "expectedStatus": 200, "description": "Login with valid admin credentials"}, {"name": "Invalid Credentials", "body": {"email": "<EMAIL>", "password": "wrongpass"}, "expectedStatus": 401, "description": "Test error handling for invalid credentials"}]}, {"path": "/api/systems", "method": "GET", "description": "Technical systems endpoint", "testCases": [{"name": "Get All Systems", "expectedStatus": 200, "description": "Retrieve all technical systems"}]}, {"path": "/api/services/technical", "method": "GET", "description": "Technical services endpoint", "testCases": [{"name": "Get Technical Services", "expectedStatus": 200, "description": "Retrieve all technical services"}]}, {"path": "/api/services/premium", "method": "GET", "description": "Premium services endpoint", "testCases": [{"name": "Get Premium Services", "expectedStatus": 200, "description": "Retrieve all premium services"}]}], "testResults": [{"success": true, "endpoint": "Health Check", "path": "/health", "method": "GET", "statusCode": 200, "response": "{\"status\":\"healthy\",\"timestamp\":\"2025-07-21T23:17:48.887Z\",\"version\":\"0.0.0\",\"environment\":\"development\",\"database\":\"connected\",\"uptime\":12.039174951}", "working": true}, {"success": true, "endpoint": "Login Endpoint", "path": "/api/auth/login", "method": "POST", "statusCode": 400, "response": "{\"success\":false,\"error\":\"Unexpected token ''', \\\"'{email:ad\\\"... is not valid JSON\",\"code\":\"INTERNAL_ERROR\",\"timestamp\":\"2025-07-21T23:17:49.505Z\",\"details\":{\"statusCode\":400,\"request\":{\"method\":\"POS...", "working": true}, {"success": true, "endpoint": "Systems API", "path": "/api/systems", "method": "GET", "statusCode": 200, "response": "{\"success\":true,\"data\":{\"systems\":[{\"id\":\"guildWar\",\"name_ar\":\"نظام حروب الروابط التلقائي\",\"name_en\":\"Automatic Guild War System\",\"description_ar\":\"نظام حروب الروابط التلقائي مع ذكاء اصطناعي متطور لإد...", "working": true}, {"success": true, "endpoint": "Technical Services", "path": "/api/services/technical", "method": "GET", "statusCode": 200, "response": "{\"success\":true,\"data\":{\"services\":[{\"id\":\"freebsd\",\"name_ar\":\"إعداد خوادم FreeBSD المحسنة\",\"name_en\":\"Optimized FreeBSD Server Setup\",\"description_ar\":\"إعداد وتحصين خوادم FreeBSD للحماية القصوى\",\"des...", "working": true}, {"success": true, "endpoint": "Premium Services", "path": "/api/services/premium", "method": "GET", "statusCode": 200, "response": "{\"success\":true,\"data\":{\"premiumContent\":[{\"id\":\"premium_advanced_systems\",\"title_ar\":\"حزمة الأنظمة المتقدمة\",\"title_en\":\"Advanced Systems Package\",\"description_ar\":\"مجموعة من أقوى الأنظمة المتقدمة لل...", "working": true}]}