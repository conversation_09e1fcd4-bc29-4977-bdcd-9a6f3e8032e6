/**
 * API Services for Khanfashariya.com
 * 
 * High-level service functions that use the API client
 * to perform specific business operations.
 * 
 * This file replaces the localStorage-based database functions
 * with proper API calls to the backend server.
 */

import { throttledApiCall, clearRequestCache } from '../utils/requestThrottle';

import apiClient, { ApiResponse, ApiError } from './apiClient';
import {
  SystemService,
  TechnicalService,
  User,
  Order,
  PremiumContent,
  PremiumPackage,
  TranslatedText
} from './database';

// API Base URL - use relative URLs in development for Vite proxy
const isDevelopment = import.meta.env.MODE === 'development';
const API_BASE_URL = isDevelopment
  ? '' // Use relative URLs for Vite proxy
  : (import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001');

// =====================================================
// SYSTEM SERVICES API
// =====================================================

/**
 * Get all system services (public - active only)
 */
export async function getSystemServices(): Promise<{ data: SystemService[]; error: ApiError | null }> {
  return throttledApiCall('getSystemServices', async () => {
    try {
      // Always use public endpoint for consistency
      // This ensures the same data structure regardless of user role
      const publicResponse = await apiClient.get<any>('/systems');
      const systemsData = publicResponse.data?.systems || publicResponse.data || [];
      return { data: systemsData, error: null };
    } catch (error) {
      console.error('Error fetching system services:', error);
      return { data: [], error: error as ApiError };
    }
  }, 5000); // Cache for 5 seconds
}

/**
 * Get all system services for admin (includes inactive systems)
 */
export async function getAdminSystemServices(): Promise<{ data: SystemService[]; error: ApiError | null }> {
  try {
    // Make direct fetch call to ensure fresh data
    const response = await fetch('/api/systems/admin', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('khanfashariya_access_token')}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const rawData = await response.json();

    // The admin endpoint returns array directly
    let systemsData: any[] = [];

    if (Array.isArray(rawData)) {
      systemsData = rawData;
    } else if (rawData && Array.isArray(rawData.data)) {
      systemsData = rawData.data;
    } else if (rawData && rawData.systems && Array.isArray(rawData.systems)) {
      systemsData = rawData.systems;
    }

    return { data: systemsData, error: null };
  } catch (error) {
    console.error('Error fetching admin system services:', error);
    return { data: [], error: error as ApiError };
  }
}

/**
 * Get system service by ID
 */
export async function getSystemService(id: string): Promise<{ data: SystemService | null; error: ApiError | null }> {
  try {
    const response = await apiClient.get<SystemService>(`/systems/${id}`);
    return { data: response.data || null, error: null };
  } catch (error) {
    console.error('Error fetching system service:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Create new system service
 */
export async function createSystemService(
  serviceData: Omit<SystemService, 'id' | 'created_at' | 'updated_at'>
): Promise<{ data: SystemService | null; error: ApiError | null }> {
  try {
    const response = await apiClient.post<SystemService>('/admin/systems', serviceData);
    return { data: response.data || null, error: null };
  } catch (error) {
    console.error('Error creating system service:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Update system service
 */
export async function updateSystemService(
  id: string, 
  serviceData: Partial<SystemService>
): Promise<{ data: SystemService | null; error: ApiError | null }> {
  try {
    const response = await apiClient.put<SystemService>(`/admin/systems/${id}`, serviceData);
    return { data: response.data || null, error: null };
  } catch (error) {
    console.error('Error updating system service:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Delete system service
 */
export async function deleteSystemService(id: string): Promise<{ error: ApiError | null }> {
  try {
    await apiClient.delete(`/admin/systems/${id}`);
    return { error: null };
  } catch (error) {
    console.error('Error deleting system service:', error);
    return { error: error as ApiError };
  }
}

// =====================================================
// TECHNICAL SERVICES API
// =====================================================

/**
 * Get all technical services
 */
export async function getTechnicalServices(): Promise<{ data: TechnicalService[]; error: ApiError | null }> {
  return throttledApiCall('getTechnicalServices', async () => {
    try {
      // Always use public endpoint for consistency
      // This ensures the same data structure regardless of user role
      const publicResponse = await apiClient.get<any>('/services/technical');
      const servicesData = publicResponse.data?.data?.services || publicResponse.data?.services || publicResponse.data || [];
      return { data: servicesData, error: null };
    } catch (error) {
      console.error('Error fetching technical services:', error);
      return { data: [], error: error as ApiError };
    }
  }, 5000); // Cache for 5 seconds
}

/**
 * Create new technical service
 */
export async function createTechnicalService(
  serviceData: Omit<TechnicalService, 'id' | 'created_at' | 'updated_at'>
): Promise<{ data: TechnicalService | null; error: ApiError | null }> {
  try {
    const response = await apiClient.post<TechnicalService>('/admin/technical-services', serviceData);
    return { data: response.data || null, error: null };
  } catch (error) {
    console.error('Error creating technical service:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Update technical service
 */
export async function updateTechnicalService(
  id: string, 
  serviceData: Partial<TechnicalService>
): Promise<{ data: TechnicalService | null; error: ApiError | null }> {
  try {
    const response = await apiClient.put<TechnicalService>(`/admin/technical-services/${id}`, serviceData);
    return { data: response.data || null, error: null };
  } catch (error) {
    console.error('Error updating technical service:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Delete technical service
 */
export async function deleteTechnicalService(id: string): Promise<{ error: ApiError | null }> {
  try {
    await apiClient.delete(`/admin/technical-services/${id}`);
    return { error: null };
  } catch (error) {
    console.error('Error deleting technical service:', error);
    return { error: error as ApiError };
  }
}

// =====================================================
// USER MANAGEMENT API
// =====================================================

/**
 * Get all users (admin only)
 */
export async function getAllUsers(): Promise<{ data: User[]; error: ApiError | null }> {
  try {
    const response = await apiClient.get<any>('/admin/users');
    // Extract users from pagination response
    const usersData = response.data?.users || response.data || [];
    return { data: usersData, error: null };
  } catch (error) {
    console.error('Error fetching users:', error);
    return { data: [], error: error as ApiError };
  }
}

/**
 * Get user profile
 */
export async function getUserProfile(userId?: string): Promise<{ data: User | null; error: ApiError | null }> {
  try {
    const url = userId ? `/users/${userId}` : '/users/profile';
    const response = await apiClient.get<User>(url);
    return { data: response.data || null, error: null };
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Update user profile
 */
export async function updateUserProfile(
  userId: string, 
  userData: Partial<User>
): Promise<{ data: User | null; error: ApiError | null }> {
  try {
    const response = await apiClient.put<User>(`/users/${userId}`, userData);
    return { data: response.data || null, error: null };
  } catch (error) {
    console.error('Error updating user profile:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Update user role (admin only)
 */
export async function updateUserRole(
  userId: string, 
  role: 'user' | 'admin'
): Promise<{ data: User | null; error: ApiError | null }> {
  try {
    const response = await apiClient.put<User>(`/users/${userId}/role`, { role });
    return { data: response.data || null, error: null };
  } catch (error) {
    console.error('Error updating user role:', error);
    return { data: null, error: error as ApiError };
  }
}

// =====================================================
// ORDERS API
// =====================================================

/**
 * Get user orders
 */
export async function getUserOrders(userId?: string): Promise<{ data: Order[]; error: ApiError | null }> {
  try {
    // Check if we have access token
    const accessToken = apiClient.getAccessToken();
    if (!accessToken) {
      console.error('❌ No access token available - user not logged in');
      return { data: [], error: { message: 'User not authenticated' } };
    }

    const response = await apiClient.get<any>('/orders');

    // Handle the response structure correctly
    // API returns: { success: true, data: { orders: [...], pagination: {...} } }
    const orders = response.data?.orders || [];

    return { data: orders, error: null };
  } catch (error) {
    console.error('❌ Error fetching user orders:', error);
    return { data: [], error: error as ApiError };
  }
}

/**
 * Get all orders (admin only)
 */
export async function getAllOrders(): Promise<{ data: Order[]; error: ApiError | null }> {
  try {
    const response = await apiClient.get<any>('/admin/orders');
    // Handle the response structure correctly
    const orders = response.data?.data?.orders || response.data?.orders || response.data || [];
    return { data: orders, error: null };
  } catch (error) {
    console.error('Error fetching all orders:', error);
    return { data: [], error: error as ApiError };
  }
}

/**
 * Create new order - Fixed to match server expectations
 */
export async function createOrder(
  orderData: any
): Promise<{ data: Order | null; error: ApiError | null }> {
  try {
    // Transform frontend order data to match server expectations
    const serverOrderData = {
      order_type: orderData.service_type || orderData.order_type || 'system_service',
      item_id: orderData.service_id || orderData.item_id,
      quantity: orderData.quantity || 1,
      notes_ar: orderData.notes_ar || (orderData.notes && orderData.notes.includes('طلب') ? orderData.notes : ''),
      notes_en: orderData.notes_en || orderData.notes || `Order for ${orderData.service_name || 'service'}`
    };

    // Validate required fields
    if (!serverOrderData.order_type || !serverOrderData.item_id) {
      throw new Error('Order type and item ID are required');
    }

    const response = await apiClient.post<any>('/orders', serverOrderData);

    // Transform response back to frontend format
    const order = response.data?.data?.order || response.data;
    return { data: order, error: null };
  } catch (error) {
    console.error('Error creating order:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Update order status (admin only)
 */
export async function updateOrderStatus(
  orderId: string,
  status: string,
  additionalData?: {
    payment_status?: string;
    admin_notes?: string;
    priority?: string;
    estimated_completion?: string;
    support_level?: string;
    maintenance_included?: boolean;
    installation_included?: boolean;
  }
): Promise<{ data: any | null; error: ApiError | null }> {
  try {
    const updateData: any = { status };

    // Add additional data if provided
    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        if (additionalData[key as keyof typeof additionalData] !== undefined) {
          updateData[key] = additionalData[key as keyof typeof additionalData];
        }
      });
    }

    const response = await apiClient.put<any>(`/admin/orders/${orderId}`, updateData);
    return { data: response.data?.data?.order || response.data || null, error: null };
  } catch (error) {
    console.error('Error updating order status:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Delete order (admin only)
 */
export async function deleteOrder(orderId: string): Promise<{ success: boolean; error: ApiError | null }> {
  try {
    const response = await apiClient.delete(`/admin/orders/${orderId}`);
    return { success: response.data?.success || false, error: null };
  } catch (error) {
    console.error('Error deleting order:', error);
    return { success: false, error: error as ApiError };
  }
}

/**
 * Add admin note to order
 */
export async function addOrderNote(
  orderId: string,
  noteAr: string,
  noteEn: string
): Promise<{ success: boolean; error: ApiError | null }> {
  try {
    const response = await apiClient.post(`/admin/orders/${orderId}/notes`, {
      note_ar: noteAr,
      note_en: noteEn
    });
    return { success: response.data?.success || false, error: null };
  } catch (error) {
    console.error('Error adding order note:', error);
    return { success: false, error: error as ApiError };
  }
}

// =====================================================
// PREMIUM CONTENT API
// =====================================================

/**
 * Get active premium edition (public)
 */
export async function getActivePremiumEdition(): Promise<{ data: PremiumContent | null; error: ApiError | null }> {
  try {
    const response = await apiClient.get<{ premiumContent: PremiumContent }>('/premium');
    return { data: response.data?.premiumContent || null, error: null };
  } catch (error) {
    console.error('Error fetching active premium edition:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Get premium add-ons (systems and services with premium pricing)
 */
export async function getPremiumAddons(): Promise<{
  data: { systems: SystemService[]; services: TechnicalService[] } | null;
  error: ApiError | null
}> {
  try {
    const response = await apiClient.get<{ systems: SystemService[]; services: TechnicalService[] }>('/premium/addons');
    return { data: response.data || { systems: [], services: [] }, error: null };
  } catch (error) {
    console.error('Error fetching premium add-ons:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Get all premium content (admin)
 */
export async function getPremiumContent(): Promise<{
  data: {
    premiumContent: PremiumContent[];
    availableSystems: SystemService[];
    availableServices: TechnicalService[]
  } | null;
  error: ApiError | null
}> {
  try {
    const response = await apiClient.get<{
      premiumContent: PremiumContent[];
      availableSystems: SystemService[];
      availableServices: TechnicalService[]
    }>('/premium/admin');
    return { data: response.data || { premiumContent: [], availableSystems: [], availableServices: [] }, error: null };
  } catch (error) {
    console.error('Error fetching premium content:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Set active premium edition
 */
export async function setActivePremiumEdition(id: string): Promise<{ error: ApiError | null }> {
  try {
    await apiClient.post(`/premium/set-active/${id}`);
    return { error: null };
  } catch (error) {
    console.error('Error setting active premium edition:', error);
    return { error: error as ApiError };
  }
}

/**
 * Update system premium pricing
 */
export async function updateSystemPremiumPricing(
  systemId: string,
  pricingData: {
    premium_price: number;
    installation_included?: boolean;
    maintenance_included?: boolean;
    is_available_for_premium?: boolean;
    description_ar?: string;
    description_en?: string;
  }
): Promise<{ error: ApiError | null }> {
  try {
    await apiClient.post(`/premium/pricing/system/${systemId}`, pricingData);
    return { error: null };
  } catch (error) {
    console.error('Error updating system premium pricing:', error);
    return { error: error as ApiError };
  }
}

/**
 * Update service premium pricing
 */
export async function updateServicePremiumPricing(
  serviceId: string,
  pricingData: {
    premium_price: number;
    installation_included?: boolean;
    maintenance_included?: boolean;
    is_available_for_premium?: boolean;
    subscription_discount_percentage?: number;
    description_ar?: string;
    description_en?: string;
  }
): Promise<{ error: ApiError | null }> {
  try {
    await apiClient.post(`/premium/pricing/service/${serviceId}`, pricingData);
    return { error: null };
  } catch (error) {
    console.error('Error updating service premium pricing:', error);
    return { error: error as ApiError };
  }
}

/**
 * Create premium content
 */
export async function createPremiumContent(
  contentData: Omit<PremiumContent, 'id' | 'created_at' | 'updated_at'>
): Promise<{ data: PremiumContent | null; error: ApiError | null }> {
  try {
    const response = await apiClient.post<PremiumContent>('/premium', contentData);
    return { data: response.data || null, error: null };
  } catch (error) {
    console.error('Error creating premium content:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Update premium content
 */
export async function updatePremiumContent(
  id: string,
  contentData: Partial<PremiumContent>
): Promise<{ data: PremiumContent | null; error: ApiError | null }> {
  try {
    const response = await apiClient.put<PremiumContent>(`/premium/${id}`, contentData);
    return { data: response.data || null, error: null };
  } catch (error) {
    console.error('Error updating premium content:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Delete premium content
 */
export async function deletePremiumContent(id: string): Promise<{ error: ApiError | null }> {
  try {
    await apiClient.delete(`/premium/${id}`);
    return { error: null };
  } catch (error) {
    console.error('Error deleting premium content:', error);
    return { error: error as ApiError };
  }
}

// =====================================================
// AUTHENTICATION HELPERS
// =====================================================

/**
 * Sign in user
 */
export async function signIn(email: string, password: string) {
  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Login failed');
    }

    // Store tokens and user data
    if (data.success && data.data) {
      const { user, tokens } = data.data;

      // Store tokens
      localStorage.setItem('khanfashariya_access_token', tokens.accessToken);
      localStorage.setItem('khanfashariya_refresh_token', tokens.refreshToken);

      // Store user data
      localStorage.setItem('khanfashariya_current_user', JSON.stringify(user));

      return { data: data.data, error: null };
    } else {
      throw new Error(data.message || 'Login failed');
    }
  } catch (error) {
    console.error('Error signing in:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Sign up user
 */
export async function signUp(email: string, password: string, userData: { username: string; full_name: string }) {
  try {
    const response = await apiClient.register({ 
      email, 
      password, 
      username: userData.username, 
      full_name: userData.full_name 
    });
    return { data: response, error: null };
  } catch (error) {
    console.error('Error signing up:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Sign out user
 */
export async function signOut() {
  try {
    // Clear tokens and user data from localStorage
    localStorage.removeItem('khanfashariya_access_token');
    localStorage.removeItem('khanfashariya_refresh_token');
    localStorage.removeItem('khanfashariya_current_user');

    // Optionally call logout API endpoint
    try {
      await fetch(`${API_BASE_URL}/api/auth/logout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('khanfashariya_access_token')}`,
          'Content-Type': 'application/json',
        },
      });
    } catch (apiError) {
      // Ignore API errors during logout - local cleanup is more important
      console.warn('Logout API call failed:', apiError);
    }

    return { error: null };
  } catch (error) {
    console.error('Error signing out:', error);
    return { error: error as ApiError };
  }
}

/**
 * Get current user from localStorage (for compatibility)
 */
export function getCurrentUser(): User | null {
  try {
    const userData = localStorage.getItem('khanfashariya_current_user');
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}
