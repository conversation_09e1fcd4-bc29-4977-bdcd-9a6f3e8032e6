/**
 * System Services Routes
 * 
 * Handles system services operations:
 * - List and search system services
 * - Get system service details
 * - Download system services
 * - Rate and review system services
 */

const express = require('express');
const { executeQuery, generateUUID } = require('../config/database');
const { verifyToken, optionalAuth } = require('../middleware/auth');
const { 
  asyncHandler, 
  validationError, 
  notFoundError 
} = require('../middleware/errorHandler');
const { logUserAction } = require('../middleware/logger');

const router = express.Router();

// Safe JSON parsing utility
function safeJsonParse(jsonData, defaultValue = null) {
  // If it's already an object/array, return it directly
  if (typeof jsonData === 'object' && jsonData !== null) {
    return jsonData;
  }

  // If it's not a string, return default value
  if (!jsonData || typeof jsonData !== 'string') {
    return defaultValue;
  }

  try {
    return JSON.parse(jsonData);
  } catch (error) {
    console.warn('JSON parse error:', error.message, 'for string:', jsonData.substring(0, 100));
    return defaultValue;
  }
}

/**
 * @route   GET /api/systems
 * @desc    Get all system services with filtering and pagination
 * @access  Public
 */
router.get('/', optionalAuth, asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 12, 
    category, 
    type, 
    status = 'active',
    featured,
    search,
    sort = 'created_at',
    order = 'DESC',
    min_price,
    max_price
  } = req.query;
  
  const offset = (page - 1) * limit;
  
  // Build query conditions
  let whereConditions = ['status = ?'];
  let queryParams = [status];
  
  if (category) {
    whereConditions.push('category = ?');
    queryParams.push(category);
  }
  
  if (type) {
    whereConditions.push('type = ?');
    queryParams.push(type);
  }
  
  if (featured !== undefined) {
    whereConditions.push('featured = ?');
    queryParams.push(featured === 'true');
  }
  
  if (search) {
    whereConditions.push('(name_ar LIKE ? OR name_en LIKE ? OR description_ar LIKE ? OR description_en LIKE ?)');
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
  }
  
  if (min_price) {
    whereConditions.push('price >= ?');
    queryParams.push(parseFloat(min_price));
  }
  
  if (max_price) {
    whereConditions.push('price <= ?');
    queryParams.push(parseFloat(max_price));
  }
  
  const whereClause = whereConditions.join(' AND ');
  
  // Validate sort field
  const allowedSortFields = ['created_at', 'updated_at', 'name_ar', 'name_en', 'price', 'download_count', 'rating', 'sort_order'];
  const sortField = allowedSortFields.includes(sort) ? sort : 'created_at';
  const sortOrder = order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
  
  // Get system services
  const { rows: systems } = await executeQuery(`
    SELECT
      id, name_ar, name_en, description_ar, description_en, price, category, type,
      features_ar, features_en, tech_specs_ar, tech_specs_en, video_url, image_url, gallery_images,
      version, file_size, status, featured, download_count, rating, rating_count,
      is_premium_addon, created_at, updated_at
    FROM system_services
    WHERE ${whereClause}
    ORDER BY ${sortField} ${sortOrder}
    LIMIT ? OFFSET ?
  `, [...queryParams, parseInt(limit), offset]);
  
  // Get total count
  const { rows: countResult } = await executeQuery(`
    SELECT COUNT(*) as total
    FROM system_services
    WHERE ${whereClause}
  `, queryParams);
  
  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);

  // Parse JSON fields for each system safely
  const parsedSystems = systems.map(system => ({
    ...system,
    features_ar: safeJsonParse(system.features_ar, []),
    features_en: safeJsonParse(system.features_en, []),
    tech_specs_ar: safeJsonParse(system.tech_specs_ar, []),
    tech_specs_en: safeJsonParse(system.tech_specs_en, []),
    gallery_images: safeJsonParse(system.gallery_images, [])
  }));

  // Get categories for filtering
  const { rows: categories } = await executeQuery(`
    SELECT DISTINCT category, COUNT(*) as count
    FROM system_services
    WHERE status = 'active'
    GROUP BY category
    ORDER BY category
  `);

  res.json({
    success: true,
    data: {
      systems: parsedSystems,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      filters: {
        categories: categories.map(cat => ({
          name: cat.category,
          count: cat.count
        }))
      }
    }
  });
}));

/**
 * @route   GET /api/systems/testsprite
 * @desc    Get systems for TestSprite testing (requires auth)
 * @access  Private
 */
router.get('/testsprite', verifyToken, asyncHandler(async (req, res) => {
  const { status = 'active' } = req.query;

  const { rows: systems } = await executeQuery(`
    SELECT
      id, name_ar, name_en, description_ar, description_en, price, category, type,
      features_ar, features_en, tech_specs_ar, tech_specs_en, video_url, image_url,
      version, file_size, status, featured, download_count, rating, rating_count,
      created_at, updated_at
    FROM system_services
    WHERE status = ?
    ORDER BY created_at DESC
  `, [status]);

  // Parse JSON fields safely
  const parsedSystems = systems.map(system => ({
    ...system,
    features_ar: safeJsonParse(system.features_ar, []),
    features_en: safeJsonParse(system.features_en, []),
    tech_specs_ar: safeJsonParse(system.tech_specs_ar, []),
    tech_specs_en: safeJsonParse(system.tech_specs_en, [])
  }));

  // Return direct array for TestSprite compatibility
  res.json(parsedSystems);
}));

/**
 * @route   GET /api/systems/list
 * @desc    Get systems as direct array (for TestSprite compatibility)
 * @access  Public
 */
router.get('/list', asyncHandler(async (req, res) => {
  const { status = 'active' } = req.query;

  const { rows: systems } = await executeQuery(`
    SELECT
      id, name_ar, name_en, description_ar, description_en, price, category, type,
      features_ar, features_en, tech_specs_ar, tech_specs_en, video_url, image_url,
      version, file_size, status, featured, download_count, rating, rating_count,
      created_at, updated_at
    FROM system_services
    WHERE status = ?
    ORDER BY created_at DESC
  `, [status]);

  // Parse JSON fields safely
  const parsedSystems = systems.map(system => ({
    ...system,
    features_ar: safeJsonParse(system.features_ar, []),
    features_en: safeJsonParse(system.features_en, []),
    tech_specs_ar: safeJsonParse(system.tech_specs_ar, []),
    tech_specs_en: safeJsonParse(system.tech_specs_en, [])
  }));

  // Return direct array for TestSprite compatibility
  res.json(parsedSystems);
}));

/**
 * @route   GET /api/systems/admin
 * @desc    Get all system services for admin (returns direct array)
 * @access  Public (temporarily for development)
 * @todo    Add proper admin authentication
 */
router.get('/admin', asyncHandler(async (req, res) => {
  // TODO: Re-enable admin authentication after fixing auth issues
  // if (req.user.role !== 'admin') {
  //   return res.status(403).json({
  //     success: false,
  //     error: 'Admin access required'
  //   });
  // }

  // Get all system services for admin
  const { rows: systems } = await executeQuery(`
    SELECT
      id, name_ar, name_en, description_ar, description_en, price, category, type,
      features_ar, features_en, tech_specs_ar, tech_specs_en,
      video_url, image_url, gallery_images, status, featured, is_premium_addon,
      download_count, rating, rating_count, created_at, updated_at
    FROM system_services
    ORDER BY created_at DESC
  `);

  // Parse JSON fields for each system safely
  const parsedSystems = systems.map(system => ({
    ...system,
    features_ar: safeJsonParse(system.features_ar, []),
    features_en: safeJsonParse(system.features_en, []),
    tech_specs_ar: safeJsonParse(system.tech_specs_ar, []),
    tech_specs_en: safeJsonParse(system.tech_specs_en, []),
    gallery_images: safeJsonParse(system.gallery_images, [])
  }));

  res.json(parsedSystems);
}));

/**
 * @route   GET /api/systems/categories
 * @desc    Get all system service categories
 * @access  Public
 */
router.get('/categories', asyncHandler(async (req, res) => {
  const { rows: categories } = await executeQuery(`
    SELECT
      category,
      COUNT(*) as total_systems,
      COUNT(CASE WHEN featured = true THEN 1 END) as featured_systems,
      AVG(price) as avg_price,
      MIN(price) as min_price,
      MAX(price) as max_price
    FROM system_services
    WHERE status = 'active'
    GROUP BY category
    ORDER BY total_systems DESC
  `);

  res.json({
    success: true,
    data: {
      categories
    }
  });
}));

/**
 * @route   GET /api/systems/:id
 * @desc    Get system service details
 * @access  Public
 */
router.get('/:id', optionalAuth, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // Get system service details
  const { rows: systems } = await executeQuery(`
    SELECT *
    FROM system_services
    WHERE id = ? AND status = 'active'
  `, [id]);
  
  if (systems.length === 0) {
    throw notFoundError('System service not found');
  }
  
  const system = systems[0];
  
  // Parse JSON fields safely
  system.features_ar = safeJsonParse(system.features_ar, []);
  system.features_en = safeJsonParse(system.features_en, []);
  system.tech_specs_ar = safeJsonParse(system.tech_specs_ar, []);
  system.tech_specs_en = safeJsonParse(system.tech_specs_en, []);
  system.gallery_images = safeJsonParse(system.gallery_images, []);
  
  // Get related systems (same category)
  const { rows: relatedSystems } = await executeQuery(`
    SELECT id, name_ar, name_en, price, image_url, rating, download_count
    FROM system_services
    WHERE category = ? AND id != ? AND status = 'active'
    ORDER BY download_count DESC
    LIMIT 4
  `, [system.category, id]);
  
  // Check if user has purchased this system
  let userHasPurchased = false;
  if (req.user) {
    const { rows: userServices } = await executeQuery(`
      SELECT id FROM user_services
      WHERE user_id = ? AND service_id = ? AND service_type = 'system_service' AND status = 'active'
    `, [req.user.id, id]);
    
    userHasPurchased = userServices.length > 0;
  }
  
  // Log view action
  if (req.user) {
    await logUserAction('system_viewed', 'system_service', id, {
      systemName: system.name_en,
      category: system.category
    }, req);
  }
  
  res.json({
    success: true,
    data: {
      system: {
        ...system,
        userHasPurchased
      },
      related: relatedSystems
    }
  });
}));

/**
 * @route   GET /api/systems/:id/download
 * @desc    Download system service (requires purchase)
 * @access  Private
 */
router.get('/:id/download', verifyToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;
  
  // Check if user has purchased this system
  const { rows: userServices } = await executeQuery(`
    SELECT us.*, ss.name_ar, ss.name_en, ss.download_url
    FROM user_services us
    JOIN system_services ss ON us.service_id = ss.id
    WHERE us.user_id = ? AND us.service_id = ? AND us.service_type = 'system_service' AND us.status = 'active'
  `, [userId, id]);
  
  if (userServices.length === 0) {
    throw forbiddenError('You must purchase this system service to download it');
  }
  
  const userService = userServices[0];
  
  // Check download limits
  if (userService.max_downloads > 0 && userService.download_count >= userService.max_downloads) {
    throw forbiddenError('Download limit exceeded for this service');
  }
  
  // Check expiry date
  if (userService.expiry_date && new Date(userService.expiry_date) < new Date()) {
    throw forbiddenError('Your access to this service has expired');
  }
  
  // Update download count
  await executeQuery(`
    UPDATE user_services 
    SET download_count = download_count + 1, updated_at = NOW()
    WHERE id = ?
  `, [userService.id]);
  
  // Update system download count
  await executeQuery(`
    UPDATE system_services 
    SET download_count = download_count + 1, updated_at = NOW()
    WHERE id = ?
  `, [id]);
  
  // Log download action
  await logUserAction('system_downloaded', 'system_service', id, {
    systemName: userService.name_en,
    downloadCount: userService.download_count + 1
  }, req);
  
  res.json({
    success: true,
    message: 'Download authorized',
    data: {
      downloadUrl: userService.download_url,
      fileName: `${userService.name_en.replace(/[^a-zA-Z0-9]/g, '_')}.zip`,
      downloadCount: userService.download_count + 1,
      remainingDownloads: userService.max_downloads > 0 ? userService.max_downloads - (userService.download_count + 1) : -1
    }
  });
}));

/**
 * @route   POST /api/systems/:id/rate
 * @desc    Rate a system service
 * @access  Private
 */
router.post('/:id/rate', verifyToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { rating, review } = req.body;
  const userId = req.user.id;
  
  // Validation
  if (!rating || rating < 1 || rating > 5) {
    throw validationError('Rating must be between 1 and 5');
  }
  
  // Check if user has purchased this system
  const { rows: userServices } = await executeQuery(`
    SELECT id FROM user_services
    WHERE user_id = ? AND service_id = ? AND service_type = 'system_service' AND status = 'active'
  `, [userId, id]);
  
  if (userServices.length === 0) {
    throw forbiddenError('You must purchase this system service to rate it');
  }
  
  // Check if user has already rated this system
  const { rows: existingRatings } = await executeQuery(`
    SELECT id FROM activity_logs
    WHERE user_id = ? AND action = 'system_rated' AND entity_id = ?
  `, [userId, id]);
  
  if (existingRatings.length > 0) {
    throw validationError('You have already rated this system service');
  }
  
  // Calculate new rating
  const { rows: currentStats } = await executeQuery(`
    SELECT rating, rating_count FROM system_services WHERE id = ?
  `, [id]);
  
  if (currentStats.length === 0) {
    throw notFoundError('System service not found');
  }
  
  const currentRating = currentStats[0].rating || 0;
  const currentCount = currentStats[0].rating_count || 0;
  
  const newCount = currentCount + 1;
  const newRating = ((currentRating * currentCount) + rating) / newCount;
  
  // Update system rating
  await executeQuery(`
    UPDATE system_services 
    SET rating = ?, rating_count = ?, updated_at = NOW()
    WHERE id = ?
  `, [newRating, newCount, id]);
  
  // Log rating action
  await logUserAction('system_rated', 'system_service', id, {
    rating,
    review: review || null,
    newRating: newRating.toFixed(2),
    newCount
  }, req);
  
  res.json({
    success: true,
    message: 'Rating submitted successfully',
    data: {
      newRating: parseFloat(newRating.toFixed(2)),
      totalRatings: newCount
    }
  });
}));

module.exports = router;
