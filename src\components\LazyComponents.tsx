import { lazy } from 'react';

/**
 * Lazy-loaded components for code splitting and performance optimization
 * 
 * This file centralizes all lazy-loaded components to improve initial bundle size
 * and loading performance. Components are loaded only when needed.
 */

// Admin components - Heavy components loaded only for admin users
export const AdminDashboard = lazy(() => 
  import('./AdminDashboard').then(module => ({ default: module.default }))
);

export const UserManagement = lazy(() => 
  import('./UserManagement').then(module => ({ default: module.default }))
);

export const OrderManagement = lazy(() => 
  import('./OrderManagement').then(module => ({ default: module.default }))
);

export const AdvancedOrderManagement = lazy(() => 
  import('./AdvancedOrderManagement').then(module => ({ default: module.default }))
);



// User components - Loaded when user interacts with specific features
export const UserDashboard = lazy(() => 
  import('./UserDashboard').then(module => ({ default: module.default }))
);

export const SimpleUserDashboard = lazy(() => 
  import('./SimpleUserDashboard').then(module => ({ default: module.default }))
);

export const UserLogin = lazy(() => 
  import('./UserLogin').then(module => ({ default: module.default }))
);

// Premium and special features - Loaded on demand
export const PremiumEdition = lazy(() => 
  import('./PremiumEdition').then(module => ({ default: module.default }))
);

export const PremiumContentManager = lazy(() => 
  import('./PremiumContentManager').then(module => ({ default: module.default }))
);

export const CustomOrderForm = lazy(() => 
  import('./CustomOrderForm').then(module => ({ default: module.default }))
);

// Embedded components - Loaded when needed in admin panel
export const EmbeddedOrderManagement = lazy(() => 
  import('./EmbeddedOrderManagement').then(module => ({ default: module.default }))
);

export const EmbeddedUserManagement = lazy(() => 
  import('./EmbeddedUserManagement').then(module => ({ default: module.default }))
);

/**
 * Preload critical components that are likely to be used soon
 * Call this function when the user is likely to need these components
 */
export const preloadCriticalComponents = () => {
  // Check authentication status from tokens (acceptable localStorage usage for auth)
  try {
    const token = localStorage.getItem('khanfashariya_access_token');
    const user = localStorage.getItem('khanfashariya_current_user');

    // Preload user login for unauthenticated users
    if (!token || !user) {
      import('./UserLogin');
    }

    // Preload user dashboard for authenticated users
    if (token && user) {
      import('./SimpleUserDashboard');
    }
  } catch (error) {
    // Fallback to login component if there's an error
    import('./UserLogin');
  }
};

/**
 * Preload admin components for admin users
 */
export const preloadAdminComponents = () => {
  import('./AdminDashboard');
  import('./UserManagement');
  import('./OrderManagement');
};
