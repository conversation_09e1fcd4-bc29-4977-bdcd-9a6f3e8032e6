import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuth } from '../hooks/useAuth';
import { useNotification } from '../hooks/useNotification';
import { useActivityLogger } from '../hooks/useActivityLogger';
import { createOrder } from '../lib/apiServices';
import { Settings, X, Send } from 'lucide-react';

interface CustomOrderFormProps {
  onClose: () => void;
}

const CustomOrderForm: React.FC<CustomOrderFormProps> = ({ onClose }) => {
  const { language } = useTranslation();
  const { userProfile, isAuthenticated } = useAuth();
  const { showNotification } = useNotification();
  const { logCustomRequestCreate } = useActivityLogger();
  const [formData, setFormData] = useState({
    projectName: '',
    description: '',
    budget: '',
    timeline: ''
  });
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!isAuthenticated || !userProfile) {
      showNotification({
        type: 'warning',
        message: language === 'ar' ? 'يجب تسجيل الدخول أولاً' : 'Please login first'
      });
      return;
    }

    setSubmitting(true);
    try {
      // Create order using correct format for server
      const result = await createOrder({
        order_type: 'system_service',
        item_id: 'custom-order-' + Date.now(), // Generate unique ID for custom orders
        quantity: 1,
        notes_ar: `طلب نظام مخصص: ${formData.projectName} - الميزانية: ${formData.budget} - الجدول الزمني: ${formData.timeline}`,
        notes_en: `Custom System Request: ${formData.projectName} - Budget: ${formData.budget} - Timeline: ${formData.timeline}`
      });

      if (result.data) {
        // Log activity
        logCustomRequestCreate(result.data.id, formData.projectName, userProfile.email);

        showNotification({
          type: 'success',
          message: language === 'ar' ? 'تم إرسال طلبك بنجاح!' : 'Your request has been sent successfully!'
        });
        onClose();
      } else if (result.error) {
        throw new Error(result.error.message);
      }
    } catch (error) {
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'حدث خطأ أثناء إرسال الطلب' : 'An error occurred while sending the request'
      });
    } finally {
      setSubmitting(false);
    }
  };

  React.useEffect(() => {
    document.body.classList.add('modal-open');
    return () => {
      document.body.classList.remove('modal-open');
    };
  }, []);

  return (
    <div className="modal-backdrop flex items-center justify-center p-4"
         onClick={(e) => e.target === e.currentTarget && onClose()}>
      <div className="bg-primary rounded-lg max-w-2xl w-full p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <Settings className="w-8 h-8 text-secondary" />
            <h2 className="text-2xl font-bold text-white">
              {language === 'ar' ? 'طلب نظام مخصص' : 'Custom System Request'}
            </h2>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-white">
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-white font-medium mb-2">
              {language === 'ar' ? 'اسم المشروع' : 'Project Name'}
            </label>
            <input
              type="text"
              value={formData.projectName}
              onChange={(e) => setFormData({...formData, projectName: e.target.value})}
              className="w-full bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none"
              placeholder={language === 'ar' ? 'أدخل اسم المشروع' : 'Enter project name'}
            />
          </div>

          <div>
            <label className="block text-white font-medium mb-2">
              {language === 'ar' ? 'وصف المشروع' : 'Project Description'}
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              rows={4}
              className="w-full bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none resize-none"
              placeholder={language === 'ar' ? 'اشرح متطلبات مشروعك...' : 'Explain your project requirements...'}
            />
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="block text-white font-medium mb-2">
                {language === 'ar' ? 'الميزانية' : 'Budget'}
              </label>
              <select
                value={formData.budget}
                onChange={(e) => setFormData({...formData, budget: e.target.value})}
                className="w-full bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none"
              >
                <option value="">{language === 'ar' ? 'اختر الميزانية' : 'Select Budget'}</option>
                <option value="500-1000">$500 - $1,000</option>
                <option value="1000-2000">$1,000 - $2,000</option>
                <option value="2000+">$2,000+</option>
              </select>
            </div>

            <div>
              <label className="block text-white font-medium mb-2">
                {language === 'ar' ? 'الجدول الزمني' : 'Timeline'}
              </label>
              <select
                value={formData.timeline}
                onChange={(e) => setFormData({...formData, timeline: e.target.value})}
                className="w-full bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none"
              >
                <option value="">{language === 'ar' ? 'اختر المدة' : 'Select Timeline'}</option>
                <option value="1-2weeks">{language === 'ar' ? '1-2 أسابيع' : '1-2 Weeks'}</option>
                <option value="1month">{language === 'ar' ? 'شهر واحد' : '1 Month'}</option>
                <option value="2months+">{language === 'ar' ? 'شهرين أو أكثر' : '2+ Months'}</option>
              </select>
            </div>
          </div>

          {/* Form buttons - Fixed positioning at bottom */}
          <div className="form-button-container">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </button>
            <button
              onClick={handleSubmit}
              disabled={submitting || !formData.projectName || !formData.description}
              className="px-6 py-2 bg-gradient-to-r from-secondary to-accent text-primary rounded-lg hover:from-secondary/90 hover:to-accent/90 transition-colors disabled:opacity-50 flex items-center icon-container"
            >
              {submitting ? (
                <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <>
                  <Send className="w-4 h-4 icon-left" />
                  <span>{language === 'ar' ? 'إرسال الطلب' : 'Submit Request'}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomOrderForm;