#!/usr/bin/env node

/**
 * Integration Test Script for Khanfashariya.com API Migration
 * 
 * This script tests the complete migration from localStorage to API backend.
 * It verifies that all endpoints work correctly and data flows properly.
 * 
 * Usage: node scripts/test-integration.js
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:5173';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Test results tracking
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  tests: []
};

function addTestResult(name, status, message, details = null) {
  testResults.total++;
  if (status === 'PASS') {
    testResults.passed++;
  } else {
    testResults.failed++;
  }
  
  testResults.tests.push({
    name,
    status,
    message,
    details,
    timestamp: new Date().toISOString()
  });
  
  const color = status === 'PASS' ? 'green' : 'red';
  log(`${status === 'PASS' ? '✅' : '❌'} ${name}: ${message}`, color);
}

/**
 * Test server health and connectivity
 */
async function testServerHealth() {
  log('\n🏥 Testing Server Health...', 'magenta');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/health`, { timeout: 5000 });
    
    if (response.status === 200 && response.data.status === 'healthy') {
      addTestResult(
        'Server Health',
        'PASS',
        `Server is healthy (${response.data.database})`,
        response.data
      );
      return true;
    } else {
      addTestResult(
        'Server Health',
        'FAIL',
        `Server unhealthy: ${response.data.status}`,
        response.data
      );
      return false;
    }
  } catch (error) {
    addTestResult(
      'Server Health',
      'FAIL',
      `Server connection failed: ${error.message}`,
      { error: error.message, code: error.code }
    );
    return false;
  }
}

/**
 * Test CORS configuration
 */
async function testCORS() {
  log('\n🌐 Testing CORS Configuration...', 'magenta');
  
  try {
    // Test preflight request
    const response = await axios.options(`${API_BASE_URL}/api/systems`, {
      headers: {
        'Origin': FRONTEND_URL,
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    });
    
    addTestResult(
      'CORS Preflight',
      'PASS',
      'CORS preflight request successful',
      { status: response.status, headers: response.headers }
    );
    
    // Test actual request with origin
    const actualResponse = await axios.get(`${API_BASE_URL}/api/systems`, {
      headers: { 'Origin': FRONTEND_URL }
    });
    
    addTestResult(
      'CORS Actual Request',
      'PASS',
      'CORS actual request successful',
      { status: actualResponse.status }
    );
    
    return true;
  } catch (error) {
    addTestResult(
      'CORS Configuration',
      'FAIL',
      `CORS test failed: ${error.message}`,
      { error: error.message, response: error.response?.data }
    );
    return false;
  }
}

/**
 * Test authentication endpoints
 */
async function testAuthentication() {
  log('\n🔐 Testing Authentication...', 'magenta');
  
  try {
    // Test login
    const loginResponse = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (loginResponse.data.success && loginResponse.data.data.tokens?.accessToken) {
      addTestResult(
        'Admin Login',
        'PASS',
        'Admin login successful',
        { hasToken: !!loginResponse.data.data.tokens?.accessToken }
      );

      const token = loginResponse.data.data.tokens.accessToken;
      
      // Test authenticated request
      const profileResponse = await axios.get(`${API_BASE_URL}/api/users/profile`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (profileResponse.data.success) {
        addTestResult(
          'Authenticated Request',
          'PASS',
          'Profile fetch with token successful',
          { userId: profileResponse.data.data.id }
        );
      }
      
      return { token, user: profileResponse.data.data };
    } else {
      addTestResult(
        'Admin Login',
        'FAIL',
        'Login failed - no token received',
        loginResponse.data
      );
      return null;
    }
  } catch (error) {
    addTestResult(
      'Authentication',
      'FAIL',
      `Authentication test failed: ${error.message}`,
      { error: error.message, response: error.response?.data }
    );
    return null;
  }
}

/**
 * Test system services endpoints
 */
async function testSystemServices(authToken = null) {
  log('\n🖥️ Testing System Services...', 'magenta');
  
  const headers = authToken ? { Authorization: `Bearer ${authToken}` } : {};
  
  try {
    // Test GET systems
    const getResponse = await axios.get(`${API_BASE_URL}/api/systems`, { headers });
    
    if (getResponse.data.success) {
      addTestResult(
        'Get System Services',
        'PASS',
        `Retrieved ${getResponse.data.data.length} systems`,
        { count: getResponse.data.data.length }
      );
    }
    
    // Test POST system (admin only)
    if (authToken) {
      const testSystem = {
        name_ar: 'نظام اختبار API',
        name_en: 'API Test System',
        description_ar: 'نظام تجريبي لاختبار API',
        description_en: 'Test system for API testing',
        price: 99,
        category: 'test',
        type: 'regular',
        status: 'active',
        features_ar: ['ميزة 1', 'ميزة 2'],
        features_en: ['Feature 1', 'Feature 2']
      };
      
      const createResponse = await axios.post(`${API_BASE_URL}/api/systems`, testSystem, { headers });
      
      if (createResponse.data.success) {
        addTestResult(
          'Create System Service',
          'PASS',
          'System created successfully',
          { systemId: createResponse.data.data.id }
        );
        
        // Clean up - delete the test system
        try {
          await axios.delete(`${API_BASE_URL}/api/systems/${createResponse.data.data.id}`, { headers });
          log('🧹 Test system cleaned up', 'cyan');
        } catch (cleanupError) {
          log('⚠️ Failed to clean up test system', 'yellow');
        }
      }
    }
    
    return true;
  } catch (error) {
    addTestResult(
      'System Services',
      'FAIL',
      `System services test failed: ${error.message}`,
      { error: error.message, response: error.response?.data }
    );
    return false;
  }
}

/**
 * Test technical services endpoints
 */
async function testTechnicalServices(authToken = null) {
  log('\n🛠️ Testing Technical Services...', 'magenta');
  
  const headers = authToken ? { Authorization: `Bearer ${authToken}` } : {};
  
  try {
    const response = await axios.get(`${API_BASE_URL}/api/services/technical`, { headers });
    
    if (response.data.success) {
      addTestResult(
        'Get Technical Services',
        'PASS',
        `Retrieved ${response.data.data.length} technical services`,
        { count: response.data.data.length }
      );
      return true;
    } else {
      addTestResult(
        'Get Technical Services',
        'FAIL',
        'Failed to retrieve technical services',
        response.data
      );
      return false;
    }
  } catch (error) {
    addTestResult(
      'Technical Services',
      'FAIL',
      `Technical services test failed: ${error.message}`,
      { error: error.message, response: error.response?.data }
    );
    return false;
  }
}

/**
 * Test environment configuration
 */
async function testEnvironmentConfig() {
  log('\n⚙️ Testing Environment Configuration...', 'magenta');
  
  // Check if .env file exists and has required variables
  const envPath = path.join(process.cwd(), '.env');
  
  try {
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const hasApiUrl = envContent.includes('VITE_API_BASE_URL');
      const hasDbConfig = envContent.includes('DB_HOST') && envContent.includes('DB_NAME');
      
      if (hasApiUrl && hasDbConfig) {
        addTestResult(
          'Environment Configuration',
          'PASS',
          'Environment variables properly configured',
          { hasApiUrl, hasDbConfig }
        );
      } else {
        addTestResult(
          'Environment Configuration',
          'FAIL',
          'Missing required environment variables',
          { hasApiUrl, hasDbConfig }
        );
      }
    } else {
      addTestResult(
        'Environment Configuration',
        'FAIL',
        '.env file not found',
        { envPath }
      );
    }
  } catch (error) {
    addTestResult(
      'Environment Configuration',
      'FAIL',
      `Environment config test failed: ${error.message}`,
      { error: error.message }
    );
  }
}

/**
 * Generate test report
 */
function generateReport() {
  log('\n📊 Generating Test Report...', 'magenta');
  
  const report = {
    summary: {
      total: testResults.total,
      passed: testResults.passed,
      failed: testResults.failed,
      passRate: ((testResults.passed / testResults.total) * 100).toFixed(2) + '%'
    },
    environment: {
      apiBaseUrl: API_BASE_URL,
      frontendUrl: FRONTEND_URL,
      nodeVersion: process.version,
      timestamp: new Date().toISOString()
    },
    tests: testResults.tests
  };
  
  // Save report to file
  const reportPath = path.join(process.cwd(), 'integration-test-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  log(`\n📋 Test Report Summary:`, 'bright');
  log(`   Total Tests: ${report.summary.total}`, 'cyan');
  log(`   Passed: ${report.summary.passed}`, 'green');
  log(`   Failed: ${report.summary.failed}`, 'red');
  log(`   Pass Rate: ${report.summary.passRate}`, 'yellow');
  log(`   Report saved to: ${reportPath}`, 'cyan');
  
  return report;
}

/**
 * Main test execution
 */
async function runIntegrationTests() {
  log('🚀 Starting Khanfashariya API Integration Tests...', 'bright');
  log(`📡 API Base URL: ${API_BASE_URL}`, 'cyan');
  log(`🌐 Frontend URL: ${FRONTEND_URL}`, 'cyan');
  
  try {
    // Test 1: Server Health
    const serverHealthy = await testServerHealth();
    if (!serverHealthy) {
      log('\n❌ Server is not healthy. Stopping tests.', 'red');
      return generateReport();
    }
    
    // Test 2: Environment Configuration
    await testEnvironmentConfig();
    
    // Test 3: CORS Configuration
    await testCORS();
    
    // Test 4: Authentication
    const authResult = await testAuthentication();
    
    // Test 5: System Services
    await testSystemServices(authResult?.token);
    
    // Test 6: Technical Services
    await testTechnicalServices(authResult?.token);
    
    // Generate final report
    const report = generateReport();
    
    if (report.summary.failed === 0) {
      log('\n🎉 All tests passed! API migration is successful.', 'green');
      process.exit(0);
    } else {
      log('\n⚠️ Some tests failed. Please review the report.', 'yellow');
      process.exit(1);
    }
    
  } catch (error) {
    log(`\n💥 Test suite failed: ${error.message}`, 'red');
    console.error(error);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runIntegrationTests();
}

module.exports = {
  runIntegrationTests,
  testServerHealth,
  testAuthentication,
  testSystemServices,
  testTechnicalServices
};
