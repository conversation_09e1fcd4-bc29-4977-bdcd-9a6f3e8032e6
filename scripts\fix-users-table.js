#!/usr/bin/env node

/**
 * Fix users table by adding missing columns
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixUsersTable() {
  let connection;
  
  try {
    console.log('🔧 Fixing users table...');
    
    // Connect to database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '123456',
      database: 'khanfashariya_db',
      charset: 'utf8mb4'
    });
    
    console.log('✅ Connected to database');
    
    // Check if columns exist
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'khanfashariya_db' 
      AND TABLE_NAME = 'users'
    `);
    
    const columnNames = columns.map(col => col.COLUMN_NAME);
    
    // Add missing columns
    const columnsToAdd = [
      { name: 'email_verified', definition: 'BOOLEAN DEFAULT FALSE' },
      { name: 'phone', definition: 'VARCHAR(20)' },
      { name: 'avatar_url', definition: 'VARCHAR(500)' },
      { name: 'last_login', definition: 'TIMESTAMP NULL' },
      { name: 'login_count', definition: 'INT DEFAULT 0' }
    ];
    
    for (const column of columnsToAdd) {
      if (!columnNames.includes(column.name)) {
        await connection.execute(`ALTER TABLE users ADD COLUMN ${column.name} ${column.definition}`);
        console.log(`✅ Added ${column.name} column`);
      } else {
        console.log(`⚠️  ${column.name} column already exists`);
      }
    }
    
    console.log('🎉 users table fixed successfully!');
    
  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run fix
fixUsersTable().catch(error => {
  console.error('Fix failed:', error);
  process.exit(1);
});
