/**
 * API Client for Khanfashariya.com
 * 
 * Centralized HTTP client using Axios with:
 * - Environment-based base URL configuration
 * - JWT token management
 * - Request/Response interceptors
 * - Error handling
 * - TypeScript support
 * 
 * This replaces localStorage-based data management with proper API calls
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

// Types for API responses
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

// Authentication types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  username: string;
  full_name: string;
}

export interface AuthResponse {
  user: {
    id: string;
    email: string;
    username: string;
    full_name: string;
    role: 'user' | 'admin';
  };
  accessToken: string;
  refreshToken: string;
}

/**
 * API Client Class
 * Manages all HTTP requests to the backend API
 */
class ApiClient {
  private client: AxiosInstance;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;

  constructor() {
    // In development, use relative URLs to work with Vite proxy
    // In production, use absolute URLs
    const isDevelopment = import.meta.env.MODE === 'development';
    const baseURL = isDevelopment
      ? '/api' // Use relative URL for Vite proxy
      : `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'}/api`;

    console.log('API Base URL:', baseURL, '(Development mode:', isDevelopment, ')'); // Debug log

    // Create axios instance with base configuration
    this.client = axios.create({
      baseURL: baseURL,
      timeout: 10000, // 10 seconds timeout
      headers: {
        'Content-Type': 'application/json',
      },
      // Fix preload warnings by setting credentials mode
      withCredentials: false,
    });

    // Load tokens from localStorage on initialization
    this.loadTokensFromStorage();

    // Setup request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        if (this.accessToken) {
          config.headers.Authorization = `Bearer ${this.accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Setup response interceptor for error handling and token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

        // If token expired, try to refresh (but only if we have a refresh token)
        if (error.response?.status === 401 && !originalRequest._retry && this.refreshToken) {
          originalRequest._retry = true;

          try {
            await this.refreshAccessToken();
            // Retry original request with new token
            if (originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${this.accessToken}`;
            }
            return this.client(originalRequest);
          } catch (refreshError) {
            // Refresh failed, clear tokens but don't redirect if already on login page
            this.clearTokens();
            if (!window.location.pathname.includes('/login')) {
              window.location.href = '/login';
            }
            return Promise.reject(refreshError);
          }
        }

        // If no refresh token available or refresh already attempted, just return the error
        if (error.response?.status === 401 && !this.refreshToken) {
          this.clearTokens();
        }

        return Promise.reject(this.handleError(error));
      }
    );
  }

  /**
   * Load tokens from localStorage
   */
  private loadTokensFromStorage(): void {
    try {
      this.accessToken = localStorage.getItem('khanfashariya_access_token');
      this.refreshToken = localStorage.getItem('khanfashariya_refresh_token');
    } catch (error) {
      console.error('Error loading tokens from storage:', error);
    }
  }

  /**
   * Save tokens to localStorage
   */
  private saveTokensToStorage(): void {
    try {
      if (this.accessToken) {
        localStorage.setItem('khanfashariya_access_token', this.accessToken);
      }
      if (this.refreshToken) {
        localStorage.setItem('khanfashariya_refresh_token', this.refreshToken);
      }
    } catch (error) {
      console.error('Error saving tokens to storage:', error);
    }
  }

  /**
   * Clear tokens from memory and localStorage
   */
  private clearTokens(): void {
    this.accessToken = null;
    this.refreshToken = null;
    localStorage.removeItem('khanfashariya_access_token');
    localStorage.removeItem('khanfashariya_refresh_token');
    localStorage.removeItem('khanfashariya_current_user');
  }

  /**
   * Refresh access token using refresh token
   */
  private async refreshAccessToken(): Promise<void> {
    if (!this.refreshToken) {
      console.warn('No refresh token available - clearing tokens and redirecting to login');
      this.clearTokens();
      throw new Error('No refresh token available');
    }

    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_BASE_URL}/api/auth/refresh`,
        { refreshToken: this.refreshToken }
      );

      if (response.data.success && response.data.data && response.data.data.tokens) {
        const { accessToken, refreshToken } = response.data.data.tokens;
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.saveTokensToStorage();
      } else {
        throw new Error('Invalid refresh response format');
      }
    } catch (error) {
      console.error('Refresh token failed:', error);
      this.clearTokens();
      throw error;
    }
  }

  /**
   * Handle API errors consistently
   */
  private handleError(error: AxiosError): ApiError {
    if (error.response) {
      // Server responded with error status
      const message = (error.response.data as any)?.error || 
                     (error.response.data as any)?.message || 
                     'An error occurred';
      return {
        message,
        status: error.response.status,
        code: (error.response.data as any)?.code
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        message: 'Network error - please check your connection',
        status: 0
      };
    } else {
      // Something else happened
      return {
        message: error.message || 'An unexpected error occurred',
        status: 0
      };
    }
  }

  /**
   * Set authentication tokens
   */
  public setTokens(accessToken: string, refreshToken: string): void {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    this.saveTokensToStorage();
  }

  /**
   * Check if user is authenticated
   */
  public isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  /**
   * Get current access token
   */
  public getAccessToken(): string | null {
    return this.accessToken;
  }

  // =====================================================
  // AUTHENTICATION ENDPOINTS
  // =====================================================

  /**
   * User login
   */
  public async login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await this.client.post('/auth/login', credentials);
      const authData = response.data.data;

      // Handle different response formats
      let accessToken: string;
      let refreshToken: string;

      if (authData.tokens) {
        // New format: { tokens: { accessToken, refreshToken } }
        accessToken = authData.tokens.accessToken;
        refreshToken = authData.tokens.refreshToken;
      } else {
        // Old format: { accessToken, refreshToken }
        accessToken = authData.accessToken;
        refreshToken = authData.refreshToken;
      }

      // Store tokens
      this.setTokens(accessToken, refreshToken);

      // Store user data in localStorage for compatibility
      localStorage.setItem('khanfashariya_current_user', JSON.stringify(authData.user));

      return response.data;
    } catch (error) {
      throw this.handleError(error as AxiosError);
    }
  }

  /**
   * User registration
   */
  public async register(userData: RegisterData): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await this.client.post('/auth/register', userData);
      const authData = response.data.data;
      
      // Store tokens
      this.setTokens(authData.accessToken, authData.refreshToken);
      
      // Store user data in localStorage for compatibility
      localStorage.setItem('khanfashariya_current_user', JSON.stringify(authData.user));
      
      return response.data;
    } catch (error) {
      throw this.handleError(error as AxiosError);
    }
  }

  /**
   * User logout
   */
  public async logout(): Promise<ApiResponse> {
    try {
      await this.client.post('/auth/logout');
      this.clearTokens();
      return { success: true, message: 'Logged out successfully' };
    } catch (error) {
      // Clear tokens even if logout request fails
      this.clearTokens();
      throw this.handleError(error as AxiosError);
    }
  }

  // =====================================================
  // GENERIC HTTP METHODS
  // =====================================================

  /**
   * Generic GET request
   */
  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.get(url, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error as AxiosError);
    }
  }

  /**
   * Generic POST request
   */
  public async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.post(url, data, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error as AxiosError);
    }
  }

  /**
   * Generic PUT request
   */
  public async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.put(url, data, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error as AxiosError);
    }
  }

  /**
   * Generic DELETE request
   */
  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.delete(url, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error as AxiosError);
    }
  }
}

// Create and export singleton instance
const apiClient = new ApiClient();
export default apiClient;

// Export commonly used methods for convenience
export const { get, post, put, delete: del } = apiClient;
export const { login, register, logout } = apiClient;
