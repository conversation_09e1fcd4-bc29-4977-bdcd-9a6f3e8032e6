import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuth } from '../hooks/useAuth';
import { useNotification } from '../hooks/useNotification';
import UserOrderManager from './orders/UserOrderManager';
import {
  getSystemServices,
  getTechnicalServices,
  createOrder,
  signOut,
} from '../lib/apiServices';
import {
  getUserSubscriptions,
  getUserInboxMessages,
} from '../lib/apiEndpoints';
import {
  Order,
  Subscription,
  InboxMessage,
  SystemService,
  TechnicalService,
} from '../lib/database';
import {
  User,
  ShoppingCart,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Package,
  Star,
  Crown,
  Settings,

  X,
  CreditCard,
  Calendar,
  Mail,
  Store,
  RefreshCw,
  DollarSign,
  Bell,
  Eye,
  Trash2
} from 'lucide-react';

interface SimpleUserDashboardProps {
  onClose: () => void;
}

const SimpleUserDashboard: React.FC<SimpleUserDashboardProps> = ({ onClose }) => {
  const { t, language } = useTranslation();
  const { userProfile } = useAuth();
  const { showNotification } = useNotification();

  // Multi-tab state
  const [activeTab, setActiveTab] = useState<'products' | 'subscriptions' | 'orders' | 'inbox' | 'shop'>('products');

  // Data state
  const [userSubscriptions, setUserSubscriptions] = useState<Subscription[]>([]);
  const [inboxMessages, setInboxMessages] = useState<InboxMessage[]>([]);
  const [availableProducts, setAvailableProducts] = useState<(SystemService | TechnicalService)[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (userProfile) {
      loadUserData();
    }
  }, [userProfile]);

  const loadUserData = async () => {
    if (!userProfile) return;

    setLoading(true);
    try {
      const [subscriptionsResult, messagesResult, systemsResult, servicesResult] = await Promise.all([
        getUserSubscriptions(userProfile.id),
        getUserInboxMessages(userProfile.id),
        getSystemServices(),
        getTechnicalServices()
      ]);

      if (subscriptionsResult.data && Array.isArray(subscriptionsResult.data)) {
        setUserSubscriptions(subscriptionsResult.data);
      } else {
        setUserSubscriptions([]);
      }

      if (messagesResult.data && Array.isArray(messagesResult.data)) {
        setInboxMessages(messagesResult.data);
      } else {
        setInboxMessages([]);
      }

      // Combine systems and services for the shop
      const products: (SystemService | TechnicalService)[] = [];
      if (systemsResult.data && Array.isArray(systemsResult.data)) {
        products.push(...systemsResult.data.filter(s => s.status === 'active'));
      }
      if (servicesResult.data && Array.isArray(servicesResult.data)) {
        products.push(...servicesResult.data.filter(s => s.status === 'active'));
      }
      setAvailableProducts(products);

    } catch (error) {
      console.error('Error loading user data:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في تحميل البيانات' : 'Failed to load data'
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'in_progress':
      case 'testing':
      case 'confirmed':
        return <Clock className="w-5 h-5 text-blue-500" />;
      case 'pending':
      case 'on_hold':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'cancelled':
      case 'refunded':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: Order['status']) => {
    const statusMap: Record<Order['status'], string> = {
      'completed': language === 'ar' ? 'مكتمل' : 'Completed',
      'in_progress': language === 'ar' ? 'قيد التنفيذ' : 'In Progress',
      'pending': language === 'ar' ? 'في الانتظار' : 'Pending',
      'cancelled': language === 'ar' ? 'ملغي' : 'Cancelled',
      'confirmed': language === 'ar' ? 'مؤكد' : 'Confirmed',
      'on_hold': language === 'ar' ? 'معلق' : 'On Hold',
      'refunded': language === 'ar' ? 'مسترد' : 'Refunded',
      'testing': language === 'ar' ? 'قيد الاختبار' : 'Testing',
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'in_progress':
      case 'testing':
      case 'confirmed':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'pending':
      case 'on_hold':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'cancelled':
      case 'refunded':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  // New handler functions for multi-tab functionality
  const handleMarkMessageAsRead = async (messageId: string) => {
    try {
      // Update locally for now - API endpoint can be implemented later
      setInboxMessages(prev =>
        prev.map(msg => msg.id === messageId ? { ...msg, isRead: true } : msg)
      );
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم تحديد الرسالة كمقروءة' : 'Message marked as read'
      });
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  const handleCancelSubscription = async (subscriptionId: string) => {
    if (!window.confirm(language === 'ar' ? 'هل أنت متأكد من إلغاء هذا الاشتراك؟' : 'Are you sure you want to cancel this subscription?')) {
      return;
    }

    try {
      // This would update the subscription status to cancelled
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم إلغاء الاشتراك بنجاح' : 'Subscription cancelled successfully'
      });
      await loadUserData(); // Reload data
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في إلغاء الاشتراك' : 'Failed to cancel subscription'
      });
    }
  };

  const handlePurchaseProduct = async (product: SystemService | TechnicalService) => {
    if (!userProfile) return;

    try {
      const isService = 'isPremiumAddon' in product;
      const result = await createOrder({
        order_type: isService ? 'technical_service' : 'system_service',
        item_id: product.id,
        quantity: 1,
        notes_ar: `شراء ${language === 'ar' ? product.name_ar : product.name_en} من لوحة التحكم`,
        notes_en: `Purchase ${language === 'ar' ? product.name_ar : product.name_en} from dashboard`
      });

      if (result.data) {
        showNotification({
          type: 'success',
          message: language === 'ar' ? 'تم إرسال طلب الشراء بنجاح!' : 'Purchase order submitted successfully!'
        });
        await loadUserData(); // Reload data
      } else {
        showNotification({
          type: 'error',
          message: language === 'ar' ? 'فشل في إرسال طلب الشراء' : 'Failed to submit purchase order'
        });
      }
    } catch (error) {
      console.error('Error purchasing product:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في إرسال طلب الشراء' : 'Failed to submit purchase order'
      });
    }
  };

  // Tab configuration for the multi-tab customer portal
  const tabs = [
    {
      id: 'products' as const,
      label: language === 'ar' ? 'منتجاتي' : 'My Products',
      icon: Package,
      count: 0 // Will be updated by UserOrderManager
    },
    {
      id: 'subscriptions' as const,
      label: language === 'ar' ? 'اشتراكاتي' : 'My Subscriptions',
      icon: RefreshCw,
      count: Array.isArray(userSubscriptions) ? userSubscriptions.filter(sub => sub.status === 'active').length : 0
    },
    {
      id: 'orders' as const,
      label: language === 'ar' ? 'سجل الطلبات' : 'Order History',
      icon: Clock,
      count: 0 // Will be updated by UserOrderManager
    },
    {
      id: 'inbox' as const,
      label: language === 'ar' ? 'الرسائل' : 'Inbox',
      icon: Mail,
      count: Array.isArray(inboxMessages) ? inboxMessages.filter(msg => !msg.isRead).length : 0
    },
    {
      id: 'shop' as const,
      label: language === 'ar' ? 'المتجر' : 'Shop',
      icon: Store,
      count: 0
    }
  ];



  React.useEffect(() => {
    document.body.classList.add('modal-open');
    return () => {
      document.body.classList.remove('modal-open');
    };
  }, []);

  return (
    <div className="modal-backdrop flex items-center justify-center p-4"
         onClick={(e) => e.target === e.currentTarget && onClose()}>
      <div className="bg-primary rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="bg-gradient-to-r from-secondary to-accent p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center">
                <User className="w-10 h-10 text-primary" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-primary">
                  {language === 'ar' ? 'مرحباً' : 'Welcome'}, {userProfile?.full_name}
                </h2>
                <p className="text-primary/70">
                  {language === 'ar' ? 'بوابة العملاء المتكاملة' : 'Integrated Customer Portal'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <button
                onClick={onClose}
                className="text-primary hover:text-primary/70 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-background border-b border-gray-700">
          <div className="flex overflow-x-auto">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 rtl:space-x-reverse px-6 py-4 border-b-2 transition-colors whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'border-secondary text-secondary bg-secondary/10'
                      : 'border-transparent text-gray-400 hover:text-white hover:bg-gray-700/50'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{tab.label}</span>
                  {tab.count > 0 && (
                    <span className="bg-secondary text-primary text-xs px-2 py-1 rounded-full">
                      {tab.count}
                    </span>
                  )}
                </button>
              );
            })}
          </div>
        </div>

        {/* Tab Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="w-8 h-8 border-4 border-secondary border-t-transparent rounded-full animate-spin"></div>
              <span className="ml-3 text-white">{language === 'ar' ? 'جاري التحميل...' : 'Loading...'}</span>
            </div>
          ) : (
            <>
              {/* Tab 1: My Products */}
              {activeTab === 'products' && (
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-white">
                    {language === 'ar' ? 'منتجاتي المشتراة' : 'My Purchased Products'}
                  </h3>

                  {/* Products will be shown through UserOrderManager in orders tab */}
                  <div className="text-center py-12">
                    <Package className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-400 mb-2">
                      {language === 'ar' ? 'راجع سجل الطلبات' : 'Check Order History'}
                    </h3>
                    <p className="text-gray-500 mb-4">
                      {language === 'ar' ? 'يمكنك مراجعة جميع منتجاتك المشتراة في سجل الطلبات' : 'You can view all your purchased products in the order history'}
                    </p>
                    <button
                      onClick={() => setActiveTab('orders')}
                      className="px-4 py-2 bg-secondary text-white rounded-lg hover:bg-secondary/80 transition-colors"
                    >
                      {language === 'ar' ? 'انتقل إلى سجل الطلبات' : 'Go to Order History'}
                    </button>
                  </div>
                </div>
              )}

              {/* Tab 2: My Subscriptions */}
              {activeTab === 'subscriptions' && (
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-white">
                    {language === 'ar' ? 'اشتراكاتي النشطة' : 'My Active Subscriptions'}
                  </h3>

                  {userSubscriptions.length > 0 ? (
                    <div className="grid gap-4">
                      {userSubscriptions.map((subscription) => (
                        <div key={subscription.id} className="bg-background rounded-lg border border-gray-700 p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4 rtl:space-x-reverse">
                              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                                <RefreshCw className="w-6 h-6 text-white" />
                              </div>
                              <div>
                                <h4 className="text-lg font-semibold text-white">
                                  {language === 'ar' ? 'خدمة اشتراك' : 'Subscription Service'}
                                </h4>
                                <p className="text-gray-400 text-sm">
                                  {subscription.billingCycle === 'monthly'
                                    ? (language === 'ar' ? 'شهري' : 'Monthly')
                                    : (language === 'ar' ? 'سنوي' : 'Yearly')
                                  }
                                </p>
                                <p className="text-gray-500 text-xs">
                                  {language === 'ar' ? 'التجديد التالي:' : 'Next billing:'} {' '}
                                  {new Date(subscription.nextBillingDate).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                                </p>
                              </div>
                            </div>

                            <div className="text-right rtl:text-left space-y-2">
                              <p className="text-xl font-bold text-secondary">${subscription.price}</p>
                              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                                subscription.status === 'active'
                                  ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                                  : 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
                              }`}>
                                <div className={`w-2 h-2 rounded-full mr-2 rtl:ml-2 rtl:mr-0 ${
                                  subscription.status === 'active' ? 'bg-green-400' : 'bg-gray-400'
                                }`}></div>
                                {subscription.status === 'active'
                                  ? (language === 'ar' ? 'نشط' : 'Active')
                                  : (language === 'ar' ? 'معلق' : 'Paused')
                                }
                              </div>
                              {subscription.status === 'active' && (
                                <button
                                  onClick={() => handleCancelSubscription(subscription.id)}
                                  className="block w-full bg-red-500/20 text-red-400 border border-red-500/30 px-3 py-1 rounded text-sm hover:bg-red-500/30 transition-colors"
                                >
                                  {language === 'ar' ? 'إلغاء الاشتراك' : 'Cancel'}
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <RefreshCw className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-400 mb-2">
                        {language === 'ar' ? 'لا توجد اشتراكات' : 'No Subscriptions'}
                      </h3>
                      <p className="text-gray-500">
                        {language === 'ar' ? 'لا توجد لديك اشتراكات نشطة حالياً' : 'You don\'t have any active subscriptions'}
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Tab 3: Order History */}
              {activeTab === 'orders' && userProfile && (
                <UserOrderManager userId={userProfile.id} />
              )}

              {/* Tab 4: Inbox */}
              {activeTab === 'inbox' && (
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-white">
                    {language === 'ar' ? 'صندوق الرسائل' : 'Message Inbox'}
                  </h3>

                  {Array.isArray(inboxMessages) && inboxMessages.length > 0 ? (
                    <div className="grid gap-4">
                      {inboxMessages.map((message) => (
                        <div key={message.id} className={`bg-background rounded-lg border p-6 ${
                          message.isRead ? 'border-gray-700' : 'border-secondary/50 bg-secondary/5'
                        }`}>
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-4 rtl:space-x-reverse flex-1">
                              <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                                message.type === 'billing' ? 'bg-green-500/20' :
                                message.type === 'alert' ? 'bg-red-500/20' :
                                'bg-blue-500/20'
                              }`}>
                                {message.type === 'billing' ? <DollarSign className="w-6 h-6 text-green-400" /> :
                                 message.type === 'alert' ? <AlertCircle className="w-6 h-6 text-red-400" /> :
                                 <Bell className="w-6 h-6 text-blue-400" />}
                              </div>
                              <div className="flex-1">
                                <h4 className="text-lg font-semibold text-white mb-2">
                                  {language === 'ar' ? message.title?.ar || 'رسالة' : message.title?.en || 'Message'}
                                </h4>
                                <p className="text-gray-300 text-sm mb-3">
                                  {language === 'ar' ? message.content?.ar || 'محتوى الرسالة' : message.content?.en || 'Message content'}
                                </p>
                                <p className="text-gray-500 text-xs">
                                  {new Date(message.createdAt).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                                </p>
                              </div>
                            </div>

                            <div className="flex items-center space-x-2 rtl:space-x-reverse">
                              {!message.isRead && (
                                <button
                                  onClick={() => handleMarkMessageAsRead(message.id)}
                                  className="bg-secondary/20 text-secondary border border-secondary/30 px-3 py-1 rounded text-sm hover:bg-secondary/30 transition-colors"
                                >
                                  <Eye className="w-4 h-4" />
                                </button>
                              )}
                              <div className={`w-3 h-3 rounded-full ${
                                message.isRead ? 'bg-gray-500' : 'bg-secondary'
                              }`}></div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Mail className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-400 mb-2">
                        {language === 'ar' ? 'لا توجد رسائل' : 'No Messages'}
                      </h3>
                      <p className="text-gray-500">
                        {language === 'ar' ? 'صندوق الرسائل فارغ' : 'Your inbox is empty'}
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Tab 5: Shop */}
              {activeTab === 'shop' && (
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-white">
                    {language === 'ar' ? 'متجر الخدمات والأنظمة' : 'Services & Systems Shop'}
                  </h3>

                  {availableProducts.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {availableProducts.map((product) => {
                        const isService = 'isPremiumAddon' in product;
                        return (
                          <div key={product.id} className="bg-background rounded-lg border border-gray-700 p-6 hover:border-secondary/50 transition-colors">
                            <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
                              <div className="w-12 h-12 bg-gradient-to-br from-secondary to-accent rounded-lg flex items-center justify-center">
                                {isService ? <Settings className="w-6 h-6 text-primary" /> : <Package className="w-6 h-6 text-primary" />}
                              </div>
                              <div className="flex-1">
                                <h4 className="text-lg font-semibold text-white">
                                  {language === 'ar' ? product.name_ar : product.name_en}
                                </h4>
                                <p className="text-gray-400 text-sm">
                                  {isService ? (language === 'ar' ? 'خدمة تقنية' : 'Technical Service') :
                                   (product as SystemService).type === 'plugin' ? (language === 'ar' ? 'إضافة خاصة' : 'Special Plugin') :
                                   (language === 'ar' ? 'نظام عادي' : 'Regular System')}
                                </p>
                              </div>
                            </div>

                            <p className="text-gray-300 text-sm mb-4 line-clamp-3">
                              {language === 'ar' ? product.description_ar : product.description_en}
                            </p>

                            <div className="flex items-center justify-between">
                              <div>
                                <p className="text-2xl font-bold text-secondary">${product.price}</p>
                                {isService && (product as TechnicalService).subscriptionType !== 'none' && (
                                  <p className="text-xs text-gray-400">
                                    /{(product as TechnicalService).subscriptionType === 'monthly'
                                      ? (language === 'ar' ? 'شهر' : 'month')
                                      : (language === 'ar' ? 'سنة' : 'year')
                                    }
                                  </p>
                                )}
                              </div>
                              <button
                                onClick={() => handlePurchaseProduct(product)}
                                className="bg-gradient-to-r from-secondary to-accent text-primary px-4 py-2 rounded-lg hover:from-secondary/90 hover:to-accent/90 transition-colors font-medium"
                              >
                                {language === 'ar' ? 'اطلب الآن' : 'Order Now'}
                              </button>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Store className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-400 mb-2">
                        {language === 'ar' ? 'لا توجد منتجات متاحة' : 'No Products Available'}
                      </h3>
                      <p className="text-gray-500">
                        {language === 'ar' ? 'لا توجد منتجات متاحة للشراء حالياً' : 'No products are currently available for purchase'}
                      </p>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default SimpleUserDashboard;