/**
 * Enhanced API Endpoints for MySQL Integration
 * 
 * This file provides improved API endpoint functions that properly handle
 * the new database schema with all required fields and proper data transformation.
 */

import apiClient, { ApiResponse, ApiError } from './apiClient';
import {
  SystemService,
  TechnicalService,
  PremiumContent,
  Order,
  User,
  Subscription,
  InboxMessage
} from './database';

// =====================================================
// SYSTEM SERVICES API ENDPOINTS
// =====================================================

/**
 * Get all system services with proper field mapping
 */
export async function getSystemServices(): Promise<{ data: SystemService[]; error: ApiError | null }> {
  try {
    const response = await apiClient.get<any>('/systems');
    
    // Transform data to match TypeScript interface
    const systemServices: SystemService[] = (response.data?.systems || response.data || []).map((service: any) => ({
      id: service.id,
      name: {
        ar: service.name_ar || service.name?.ar || '',
        en: service.name_en || service.name?.en || ''
      },
      description: {
        ar: service.description_ar || service.description?.ar || '',
        en: service.description_en || service.description?.en || ''
      },
      price: parseFloat(service.price) || 0,
      category: service.category || 'utility',
      type: service.type || 'regular',
      isPremiumAddon: Boolean(service.is_premium_addon || service.isPremiumAddon),
      features: {
        ar: Array.isArray(service.features_ar) ? service.features_ar : 
            (typeof service.features_ar === 'string' ? JSON.parse(service.features_ar || '[]') : 
            service.features?.ar || []),
        en: Array.isArray(service.features_en) ? service.features_en : 
            (typeof service.features_en === 'string' ? JSON.parse(service.features_en || '[]') : 
            service.features?.en || [])
      },
      tech_specs: {
        ar: Array.isArray(service.tech_specs_ar) ? service.tech_specs_ar : 
            (typeof service.tech_specs_ar === 'string' ? JSON.parse(service.tech_specs_ar || '[]') : 
            service.tech_specs?.ar || []),
        en: Array.isArray(service.tech_specs_en) ? service.tech_specs_en : 
            (typeof service.tech_specs_en === 'string' ? JSON.parse(service.tech_specs_en || '[]') : 
            service.tech_specs?.en || [])
      },
      video_url: service.video_url,
      image_url: service.image_url,
      gallery_images: Array.isArray(service.gallery_images) ? service.gallery_images : 
                     (typeof service.gallery_images === 'string' ? JSON.parse(service.gallery_images || '[]') : []),
      status: service.status || 'active',
      created_at: service.created_at,
      updated_at: service.updated_at
    }));

    return { data: systemServices, error: null };
  } catch (error) {
    console.error('Error fetching system services:', error);
    return { data: [], error: error as ApiError };
  }
}

/**
 * Create system service with proper data transformation
 */
export async function createSystemService(
  serviceData: Omit<SystemService, 'id' | 'created_at' | 'updated_at'>
): Promise<{ data: SystemService | null; error: ApiError | null }> {
  try {
    // Transform data for MySQL storage
    const mysqlData = {
      name_ar: serviceData.name.ar,
      name_en: serviceData.name.en,
      description_ar: serviceData.description.ar,
      description_en: serviceData.description.en,
      price: serviceData.price,
      category: serviceData.category,
      type: serviceData.type,
      is_premium_addon: serviceData.isPremiumAddon || false,
      features_ar: JSON.stringify(serviceData.features.ar),
      features_en: JSON.stringify(serviceData.features.en),
      tech_specs_ar: JSON.stringify(serviceData.tech_specs.ar),
      tech_specs_en: JSON.stringify(serviceData.tech_specs.en),
      video_url: serviceData.video_url,
      image_url: serviceData.image_url,
      gallery_images: JSON.stringify(serviceData.gallery_images || []),
      status: serviceData.status
    };

    const response = await apiClient.post<any>('/admin/systems', mysqlData);
    
    // Transform response back to TypeScript interface
    const createdService = transformSystemServiceFromMySQL(response.data);
    return { data: createdService, error: null };
  } catch (error) {
    console.error('Error creating system service:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Update system service
 */
export async function updateSystemService(
  id: string,
  serviceData: Partial<SystemService>
): Promise<{ data: SystemService | null; error: ApiError | null }> {
  try {
    // Transform data for MySQL storage
    const mysqlData: any = {};
    
    if (serviceData.name) {
      mysqlData.name_ar = serviceData.name.ar;
      mysqlData.name_en = serviceData.name.en;
    }
    if (serviceData.description) {
      mysqlData.description_ar = serviceData.description.ar;
      mysqlData.description_en = serviceData.description.en;
    }
    if (serviceData.price !== undefined) mysqlData.price = serviceData.price;
    if (serviceData.category) mysqlData.category = serviceData.category;
    if (serviceData.type) mysqlData.type = serviceData.type;
    if (serviceData.isPremiumAddon !== undefined) mysqlData.is_premium_addon = serviceData.isPremiumAddon;
    if (serviceData.features) {
      mysqlData.features_ar = JSON.stringify(serviceData.features.ar);
      mysqlData.features_en = JSON.stringify(serviceData.features.en);
    }
    if (serviceData.tech_specs) {
      mysqlData.tech_specs_ar = JSON.stringify(serviceData.tech_specs.ar);
      mysqlData.tech_specs_en = JSON.stringify(serviceData.tech_specs.en);
    }
    if (serviceData.video_url) mysqlData.video_url = serviceData.video_url;
    if (serviceData.image_url) mysqlData.image_url = serviceData.image_url;
    if (serviceData.gallery_images) mysqlData.gallery_images = JSON.stringify(serviceData.gallery_images);
    if (serviceData.status) mysqlData.status = serviceData.status;

    const response = await apiClient.put<any>(`/admin/systems/${id}`, mysqlData);
    
    // Transform response back to TypeScript interface
    const updatedService = transformSystemServiceFromMySQL(response.data);
    return { data: updatedService, error: null };
  } catch (error) {
    console.error('Error updating system service:', error);
    return { data: null, error: error as ApiError };
  }
}

// =====================================================
// TECHNICAL SERVICES API ENDPOINTS
// =====================================================

/**
 * Get all technical services with proper field mapping
 */
export async function getTechnicalServices(): Promise<{ data: TechnicalService[]; error: ApiError | null }> {
  try {
    const response = await apiClient.get<any>('/services/technical');
    
    // Transform data to match TypeScript interface
    const technicalServices: TechnicalService[] = (response.data?.services || response.data || []).map((service: any) => ({
      id: service.id,
      name: {
        ar: service.name_ar || service.name?.ar || '',
        en: service.name_en || service.name?.en || ''
      },
      description: {
        ar: service.description_ar || service.description?.ar || '',
        en: service.description_en || service.description?.en || ''
      },
      price: parseFloat(service.price) || 0,
      category: service.category || 'maintenance',
      isPremiumAddon: Boolean(service.is_premium_addon || service.isPremiumAddon),
      premiumPrice: parseFloat(service.premium_price || service.premiumPrice) || 0,
      subscriptionType: service.subscription_type || service.subscriptionType || 'none',
      features: {
        ar: Array.isArray(service.features_ar) ? service.features_ar : 
            (typeof service.features_ar === 'string' ? JSON.parse(service.features_ar || '[]') : 
            service.features?.ar || []),
        en: Array.isArray(service.features_en) ? service.features_en : 
            (typeof service.features_en === 'string' ? JSON.parse(service.features_en || '[]') : 
            service.features?.en || [])
      },
      tech_specs: {
        ar: Array.isArray(service.tech_specs_ar) ? service.tech_specs_ar : 
            (typeof service.tech_specs_ar === 'string' ? JSON.parse(service.tech_specs_ar || '[]') : 
            service.tech_specs?.ar || []),
        en: Array.isArray(service.tech_specs_en) ? service.tech_specs_en : 
            (typeof service.tech_specs_en === 'string' ? JSON.parse(service.tech_specs_en || '[]') : 
            service.tech_specs?.en || [])
      },
      video_url: service.video_url,
      image_url: service.image_url,
      gallery_images: Array.isArray(service.gallery_images) ? service.gallery_images : 
                     (typeof service.gallery_images === 'string' ? JSON.parse(service.gallery_images || '[]') : []),
      status: service.status || 'active',
      created_at: service.created_at,
      updated_at: service.updated_at
    }));

    return { data: technicalServices, error: null };
  } catch (error) {
    console.error('Error fetching technical services:', error);
    return { data: [], error: error as ApiError };
  }
}

/**
 * Create technical service with proper data transformation
 */
export async function createTechnicalService(
  serviceData: Omit<TechnicalService, 'id' | 'created_at' | 'updated_at'>
): Promise<{ data: TechnicalService | null; error: ApiError | null }> {
  try {
    // Transform data for MySQL storage
    const mysqlData = {
      name_ar: serviceData.name.ar,
      name_en: serviceData.name.en,
      description_ar: serviceData.description.ar,
      description_en: serviceData.description.en,
      price: serviceData.price,
      category: serviceData.category,
      is_premium_addon: serviceData.isPremiumAddon,
      premium_price: serviceData.premiumPrice,
      subscription_type: serviceData.subscriptionType,
      features_ar: JSON.stringify(serviceData.features.ar),
      features_en: JSON.stringify(serviceData.features.en),
      tech_specs_ar: JSON.stringify(serviceData.tech_specs.ar),
      tech_specs_en: JSON.stringify(serviceData.tech_specs.en),
      video_url: serviceData.video_url,
      image_url: serviceData.image_url,
      gallery_images: JSON.stringify(serviceData.gallery_images || []),
      status: serviceData.status
    };

    const response = await apiClient.post<any>('/admin/technical-services', mysqlData);
    
    // Transform response back to TypeScript interface
    const createdService = transformTechnicalServiceFromMySQL(response.data);
    return { data: createdService, error: null };
  } catch (error) {
    console.error('Error creating technical service:', error);
    return { data: null, error: error as ApiError };
  }
}

// =====================================================
// PREMIUM CONTENT API ENDPOINTS
// =====================================================

/**
 * Get all premium content with proper field mapping
 */
export async function getPremiumContent(): Promise<{ data: PremiumContent[]; error: ApiError | null }> {
  try {
    const response = await apiClient.get<any>('/premium/content');
    
    // Transform data to match TypeScript interface
    const premiumContent: PremiumContent[] = (response.data?.content || response.data || []).map((content: any) => ({
      id: content.id,
      name: {
        ar: content.name_ar || content.name?.ar || '',
        en: content.name_en || content.name?.en || ''
      },
      description: {
        ar: content.description_ar || content.description?.ar || '',
        en: content.description_en || content.description?.en || ''
      },
      detailed_description: {
        ar: content.detailed_description_ar || content.detailed_description?.ar || '',
        en: content.detailed_description_en || content.detailed_description?.en || ''
      },
      features: {
        ar: Array.isArray(content.features_ar) ? content.features_ar : 
            (typeof content.features_ar === 'string' ? JSON.parse(content.features_ar || '[]') : 
            content.features?.ar || []),
        en: Array.isArray(content.features_en) ? content.features_en : 
            (typeof content.features_en === 'string' ? JSON.parse(content.features_en || '[]') : 
            content.features?.en || [])
      },
      technical_specs: {
        ar: Array.isArray(content.technical_specs_ar) ? content.technical_specs_ar : 
            (typeof content.technical_specs_ar === 'string' ? JSON.parse(content.technical_specs_ar || '[]') : 
            content.technical_specs?.ar || []),
        en: Array.isArray(content.technical_specs_en) ? content.technical_specs_en : 
            (typeof content.technical_specs_en === 'string' ? JSON.parse(content.technical_specs_en || '[]') : 
            content.technical_specs?.en || [])
      },
      images: Array.isArray(content.images) ? content.images : 
              (typeof content.images === 'string' ? JSON.parse(content.images || '[]') : []),
      videos: Array.isArray(content.videos) ? content.videos : 
              (typeof content.videos === 'string' ? JSON.parse(content.videos || '[]') : []),
      price: parseFloat(content.price) || 0,
      original_price: content.original_price ? parseFloat(content.original_price) : undefined,
      category: content.category || 'complete_package',
      status: content.status || 'active',
      is_active: Boolean(content.is_active),
      created_at: content.created_at,
      updated_at: content.updated_at
    }));

    return { data: premiumContent, error: null };
  } catch (error) {
    console.error('Error fetching premium content:', error);
    return { data: [], error: error as ApiError };
  }
}

// =====================================================
// HELPER FUNCTIONS FOR DATA TRANSFORMATION
// =====================================================

/**
 * Transform MySQL system service data to TypeScript interface
 */
function transformSystemServiceFromMySQL(mysqlData: any): SystemService {
  return {
    id: mysqlData.id,
    name: {
      ar: mysqlData.name_ar || '',
      en: mysqlData.name_en || ''
    },
    description: {
      ar: mysqlData.description_ar || '',
      en: mysqlData.description_en || ''
    },
    price: parseFloat(mysqlData.price) || 0,
    category: mysqlData.category || 'utility',
    type: mysqlData.type || 'regular',
    isPremiumAddon: Boolean(mysqlData.is_premium_addon),
    features: {
      ar: typeof mysqlData.features_ar === 'string' ? JSON.parse(mysqlData.features_ar || '[]') : (mysqlData.features_ar || []),
      en: typeof mysqlData.features_en === 'string' ? JSON.parse(mysqlData.features_en || '[]') : (mysqlData.features_en || [])
    },
    tech_specs: {
      ar: typeof mysqlData.tech_specs_ar === 'string' ? JSON.parse(mysqlData.tech_specs_ar || '[]') : (mysqlData.tech_specs_ar || []),
      en: typeof mysqlData.tech_specs_en === 'string' ? JSON.parse(mysqlData.tech_specs_en || '[]') : (mysqlData.tech_specs_en || [])
    },
    video_url: mysqlData.video_url,
    image_url: mysqlData.image_url,
    gallery_images: typeof mysqlData.gallery_images === 'string' ? JSON.parse(mysqlData.gallery_images || '[]') : (mysqlData.gallery_images || []),
    status: mysqlData.status || 'active',
    created_at: mysqlData.created_at,
    updated_at: mysqlData.updated_at
  };
}

/**
 * Transform MySQL technical service data to TypeScript interface
 */
function transformTechnicalServiceFromMySQL(mysqlData: any): TechnicalService {
  return {
    id: mysqlData.id,
    name: {
      ar: mysqlData.name_ar || '',
      en: mysqlData.name_en || ''
    },
    description: {
      ar: mysqlData.description_ar || '',
      en: mysqlData.description_en || ''
    },
    price: parseFloat(mysqlData.price) || 0,
    category: mysqlData.category || 'maintenance',
    isPremiumAddon: Boolean(mysqlData.is_premium_addon),
    premiumPrice: parseFloat(mysqlData.premium_price) || 0,
    subscriptionType: mysqlData.subscription_type || 'none',
    features: {
      ar: typeof mysqlData.features_ar === 'string' ? JSON.parse(mysqlData.features_ar || '[]') : (mysqlData.features_ar || []),
      en: typeof mysqlData.features_en === 'string' ? JSON.parse(mysqlData.features_en || '[]') : (mysqlData.features_en || [])
    },
    tech_specs: {
      ar: typeof mysqlData.tech_specs_ar === 'string' ? JSON.parse(mysqlData.tech_specs_ar || '[]') : (mysqlData.tech_specs_ar || []),
      en: typeof mysqlData.tech_specs_en === 'string' ? JSON.parse(mysqlData.tech_specs_en || '[]') : (mysqlData.tech_specs_en || [])
    },
    video_url: mysqlData.video_url,
    image_url: mysqlData.image_url,
    gallery_images: typeof mysqlData.gallery_images === 'string' ? JSON.parse(mysqlData.gallery_images || '[]') : (mysqlData.gallery_images || []),
    status: mysqlData.status || 'active',
    created_at: mysqlData.created_at,
    updated_at: mysqlData.updated_at
  };
}

// =====================================================
// ADMIN API ENDPOINTS
// =====================================================

/**
 * Delete system service (admin only)
 */
export async function deleteSystemService(id: string): Promise<{ error: ApiError | null }> {
  try {
    await apiClient.delete(`/admin/systems/${id}`);
    return { error: null };
  } catch (error) {
    console.error('Error deleting system service:', error);
    return { error: error as ApiError };
  }
}

/**
 * Update technical service (admin only)
 */
export async function updateTechnicalService(
  id: string,
  serviceData: Partial<TechnicalService>
): Promise<{ data: TechnicalService | null; error: ApiError | null }> {
  try {
    // Transform data for MySQL storage
    const mysqlData: any = {};
    
    if (serviceData.name) {
      mysqlData.name_ar = serviceData.name.ar;
      mysqlData.name_en = serviceData.name.en;
    }
    if (serviceData.description) {
      mysqlData.description_ar = serviceData.description.ar;
      mysqlData.description_en = serviceData.description.en;
    }
    if (serviceData.price !== undefined) mysqlData.price = serviceData.price;
    if (serviceData.category) mysqlData.category = serviceData.category;
    if (serviceData.isPremiumAddon !== undefined) mysqlData.is_premium_addon = serviceData.isPremiumAddon;
    if (serviceData.premiumPrice !== undefined) mysqlData.premium_price = serviceData.premiumPrice;
    if (serviceData.subscriptionType) mysqlData.subscription_type = serviceData.subscriptionType;
    if (serviceData.features) {
      mysqlData.features_ar = JSON.stringify(serviceData.features.ar);
      mysqlData.features_en = JSON.stringify(serviceData.features.en);
    }
    if (serviceData.tech_specs) {
      mysqlData.tech_specs_ar = JSON.stringify(serviceData.tech_specs.ar);
      mysqlData.tech_specs_en = JSON.stringify(serviceData.tech_specs.en);
    }
    if (serviceData.video_url) mysqlData.video_url = serviceData.video_url;
    if (serviceData.image_url) mysqlData.image_url = serviceData.image_url;
    if (serviceData.gallery_images) mysqlData.gallery_images = JSON.stringify(serviceData.gallery_images);
    if (serviceData.status) mysqlData.status = serviceData.status;

    const response = await apiClient.put<any>(`/admin/technical-services/${id}`, mysqlData);
    
    // Transform response back to TypeScript interface
    const updatedService = transformTechnicalServiceFromMySQL(response.data);
    return { data: updatedService, error: null };
  } catch (error) {
    console.error('Error updating technical service:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Delete technical service (admin only)
 */
export async function deleteTechnicalService(id: string): Promise<{ error: ApiError | null }> {
  try {
    await apiClient.delete(`/admin/technical-services/${id}`);
    return { error: null };
  } catch (error) {
    console.error('Error deleting technical service:', error);
    return { error: error as ApiError };
  }
}

/**
 * Create premium content (admin only)
 */
export async function createPremiumContent(
  contentData: Omit<PremiumContent, 'id' | 'created_at' | 'updated_at'>
): Promise<{ data: PremiumContent | null; error: ApiError | null }> {
  try {
    // Transform data for MySQL storage
    const mysqlData = {
      name_ar: contentData.name.ar,
      name_en: contentData.name.en,
      description_ar: contentData.description.ar,
      description_en: contentData.description.en,
      detailed_description_ar: contentData.detailed_description.ar,
      detailed_description_en: contentData.detailed_description.en,
      features_ar: JSON.stringify(contentData.features.ar),
      features_en: JSON.stringify(contentData.features.en),
      technical_specs_ar: JSON.stringify(contentData.technical_specs.ar),
      technical_specs_en: JSON.stringify(contentData.technical_specs.en),
      images: JSON.stringify(contentData.images),
      videos: JSON.stringify(contentData.videos),
      price: contentData.price,
      original_price: contentData.original_price,
      category: contentData.category,
      status: contentData.status,
      is_active: contentData.is_active
    };

    const response = await apiClient.post<any>('/admin/premium-content', mysqlData);
    
    // Transform response back to TypeScript interface
    const createdContent = transformPremiumContentFromMySQL(response.data);
    return { data: createdContent, error: null };
  } catch (error) {
    console.error('Error creating premium content:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Update premium content (admin only)
 */
export async function updatePremiumContent(
  id: string,
  contentData: Partial<PremiumContent>
): Promise<{ data: PremiumContent | null; error: ApiError | null }> {
  try {
    // Transform data for MySQL storage
    const mysqlData: any = {};
    
    if (contentData.name) {
      mysqlData.name_ar = contentData.name.ar;
      mysqlData.name_en = contentData.name.en;
    }
    if (contentData.description) {
      mysqlData.description_ar = contentData.description.ar;
      mysqlData.description_en = contentData.description.en;
    }
    if (contentData.detailed_description) {
      mysqlData.detailed_description_ar = contentData.detailed_description.ar;
      mysqlData.detailed_description_en = contentData.detailed_description.en;
    }
    if (contentData.features) {
      mysqlData.features_ar = JSON.stringify(contentData.features.ar);
      mysqlData.features_en = JSON.stringify(contentData.features.en);
    }
    if (contentData.technical_specs) {
      mysqlData.technical_specs_ar = JSON.stringify(contentData.technical_specs.ar);
      mysqlData.technical_specs_en = JSON.stringify(contentData.technical_specs.en);
    }
    if (contentData.images) mysqlData.images = JSON.stringify(contentData.images);
    if (contentData.videos) mysqlData.videos = JSON.stringify(contentData.videos);
    if (contentData.price !== undefined) mysqlData.price = contentData.price;
    if (contentData.original_price !== undefined) mysqlData.original_price = contentData.original_price;
    if (contentData.category) mysqlData.category = contentData.category;
    if (contentData.status) mysqlData.status = contentData.status;
    if (contentData.is_active !== undefined) mysqlData.is_active = contentData.is_active;

    const response = await apiClient.put<any>(`/admin/premium-content/${id}`, mysqlData);
    
    // Transform response back to TypeScript interface
    const updatedContent = transformPremiumContentFromMySQL(response.data);
    return { data: updatedContent, error: null };
  } catch (error) {
    console.error('Error updating premium content:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Delete premium content (admin only)
 */
export async function deletePremiumContent(id: string): Promise<{ error: ApiError | null }> {
  try {
    await apiClient.delete(`/admin/premium-content/${id}`);
    return { error: null };
  } catch (error) {
    console.error('Error deleting premium content:', error);
    return { error: error as ApiError };
  }
}

// =====================================================
// ORDERS AND SUBSCRIPTIONS API ENDPOINTS
// =====================================================

/**
 * Get user orders with enhanced data
 */
export async function getUserOrders(userId?: string): Promise<{ data: Order[]; error: ApiError | null }> {
  try {
    const url = userId ? `/orders/user/${userId}` : '/orders/my';
    const response = await apiClient.get<any>(url);
    
    // Transform data to match TypeScript interface
    const orders: Order[] = (response.data?.orders || response.data || []).map((order: any) => ({
      id: order.id,
      user_id: order.user_id,
      service_name: order.service_name,
      service_type: order.service_type,
      type: order.type || 'standard',
      status: order.status || 'pending',
      priority: order.priority || 'medium',
      price: parseFloat(order.price) || 0,
      details: order.details,
      subscriptionId: order.subscriptionId || order.subscription_id,
      purchase_date: order.purchase_date,
      start_date: order.start_date,
      completion_date: order.completion_date,
      estimated_completion: order.estimated_completion,
      notes: order.notes,
      admin_notes: order.admin_notes,
      progress_percentage: parseInt(order.progress_percentage) || 0,
      files_attached: Array.isArray(order.files_attached) ? order.files_attached : 
                     (typeof order.files_attached === 'string' ? JSON.parse(order.files_attached || '[]') : []),
      communication_log: Array.isArray(order.communication_log) ? order.communication_log : 
                        (typeof order.communication_log === 'string' ? JSON.parse(order.communication_log || '[]') : [])
    }));

    return { data: orders, error: null };
  } catch (error) {
    console.error('Error fetching user orders:', error);
    return { data: [], error: error as ApiError };
  }
}

/**
 * Create order with enhanced data structure
 */
export async function createOrder(
  orderData: Omit<Order, 'id' | 'purchase_date'>
): Promise<{ data: Order | null; error: ApiError | null }> {
  try {
    // Transform data for MySQL storage
    const mysqlData = {
      user_id: orderData.user_id,
      service_name: orderData.service_name,
      service_type: orderData.service_type,
      type: orderData.type,
      status: orderData.status,
      priority: orderData.priority,
      price: orderData.price,
      details: orderData.details,
      subscription_id: orderData.subscriptionId,
      start_date: orderData.start_date,
      completion_date: orderData.completion_date,
      estimated_completion: orderData.estimated_completion,
      notes: orderData.notes,
      admin_notes: orderData.admin_notes,
      progress_percentage: orderData.progress_percentage,
      files_attached: JSON.stringify(orderData.files_attached || []),
      communication_log: JSON.stringify(orderData.communication_log || [])
    };

    const response = await apiClient.post<any>('/orders', mysqlData);
    
    // Transform response back to TypeScript interface
    const createdOrder = transformOrderFromMySQL(response.data);
    return { data: createdOrder, error: null };
  } catch (error) {
    console.error('Error creating order:', error);
    return { data: null, error: error as ApiError };
  }
}

/**
 * Get user subscriptions
 */
export async function getUserSubscriptions(userId?: string): Promise<{ data: Subscription[]; error: ApiError | null }> {
  try {
    const url = userId ? `/users/${userId}/subscriptions` : '/users/profile';
    const response = await apiClient.get<any>(url);

    // Handle subscriptions from user profile or direct endpoint
    const subscriptions = response.data?.data?.subscriptions || response.data?.subscriptions || [];
    return { data: subscriptions, error: null };
  } catch (error) {
    console.error('Error fetching user subscriptions:', error);
    return { data: [], error: error as ApiError };
  }
}

/**
 * Get user inbox messages
 */
export async function getUserInboxMessages(userId?: string): Promise<{ data: InboxMessage[]; error: ApiError | null }> {
  try {
    const url = userId ? `/users/${userId}/messages` : '/users/profile';
    const response = await apiClient.get<any>(url);
    
    // Transform data to match TypeScript interface
    const rawMessages = response.data?.data?.messages || response.data?.messages || [];
    const messages: InboxMessage[] = rawMessages.map((message: any) => ({
      id: message.id,
      userId: message.userId || message.user_id,
      title: {
        ar: message.subject_ar || message.title_ar || '',
        en: message.subject_en || message.title_en || ''
      },
      content: {
        ar: message.message_ar || message.content_ar || '',
        en: message.message_en || message.content_en || ''
      },
      isRead: Boolean(message.isRead || message.is_read),
      type: message.message_type || message.type || 'alert',
      createdAt: message.createdAt || message.created_at
    }));

    return { data: messages, error: null };
  } catch (error) {
    console.error('Error fetching inbox messages:', error);
    return { data: [], error: error as ApiError };
  }
}

// =====================================================
// ADDITIONAL HELPER FUNCTIONS
// =====================================================

/**
 * Transform MySQL premium content data to TypeScript interface
 */
function transformPremiumContentFromMySQL(mysqlData: any): PremiumContent {
  return {
    id: mysqlData.id,
    name: {
      ar: mysqlData.name_ar || '',
      en: mysqlData.name_en || ''
    },
    description: {
      ar: mysqlData.description_ar || '',
      en: mysqlData.description_en || ''
    },
    detailed_description: {
      ar: mysqlData.detailed_description_ar || '',
      en: mysqlData.detailed_description_en || ''
    },
    features: {
      ar: typeof mysqlData.features_ar === 'string' ? JSON.parse(mysqlData.features_ar || '[]') : (mysqlData.features_ar || []),
      en: typeof mysqlData.features_en === 'string' ? JSON.parse(mysqlData.features_en || '[]') : (mysqlData.features_en || [])
    },
    technical_specs: {
      ar: typeof mysqlData.technical_specs_ar === 'string' ? JSON.parse(mysqlData.technical_specs_ar || '[]') : (mysqlData.technical_specs_ar || []),
      en: typeof mysqlData.technical_specs_en === 'string' ? JSON.parse(mysqlData.technical_specs_en || '[]') : (mysqlData.technical_specs_en || [])
    },
    images: typeof mysqlData.images === 'string' ? JSON.parse(mysqlData.images || '[]') : (mysqlData.images || []),
    videos: typeof mysqlData.videos === 'string' ? JSON.parse(mysqlData.videos || '[]') : (mysqlData.videos || []),
    price: parseFloat(mysqlData.price) || 0,
    original_price: mysqlData.original_price ? parseFloat(mysqlData.original_price) : undefined,
    category: mysqlData.category || 'complete_package',
    status: mysqlData.status || 'active',
    is_active: Boolean(mysqlData.is_active),
    created_at: mysqlData.created_at,
    updated_at: mysqlData.updated_at
  };
}

/**
 * Transform MySQL order data to TypeScript interface
 */
function transformOrderFromMySQL(mysqlData: any): Order {
  return {
    id: mysqlData.id,
    user_id: mysqlData.user_id,
    service_name: mysqlData.service_name,
    service_type: mysqlData.service_type,
    type: mysqlData.type || 'standard',
    status: mysqlData.status || 'pending',
    priority: mysqlData.priority || 'medium',
    price: parseFloat(mysqlData.price) || 0,
    details: mysqlData.details,
    subscriptionId: mysqlData.subscription_id,
    purchase_date: mysqlData.purchase_date,
    start_date: mysqlData.start_date,
    completion_date: mysqlData.completion_date,
    estimated_completion: mysqlData.estimated_completion,
    notes: mysqlData.notes,
    admin_notes: mysqlData.admin_notes,
    progress_percentage: parseInt(mysqlData.progress_percentage) || 0,
    files_attached: typeof mysqlData.files_attached === 'string' ? JSON.parse(mysqlData.files_attached || '[]') : (mysqlData.files_attached || []),
    communication_log: typeof mysqlData.communication_log === 'string' ? JSON.parse(mysqlData.communication_log || '[]') : (mysqlData.communication_log || [])
  };
}