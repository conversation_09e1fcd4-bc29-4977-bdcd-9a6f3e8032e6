import React from 'react';
import {
  Package,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  TrendingUp,
  TrendingDown,
  Users,
  Calendar,
  CreditCard,
  Crown,
  RefreshCw,
  AlertCircle
} from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';

interface Order {
  id: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'testing' | 'completed' | 'cancelled' | 'refunded' | 'on_hold' | 'expired';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  order_category?: 'standard' | 'premium_base' | 'premium_custom' | 'subscription' | 'maintenance';

  // Enhanced pricing structure
  final_price: number;
  unit_price?: number;
  base_price?: number;
  addons_price?: number;
  subscription_price?: number;
  maintenance_price?: number;
  total_price?: number;
  discount_amount?: number;
  tax_amount?: number;

  created_at: string;
  user_id: string;
  order_number?: string;
  order_type?: string;
  item_name_ar?: string;
  item_name_en?: string;
  payment_status?: 'pending' | 'paid' | 'partial' | 'failed' | 'refunded' | 'disputed';
  subscription_type?: 'none' | 'monthly' | 'quarterly' | 'yearly' | 'lifetime';

  // User information from JOIN
  username?: string;
  email?: string;
  full_name?: string;
}

interface OrderStatisticsProps {
  orders: Order[];
}

const OrderStatistics: React.FC<OrderStatisticsProps> = ({ orders }) => {
  const { language } = useTranslation();

  // Calculate comprehensive statistics
  const stats = React.useMemo(() => {
    const getOrderValue = (order: Order) => {
      // Enhanced pricing calculation with proper fallback chain
      const finalPrice = parseFloat(order.final_price?.toString() || '0');
      const totalPrice = parseFloat(order.total_price?.toString() || '0');
      const basePrice = parseFloat(order.base_price?.toString() || '0');
      const unitPrice = parseFloat(order.unit_price?.toString() || '0');

      return finalPrice || totalPrice || basePrice || unitPrice || 0;
    };

    // Enhanced order categorization
    const completedOrders = orders.filter(o => o.status === 'completed');
    const pendingOrders = orders.filter(o => ['pending', 'confirmed'].includes(o.status));
    const activeOrders = orders.filter(o => ['in_progress', 'testing'].includes(o.status));
    const cancelledOrders = orders.filter(o => ['cancelled', 'refunded', 'expired'].includes(o.status));
    const onHoldOrders = orders.filter(o => o.status === 'on_hold');

    // Premium and subscription orders
    const premiumOrders = orders.filter(o => ['premium_base', 'premium_custom'].includes(o.order_category || ''));
    const subscriptionOrders = orders.filter(o => o.subscription_type && o.subscription_type !== 'none');

    // Priority-based orders
    const urgentOrders = orders.filter(o => o.priority === 'urgent');
    const highPriorityOrders = orders.filter(o => o.priority === 'high');

    // Revenue calculations
    const totalRevenue = completedOrders.reduce((sum, o) => sum + getOrderValue(o), 0);
    const pendingRevenue = pendingOrders.reduce((sum, o) => sum + getOrderValue(o), 0);
    const activeRevenue = activeOrders.reduce((sum, o) => sum + getOrderValue(o), 0);
    const premiumRevenue = premiumOrders.reduce((sum, o) => sum + getOrderValue(o), 0);
    const subscriptionRevenue = subscriptionOrders.reduce((sum, o) => sum + getOrderValue(o), 0);

    const averageOrderValue = completedOrders.length > 0 ? totalRevenue / completedOrders.length : 0;
    const averagePremiumValue = premiumOrders.length > 0 ? premiumRevenue / premiumOrders.length : 0;

    // Customer analytics
    const uniqueCustomers = new Set(orders.map(o => o.user_id)).size;
    const premiumCustomers = new Set(premiumOrders.map(o => o.user_id)).size;
    const subscriptionCustomers = new Set(subscriptionOrders.map(o => o.user_id)).size;

    // Calculate this month's orders
    const thisMonth = new Date();
    const thisMonthOrders = orders.filter(o => {
      const orderDate = new Date(o.created_at);
      return orderDate.getMonth() === thisMonth.getMonth() &&
             orderDate.getFullYear() === thisMonth.getFullYear();
    });

    const thisMonthRevenue = thisMonthOrders
      .filter(o => o.status === 'completed')
      .reduce((sum, o) => sum + getOrderValue(o), 0);

    return {
      // Basic counts
      total: orders.length,
      completed: completedOrders.length,
      pending: pendingOrders.length,
      active: activeOrders.length,
      cancelled: cancelledOrders.length,
      onHold: onHoldOrders.length,

      // Premium and subscription metrics
      premium: premiumOrders.length,
      subscription: subscriptionOrders.length,
      urgent: urgentOrders.length,
      highPriority: highPriorityOrders.length,

      // Revenue metrics
      totalRevenue,
      pendingRevenue,
      activeRevenue,
      premiumRevenue,
      subscriptionRevenue,
      averageOrderValue,
      averagePremiumValue,

      // Customer metrics
      uniqueCustomers,
      premiumCustomers,
      subscriptionCustomers,

      // Time-based metrics
      thisMonthOrders: thisMonthOrders.length,
      thisMonthRevenue,
      completionRate: orders.length > 0 ? (completedOrders.length / orders.length) * 100 : 0,

      // Legacy support
      processing: activeOrders.length // For backward compatibility
    };
  }, [orders]);

  const StatCard = ({ 
    title, 
    value, 
    icon: Icon, 
    color, 
    subtitle, 
    trend 
  }: {
    title: string;
    value: string | number;
    icon: any;
    color: string;
    subtitle?: string;
    trend?: { value: number; isPositive: boolean };
  }) => (
    <div className={`bg-gradient-to-br ${color} border-2 rounded-xl p-5 hover:shadow-xl hover:scale-105 transition-all duration-300 backdrop-blur-sm`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-semibold text-gray-200 mb-2">{title}</p>
          <p className="text-3xl font-bold text-white mb-2 tracking-tight">{value}</p>
          {subtitle && (
            <p className="text-xs text-gray-300 font-medium">{subtitle}</p>
          )}
          {trend && (
            <div className={`flex items-center mt-2 text-xs font-semibold ${trend.isPositive ? 'text-green-300' : 'text-red-300'}`}>
              {trend.isPositive ? <TrendingUp className="w-3 h-3 mr-1" /> : <TrendingDown className="w-3 h-3 mr-1" />}
              <span>{Math.abs(trend.value)}%</span>
            </div>
          )}
        </div>
        <div className="ml-4">
          <div className="w-14 h-14 bg-white/20 rounded-xl flex items-center justify-center shadow-lg">
            <Icon className="w-7 h-7 text-white" />
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6 gap-4 mb-6">
      {/* Total Orders */}
      <StatCard
        title={language === 'ar' ? 'إجمالي الطلبات' : 'Total Orders'}
        value={stats.total}
        icon={Package}
        color="from-blue-600/40 to-blue-700/30 border-blue-400"
        subtitle={language === 'ar' ? 'جميع الطلبات' : 'All orders'}
      />

      {/* Completed Orders */}
      <StatCard
        title={language === 'ar' ? 'طلبات مكتملة' : 'Completed Orders'}
        value={stats.completed}
        icon={CheckCircle}
        color="from-emerald-600/40 to-emerald-700/30 border-emerald-400"
        subtitle={`${stats.completionRate.toFixed(1)}% ${language === 'ar' ? 'معدل الإنجاز' : 'completion rate'}`}
      />

      {/* Pending Orders */}
      <StatCard
        title={language === 'ar' ? 'طلبات معلقة' : 'Pending Orders'}
        value={stats.pending}
        icon={Clock}
        color="from-amber-600/40 to-amber-700/30 border-amber-400"
        subtitle={language === 'ar' ? 'تحتاج معالجة' : 'Need processing'}
      />

      {/* Total Revenue */}
      <StatCard
        title={language === 'ar' ? 'الإيرادات المحققة' : 'Confirmed Revenue'}
        value={`$${stats.totalRevenue.toFixed(2)}`}
        icon={DollarSign}
        color="from-purple-600/40 to-purple-700/30 border-purple-400"
        subtitle={language === 'ar' ? 'من الطلبات المكتملة' : 'From completed orders'}
      />

      {/* Active Orders */}
      <StatCard
        title={language === 'ar' ? 'طلبات نشطة' : 'Active Orders'}
        value={stats.active}
        icon={RefreshCw}
        color="from-blue-600/40 to-blue-700/30 border-blue-400"
        subtitle={language === 'ar' ? 'قيد التنفيذ والاختبار' : 'In progress & testing'}
      />

      {/* Premium Orders */}
      <StatCard
        title={language === 'ar' ? 'طلبات مميزة' : 'Premium Orders'}
        value={stats.premium}
        icon={Crown}
        color="from-yellow-600/40 to-yellow-700/30 border-yellow-400"
        subtitle={`$${stats.premiumRevenue.toFixed(0)} ${language === 'ar' ? 'إيرادات' : 'revenue'}`}
      />

      {/* Subscription Orders */}
      <StatCard
        title={language === 'ar' ? 'طلبات اشتراك' : 'Subscription Orders'}
        value={stats.subscription}
        icon={Calendar}
        color="from-purple-600/40 to-purple-700/30 border-purple-400"
        subtitle={`${stats.subscriptionCustomers} ${language === 'ar' ? 'عملاء' : 'customers'}`}
      />

      {/* Urgent Orders */}
      <StatCard
        title={language === 'ar' ? 'طلبات عاجلة' : 'Urgent Orders'}
        value={stats.urgent}
        icon={AlertCircle}
        color="from-red-600/40 to-red-700/30 border-red-400"
        subtitle={language === 'ar' ? 'تحتاج اهتمام فوري' : 'Need immediate attention'}
      />

      {/* Total Revenue */}
      <StatCard
        title={language === 'ar' ? 'الإيرادات المحققة' : 'Total Revenue'}
        value={`$${stats.totalRevenue.toFixed(2)}`}
        icon={DollarSign}
        color="from-green-600/40 to-green-700/30 border-green-400"
        subtitle={`$${stats.averageOrderValue.toFixed(0)} ${language === 'ar' ? 'متوسط الطلب' : 'avg order'}`}
      />

      {/* Pending Revenue */}
      <StatCard
        title={language === 'ar' ? 'إيرادات معلقة' : 'Pending Revenue'}
        value={`$${stats.pendingRevenue.toFixed(2)}`}
        icon={Clock}
        color="from-orange-600/40 to-orange-700/30 border-orange-400"
        subtitle={language === 'ar' ? 'من الطلبات المعلقة' : 'From pending orders'}
      />

      {/* Average Order Value */}
      <StatCard
        title={language === 'ar' ? 'متوسط قيمة الطلب' : 'Average Order Value'}
        value={`$${stats.averageOrderValue.toFixed(2)}`}
        icon={TrendingUp}
        color="from-teal-500/20 to-teal-600/10 border-teal-500"
        subtitle={language === 'ar' ? 'للطلبات المكتملة' : 'For completed orders'}
      />

      {/* Unique Customers */}
      <StatCard
        title={language === 'ar' ? 'عملاء فريدون' : 'Unique Customers'}
        value={stats.uniqueCustomers}
        icon={Users}
        color="from-indigo-500/20 to-indigo-600/10 border-indigo-500"
        subtitle={language === 'ar' ? 'عملاء مختلفون' : 'Different customers'}
      />

      {/* This Month Orders */}
      <StatCard
        title={language === 'ar' ? 'طلبات هذا الشهر' : 'This Month Orders'}
        value={stats.thisMonthOrders}
        icon={Calendar}
        color="from-pink-500/20 to-pink-600/10 border-pink-500"
        subtitle={`$${stats.thisMonthRevenue.toFixed(2)} ${language === 'ar' ? 'إيرادات' : 'revenue'}`}
      />

      {/* Processing Orders */}
      <StatCard
        title={language === 'ar' ? 'قيد المعالجة' : 'Processing Orders'}
        value={stats.processing}
        icon={Package}
        color="from-cyan-500/20 to-cyan-600/10 border-cyan-500"
        subtitle={language === 'ar' ? 'قيد التنفيذ' : 'In progress'}
      />

      {/* Cancelled Orders */}
      <StatCard
        title={language === 'ar' ? 'طلبات ملغية' : 'Cancelled Orders'}
        value={stats.cancelled}
        icon={XCircle}
        color="from-red-500/20 to-red-600/10 border-red-500"
        subtitle={language === 'ar' ? 'تم إلغاؤها' : 'Were cancelled'}
      />
    </div>
  );
};

export default OrderStatistics;
