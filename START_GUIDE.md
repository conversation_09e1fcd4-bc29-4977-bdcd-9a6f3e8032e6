# 🚀 دليل التشغيل السريع - Khanfashariya

## 📋 طرق التشغيل المتاحة

### 🎯 الطريقة الأسرع (مستحسنة)
```bash
# تشغيل سريع بملف bat
quick-start.bat
```

### 🔧 طرق أخرى:

#### 1. باستخدام PowerShell (الأكثر تفصيلاً)
```powershell
# تشغيل PowerShell كمدير
powershell -ExecutionPolicy Bypass -File start-services.ps1
```

#### 2. باستخدام npm
```bash
# تشغيل الخوادم يدوياً ثم الحصول على الروابط
npm run dev:full
npm run start:all
```

#### 3. التشغيل اليدوي الكامل
```bash
# 1. تشغيل Backend
npm run start

# 2. تشغيل Frontend (في نافذة جديدة)
npm run dev

# 3. تشغيل ngrok للBackend (في نافذة جديدة)
ngrok http 3001

# 4. تشغيل ngrok للFrontend (في نافذة جديدة)
ngrok http 5173 --web-addr=localhost:4041

# 5. الحصول على الروابط
node scripts/get-ngrok-urls.js
```

---

## 📁 الملفات المنشأة بعد التشغيل

### 📄 current-ngrok-urls.json
يحتوي على الروابط الحالية:
```json
{
  "timestamp": "2025-07-22T...",
  "backend": {
    "url": "https://xxxxx.ngrok-free.app",
    "local": "http://localhost:3001"
  },
  "frontend": {
    "url": "https://yyyyy.ngrok-free.app", 
    "local": "http://localhost:5173"
  },
  "testsprite_endpoints": {
    "health": "https://xxxxx.ngrok-free.app/health",
    "admin_login": "https://xxxxx.ngrok-free.app/api/auth/login",
    ...
  }
}
```

### 📄 testsprite-urls.json (محدث تلقائياً)
ملف تكوين TestSprite محدث بالروابط الجديدة

---

## 🔍 التحقق من النجاح

### ✅ علامات النجاح:
1. **4 نوافذ مفتوحة**:
   - 🔧 Backend Server (port 3001)
   - 🎨 Frontend Server (port 5173)  
   - 🌐 Backend ngrok
   - 🌐 Frontend ngrok

2. **ملف current-ngrok-urls.json موجود** مع روابط صحيحة

3. **اختبار الروابط**:
   ```bash
   # اختبار Backend
   curl https://xxxxx.ngrok-free.app/health
   
   # اختبار Frontend
   # افتح الرابط في المتصفح
   ```

---

## ⚠️ استكشاف الأخطاء

### ❌ المشاكل الشائعة:

#### 1. "ngrok غير مثبت"
```bash
npm install -g ngrok
# أو
choco install ngrok  # إذا كان لديك Chocolatey
```

#### 2. "المنفذ مستخدم"
```bash
# إيقاف العمليات
taskkill /f /im node.exe
taskkill /f /im ngrok.exe
```

#### 3. "الخادم لا يستجيب"
- انتظر 10-15 ثانية إضافية
- تحقق من نوافذ الخوادم للأخطاء
- أعد تشغيل السكريبت

#### 4. "ngrok لا يعطي روابط"
- تحقق من اتصال الإنترنت
- أعد تشغيل ngrok يدوياً
- تحقق من حساب ngrok

---

## 🎯 للاستخدام مع TestSprite

بعد التشغيل الناجح:

1. **افتح ملف** `current-ngrok-urls.json`
2. **انسخ الروابط**:
   - Frontend: للواجهة
   - Backend: للـ API
3. **استخدم الروابط في TestSprite**
4. **ابدأ الاختبارات**

---

## 📞 الدعم

إذا واجهت مشاكل:
1. تحقق من الملفات المنشأة
2. راجع نوافذ الخوادم للأخطاء  
3. أعد تشغيل السكريبت
4. استخدم التشغيل اليدوي كبديل

---

## 🚀 تشغيل سريع - خطوة واحدة!

```bash
# فقط شغل هذا الملف
quick-start.bat
```

**وانتظر حتى ترى "✅ تم التشغيل بنجاح!"**
