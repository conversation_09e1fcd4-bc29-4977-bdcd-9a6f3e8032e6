const axios = require('axios');

async function testAdminEndpoints() {
  console.log('🔍 اختبار endpoints الإدارة بالتفصيل\n');
  
  try {
    // تسجيل الدخول كإداري
    console.log('1️⃣ تسجيل الدخول كإداري...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.data.tokens.accessToken;
    const headers = { Authorization: `Bearer ${token}` };
    console.log('   ✅ تم تسجيل الدخول بنجاح');
    
    // اختبار admin systems endpoint
    console.log('\n2️⃣ اختبار admin systems endpoint...');
    try {
      const adminSystemsResponse = await axios.get('http://localhost:3001/api/systems/admin', { headers });
      console.log('   ✅ Admin systems endpoint يعمل');
      console.log('   📊 عدد الأنظمة:', adminSystemsResponse.data.length);
      console.log('   📋 نوع البيانات:', Array.isArray(adminSystemsResponse.data) ? 'Array' : typeof adminSystemsResponse.data);
      
      if (adminSystemsResponse.data.length > 0) {
        const firstSystem = adminSystemsResponse.data[0];
        console.log('   🔍 النظام الأول:', firstSystem.name_ar);
        console.log('   🔍 الحقول المتاحة:', Object.keys(firstSystem));
      }
    } catch (adminSystemsError) {
      console.error('   ❌ خطأ في admin systems:', adminSystemsError.response?.status, adminSystemsError.response?.data);
    }
    
    // اختبار admin services endpoint
    console.log('\n3️⃣ اختبار admin services endpoint...');
    try {
      const adminServicesResponse = await axios.get('http://localhost:3001/api/services/admin/technical', { headers });
      console.log('   ✅ Admin services endpoint يعمل');
      console.log('   📊 عدد الخدمات:', adminServicesResponse.data.length);
      console.log('   📋 نوع البيانات:', Array.isArray(adminServicesResponse.data) ? 'Array' : typeof adminServicesResponse.data);
      
      if (adminServicesResponse.data.length > 0) {
        const firstService = adminServicesResponse.data[0];
        console.log('   🔍 الخدمة الأولى:', firstService.name_ar);
        console.log('   🔍 الحقول المتاحة:', Object.keys(firstService));
      }
    } catch (adminServicesError) {
      console.error('   ❌ خطأ في admin services:', adminServicesError.response?.status, adminServicesError.response?.data);
    }
    
    // مقارنة مع public endpoints
    console.log('\n4️⃣ مقارنة مع public endpoints...');
    
    const publicSystemsResponse = await axios.get('http://localhost:3001/api/systems');
    const publicServicesResponse = await axios.get('http://localhost:3001/api/services/technical');
    
    console.log('   📊 Public systems:', publicSystemsResponse.data.data.systems.length);
    console.log('   📊 Public services:', publicServicesResponse.data.data.services.length);
    
    console.log('\n5️⃣ تحليل هيكل البيانات...');
    console.log('   🔍 Public systems structure:', Object.keys(publicSystemsResponse.data));
    console.log('   🔍 Public services structure:', Object.keys(publicServicesResponse.data));
    
    // اختبار مع مستخدم عادي
    console.log('\n6️⃣ اختبار مع مستخدم عادي...');
    try {
      const userLoginResponse = await axios.post('http://localhost:3001/api/auth/login', {
        email: '<EMAIL>',
        password: '123456'
      });
      
      const userToken = userLoginResponse.data.data.tokens.accessToken;
      const userHeaders = { Authorization: `Bearer ${userToken}` };
      
      const userSystemsResponse = await axios.get('http://localhost:3001/api/systems', { userHeaders });
      const userServicesResponse = await axios.get('http://localhost:3001/api/services/technical', { userHeaders });
      
      console.log('   📊 User systems:', userSystemsResponse.data.data.systems.length);
      console.log('   📊 User services:', userServicesResponse.data.data.services.length);
      
    } catch (userError) {
      console.error('   ❌ خطأ في اختبار المستخدم العادي:', userError.message);
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

testAdminEndpoints();
