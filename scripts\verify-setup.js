#!/usr/bin/env node

/**
 * سكريبت للتحقق من جاهزية المشروع لـ TestSprite
 */

const { exec } = require('child_process');
const fs = require('fs');

console.log('🔍 فحص جاهزية المشروع لـ TestSprite');
console.log('=====================================');

const checks = [
  {
    name: 'قاعدة البيانات MySQL',
    test: () => checkDatabase(),
    required: true
  },
  {
    name: 'Backend Server (Port 3001)',
    test: () => checkBackend(),
    required: true
  },
  {
    name: 'Frontend Server (Port 5173)',
    test: () => checkFrontend(),
    required: true
  },
  {
    name: 'ngrok متاح',
    test: () => checkNgrok(),
    required: true
  },
  {
    name: 'ملفات المشروع',
    test: () => checkFiles(),
    required: true
  }
];

function checkDatabase() {
  return new Promise((resolve) => {
    exec('curl -s http://localhost:3001/health', (error, stdout) => {
      if (error) {
        resolve({ success: false, message: 'Backend غير متاح - تأكد من تشغيل المشروع' });
      } else {
        try {
          const response = JSON.parse(stdout);
          if (response.database === 'connected') {
            resolve({ success: true, message: 'قاعدة البيانات متصلة' });
          } else {
            resolve({ success: false, message: 'قاعدة البيانات غير متصلة' });
          }
        } catch {
          resolve({ success: false, message: 'خطأ في الاستجابة' });
        }
      }
    });
  });
}

function checkBackend() {
  return new Promise((resolve) => {
    exec('curl -s http://localhost:3001/api/status', (error, stdout) => {
      if (error) {
        resolve({ success: false, message: 'Backend غير يعمل على المنفذ 3001' });
      } else {
        resolve({ success: true, message: 'Backend يعمل بشكل صحيح' });
      }
    });
  });
}

function checkFrontend() {
  return new Promise((resolve) => {
    exec('curl -s -o /dev/null -w "%{http_code}" http://localhost:5173', (error, stdout) => {
      if (error || stdout !== '200') {
        resolve({ success: false, message: 'Frontend غير يعمل على المنفذ 5173' });
      } else {
        resolve({ success: true, message: 'Frontend يعمل بشكل صحيح' });
      }
    });
  });
}

function checkNgrok() {
  return new Promise((resolve) => {
    exec('ngrok version', (error) => {
      if (error) {
        resolve({ success: false, message: 'ngrok غير مثبت - قم بتثبيته: npm install -g ngrok' });
      } else {
        resolve({ success: true, message: 'ngrok متاح ومثبت' });
      }
    });
  });
}

function checkFiles() {
  return new Promise((resolve) => {
    const requiredFiles = [
      'package.json',
      'server/server.js',
      '.env',
      'api-documentation.json'
    ];
    
    const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
    
    if (missingFiles.length > 0) {
      resolve({ 
        success: false, 
        message: `ملفات مفقودة: ${missingFiles.join(', ')}` 
      });
    } else {
      resolve({ success: true, message: 'جميع الملفات المطلوبة موجودة' });
    }
  });
}

async function runChecks() {
  console.log('⏳ جاري الفحص...\n');
  
  let allPassed = true;
  
  for (const check of checks) {
    process.stdout.write(`🔍 ${check.name}... `);
    
    try {
      const result = await check.test();
      
      if (result.success) {
        console.log(`✅ ${result.message}`);
      } else {
        console.log(`❌ ${result.message}`);
        if (check.required) {
          allPassed = false;
        }
      }
    } catch (error) {
      console.log(`❌ خطأ: ${error.message}`);
      if (check.required) {
        allPassed = false;
      }
    }
  }
  
  console.log('\n' + '='.repeat(50));
  
  if (allPassed) {
    console.log('🎉 المشروع جاهز لـ TestSprite!');
    console.log('');
    console.log('📋 الخطوات التالية:');
    console.log('1. شغل: npm run testsprite:auto');
    console.log('2. انسخ البيانات إلى TestSprite');
    console.log('3. ابدأ الاختبار');
  } else {
    console.log('❌ المشروع غير جاهز');
    console.log('');
    console.log('🔧 اصلح المشاكل أعلاه ثم أعد المحاولة');
    console.log('💡 نصائح:');
    console.log('   - تأكد من تشغيل: npm run dev:full');
    console.log('   - تأكد من عمل WAMP/XAMPP');
    console.log('   - ثبت ngrok: npm install -g ngrok');
  }
}

runChecks().catch(console.error);