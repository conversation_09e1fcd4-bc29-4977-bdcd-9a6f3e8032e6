-- Enhanced Orders Management Schema
-- Comprehensive order system supporting complex Premium Edition orders with add-ons

-- Drop existing orders table to recreate with enhanced structure
DROP TABLE IF EXISTS `orders`;

-- Enhanced Orders Table
CREATE TABLE `orders` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `order_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  
  -- Order Classification
  `order_type` enum('system_service','technical_service','premium_content','premium_package','custom_request') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `order_category` enum('standard','premium_base','premium_custom','subscription','maintenance') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'standard',
  
  -- Item Information
  `item_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_name_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_name_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  
  -- Pricing Structure
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `base_price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `addons_price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `subscription_price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `maintenance_price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `total_price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `final_price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `currency` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'USD',
  
  -- Order Status Management
  `status` enum('pending','confirmed','in_progress','testing','completed','cancelled','refunded','on_hold','expired') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `priority` enum('low','medium','high','urgent') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'medium',
  `progress_percentage` int(3) DEFAULT 0,
  
  -- Payment Information
  `payment_status` enum('pending','paid','partial','failed','refunded','disputed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `payment_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `payment_reference` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `payment_gateway` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  
  -- Subscription Management
  `subscription_type` enum('none','monthly','quarterly','yearly','lifetime') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'none',
  `subscription_duration` int(11) NULL COMMENT 'Duration in months, NULL for lifetime',
  `subscription_start_date` timestamp NULL,
  `subscription_end_date` timestamp NULL,
  `auto_renewal` tinyint(1) DEFAULT 0,
  
  -- Service Details
  `maintenance_included` tinyint(1) DEFAULT 0,
  `installation_included` tinyint(1) DEFAULT 0,
  `support_level` enum('basic','standard','premium','enterprise') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'basic',
  
  -- Complex Order Details (JSON)
  `order_details` json NULL COMMENT 'Complex order structure with add-ons, customizations, etc.',
  `selected_addons` json NULL COMMENT 'Selected systems and services as add-ons',
  `customization_options` json NULL COMMENT 'Custom configurations and options',
  
  -- Timeline Management
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `confirmed_at` timestamp NULL,
  `started_at` timestamp NULL,
  `estimated_completion` timestamp NULL,
  `completed_at` timestamp NULL,
  `delivery_date` timestamp NULL,
  
  -- Notes and Communication
  `notes_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `notes_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `admin_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `customer_requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  
  -- File Attachments
  `attached_files` json NULL COMMENT 'File attachments and documents',
  
  -- Audit Trail
  `created_by` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `last_modified_by` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_number` (`order_number`) USING BTREE,
  INDEX `user_id` (`user_id`) USING BTREE,
  INDEX `status` (`status`) USING BTREE,
  INDEX `order_type` (`order_type`) USING BTREE,
  INDEX `payment_status` (`payment_status`) USING BTREE,
  INDEX `subscription_type` (`subscription_type`) USING BTREE,
  INDEX `created_at` (`created_at`) USING BTREE,
  INDEX `estimated_completion` (`estimated_completion`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- Order Status History Table
CREATE TABLE `order_status_history` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `order_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `old_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `new_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `changed_by` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `change_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id` (`order_id`) USING BTREE,
  INDEX `created_at` (`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- Order Items Table (for complex orders with multiple items)
CREATE TABLE `order_items` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `order_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_type` enum('base_product','addon_system','addon_service','customization','maintenance') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_name_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_name_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `total_price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `item_details` json NULL COMMENT 'Specific item configuration and options',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id` (`order_id`) USING BTREE,
  INDEX `item_type` (`item_type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- Subscription Management Table
CREATE TABLE `subscriptions` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `order_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `subscription_type` enum('monthly','quarterly','yearly','lifetime') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('active','paused','cancelled','expired','pending') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `start_date` timestamp NOT NULL,
  `end_date` timestamp NULL,
  `next_billing_date` timestamp NULL,
  `billing_amount` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `auto_renewal` tinyint(1) DEFAULT 1,
  `payment_method_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id` (`order_id`) USING BTREE,
  INDEX `user_id` (`user_id`) USING BTREE,
  INDEX `status` (`status`) USING BTREE,
  INDEX `next_billing_date` (`next_billing_date`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- Order Communication Log
CREATE TABLE `order_communications` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `order_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `sender_type` enum('admin','customer','system') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `sender_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `message_type` enum('note','status_update','payment_update','delivery_update','custom') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject_ar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `subject_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `message_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `message_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `attachments` json NULL,
  `is_internal` tinyint(1) DEFAULT 0 COMMENT 'Internal admin notes not visible to customer',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id` (`order_id`) USING BTREE,
  INDEX `sender_type` (`sender_type`) USING BTREE,
  INDEX `created_at` (`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;
