/**
 * User Management Routes
 * 
 * Handles user-related operations:
 * - User profile management
 * - User orders and subscriptions
 * - User messages and notifications
 * - User preferences and settings
 */

const express = require('express');
const { executeQuery, generateUUID } = require('../config/database');
const { verifyToken, requireOwnerOrAdmin } = require('../middleware/auth');
const { 
  asyncHandler, 
  validationError, 
  notFoundError,
  forbiddenError 
} = require('../middleware/errorHandler');
const { logUserAction } = require('../middleware/logger');

const router = express.Router();

/**
 * @route   GET /api/users/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/profile', verifyToken, asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // Get user profile with additional stats
  const { rows: users } = await executeQuery(`
    SELECT 
      u.*,
      COUNT(DISTINCT o.id) as total_orders,
      COUNT(DISTINCT s.id) as active_subscriptions,
      COUNT(DISTINCT im.id) as unread_messages
    FROM users u
    LEFT JOIN orders o ON u.id = o.user_id
    LEFT JOIN subscriptions s ON u.id = s.user_id AND s.status = 'active'
    LEFT JOIN inbox_messages im ON u.id = im.user_id AND im.is_read = false
    WHERE u.id = ?
    GROUP BY u.id
  `, [userId]);
  
  if (users.length === 0) {
    throw notFoundError('User not found');
  }
  
  const user = users[0];
  
  // Remove sensitive data
  delete user.password_hash;
  
  res.json({
    success: true,
    data: {
      user: {
        ...user,
        stats: {
          total_orders: user.total_orders || 0,
          active_subscriptions: user.active_subscriptions || 0,
          unread_messages: user.unread_messages || 0
        }
      }
    }
  });
}));

/**
 * @route   PUT /api/users/profile
 * @desc    Update user profile
 * @access  Private
 */
router.put('/profile', verifyToken, asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { full_name, phone, avatar_url } = req.body;
  
  // Validation
  if (!full_name || full_name.trim().length < 2) {
    throw validationError('Full name must be at least 2 characters long');
  }
  
  if (phone && !/^[\+]?[0-9\s\-\(\)]{10,20}$/.test(phone)) {
    throw validationError('Invalid phone number format');
  }
  
  if (avatar_url && !/^https?:\/\/.+/.test(avatar_url)) {
    throw validationError('Avatar URL must be a valid HTTP/HTTPS URL');
  }
  
  // Update user profile
  await executeQuery(
    'UPDATE users SET full_name = ?, phone = ?, avatar_url = ?, updated_at = NOW() WHERE id = ?',
    [full_name.trim(), phone || null, avatar_url || null, userId]
  );
  
  // Get updated user data
  const { rows: users } = await executeQuery(
    'SELECT id, email, username, full_name, phone, avatar_url, role, status, created_at, updated_at FROM users WHERE id = ?',
    [userId]
  );
  
  // Log user action
  await logUserAction('profile_updated', 'user', userId, {
    updated_fields: { full_name, phone, avatar_url }
  }, req);
  
  res.json({
    success: true,
    message: 'Profile updated successfully',
    data: {
      user: users[0]
    }
  });
}));

/**
 * @route   GET /api/users/:userId/orders
 * @desc    Get user orders
 * @access  Private (owner or admin)
 */
router.get('/:userId/orders', verifyToken, requireOwnerOrAdmin('userId'), asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { page = 1, limit = 10, status, order_type } = req.query;
  
  const offset = (page - 1) * limit;
  
  // Build query conditions
  let whereConditions = ['o.user_id = ?'];
  let queryParams = [userId];
  
  if (status) {
    whereConditions.push('o.status = ?');
    queryParams.push(status);
  }
  
  if (order_type) {
    whereConditions.push('o.order_type = ?');
    queryParams.push(order_type);
  }
  
  const whereClause = whereConditions.join(' AND ');
  
  // Get orders with pagination
  const { rows: orders } = await executeQuery(`
    SELECT 
      o.*,
      u.username,
      u.email
    FROM orders o
    JOIN users u ON o.user_id = u.id
    WHERE ${whereClause}
    ORDER BY o.created_at DESC
    LIMIT ? OFFSET ?
  `, [...queryParams, parseInt(limit), offset]);
  
  // Get total count
  const { rows: countResult } = await executeQuery(`
    SELECT COUNT(*) as total
    FROM orders o
    WHERE ${whereClause}
  `, queryParams);
  
  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.json({
    success: true,
    data: {
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @route   GET /api/users/:userId/subscriptions
 * @desc    Get user subscriptions
 * @access  Private (owner or admin)
 */
router.get('/:userId/subscriptions', verifyToken, requireOwnerOrAdmin('userId'), asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { status = 'active' } = req.query;
  
  // Get subscriptions (using premium_content as packages)
  const { rows: subscriptions } = await executeQuery(`
    SELECT
      s.*,
      p.title_ar as name_ar,
      p.title_en as name_en,
      p.description_ar,
      p.description_en,
      p.price,
      'premium' as support_level
    FROM subscriptions s
    JOIN premium_content p ON s.package_id = p.id
    WHERE s.user_id = ? AND s.status = ?
    ORDER BY s.created_at DESC
  `, [userId, status]);
  
  res.json({
    success: true,
    data: {
      subscriptions
    }
  });
}));

/**
 * @route   GET /api/users/:userId/messages
 * @desc    Get user inbox messages
 * @access  Private (owner or admin)
 */
router.get('/:userId/messages', verifyToken, requireOwnerOrAdmin('userId'), asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { page = 1, limit = 20, is_read, message_type } = req.query;
  
  const offset = (page - 1) * limit;
  
  // Build query conditions
  let whereConditions = ['user_id = ?'];
  let queryParams = [userId];
  
  if (is_read !== undefined) {
    whereConditions.push('is_read = ?');
    queryParams.push(is_read === 'true');
  }
  
  if (message_type) {
    whereConditions.push('message_type = ?');
    queryParams.push(message_type);
  }
  
  // Only show non-expired messages
  whereConditions.push('(expires_at IS NULL OR expires_at > NOW())');
  
  const whereClause = whereConditions.join(' AND ');
  
  // Get messages
  const { rows: messages } = await executeQuery(`
    SELECT *
    FROM inbox_messages
    WHERE ${whereClause}
    ORDER BY created_at DESC
    LIMIT ? OFFSET ?
  `, [...queryParams, parseInt(limit), offset]);
  
  // Get total count
  const { rows: countResult } = await executeQuery(`
    SELECT COUNT(*) as total
    FROM inbox_messages
    WHERE ${whereClause}
  `, queryParams);
  
  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.json({
    success: true,
    data: {
      messages,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @route   PUT /api/users/:userId/messages/:messageId/read
 * @desc    Mark message as read
 * @access  Private (owner or admin)
 */
router.put('/:userId/messages/:messageId/read', verifyToken, requireOwnerOrAdmin('userId'), asyncHandler(async (req, res) => {
  const { userId, messageId } = req.params;
  
  // Check if message exists and belongs to user
  const { rows: messages } = await executeQuery(
    'SELECT id FROM inbox_messages WHERE id = ? AND user_id = ?',
    [messageId, userId]
  );
  
  if (messages.length === 0) {
    throw notFoundError('Message not found');
  }
  
  // Mark as read
  await executeQuery(
    'UPDATE inbox_messages SET is_read = true, read_at = NOW(), updated_at = NOW() WHERE id = ?',
    [messageId]
  );
  
  // Log user action
  await logUserAction('message_read', 'message', messageId, {
    userId
  }, req);
  
  res.json({
    success: true,
    message: 'Message marked as read'
  });
}));

/**
 * @route   GET /api/users/:userId/services
 * @desc    Get user purchased services
 * @access  Private (owner or admin)
 */
router.get('/:userId/services', verifyToken, requireOwnerOrAdmin('userId'), asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { status = 'active', service_type } = req.query;
  
  // Build query conditions
  let whereConditions = ['us.user_id = ? AND us.status = ?'];
  let queryParams = [userId, status];
  
  if (service_type) {
    whereConditions.push('us.service_type = ?');
    queryParams.push(service_type);
  }
  
  const whereClause = whereConditions.join(' AND ');
  
  // Get user services with details
  const { rows: services } = await executeQuery(`
    SELECT 
      us.*,
      CASE 
        WHEN us.service_type = 'system_service' THEN ss.name_ar
        WHEN us.service_type = 'technical_service' THEN ts.name_ar
        WHEN us.service_type = 'premium_content' THEN pc.title_ar
      END as name_ar,
      CASE 
        WHEN us.service_type = 'system_service' THEN ss.name_en
        WHEN us.service_type = 'technical_service' THEN ts.name_en
        WHEN us.service_type = 'premium_content' THEN pc.title_en
      END as name_en,
      CASE 
        WHEN us.service_type = 'system_service' THEN ss.price
        WHEN us.service_type = 'technical_service' THEN ts.price
        WHEN us.service_type = 'premium_content' THEN pc.price
      END as price
    FROM user_services us
    LEFT JOIN system_services ss ON us.service_id = ss.id AND us.service_type = 'system_service'
    LEFT JOIN technical_services ts ON us.service_id = ts.id AND us.service_type = 'technical_service'
    LEFT JOIN premium_content pc ON us.service_id = pc.id AND us.service_type = 'premium_content'
    WHERE ${whereClause}
    ORDER BY us.purchase_date DESC
  `, queryParams);
  
  res.json({
    success: true,
    data: {
      services
    }
  });
}));

/**
 * @route   GET /api/users/:userId/dashboard
 * @desc    Get user dashboard data
 * @access  Private (owner or admin)
 */
router.get('/:userId/dashboard', verifyToken, requireOwnerOrAdmin('userId'), asyncHandler(async (req, res) => {
  const { userId } = req.params;
  
  // Get dashboard statistics
  const [
    { rows: orderStats },
    { rows: subscriptionStats },
    { rows: messageStats },
    { rows: serviceStats },
    { rows: recentOrders },
    { rows: recentMessages }
  ] = await Promise.all([
    // Order statistics
    executeQuery(`
      SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
        COALESCE(SUM(final_price), 0) as total_spent
      FROM orders 
      WHERE user_id = ?
    `, [userId]),
    
    // Subscription statistics
    executeQuery(`
      SELECT 
        COUNT(*) as total_subscriptions,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_subscriptions,
        COUNT(CASE WHEN end_date < CURDATE() THEN 1 END) as expired_subscriptions
      FROM subscriptions 
      WHERE user_id = ?
    `, [userId]),
    
    // Message statistics
    executeQuery(`
      SELECT 
        COUNT(*) as total_messages,
        COUNT(CASE WHEN is_read = false THEN 1 END) as unread_messages
      FROM inbox_messages 
      WHERE user_id = ? AND (expires_at IS NULL OR expires_at > NOW())
    `, [userId]),
    
    // Service statistics
    executeQuery(`
      SELECT 
        COUNT(*) as total_services,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_services
      FROM user_services 
      WHERE user_id = ?
    `, [userId]),
    
    // Recent orders
    executeQuery(`
      SELECT id, order_number, order_type, item_name_ar, item_name_en, final_price, status, created_at
      FROM orders 
      WHERE user_id = ?
      ORDER BY created_at DESC 
      LIMIT 5
    `, [userId]),
    
    // Recent messages
    executeQuery(`
      SELECT id, subject_ar, subject_en, message_type, priority, is_read, created_at
      FROM inbox_messages 
      WHERE user_id = ? AND (expires_at IS NULL OR expires_at > NOW())
      ORDER BY created_at DESC 
      LIMIT 5
    `, [userId])
  ]);
  
  res.json({
    success: true,
    data: {
      stats: {
        orders: orderStats[0],
        subscriptions: subscriptionStats[0],
        messages: messageStats[0],
        services: serviceStats[0]
      },
      recent: {
        orders: recentOrders,
        messages: recentMessages
      }
    }
  });
}));

module.exports = router;
