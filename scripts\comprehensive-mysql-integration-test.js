const axios = require('axios');
const puppeteer = require('puppeteer');

async function comprehensiveTest() {
  console.log('🚀 COMPREHENSIVE MySQL INTEGRATION TEST\n');
  console.log('=' .repeat(60));
  
  let totalTests = 0;
  let passedTests = 0;
  
  function test(name, condition) {
    totalTests++;
    if (condition) {
      console.log(`✅ ${name}`);
      passedTests++;
    } else {
      console.log(`❌ ${name}`);
    }
  }
  
  try {
    // 1. API ENDPOINTS TESTING
    console.log('\n📡 API ENDPOINTS TESTING');
    console.log('-'.repeat(40));
    
    // Test Systems API
    const systemsResponse = await axios.get('http://localhost:3001/api/systems');
    test('Systems API responds', systemsResponse.status === 200);
    test('Systems API returns data', systemsResponse.data.success && systemsResponse.data.data.systems.length > 0);
    
    const firstSystem = systemsResponse.data.data.systems[0];
    test('Systems have required fields', firstSystem.name_ar && firstSystem.name_en && firstSystem.price);
    test('Systems have tech_specs fields', firstSystem.tech_specs_ar !== undefined && firstSystem.tech_specs_en !== undefined);
    test('Systems features are arrays', Array.isArray(firstSystem.features_ar) && Array.isArray(firstSystem.features_en));
    
    // Test Technical Services API
    const servicesResponse = await axios.get('http://localhost:3001/api/services/technical');
    test('Services API responds', servicesResponse.status === 200);
    test('Services API returns data', servicesResponse.data.success && servicesResponse.data.data.services.length > 0);
    
    const firstService = servicesResponse.data.data.services[0];
    test('Services have required fields', firstService.name_ar && firstService.name_en && firstService.price);
    test('Services have premium fields', firstService.is_premium_addon !== undefined && firstService.premium_price !== undefined);
    test('Services have subscription type', firstService.subscription_type !== undefined);
    
    // Test Premium Content API
    const premiumResponse = await axios.get('http://localhost:3001/api/services/premium');
    test('Premium API responds', premiumResponse.status === 200);
    test('Premium API returns data', premiumResponse.data.success && premiumResponse.data.data.premiumContent.length > 0);
    
    // 2. AUTHENTICATION TESTING
    console.log('\n🔐 AUTHENTICATION TESTING');
    console.log('-'.repeat(40));
    
    // Test Admin Login
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    test('Admin login works', loginResponse.status === 200 && loginResponse.data.data.tokens.accessToken);
    
    const token = loginResponse.data.data.tokens.accessToken;
    const headers = { Authorization: `Bearer ${token}` };
    
    // 3. ADMIN PANEL TESTING
    console.log('\n👑 ADMIN PANEL TESTING');
    console.log('-'.repeat(40));
    
    // Test Admin Systems
    const adminSystemsResponse = await axios.get('http://localhost:3001/api/systems/admin', { headers });
    test('Admin systems endpoint works', adminSystemsResponse.status === 200 && adminSystemsResponse.data.length > 0);
    
    const adminFirstSystem = adminSystemsResponse.data[0];
    test('Admin systems have all fields', adminFirstSystem.tech_specs_ar !== undefined && Array.isArray(adminFirstSystem.features_ar));
    
    // Test Admin Services
    const adminServicesResponse = await axios.get('http://localhost:3001/api/services/admin/technical', { headers });
    test('Admin services endpoint works', adminServicesResponse.status === 200 && adminServicesResponse.data.length > 0);
    
    const adminFirstService = adminServicesResponse.data[0];
    test('Admin services have premium fields', adminFirstService.is_premium_addon !== undefined && adminFirstService.premium_price !== undefined);
    
    // Test Dashboard
    const dashboardResponse = await axios.get('http://localhost:3001/api/admin/dashboard', { headers });
    test('Admin dashboard works', dashboardResponse.status === 200);
    
    // Test Users Management
    const usersResponse = await axios.get('http://localhost:3001/api/admin/users', { headers });
    test('Users management works', usersResponse.status === 200);
    
    // Test Orders Management
    const ordersResponse = await axios.get('http://localhost:3001/api/orders', { headers });
    test('Orders management works', ordersResponse.status === 200);
    
    // 4. FRONTEND TESTING
    console.log('\n🌐 FRONTEND TESTING');
    console.log('-'.repeat(40));
    
    const browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1080 });
    
    // Test Homepage
    await page.goto('http://localhost:5173', { waitUntil: 'networkidle2', timeout: 30000 });
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const systemsSection = await page.$('#systems');
    test('Homepage systems section exists', !!systemsSection);
    
    const systemCards = await page.$$('#systems .group, #systems [class*="card"]');
    test('Homepage displays system cards', systemCards.length > 0);
    
    const servicesSection = await page.$('#services');
    test('Homepage services section exists', !!servicesSection);
    
    const serviceCards = await page.$$('#services .group, #services [class*="card"]');
    test('Homepage displays service cards', serviceCards.length > 0);
    
    await browser.close();
    
    // 5. ORDER SYSTEM TESTING
    console.log('\n📦 ORDER SYSTEM TESTING');
    console.log('-'.repeat(40));
    
    // Test Order Creation
    const orderData = {
      order_type: 'system_service',
      item_id: firstSystem.id,
      item_name_ar: firstSystem.name_ar,
      item_name_en: firstSystem.name_en,
      final_price: firstSystem.price,
      quantity: 1,
      notes_ar: 'طلب تجريبي',
      notes_en: 'Test order'
    };
    
    const createOrderResponse = await axios.post('http://localhost:3001/api/orders', orderData, { headers });
    test('Order creation works', createOrderResponse.status === 201);
    test('Order returns order number', createOrderResponse.data.data.order_number);
    
  } catch (error) {
    console.error('\n❌ Test Error:', error.message);
  }
  
  // FINAL RESULTS
  console.log('\n' + '='.repeat(60));
  console.log('📊 FINAL TEST RESULTS');
  console.log('='.repeat(60));
  
  const successRate = Math.round((passedTests / totalTests) * 100);
  console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
  console.log(`📈 Success Rate: ${successRate}%`);
  
  if (successRate >= 90) {
    console.log('🎉 EXCELLENT! MySQL integration is working correctly!');
  } else if (successRate >= 75) {
    console.log('⚠️  GOOD! Most functionality is working, minor issues remain.');
  } else {
    console.log('❌ NEEDS WORK! Significant issues need to be addressed.');
  }
  
  console.log('\n🔗 Access URLs:');
  console.log('   🌐 Homepage: http://localhost:5173');
  console.log('   👑 Admin Panel: http://localhost:5173/admin');
  console.log('   🔐 Admin Login: <EMAIL> / admin123');
}

comprehensiveTest();
