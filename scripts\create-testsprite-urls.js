#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const fs = require('fs');

console.log('🎯 Creating correct URLs for TestSprite...\n');

async function createTestSpriteURLs() {
  try {
    // Step 1: Kill all ngrok
    console.log('🔪 Stopping existing ngrok processes...');
    await new Promise((resolve) => {
      exec('taskkill /F /IM ngrok.exe 2>nul || echo "No ngrok processes"', () => {
        setTimeout(resolve, 2000);
      });
    });

    // Step 2: Start frontend ngrok (port 5173)
    console.log('🚇 Starting Frontend ngrok (port 5173)...');
    const frontendProcess = spawn('npx', ['ngrok', 'http', '5173'], {
      stdio: ['ignore', 'pipe', 'pipe'],
      shell: true
    });

    let frontendURL = null;
    await new Promise((resolve) => {
      let resolved = false;
      
      const handleOutput = (data) => {
        const output = data.toString();
        const match = output.match(/https:\/\/[a-z0-9]+\.ngrok-free\.app/);
        if (match && !resolved) {
          frontendURL = match[0];
          console.log(`✅ Frontend URL: ${frontendURL}`);
          resolved = true;
          resolve();
        }
      };

      frontendProcess.stdout.on('data', handleOutput);
      frontendProcess.stderr.on('data', handleOutput);

      setTimeout(() => {
        if (!resolved) {
          console.log('⏰ Frontend timeout');
          resolved = true;
          resolve();
        }
      }, 15000);
    });

    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 3: Start backend ngrok (port 3001)
    console.log('🚇 Starting Backend ngrok (port 3001)...');
    const backendProcess = spawn('npx', ['ngrok', 'http', '3001'], {
      stdio: ['ignore', 'pipe', 'pipe'],
      shell: true
    });

    let backendURL = null;
    await new Promise((resolve) => {
      let resolved = false;
      
      const handleOutput = (data) => {
        const output = data.toString();
        const match = output.match(/https:\/\/[a-z0-9]+\.ngrok-free\.app/);
        if (match && !resolved) {
          backendURL = match[0];
          console.log(`✅ Backend URL: ${backendURL}`);
          resolved = true;
          resolve();
        }
      };

      backendProcess.stdout.on('data', handleOutput);
      backendProcess.stderr.on('data', handleOutput);

      setTimeout(() => {
        if (!resolved) {
          console.log('⏰ Backend timeout');
          resolved = true;
          resolve();
        }
      }, 15000);
    });

    // Step 4: Wait for ngrok API
    console.log('⏳ Waiting for ngrok API...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Step 5: Get tunnel info
    const tunnels = await new Promise((resolve) => {
      exec('curl -s http://127.0.0.1:4040/api/tunnels', (error, stdout) => {
        if (error) {
          resolve([]);
          return;
        }
        try {
          const data = JSON.parse(stdout);
          resolve(data.tunnels || []);
        } catch {
          resolve([]);
        }
      });
    });

    console.log('\n🔗 Active Tunnels:');
    tunnels.forEach(tunnel => {
      const type = tunnel.config.addr.includes('5173') ? '[FRONTEND]' : 
                   tunnel.config.addr.includes('3001') ? '[BACKEND]' : '[UNKNOWN]';
      console.log(`  ${type} ${tunnel.public_url}`);
    });

    // Step 6: Create TestSprite configuration
    const frontendTunnel = tunnels.find(t => t.config.addr.includes('5173'));
    const backendTunnel = tunnels.find(t => t.config.addr.includes('3001'));

    const finalFrontendURL = frontendTunnel ? frontendTunnel.public_url : frontendURL;
    const finalBackendURL = backendTunnel ? backendTunnel.public_url : backendURL;

    const testSpriteConfig = {
      name: "Khanfashariya - TestSprite Ready",
      timestamp: new Date().toISOString(),
      
      // URLs for TestSprite
      frontend_url: finalFrontendURL,
      backend_api_url: finalBackendURL,
      
      // TestSprite Configuration
      testsprite_settings: {
        base_url: finalBackendURL,
        headers: {
          "ngrok-skip-browser-warning": "true",
          "User-Agent": "TestSprite/1.0",
          "Content-Type": "application/json",
          "Accept": "application/json"
        }
      },
      
      // Test Endpoints
      endpoints: [
        {
          name: "Health Check",
          url: `${finalBackendURL}/health`,
          method: "GET",
          expected_status: 200
        },
        {
          name: "Admin Login",
          url: `${finalBackendURL}/api/auth/login`,
          method: "POST",
          body: {
            email: "<EMAIL>",
            password: "admin123"
          },
          expected_status: 200
        },
        {
          name: "Get Systems",
          url: `${finalBackendURL}/api/systems`,
          method: "GET",
          expected_status: 200
        },
        {
          name: "Get Technical Services",
          url: `${finalBackendURL}/api/services/technical`,
          method: "GET",
          expected_status: 200
        },
        {
          name: "Get Premium Services",
          url: `${finalBackendURL}/api/services/premium`,
          method: "GET",
          expected_status: 200
        }
      ],
      
      // Frontend Pages to Test
      frontend_pages: [
        `${finalFrontendURL}/`,
        `${finalFrontendURL}/login`,
        `${finalFrontendURL}/register`,
        `${finalFrontendURL}/services`,
        `${finalFrontendURL}/profile`,
        `${finalFrontendURL}/admin`
      ]
    };

    // Save configuration
    fs.writeFileSync('testsprite-urls.json', JSON.stringify(testSpriteConfig, null, 2));

    console.log('\n✅ TestSprite URLs created successfully!');
    console.log('\n📋 Configuration saved to: testsprite-urls.json');
    
    console.log('\n🎯 For TestSprite:');
    console.log(`Frontend URL: ${finalFrontendURL}`);
    console.log(`Backend API URL: ${finalBackendURL}`);
    
    console.log('\n📝 TestSprite Setup:');
    console.log('1. Use Backend URL as Base URL in TestSprite');
    console.log('2. Add headers: ngrok-skip-browser-warning: true');
    console.log('3. Test all endpoints listed above');
    console.log('4. Test frontend pages separately');

    console.log('\n⏳ Keeping tunnels alive... Press Ctrl+C to stop');

    // Keep alive
    setInterval(() => {
      const time = new Date().toLocaleTimeString();
      console.log(`✅ Tunnels active at ${time}`);
    }, 30000);

  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    process.exit(1);
  }
}

createTestSpriteURLs();
