#!/usr/bin/env node

const mysql = require('mysql2/promise');

async function fixJSONArabicData() {
  console.log('🔧 Fixing JSON Arabic Data Issues...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '123456',
      database: 'khan<PERSON><PERSON>riya_db'
    });

    console.log('✅ Connected to MySQL database\n');

    // Fix system_services table
    console.log('1️⃣ Fixing system_services table...');
    
    // Get all system services
    const [systems] = await connection.execute('SELECT * FROM system_services');
    
    for (const system of systems) {
      console.log(`   Fixing: ${system.name_ar}`);
      
      // Fix features_ar and features_en
      let features_ar = [];
      let features_en = [];
      
      try {
        if (system.features_ar) {
          if (typeof system.features_ar === 'string') {
            // Try to parse, if fails, split by comma
            try {
              features_ar = JSON.parse(system.features_ar);
            } catch {
              features_ar = system.features_ar.split(',').map(f => f.trim());
            }
          } else {
            features_ar = system.features_ar;
          }
        }
        
        if (system.features_en) {
          if (typeof system.features_en === 'string') {
            try {
              features_en = JSON.parse(system.features_en);
            } catch {
              features_en = system.features_en.split(',').map(f => f.trim());
            }
          } else {
            features_en = system.features_en;
          }
        }
      } catch (error) {
        console.log(`     Warning: Could not parse features for ${system.name_ar}`);
        features_ar = [];
        features_en = [];
      }
      
      // Update with properly formatted JSON
      await connection.execute(`
        UPDATE system_services 
        SET features_ar = ?, features_en = ?, tech_specs_ar = ?, tech_specs_en = ?, gallery_images = ?
        WHERE id = ?
      `, [
        JSON.stringify(features_ar),
        JSON.stringify(features_en),
        JSON.stringify([]), // Empty tech specs for now
        JSON.stringify([]), // Empty tech specs for now
        JSON.stringify([]), // Empty gallery for now
        system.id
      ]);
      
      console.log(`     ✅ Fixed: ${system.name_ar}`);
    }

    // Fix technical_services table
    console.log('\n2️⃣ Fixing technical_services table...');
    
    const [services] = await connection.execute('SELECT * FROM technical_services');
    
    for (const service of services) {
      console.log(`   Fixing: ${service.name_ar}`);
      
      let features_ar = [];
      let features_en = [];
      
      try {
        if (service.features_ar) {
          if (typeof service.features_ar === 'string') {
            try {
              features_ar = JSON.parse(service.features_ar);
            } catch {
              features_ar = service.features_ar.split(',').map(f => f.trim());
            }
          }
        }
        
        if (service.features_en) {
          if (typeof service.features_en === 'string') {
            try {
              features_en = JSON.parse(service.features_en);
            } catch {
              features_en = service.features_en.split(',').map(f => f.trim());
            }
          }
        }
      } catch (error) {
        console.log(`     Warning: Could not parse features for ${service.name_ar}`);
        features_ar = [];
        features_en = [];
      }
      
      await connection.execute(`
        UPDATE technical_services 
        SET features_ar = ?, features_en = ?, tech_specs_ar = ?, tech_specs_en = ?, gallery_images = ?
        WHERE id = ?
      `, [
        JSON.stringify(features_ar),
        JSON.stringify(features_en),
        JSON.stringify([]),
        JSON.stringify([]),
        JSON.stringify([]),
        service.id
      ]);
      
      console.log(`     ✅ Fixed: ${service.name_ar}`);
    }

    // Fix premium_content table
    console.log('\n3️⃣ Fixing premium_content table...');
    
    const [premium] = await connection.execute('SELECT * FROM premium_content');
    
    for (const item of premium) {
      console.log(`   Fixing: ${item.title_ar}`);
      
      let features_ar = [];
      let features_en = [];
      let included_systems = [];
      let included_services = [];
      let gallery_images = [];
      
      try {
        // Fix all JSON fields
        if (item.features_ar) {
          try {
            features_ar = JSON.parse(item.features_ar);
          } catch {
            features_ar = [];
          }
        }
        
        if (item.features_en) {
          try {
            features_en = JSON.parse(item.features_en);
          } catch {
            features_en = [];
          }
        }
        
        if (item.included_systems) {
          try {
            included_systems = JSON.parse(item.included_systems);
          } catch {
            included_systems = [];
          }
        }
        
        if (item.included_services) {
          try {
            included_services = JSON.parse(item.included_services);
          } catch {
            included_services = [];
          }
        }
        
        if (item.gallery_images) {
          try {
            gallery_images = JSON.parse(item.gallery_images);
          } catch {
            gallery_images = [];
          }
        }
      } catch (error) {
        console.log(`     Warning: Could not parse data for ${item.title_ar}`);
      }
      
      await connection.execute(`
        UPDATE premium_content 
        SET features_ar = ?, features_en = ?, included_systems = ?, included_services = ?, gallery_images = ?
        WHERE id = ?
      `, [
        JSON.stringify(features_ar),
        JSON.stringify(features_en),
        JSON.stringify(included_systems),
        JSON.stringify(included_services),
        JSON.stringify(gallery_images),
        item.id
      ]);
      
      console.log(`     ✅ Fixed: ${item.title_ar}`);
    }

    // Test the fixes
    console.log('\n4️⃣ Testing fixes...');
    
    const [testSystems] = await connection.execute('SELECT id, name_ar, features_ar FROM system_services LIMIT 2');
    console.log('   System services test:');
    testSystems.forEach(system => {
      try {
        const features = JSON.parse(system.features_ar);
        console.log(`     ✅ ${system.name_ar}: ${features.length} features`);
      } catch (error) {
        console.log(`     ❌ ${system.name_ar}: JSON parse failed`);
      }
    });

    const [testServices] = await connection.execute('SELECT id, name_ar, features_ar FROM technical_services LIMIT 2');
    console.log('   Technical services test:');
    testServices.forEach(service => {
      try {
        const features = JSON.parse(service.features_ar);
        console.log(`     ✅ ${service.name_ar}: ${features.length} features`);
      } catch (error) {
        console.log(`     ❌ ${service.name_ar}: JSON parse failed`);
      }
    });

    await connection.end();

    console.log('\n✅ JSON Arabic data fixes completed!');
    console.log('\n🔄 Next steps:');
    console.log('1. Restart backend server');
    console.log('2. Test API endpoints again');
    console.log('3. Check frontend data display');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

fixJSONArabicData();
