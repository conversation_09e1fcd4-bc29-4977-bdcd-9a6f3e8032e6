#!/usr/bin/env node

/**
 * Khanfashariya.com API Server
 * 
 * Express.js server with comprehensive API endpoints for:
 * - Authentication & User Management
 * - System Services & Technical Services
 * - Premium Content & Packages
 * - Orders & Subscriptions
 * - File Uploads & Management
 * - Admin Panel Operations
 * 
 * Features:
 * - JWT Authentication with refresh tokens
 * - Rate limiting and security middleware
 * - Comprehensive error handling
 * - Request logging and monitoring
 * - File upload support
 * - Bilingual content support
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
require('dotenv').config();

// Import custom modules
const { connectDatabase, closeDatabase } = require('./config/database');
const { dbHealthCheck, criticalDbHealthCheck } = require('./middleware/dbHealthCheck');
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const systemRoutes = require('./routes/systems');
const serviceRoutes = require('./routes/services');
const orderRoutes = require('./routes/orders');
const adminRoutes = require('./routes/admin');
const fileRoutes = require('./routes/files');
const { errorHandler, notFoundHandler } = require('./middleware/errorHandler');
const { requestLogger } = require('./middleware/logger');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3001;

// =====================================================
// SECURITY & MIDDLEWARE CONFIGURATION
// =====================================================

// Security headers
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https:"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      connectSrc: ["'self'", "https:"],
      fontSrc: ["'self'", "https:", "data:"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'", "https:", "blob:"],
      frameSrc: ["'self'", "https:"],
    },
  },
}));

// CORS configuration - Dynamic and secure
const allowedOrigins = [
  'http://localhost:5173', // Vite dev server
  'http://localhost:3000', // Alternative dev server
  'http://localhost',      // WAMP server testing
  'http://localhost/kfs',  // WAMP server with subdirectory
  'https://70f354611634.ngrok-free.app', // Frontend ngrok
  'https://7b93f343ea56.ngrok-free.app',  // Backend ngrok
];

// Add production URLs if specified
if (process.env.FRONTEND_PROD_URL) {
  allowedOrigins.push(process.env.FRONTEND_PROD_URL);
  allowedOrigins.push(`www.${process.env.FRONTEND_PROD_URL.replace('https://', '').replace('http://', '')}`);
}

// Add production defaults
if (process.env.NODE_ENV === 'production') {
  allowedOrigins.push('https://khanfashariya.com', 'https://www.khanfashariya.com');
}

app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked request from origin: ${origin}`);
      console.log('Allowed origins:', allowedOrigins);
      callback(new Error('Not allowed by CORS policy'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count']
}));

// Compression middleware
app.use(compression());

// Request parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging
app.use(morgan('combined'));
app.use(requestLogger);

// Database health check middleware
app.use(dbHealthCheck);

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 1000, // Increased for development - limit each IP to 1000 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// // app.use('/api/', limiter); // DISABLED TO FIX INFINITE REQUESTS // DISABLED TO FIX INFINITE REQUESTS

// Stricter rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: '15 minutes'
  }
});

// =====================================================
// FILE UPLOAD CONFIGURATION
// =====================================================

// Ensure upload directory exists
const uploadDir = process.env.UPLOAD_DIR || 'uploads';
fs.mkdir(uploadDir, { recursive: true }).catch(console.error);

// Multer configuration for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const fileFilter = (req, file, cb) => {
  const allowedTypes = process.env.ALLOWED_FILE_TYPES 
    ? process.env.ALLOWED_FILE_TYPES.split(',')
    : ['jpg', 'jpeg', 'png', 'gif', 'mp4', 'avi', 'mov'];
  
  const ext = path.extname(file.originalname).toLowerCase().slice(1);
  
  if (allowedTypes.includes(ext)) {
    cb(null, true);
  } else {
    cb(new Error(`File type .${ext} is not allowed. Allowed types: ${allowedTypes.join(', ')}`), false);
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB default
  },
  fileFilter: fileFilter
});

// =====================================================
// HEALTH CHECK & STATUS ENDPOINTS
// =====================================================

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    // Test database connection
    const db = await connectDatabase();
    await db.execute('SELECT 1');
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      database: 'connected',
      uptime: process.uptime()
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      database: 'disconnected'
    });
  }
});

// API status endpoint
app.get('/api/status', (req, res) => {
  res.json({
    message: 'Khanfashariya API Server is running',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      auth: '/api/auth',
      users: '/api/users',
      systems: '/api/systems',
      services: '/api/services',
      orders: '/api/orders',
      admin: '/api/admin',
      files: '/api/files'
    }
  });
});

// Root route - API information
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Khanfashariya Backend API Server',
    version: '1.0.0',
    description: 'Backend API for Khanfashariya Metin2 Services Platform',
    frontend: 'http://localhost:5173',
    api: 'http://localhost:3001/api',
    health: 'http://localhost:3001/health',
    timestamp: new Date().toISOString(),
    availableEndpoints: [
      'GET /health - Health check',
      'GET /api/status - API status',
      'POST /api/auth/login - User login',
      'POST /api/auth/register - User registration',
      'GET /api/users/profile - User profile',
      'GET /api/systems - System services',
      'GET /api/services - Technical services',
      'GET /api/orders - Order management',
      'GET /api/admin/dashboard - Admin dashboard'
    ],
    documentation: {
      note: 'This is the backend API server. For the main website, visit http://localhost:5173',
      apiBase: 'http://localhost:3001/api'
    }
  });
});

// =====================================================
// API ROUTES
// =====================================================

// Authentication routes (with stricter rate limiting)
app.use('/api/auth', authRoutes); // authLimiter DISABLED

// Main API routes
app.use('/api/users', userRoutes);
app.use('/api/systems', systemRoutes);
app.use('/api/services', serviceRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/premium', require('./routes/premium')); // Premium content management
app.use('/api/admin', criticalDbHealthCheck, adminRoutes); // Critical operations need extra health check
app.use('/api/files', fileRoutes);

// Serve uploaded files
app.use('/uploads', express.static(uploadDir));

// =====================================================
// ERROR HANDLING
// =====================================================

// 404 handler for undefined routes
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// =====================================================
// SERVER STARTUP
// =====================================================

/**
 * Start the server
 */
async function startServer() {
  try {
    // Test database connection
    console.log('🔌 Connecting to database...');
    await connectDatabase();
    console.log('✅ Database connected successfully');
    
    // Start the server
    const server = app.listen(PORT, () => {
      console.log('🚀 Khanfashariya API Server started successfully!');
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🌐 Server running on: http://localhost:${PORT}`);
      console.log(`📡 API endpoints available at: http://localhost:${PORT}/api`);
      console.log(`💾 File uploads directory: ${uploadDir}`);
      console.log(`🔒 Rate limiting: ${process.env.RATE_LIMIT_MAX_REQUESTS || 100} requests per ${(parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000) / 60000} minutes`);
      console.log('📋 Available endpoints:');
      console.log('   🔐 /api/auth - Authentication');
      console.log('   👤 /api/users - User management');
      console.log('   🖥️  /api/systems - System services');
      console.log('   🛠️  /api/services - Technical services');
      console.log('   📦 /api/orders - Order management');
      console.log('   👑 /api/admin - Admin operations');
      console.log('   📁 /api/files - File management');
      console.log('   ❤️  /health - Health check');
    });
    
    // Graceful shutdown handling
    process.on('SIGTERM', () => gracefulShutdown(server));
    process.on('SIGINT', () => gracefulShutdown(server));
    
  } catch (error) {
    console.error('💥 Failed to start server:', error.message);
    console.error('🔧 Troubleshooting:');
    console.error('   1. Make sure MySQL/WampServer is running');
    console.error('   2. Check database credentials in .env file');
    console.error('   3. Ensure database schema is set up (run: npm run setup:db)');
    console.error('   4. Check if port', PORT, 'is available');
    process.exit(1);
  }
}

/**
 * Graceful shutdown
 */
async function gracefulShutdown(server) {
  console.log('🛑 Received shutdown signal, starting graceful shutdown...');
  
  // Stop accepting new connections
  server.close(async () => {
    console.log('🔌 HTTP server closed');
    
    try {
      // Close database connections
      await closeDatabase();
      console.log('💾 Database connections closed');
      
      console.log('✅ Graceful shutdown completed');
      process.exit(0);
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
      process.exit(1);
    }
  });
  
  // Force shutdown after 30 seconds
  setTimeout(() => {
    console.error('⏰ Forced shutdown after timeout');
    process.exit(1);
  }, 30000);
}

// Start the server if this file is run directly
if (require.main === module) {
  startServer();
}

module.exports = app;
