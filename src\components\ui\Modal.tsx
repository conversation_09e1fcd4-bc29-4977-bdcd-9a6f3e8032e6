import React, { useEffect, useRef } from 'react';
import { X } from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';
import Button from './Button';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  showCloseButton?: boolean;
  className?: string;
  overlayClassName?: string;
}

interface ModalHeaderProps {
  children: React.ReactNode;
  className?: string;
}

interface ModalBodyProps {
  children: React.ReactNode;
  className?: string;
}

interface ModalFooterProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Enhanced Modal component with accessibility and customization features
 * 
 * Features:
 * - Multiple sizes (sm, md, lg, xl, full)
 * - Keyboard navigation and focus management
 * - Overlay click and escape key handling
 * - Smooth animations
 * - RTL layout support
 * - Accessibility features (ARIA attributes, focus trap)
 * - Compound components (Header, Body, Footer)
 * - Body scroll prevention
 */
const Modal: React.FC<ModalProps> & {
  Header: React.FC<ModalHeaderProps>;
  Body: React.FC<ModalBodyProps>;
  Footer: React.FC<ModalFooterProps>;
} = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  closeOnOverlayClick = true,
  closeOnEscape = true,
  showCloseButton = true,
  className = '',
  overlayClassName = ''
}) => {
  const { language, t } = useTranslation();
  const modalRef = useRef<HTMLDivElement>(null);
  const previousFocusRef = useRef<HTMLElement | null>(null);

  const isRTL = language === 'ar';

  // Size classes with proper responsive behavior
  const sizeClasses = {
    sm: 'max-w-md max-h-[90vh]',
    md: 'max-w-lg max-h-[90vh]',
    lg: 'max-w-2xl max-h-[90vh]',
    xl: 'max-w-4xl max-h-[90vh]',
    full: 'max-w-[95vw] max-h-[95vh]'
  };

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && closeOnEscape && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, closeOnEscape, onClose]);

  // Focus management
  useEffect(() => {
    if (isOpen) {
      // Store the currently focused element
      previousFocusRef.current = document.activeElement as HTMLElement;
      
      // Focus the modal
      if (modalRef.current) {
        modalRef.current.focus();
      }
    } else {
      // Restore focus to the previously focused element
      if (previousFocusRef.current) {
        previousFocusRef.current.focus();
      }
    }
  }, [isOpen]);

  // Body scroll prevention
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = 'unset';
      };
    }
  }, [isOpen]);

  // Focus trap
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Tab') {
      const focusableElements = modalRef.current?.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      if (focusableElements && focusableElements.length > 0) {
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
        
        if (event.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            event.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            event.preventDefault();
          }
        }
      }
    }
  };

  // Handle overlay click
  const handleOverlayClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget && closeOnOverlayClick) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 z-[9999] flex items-center justify-center p-4 ${overlayClassName}`}
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby={title ? 'modal-title' : undefined}
    >
      {/* Overlay */}
      <div className="absolute inset-0 bg-black/80 backdrop-blur-md animate-in fade-in duration-200" />

      {/* Modal */}
      <div
        ref={modalRef}
        className={`
          relative w-full ${sizeClasses[size]} bg-gradient-to-br from-primary to-background
          rounded-xl shadow-2xl border border-accent/30 animate-in zoom-in-95 duration-200
          backdrop-blur-sm bg-opacity-95 flex flex-col overflow-hidden
          ${className}
        `}
        tabIndex={-1}
        onKeyDown={handleKeyDown}
      >
        {/* Header */}
        {(title || showCloseButton) && (
          <div className="flex items-center justify-between p-6 border-b border-accent-500/20">
            {title && (
              <h2
                id="modal-title"
                className={`text-xl font-bold text-white ${isRTL ? 'text-right' : 'text-left'}`}
              >
                {title}
              </h2>
            )}
            
            {showCloseButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-text-tertiary hover:text-text-primary"
                aria-label={t('common.close', 'Close')}
              >
                <X className="w-5 h-5" />
              </Button>
            )}
          </div>
        )}

        {/* Content */}
        <div className="relative flex-1 overflow-hidden">
          {children}
        </div>
      </div>
    </div>
  );
};

/**
 * Modal Header component
 */
const ModalHeader: React.FC<ModalHeaderProps> = ({ children, className = '' }) => {
  return (
    <div className={`p-6 border-b border-accent-500/20 ${className}`}>
      {children}
    </div>
  );
};

/**
 * Modal Body component with proper scrolling
 */
const ModalBody: React.FC<ModalBodyProps> = ({ children, className = '' }) => {
  return (
    <div className={`p-6 flex-1 overflow-y-auto ${className}`}>
      {children}
    </div>
  );
};

/**
 * Modal Footer component - fixed at bottom with RTL support
 */
const ModalFooter: React.FC<ModalFooterProps> = ({ children, className = '' }) => {
  const { language } = useTranslation();
  const isRTL = language === 'ar';

  return (
    <div className={`p-6 border-t border-accent-500/20 flex-shrink-0 flex ${isRTL ? 'flex-row-reverse' : 'flex-row'} justify-end space-x-3 ${isRTL ? 'space-x-reverse' : ''} ${className}`}>
      {children}
    </div>
  );
};

// Attach compound components
Modal.Header = ModalHeader;
Modal.Body = ModalBody;
Modal.Footer = ModalFooter;

export default Modal;
