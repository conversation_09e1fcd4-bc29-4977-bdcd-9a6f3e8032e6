/**
 * Check Table Columns
 * 
 * This script checks the actual columns in database tables
 */

const { executeQuery } = require('../server/config/database');
require('dotenv').config();

async function checkTableColumns() {
  console.log('🔍 Checking database table columns...\n');
  
  try {
    const dbName = process.env.DB_NAME || 'khanfashariya_db';
    
    // Check system_services table
    console.log('1️⃣ Checking system_services table columns...');
    const { rows: systemColumns } = await executeQuery(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM information_schema.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'system_services'
      ORDER BY ORDINAL_POSITION
    `, [dbName]);
    
    if (systemColumns.length === 0) {
      console.log('❌ system_services table not found');
    } else {
      console.log(`✅ system_services table has ${systemColumns.length} columns:`);
      systemColumns.forEach(col => {
        console.log(`   ${col.COLUMN_NAME}: ${col.DATA_TYPE}`);
      });
    }
    
    // Check technical_services table
    console.log('\n2️⃣ Checking technical_services table columns...');
    const { rows: serviceColumns } = await executeQuery(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM information_schema.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'technical_services'
      ORDER BY ORDINAL_POSITION
    `, [dbName]);
    
    if (serviceColumns.length === 0) {
      console.log('❌ technical_services table not found');
    } else {
      console.log(`✅ technical_services table has ${serviceColumns.length} columns:`);
      serviceColumns.forEach(col => {
        console.log(`   ${col.COLUMN_NAME}: ${col.DATA_TYPE}`);
      });
    }
    
    // Check for missing columns that code expects
    console.log('\n3️⃣ Checking for expected columns...');
    
    const expectedSystemColumns = [
      'id', 'name_ar', 'name_en', 'description_ar', 'description_en', 
      'price', 'category', 'type', 'features_ar', 'features_en', 
      'tech_specs_ar', 'tech_specs_en', 'video_url', 'image_url', 
      'gallery_images', 'status', 'featured'
    ];
    
    const expectedServiceColumns = [
      'id', 'name_ar', 'name_en', 'description_ar', 'description_en', 
      'price', 'category', 'service_type', 'features_ar', 'features_en', 
      'video_url', 'image_url', 'gallery_images', 'status', 'featured'
    ];
    
    // Check system_services missing columns
    const systemColumnNames = systemColumns.map(col => col.COLUMN_NAME);
    const missingSystemColumns = expectedSystemColumns.filter(col => !systemColumnNames.includes(col));
    
    if (missingSystemColumns.length > 0) {
      console.log('⚠️  Missing columns in system_services:');
      missingSystemColumns.forEach(col => console.log(`   - ${col}`));
    } else {
      console.log('✅ All expected columns found in system_services');
    }
    
    // Check technical_services missing columns
    const serviceColumnNames = serviceColumns.map(col => col.COLUMN_NAME);
    const missingServiceColumns = expectedServiceColumns.filter(col => !serviceColumnNames.includes(col));
    
    if (missingServiceColumns.length > 0) {
      console.log('⚠️  Missing columns in technical_services:');
      missingServiceColumns.forEach(col => console.log(`   - ${col}`));
    } else {
      console.log('✅ All expected columns found in technical_services');
    }
    
    // Check for extra columns that might cause issues
    console.log('\n4️⃣ Checking for extra columns...');
    
    const extraSystemColumns = systemColumnNames.filter(col => 
      !expectedSystemColumns.includes(col) && 
      !['created_at', 'updated_at', 'download_count', 'rating', 'rating_count', 'version', 'file_size', 'sort_order'].includes(col)
    );
    
    const extraServiceColumns = serviceColumnNames.filter(col => 
      !expectedServiceColumns.includes(col) && 
      !['created_at', 'updated_at', 'order_count', 'rating', 'rating_count', 'delivery_time_ar', 'delivery_time_en', 'process_steps_ar', 'process_steps_en', 'sort_order'].includes(col)
    );
    
    if (extraSystemColumns.length > 0) {
      console.log('ℹ️  Extra columns in system_services:');
      extraSystemColumns.forEach(col => console.log(`   + ${col}`));
    }
    
    if (extraServiceColumns.length > 0) {
      console.log('ℹ️  Extra columns in technical_services:');
      extraServiceColumns.forEach(col => console.log(`   + ${col}`));
    }
    
    // Sample data check
    console.log('\n5️⃣ Checking sample data...');
    
    try {
      const { rows: systemSample } = await executeQuery(`
        SELECT id, name_ar, name_en, features_ar 
        FROM system_services 
        LIMIT 1
      `);
      
      if (systemSample.length > 0) {
        console.log('✅ Sample system_services data found');
        console.log(`   ID: ${systemSample[0].id}`);
        console.log(`   Name AR: ${systemSample[0].name_ar}`);
        console.log(`   Name EN: ${systemSample[0].name_en}`);
        console.log(`   Features AR: ${systemSample[0].features_ar ? 'Has data' : 'Empty'}`);
      } else {
        console.log('⚠️  No data in system_services table');
      }
    } catch (error) {
      console.log('❌ Error checking system_services data:', error.message);
    }
    
    try {
      const { rows: serviceSample } = await executeQuery(`
        SELECT id, name_ar, name_en, features_ar 
        FROM technical_services 
        LIMIT 1
      `);
      
      if (serviceSample.length > 0) {
        console.log('✅ Sample technical_services data found');
        console.log(`   ID: ${serviceSample[0].id}`);
        console.log(`   Name AR: ${serviceSample[0].name_ar}`);
        console.log(`   Name EN: ${serviceSample[0].name_en}`);
        console.log(`   Features AR: ${serviceSample[0].features_ar ? 'Has data' : 'Empty'}`);
      } else {
        console.log('⚠️  No data in technical_services table');
      }
    } catch (error) {
      console.log('❌ Error checking technical_services data:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Error checking table columns:', error.message);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  checkTableColumns()
    .then(() => {
      console.log('\n✅ Table column check completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Check failed:', error.message);
      process.exit(1);
    });
}

module.exports = { checkTableColumns };