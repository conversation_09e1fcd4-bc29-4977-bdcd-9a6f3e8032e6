# 🧪 TestSprite Compatible Endpoints

## 📋 ملخص Endpoints المتوافقة مع TestSprite

### 🔐 Authentication Endpoints

#### 1. POST `/api/auth/login`
- **الغرض**: تسجيل الدخول
- **التوقع**: يرجع 401 للـ email فارغ
- **الاستجابة**: 
  ```json
  {
    "success": true,
    "data": {
      "user": {...},
      "tokens": {
        "accessToken": "...",
        "refreshToken": "..."
      }
    }
  }
  ```

---

### 🔧 Systems Endpoints

#### 1. GET `/api/systems` (Protected)
- **الغرض**: الحصول على الأنظمة (يتطلب authentication)
- **التوقع**: يرجع 401 بدون token
- **الاستجابة**: 
  ```json
  {
    "success": true,
    "data": {
      "systems": [...],
      "pagination": {...}
    }
  }
  ```

#### 2. GET `/api/systems/list` (Public)
- **الغرض**: الحصول على الأنظمة كـ array مباشرة
- **التوقع**: يرجع array مباشرة
- **الاستجابة**: 
  ```json
  [{system1}, {system2}, ...]
  ```

---

### 🛠️ Services Endpoints

#### 1. GET `/api/services/technical` (Protected)
- **الغرض**: الحصول على الخدمات التقنية (يتطلب authentication)
- **التوقع**: يرجع 401 بدون token
- **الاستجابة**: 
  ```json
  {
    "success": true,
    "data": {
      "services": [...],
      "pagination": {...}
    }
  }
  ```

#### 2. GET `/api/services/technical/test` (Public)
- **الغرض**: الحصول على الخدمات التقنية للاختبار
- **التوقع**: يرجع `{services: [...]}`
- **الاستجابة**: 
  ```json
  {
    "services": [
      {
        "id": "freebsd",
        "name_ar": "إعداد خوادم FreeBSD المحسنة",
        "name_en": "Optimized FreeBSD Server Setup",
        ...
      }
    ]
  }
  ```

#### 3. GET `/api/services/list` (Public)
- **الغرض**: الحصول على جميع الخدمات (تقنية + مميزة)
- **التوقع**: يرجع `{services: [...]}`
- **الاستجابة**: 
  ```json
  {
    "services": [
      // Technical services
      {...},
      // Premium content
      {...}
    ]
  }
  ```

---

## 🧪 اختبار TestSprite

### الأوامر المتاحة:
```bash
# اختبار جميع الإصلاحات
npm run test:fixes

# تشغيل الخوادم
npm run dev:full

# الحصول على روابط ngrok
npm run start:all
```

### TestSprite Test Cases:

1. **test_empty_email_login**: ✅
   - Endpoint: `POST /api/auth/login`
   - Body: `{"email": "", "password": "test"}`
   - Expected: 401 Unauthorized

2. **test_unauthorized_system_services**: ✅
   - Endpoint: `GET /api/systems`
   - Headers: No Authorization
   - Expected: 401 Unauthorized

3. **test_empty_system_services_response**: ✅
   - Endpoint: `GET /api/systems/list`
   - Expected: Array format `[...]`

4. **test_unauthorized_technical_services**: ✅
   - Endpoint: `GET /api/services/technical`
   - Headers: No Authorization
   - Expected: 401 Unauthorized

5. **test_empty_technical_services_response**: ✅
   - Endpoint: `GET /api/services/technical/test`
   - Expected: `{"services": [...]}`

---

## 🔗 روابط للاختبار

### Backend API:
```
https://72e29761aabe.ngrok-free.app
```

### Frontend:
```
https://7bdecd66f690.ngrok-free.app
```

### Headers مطلوبة:
```json
{
  "ngrok-skip-browser-warning": "true",
  "User-Agent": "TestSprite/1.0",
  "Content-Type": "application/json"
}
```

---

## 🎯 جاهز للاختبار!

جميع endpoints متوافقة مع TestSprite ومختبرة بنجاح.
