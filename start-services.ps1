# Khanfashariya - تشغيل جميع الخدمات
# PowerShell Script

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    🚀 تشغيل جميع خدمات المشروع" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# التحقق من المتطلبات
Write-Host "🔍 التحقق من المتطلبات..." -ForegroundColor Blue

# فحص Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js متوفر: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js غير مثبت!" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# فحص ngrok
try {
    $ngrokVersion = ngrok version
    Write-Host "✅ ngrok متوفر" -ForegroundColor Green
} catch {
    Write-Host "❌ ngrok غير مثبت!" -ForegroundColor Red
    Write-Host "💡 قم بتثبيته: npm install -g ngrok" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# إيقاف العمليات السابقة
Write-Host ""
Write-Host "🛑 إيقاف العمليات السابقة..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "ngrok" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2

# تشغيل الخادم الخلفي
Write-Host ""
Write-Host "🔧 تشغيل الخادم الخلفي..." -ForegroundColor Blue
Start-Process -FilePath "cmd" -ArgumentList "/k", "title 🔧 Backend-Server && npm run start" -WindowStyle Normal

# تشغيل الخادم الأمامي
Write-Host "🎨 تشغيل الخادم الأمامي..." -ForegroundColor Blue
Start-Process -FilePath "cmd" -ArgumentList "/k", "title 🎨 Frontend-Server && npm run dev" -WindowStyle Normal

# انتظار تشغيل الخوادم
Write-Host "⏳ انتظار تشغيل الخوادم..." -ForegroundColor Yellow
Start-Sleep -Seconds 8

# فحص الخوادم
Write-Host ""
Write-Host "🔍 فحص الخوادم..." -ForegroundColor Blue

$backendRunning = $false
$frontendRunning = $false

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/health" -TimeoutSec 5 -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ الخادم الخلفي يعمل" -ForegroundColor Green
        $backendRunning = $true
    }
} catch {
    Write-Host "⚠️ الخادم الخلفي لم يبدأ بعد..." -ForegroundColor Yellow
}

try {
    $response = Invoke-WebRequest -Uri "http://localhost:5173" -TimeoutSec 5 -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ الخادم الأمامي يعمل" -ForegroundColor Green
        $frontendRunning = $true
    }
} catch {
    Write-Host "⚠️ الخادم الأمامي لم يبدأ بعد..." -ForegroundColor Yellow
}

if (-not $backendRunning -or -not $frontendRunning) {
    Write-Host "⏳ انتظار إضافي للخوادم..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5
}

# تشغيل ngrok
Write-Host ""
Write-Host "🌐 تشغيل ngrok..." -ForegroundColor Blue

Write-Host "🔧 تشغيل ngrok للخادم الخلفي..." -ForegroundColor Cyan
Start-Process -FilePath "cmd" -ArgumentList "/k", "title 🌐 Backend-ngrok && ngrok http 3001" -WindowStyle Normal

Write-Host "🎨 تشغيل ngrok للخادم الأمامي..." -ForegroundColor Cyan
Start-Process -FilePath "cmd" -ArgumentList "/k", "title 🌐 Frontend-ngrok && ngrok http 5173 --web-addr=localhost:4041" -WindowStyle Normal

# انتظار تشغيل ngrok
Write-Host "⏳ انتظار تشغيل ngrok..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# الحصول على الروابط
Write-Host ""
Write-Host "🔗 الحصول على روابط ngrok..." -ForegroundColor Blue

$backendUrl = "غير متاح"
$frontendUrl = "غير متاح"

# محاولة الحصول على رابط Backend
try {
    $backendResponse = Invoke-RestMethod -Uri "http://127.0.0.1:4040/api/tunnels" -TimeoutSec 5
    if ($backendResponse.tunnels -and $backendResponse.tunnels.Count -gt 0) {
        $backendUrl = $backendResponse.tunnels[0].public_url
        Write-Host "✅ Backend URL: $backendUrl" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ لم يتم العثور على Backend ngrok" -ForegroundColor Yellow
}

# محاولة الحصول على رابط Frontend
try {
    $frontendResponse = Invoke-RestMethod -Uri "http://127.0.0.1:4041/api/tunnels" -TimeoutSec 5
    if ($frontendResponse.tunnels -and $frontendResponse.tunnels.Count -gt 0) {
        $frontendUrl = $frontendResponse.tunnels[0].public_url
        Write-Host "✅ Frontend URL: $frontendUrl" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ لم يتم العثور على Frontend ngrok" -ForegroundColor Yellow
}

# إنشاء ملف التكوين
$config = @{
    timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    status = "ready"
    backend = @{
        name = "Khanfashariya Backend API"
        url = $backendUrl
        local = "http://localhost:3001"
        port = 3001
    }
    frontend = @{
        name = "Khanfashariya Frontend"
        url = $frontendUrl
        local = "http://localhost:5173"
        port = 5173
    }
    testsprite_endpoints = @{}
    ready_for_testsprite = ($backendUrl -ne "غير متاح" -and $frontendUrl -ne "غير متاح")
}

# إضافة endpoints إذا كان Backend متاح
if ($backendUrl -ne "غير متاح") {
    $config.testsprite_endpoints = @{
        health = "$backendUrl/health"
        admin_login = "$backendUrl/api/auth/login"
        systems = "$backendUrl/api/systems"
        technical_services = "$backendUrl/api/services/technical"
        premium_services = "$backendUrl/api/services/premium"
    }
}

# حفظ التكوين
$configJson = $config | ConvertTo-Json -Depth 10
$configJson | Out-File -FilePath "current-ngrok-urls.json" -Encoding UTF8

# تشغيل سكريبت Node.js للتحديث الإضافي
if (Test-Path "scripts/get-ngrok-urls.js") {
    Write-Host "📋 تحديث ملفات التكوين..." -ForegroundColor Blue
    node scripts/get-ngrok-urls.js
}

# النتائج النهائية
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           ✅ تم التشغيل بنجاح!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 الخدمات المشغلة:" -ForegroundColor Yellow
Write-Host "  🔧 Backend Server: http://localhost:3001" -ForegroundColor White
Write-Host "  🎨 Frontend Server: http://localhost:5173" -ForegroundColor White
Write-Host "  🌐 Backend ngrok: $backendUrl" -ForegroundColor White
Write-Host "  🌐 Frontend ngrok: $frontendUrl" -ForegroundColor White
Write-Host ""
Write-Host "📁 الروابط محفوظة في: current-ngrok-urls.json" -ForegroundColor Cyan
Write-Host ""

if ($config.ready_for_testsprite) {
    Write-Host "🚀 جاهز للاستخدام مع TestSprite!" -ForegroundColor Green
} else {
    Write-Host "⚠️ بعض الخدمات غير متاحة. تحقق من النوافذ المفتوحة." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "⚠️ احتفظ بجميع النوافذ مفتوحة للحفاظ على الخدمات" -ForegroundColor Red
Write-Host ""
Read-Host "اضغط Enter للإنهاء"
