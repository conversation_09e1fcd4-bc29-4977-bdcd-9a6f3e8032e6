import React from 'react';
import { 
  Package, 
  User, 
  Calendar, 
  DollarSign, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Eye,
  Edit,
  Trash2,
  MessageSquare,
  CreditCard,
  MapPin
} from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';

interface OrderItem {
  id: string;
  name: { ar: string; en: string };
  price: number;
  quantity: number;
  type: string;
}

interface Order {
  id: string;
  user_id: string;
  order_number?: string;
  order_type: 'system_service' | 'technical_service' | 'premium_content' | 'premium_package' | 'custom_request';
  order_category?: 'standard' | 'premium_base' | 'premium_custom' | 'subscription' | 'maintenance';
  item_name_ar: string;
  item_name_en: string;

  // Enhanced pricing structure
  unit_price?: number;
  base_price?: number;
  addons_price?: number;
  subscription_price?: number;
  maintenance_price?: number;
  total_price?: number;
  discount_amount?: number;
  tax_amount?: number;
  final_price: number;

  // Enhanced status management
  status: 'pending' | 'confirmed' | 'in_progress' | 'testing' | 'completed' | 'cancelled' | 'refunded' | 'on_hold' | 'expired';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  progress_percentage?: number;

  // Timeline
  created_at: string;
  updated_at?: string;
  confirmed_at?: string;
  started_at?: string;
  estimated_completion?: string;
  completed_at?: string;
  completion_date?: string; // Legacy field

  // Payment and subscription
  payment_status?: 'pending' | 'paid' | 'partial' | 'failed' | 'refunded' | 'disputed';
  payment_method?: string;
  payment_reference?: string;
  subscription_type?: 'none' | 'monthly' | 'quarterly' | 'yearly' | 'lifetime';

  // Service details
  maintenance_included?: boolean;
  installation_included?: boolean;
  support_level?: 'basic' | 'standard' | 'premium' | 'enterprise';

  // Notes
  notes_ar?: string;
  notes_en?: string;
  admin_notes?: string;
  customer_requirements?: string;

  // User information from JOIN
  username?: string;
  email?: string;
  full_name?: string;
}

interface User {
  id: string;
  username: string;
  email: string;
}

interface OrderCardProps {
  order: Order;
  user?: User;
  viewMode: 'grid' | 'list';
  onView: (order: Order) => void;
  onEdit: (order: Order) => void;
  onDelete: (order: Order) => void;
  onStatusUpdate: (orderId: string, status: string) => void;
}

const OrderCard: React.FC<OrderCardProps> = ({
  order,
  user,
  viewMode,
  onView,
  onEdit,
  onDelete,
  onStatusUpdate
}) => {
  const { language } = useTranslation();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'processing': return <Package className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'cancelled': return <XCircle className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-amber-500/30 text-amber-200 border-amber-400/50';
      case 'confirmed': return 'bg-cyan-500/30 text-cyan-200 border-cyan-400/50';
      case 'in_progress': return 'bg-blue-500/30 text-blue-200 border-blue-400/50';
      case 'testing': return 'bg-purple-500/30 text-purple-200 border-purple-400/50';
      case 'completed': return 'bg-emerald-500/30 text-emerald-200 border-emerald-400/50';
      case 'cancelled': return 'bg-red-500/30 text-red-200 border-red-400/50';
      case 'refunded': return 'bg-orange-500/30 text-orange-200 border-orange-400/50';
      case 'on_hold': return 'bg-gray-500/30 text-gray-200 border-gray-400/50';
      case 'expired': return 'bg-red-700/30 text-red-200 border-red-600/50';
      // Legacy status support
      case 'processing': return 'bg-blue-500/30 text-blue-200 border-blue-400/50';
      default: return 'bg-gray-500/30 text-gray-200 border-gray-400/50';
    }
  };

  const getStatusText = (status: string) => {
    if (language === 'ar') {
      switch (status) {
        case 'pending': return 'معلق';
        case 'confirmed': return 'مؤكد';
        case 'in_progress': return 'قيد التنفيذ';
        case 'testing': return 'قيد الاختبار';
        case 'completed': return 'مكتمل';
        case 'cancelled': return 'ملغي';
        case 'refunded': return 'مسترد';
        case 'on_hold': return 'معلق مؤقتاً';
        case 'expired': return 'منتهي الصلاحية';
        // Legacy status support
        case 'processing': return 'قيد التنفيذ';
        default: return 'غير محدد';
      }
    } else {
      switch (status) {
        case 'pending': return 'Pending';
        case 'confirmed': return 'Confirmed';
        case 'in_progress': return 'In Progress';
        case 'testing': return 'Testing';
        case 'completed': return 'Completed';
        case 'cancelled': return 'Cancelled';
        case 'refunded': return 'Refunded';
        case 'on_hold': return 'On Hold';
        case 'expired': return 'Expired';
        // Legacy status support
        case 'processing': return 'Processing';
        default: return status.charAt(0).toUpperCase() + status.slice(1);
      }
    }
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) {
      return language === 'ar' ? 'غير محدد' : 'Not specified';
    }

    try {
      // Handle different date formats
      let date: Date;

      // If it's already a valid date string
      if (dateString.includes('T') || dateString.includes('-')) {
        date = new Date(dateString);
      } else {
        // Try parsing as timestamp
        const timestamp = parseInt(dateString);
        if (!isNaN(timestamp)) {
          date = new Date(timestamp);
        } else {
          date = new Date(dateString);
        }
      }

      if (isNaN(date.getTime())) {
        console.warn('Invalid date format:', dateString);
        return language === 'ar' ? 'تاريخ غير صحيح' : 'Invalid date';
      }

      // Format date with proper locale
      return date.toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.warn('Error formatting date:', error, dateString);
      return language === 'ar' ? 'تاريخ غير صحيح' : 'Invalid date';
    }
  };

  const formatTime = (dateString: string | null | undefined) => {
    if (!dateString) {
      return '';
    }

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return '';
      }

      return date.toLocaleTimeString(language === 'ar' ? 'ar-EG' : 'en-US', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return '';
    }
  };

  const getOrderValue = () => {
    // Enhanced pricing calculation with comprehensive fallback chain
    const finalPrice = parseFloat(order.final_price?.toString() || '0');
    const totalPrice = parseFloat(order.total_price?.toString() || '0');
    const basePrice = parseFloat(order.base_price?.toString() || '0');
    const addonsPrice = parseFloat(order.addons_price?.toString() || '0');
    const subscriptionPrice = parseFloat(order.subscription_price?.toString() || '0');
    const maintenancePrice = parseFloat(order.maintenance_price?.toString() || '0');
    const unitPrice = parseFloat(order.unit_price?.toString() || '0');

    // For complex orders, calculate total if components exist
    if (basePrice > 0 || addonsPrice > 0 || subscriptionPrice > 0 || maintenancePrice > 0) {
      const calculatedTotal = basePrice + addonsPrice + subscriptionPrice + maintenancePrice;
      if (calculatedTotal > 0) {
        return calculatedTotal;
      }
    }

    // Try to parse from order details if available
    if (order.customer_requirements) {
      try {
        const orderDetails = JSON.parse(order.customer_requirements);
        if (orderDetails.pricing_breakdown?.total_price) {
          const detailsTotal = parseFloat(orderDetails.pricing_breakdown.total_price.toString() || '0');
          if (detailsTotal > 0) {
            return detailsTotal;
          }
        }
        if (orderDetails.total_price) {
          const detailsTotal = parseFloat(orderDetails.total_price.toString() || '0');
          if (detailsTotal > 0) {
            return detailsTotal;
          }
        }
      } catch (error) {
        console.warn('Error parsing order pricing details:', error);
      }
    }

    // Standard fallback chain
    return finalPrice || totalPrice || basePrice || unitPrice || 0;
  };

  const getPriceBreakdown = () => {
    const base = parseFloat(order.base_price?.toString() || '0');
    const addons = parseFloat(order.addons_price?.toString() || '0');
    const subscription = parseFloat(order.subscription_price?.toString() || '0');
    const maintenance = parseFloat(order.maintenance_price?.toString() || '0');
    const discount = parseFloat(order.discount_amount?.toString() || '0');
    const tax = parseFloat(order.tax_amount?.toString() || '0');

    return { base, addons, subscription, maintenance, discount, tax };
  };

  const getPaymentMethod = () => {
    if (!order.payment_method) {
      return language === 'ar' ? 'غير محدد' : 'Not specified';
    }

    const paymentMethods: { [key: string]: { ar: string; en: string } } = {
      'credit_card': { ar: 'بطاقة ائتمان', en: 'Credit Card' },
      'paypal': { ar: 'باي بال', en: 'PayPal' },
      'bank_transfer': { ar: 'تحويل بنكي', en: 'Bank Transfer' },
      'cash': { ar: 'نقداً', en: 'Cash' },
      'crypto': { ar: 'عملة رقمية', en: 'Cryptocurrency' }
    };

    const method = paymentMethods[order.payment_method];
    return method ? (language === 'ar' ? method.ar : method.en) : order.payment_method;
  };

  const getServiceName = () => {
    // Enhanced service name display for complex orders
    const itemNameAr = order.item_name_ar;
    const itemNameEn = order.item_name_en;

    // If we have proper item names, use them
    if (itemNameAr && itemNameEn) {
      return language === 'ar' ? itemNameAr : itemNameEn;
    }

    // Fallback for Premium Edition orders
    if (order.order_type === 'premium_package' || order.order_type === 'premium_content') {
      // Try to parse order details for better display
      try {
        if (order.customer_requirements) {
          const orderDetails = JSON.parse(order.customer_requirements);
          if (orderDetails.base_package) {
            const baseName = language === 'ar' ? orderDetails.base_package.name_ar : orderDetails.base_package.name_en;
            if (baseName) {
              const addonsCount = (orderDetails.selected_systems?.length || 0) + (orderDetails.selected_services?.length || 0);
              if (addonsCount > 0) {
                return language === 'ar'
                  ? `${baseName} + ${addonsCount} إضافات`
                  : `${baseName} + ${addonsCount} Add-ons`;
              }
              return baseName;
            }
          }
        }
      } catch (error) {
        console.warn('Error parsing order details:', error);
      }

      // Default Premium Edition names
      return language === 'ar' ? 'النسخة المميزة' : 'Premium Edition';
    }

    // Fallback based on order type
    const typeNames = {
      ar: {
        system_service: 'خدمة نظام',
        technical_service: 'خدمة تقنية',
        premium_content: 'محتوى مميز',
        premium_package: 'النسخة المميزة',
        custom_request: 'طلب مخصص'
      },
      en: {
        system_service: 'System Service',
        technical_service: 'Technical Service',
        premium_content: 'Premium Content',
        premium_package: 'Premium Edition',
        custom_request: 'Custom Request'
      }
    };

    return typeNames[language][order.order_type] || (language === 'ar' ? 'طلب غير محدد' : 'Unknown Order');
  };

  const getUserDisplayName = () => {
    // Use embedded user data first, fallback to user prop
    const username = order.username || user?.username;
    const fullName = order.full_name || user?.full_name;
    const email = order.email || user?.email;

    if (fullName && fullName.trim()) {
      return fullName;
    }
    if (username && username.trim()) {
      return username;
    }
    if (email && email.trim()) {
      return email.split('@')[0]; // Use email prefix as fallback
    }

    return language === 'ar' ? 'مستخدم غير معروف' : 'Unknown User';
  };

  const getOrderType = () => {
    if (language === 'ar') {
      switch (order.order_type) {
        case 'system_service': return 'خدمة نظام';
        case 'technical_service': return 'خدمة تقنية';
        case 'premium_content': return 'محتوى مميز';
        case 'premium_package': return 'باقة مميزة';
        case 'custom_request': return 'طلب مخصص';
        default: return 'غير محدد';
      }
    } else {
      switch (order.order_type) {
        case 'system_service': return 'System Service';
        case 'technical_service': return 'Technical Service';
        case 'premium_content': return 'Premium Content';
        case 'premium_package': return 'Premium Package';
        case 'custom_request': return 'Custom Request';
        default: return 'Unknown';
      }
    }
  };

  const getOrderCategory = () => {
    if (!order.order_category) return '';

    if (language === 'ar') {
      switch (order.order_category) {
        case 'standard': return 'عادي';
        case 'premium_base': return 'مميز أساسي';
        case 'premium_custom': return 'مميز مخصص';
        case 'subscription': return 'اشتراك';
        case 'maintenance': return 'صيانة';
        default: return order.order_category;
      }
    } else {
      switch (order.order_category) {
        case 'standard': return 'Standard';
        case 'premium_base': return 'Premium Base';
        case 'premium_custom': return 'Premium Custom';
        case 'subscription': return 'Subscription';
        case 'maintenance': return 'Maintenance';
        default: return order.order_category;
      }
    }
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-400';
      case 'high': return 'text-orange-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const getPriorityText = (priority?: string) => {
    if (!priority) return '';

    if (language === 'ar') {
      switch (priority) {
        case 'urgent': return 'عاجل';
        case 'high': return 'عالي';
        case 'medium': return 'متوسط';
        case 'low': return 'منخفض';
        default: return priority;
      }
    } else {
      return priority.charAt(0).toUpperCase() + priority.slice(1);
    }
  };

  if (viewMode === 'list') {
    return (
      <div className="bg-gradient-to-r from-gray-900/50 to-gray-800/30 border border-gray-700/50 rounded-xl p-4 hover:border-secondary/50 transition-all duration-300 hover:shadow-lg hover:shadow-secondary/10">
        <div className="flex items-center justify-between">
          {/* Left Section - Order Info */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse flex-1">
            <div className="w-12 h-12 bg-gradient-to-br from-secondary to-accent rounded-xl flex items-center justify-center shadow-lg">
              <Package className="w-6 h-6 text-white" />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
                <h3 className="text-lg font-bold text-white truncate">
                  {getServiceName()}
                </h3>
                <span className="text-xs text-gray-400">
                  #{order.order_number || order.id.slice(0, 8)}
                </span>
              </div>
              
              <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-400">
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  <User className="w-3 h-3" />
                  <span>{getUserDisplayName()}</span>
                </div>
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  <Calendar className="w-3 h-3" />
                  <span>{formatDate(order.created_at)}</span>
                </div>
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  <CreditCard className="w-3 h-3" />
                  <span>{getPaymentMethod()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Center Section - Status & Price */}
          <div className="flex items-center space-x-6 rtl:space-x-reverse">
            <div className="text-center">
              <div className="text-2xl font-bold text-secondary mb-1">
                ${getOrderValue().toFixed(2)}
              </div>
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(order.status)}`}>
                {getStatusIcon(order.status)}
                <span className="ml-1 rtl:mr-1 rtl:ml-0">{getStatusText(order.status)}</span>
              </div>
            </div>
          </div>

          {/* Right Section - Actions */}
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {order.status === 'pending' && (
              <>
                <button
                  onClick={() => onStatusUpdate(order.id, 'completed')}
                  className="p-2 bg-emerald-500/20 text-emerald-400 border border-emerald-500/30 rounded-lg hover:bg-emerald-500/30 transition-colors"
                  title={language === 'ar' ? 'قبول' : 'Accept'}
                >
                  <CheckCircle className="w-4 h-4" />
                </button>
                <button
                  onClick={() => onStatusUpdate(order.id, 'cancelled')}
                  className="p-2 bg-red-500/20 text-red-400 border border-red-500/30 rounded-lg hover:bg-red-500/30 transition-colors"
                  title={language === 'ar' ? 'رفض' : 'Reject'}
                >
                  <XCircle className="w-4 h-4" />
                </button>
              </>
            )}
            
            <button
              onClick={() => onView(order)}
              className="p-2 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded-lg hover:bg-blue-500/30 transition-colors"
              title={language === 'ar' ? 'عرض' : 'View'}
            >
              <Eye className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => onEdit(order)}
              className="p-2 bg-amber-500/20 text-amber-400 border border-amber-500/30 rounded-lg hover:bg-amber-500/30 transition-colors"
              title={language === 'ar' ? 'تعديل' : 'Edit'}
            >
              <Edit className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => onDelete(order)}
              className="p-2 bg-red-500/20 text-red-400 border border-red-500/30 rounded-lg hover:bg-red-500/30 transition-colors"
              title={language === 'ar' ? 'حذف' : 'Delete'}
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Grid View
  return (
    <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/40 border border-gray-700/50 rounded-xl p-5 hover:border-secondary/50 transition-all duration-300 hover:shadow-lg hover:shadow-secondary/10 group">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className="w-12 h-12 bg-gradient-to-br from-secondary to-accent rounded-xl flex items-center justify-center shadow-lg">
            <Package className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-white group-hover:text-secondary transition-colors">
              {getServiceName()}
            </h3>
            <p className="text-xs text-gray-400">
              #{order.order_number || order.id.slice(0, 8)}
            </p>
          </div>
        </div>
        
        <div className={`inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium border ${getStatusColor(order.status)}`}>
          {getStatusIcon(order.status)}
          <span className="ml-1 rtl:mr-1 rtl:ml-0">{getStatusText(order.status)}</span>
        </div>
      </div>

      {/* Details */}
      <div className="space-y-3 mb-4">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-400">
            <User className="w-4 h-4" />
            <span>{language === 'ar' ? 'العميل:' : 'Customer:'}</span>
          </div>
          <span className="text-white font-medium">{getUserDisplayName()}</span>
        </div>
        
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-400">
            <Calendar className="w-4 h-4" />
            <span>{language === 'ar' ? 'التاريخ:' : 'Date:'}</span>
          </div>
          <span className="text-white font-medium">{formatDate(order.created_at)}</span>
        </div>
        
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-400">
            <CreditCard className="w-4 h-4" />
            <span>{language === 'ar' ? 'الدفع:' : 'Payment:'}</span>
          </div>
          <span className="text-white font-medium">{getPaymentMethod()}</span>
        </div>
      </div>

      {/* Price */}
      <div className="text-center mb-4 py-3 bg-secondary/10 rounded-lg border border-secondary/20">
        <div className="text-2xl font-bold text-secondary">
          ${getOrderValue().toFixed(2)}
        </div>
        <div className="text-xs text-gray-400">
          {language === 'ar' ? 'قيمة الطلب' : 'Order Value'}
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between space-x-2 rtl:space-x-reverse">
        {order.status === 'pending' ? (
          <div className="flex space-x-1 rtl:space-x-reverse">
            <button
              onClick={() => onStatusUpdate(order.id, 'completed')}
              className="flex-1 px-3 py-2 bg-emerald-500/20 text-emerald-400 border border-emerald-500/30 rounded-lg hover:bg-emerald-500/30 transition-colors text-xs font-medium"
            >
              <CheckCircle className="w-3 h-3 mx-auto" />
            </button>
            <button
              onClick={() => onStatusUpdate(order.id, 'cancelled')}
              className="flex-1 px-3 py-2 bg-red-500/20 text-red-400 border border-red-500/30 rounded-lg hover:bg-red-500/30 transition-colors text-xs font-medium"
            >
              <XCircle className="w-3 h-3 mx-auto" />
            </button>
          </div>
        ) : (
          <div className="flex-1 text-center text-xs text-gray-400">
            {order.status === 'completed' ? (language === 'ar' ? 'مكتمل' : 'Completed') : 
             order.status === 'cancelled' ? (language === 'ar' ? 'ملغي' : 'Cancelled') : 
             (language === 'ar' ? 'قيد المعالجة' : 'Processing')}
          </div>
        )}
        
        <div className="flex space-x-1 rtl:space-x-reverse">
          <button
            onClick={() => onView(order)}
            className="p-2 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded-lg hover:bg-blue-500/30 transition-colors"
          >
            <Eye className="w-3 h-3" />
          </button>
          <button
            onClick={() => onEdit(order)}
            className="p-2 bg-amber-500/20 text-amber-400 border border-amber-500/30 rounded-lg hover:bg-amber-500/30 transition-colors"
          >
            <Edit className="w-3 h-3" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default OrderCard;
