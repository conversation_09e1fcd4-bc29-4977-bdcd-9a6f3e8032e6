#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class StableNgrokManager {
  constructor() {
    this.processes = [];
    this.tunnelInfo = {};
  }

  log(message, color = 'reset') {
    const timestamp = new Date().toISOString();
    console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
  }

  async killAllNgrok() {
    return new Promise((resolve) => {
      this.log('🔍 Stopping all ngrok processes...', 'yellow');
      
      // Use PowerShell to kill ngrok processes
      const killCommand = 'powershell "Get-Process ngrok -ErrorAction SilentlyContinue | Stop-Process -Force"';
      
      exec(killCommand, (error) => {
        if (error) {
          this.log('⚠️ No ngrok processes found or error killing them', 'yellow');
        } else {
          this.log('✅ All ngrok processes stopped', 'green');
        }
        
        // Wait a moment for cleanup
        setTimeout(resolve, 3000);
      });
    });
  }

  async checkBackendServer() {
    return new Promise((resolve) => {
      this.log('🔍 Checking backend server...', 'blue');
      
      exec('curl -s http://localhost:3001/health', (error, stdout) => {
        if (error) {
          this.log('❌ Backend server not responding', 'red');
          resolve(false);
        } else {
          this.log('✅ Backend server is running', 'green');
          resolve(true);
        }
      });
    });
  }

  async startNgrokTunnel(port, name) {
    return new Promise((resolve) => {
      this.log(`🚇 Starting ${name} tunnel on port ${port}...`, 'cyan');
      
      const ngrokProcess = spawn('npx', ['ngrok', 'http', port.toString(), '--log=stdout'], {
        stdio: ['ignore', 'pipe', 'pipe'],
        shell: true,
        cwd: process.cwd()
      });

      let tunnelUrl = null;
      let startupComplete = false;

      const processOutput = (data) => {
        const output = data.toString();
        
        // Look for tunnel URL
        const urlMatch = output.match(/https:\/\/[a-z0-9]+\.ngrok-free\.app/);
        if (urlMatch && !tunnelUrl) {
          tunnelUrl = urlMatch[0];
          this.log(`🔗 ${name} tunnel URL: ${tunnelUrl}`, 'green');
        }

        // Check for successful startup
        if (output.includes('started tunnel') || 
            output.includes('forwarding') ||
            (tunnelUrl && output.includes('session started'))) {
          
          if (!startupComplete) {
            startupComplete = true;
            this.log(`✅ ${name} tunnel started successfully`, 'green');
            
            this.tunnelInfo[name] = {
              port,
              url: tunnelUrl,
              process: ngrokProcess
            };
            
            resolve({ success: true, url: tunnelUrl, process: ngrokProcess });
          }
        }

        // Check for errors
        if (output.includes('ERROR') || output.includes('failed')) {
          this.log(`❌ ${name} tunnel error: ${output}`, 'red');
          if (!startupComplete) {
            startupComplete = true;
            resolve({ success: false, error: output });
          }
        }
      };

      ngrokProcess.stdout.on('data', processOutput);
      ngrokProcess.stderr.on('data', processOutput);

      ngrokProcess.on('error', (error) => {
        this.log(`❌ Failed to start ${name} tunnel: ${error.message}`, 'red');
        if (!startupComplete) {
          startupComplete = true;
          resolve({ success: false, error: error.message });
        }
      });

      ngrokProcess.on('exit', (code) => {
        this.log(`⚠️ ${name} tunnel process exited with code ${code}`, 'yellow');
        if (!startupComplete) {
          startupComplete = true;
          resolve({ success: false, error: `Process exited with code ${code}` });
        }
      });

      // Timeout after 30 seconds
      setTimeout(() => {
        if (!startupComplete) {
          this.log(`⏰ ${name} tunnel startup timeout`, 'yellow');
          ngrokProcess.kill();
          startupComplete = true;
          resolve({ success: false, error: 'Startup timeout' });
        }
      }, 30000);

      this.processes.push({ name, process: ngrokProcess });
    });
  }

  async waitForTunnelAPI() {
    this.log('⏳ Waiting for ngrok API to be ready...', 'blue');
    
    for (let i = 0; i < 10; i++) {
      try {
        await new Promise((resolve, reject) => {
          exec('curl -s http://127.0.0.1:4040/api/tunnels', (error, stdout) => {
            if (error) {
              reject(error);
            } else {
              try {
                JSON.parse(stdout);
                resolve();
              } catch {
                reject(new Error('Invalid JSON'));
              }
            }
          });
        });
        
        this.log('✅ Ngrok API is ready', 'green');
        return true;
      } catch {
        this.log(`🔄 Attempt ${i + 1}/10 - API not ready yet...`, 'yellow');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    this.log('❌ Ngrok API failed to become ready', 'red');
    return false;
  }

  async getTunnelInfo() {
    return new Promise((resolve) => {
      exec('curl -s http://127.0.0.1:4040/api/tunnels', (error, stdout) => {
        if (error) {
          resolve([]);
          return;
        }

        try {
          const data = JSON.parse(stdout);
          resolve(data.tunnels || []);
        } catch {
          resolve([]);
        }
      });
    });
  }

  async restartNgrokStable() {
    this.log('\n🔄 Starting Stable Ngrok Restart...', 'bright');
    this.log('=' * 60, 'blue');

    try {
      // Step 1: Kill all existing ngrok processes
      await this.killAllNgrok();

      // Step 2: Check backend server
      const backendRunning = await this.checkBackendServer();
      if (!backendRunning) {
        throw new Error('Backend server is not running. Please start it first with: npm run start');
      }

      // Step 3: Start backend tunnel
      this.log('\n📋 Starting backend tunnel...', 'magenta');
      const backendResult = await this.startNgrokTunnel(3001, 'Backend');
      
      if (!backendResult.success) {
        throw new Error(`Failed to start backend tunnel: ${backendResult.error}`);
      }

      // Step 4: Wait for API to be ready
      const apiReady = await this.waitForTunnelAPI();
      if (!apiReady) {
        this.log('⚠️ API not ready, but continuing...', 'yellow');
      }

      // Step 5: Get tunnel information
      this.log('\n📋 Getting tunnel information...', 'magenta');
      const tunnels = await this.getTunnelInfo();
      
      if (tunnels.length > 0) {
        this.log('\n🔗 Active Tunnels:', 'green');
        tunnels.forEach(tunnel => {
          this.log(`  📍 ${tunnel.name || 'Tunnel'}: ${tunnel.public_url}`, 'blue');
        });

        // Save tunnel info
        const tunnelData = {
          timestamp: new Date().toISOString(),
          status: 'active',
          tunnels: tunnels.map(t => ({
            name: t.name || 'command_line',
            public_url: t.public_url,
            local_addr: t.config.addr,
            proto: t.proto
          })),
          testsprite_ready: true
        };

        fs.writeFileSync('ngrok-stable.json', JSON.stringify(tunnelData, null, 2));
        this.log('\n💾 Tunnel info saved to ngrok-stable.json', 'blue');

        // Update TestSprite config
        await this.updateTestSpriteConfig(tunnels[0].public_url);

        this.log('\n🎉 Ngrok restart completed successfully!', 'green');
        this.log('🔗 TestSprite is ready to use with stable tunnels', 'green');
        
        return tunnels[0].public_url;

      } else {
        throw new Error('No tunnels were created');
      }

    } catch (error) {
      this.log(`❌ Ngrok restart failed: ${error.message}`, 'red');
      this.cleanup();
      throw error;
    }
  }

  async updateTestSpriteConfig(baseUrl) {
    try {
      const configPath = 'testsprite-config.json';
      let config = {};
      
      if (fs.existsSync(configPath)) {
        config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      }
      
      config.baseUrl = baseUrl;
      config.timestamp = new Date().toISOString();
      config.status = 'updated';
      
      fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
      this.log(`✅ TestSprite config updated with new URL: ${baseUrl}`, 'green');
      
    } catch (error) {
      this.log(`⚠️ Could not update TestSprite config: ${error.message}`, 'yellow');
    }
  }

  cleanup() {
    this.log('\n🧹 Cleaning up processes...', 'yellow');
    this.processes.forEach(({ name, process }) => {
      if (process && !process.killed) {
        this.log(`🔪 Stopping ${name}...`, 'yellow');
        process.kill();
      }
    });
  }

  async keepAlive() {
    this.log('\n⏳ Keeping tunnels alive... Press Ctrl+C to stop', 'cyan');
    
    // Monitor tunnels every 30 seconds
    const monitor = setInterval(async () => {
      const tunnels = await this.getTunnelInfo();
      if (tunnels.length === 0) {
        this.log('⚠️ No tunnels detected, restarting...', 'yellow');
        clearInterval(monitor);
        await this.restartNgrokStable();
      } else {
        this.log(`✅ ${tunnels.length} tunnel(s) active`, 'green');
      }
    }, 30000);

    // Keep process alive
    process.on('SIGINT', () => {
      clearInterval(monitor);
      this.cleanup();
      process.exit(0);
    });
  }
}

// Main execution
async function main() {
  const manager = new StableNgrokManager();
  
  try {
    const tunnelUrl = await manager.restartNgrokStable();
    
    console.log('\n✅ Ngrok is now stable and ready!');
    console.log(`🔗 TestSprite URL: ${tunnelUrl}`);
    console.log('📁 Check ngrok-stable.json for details');
    
    // Keep alive
    await manager.keepAlive();
    
  } catch (error) {
    console.error(`❌ Failed to restart ngrok: ${error.message}`);
    manager.cleanup();
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error(`❌ Fatal error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = StableNgrokManager;
