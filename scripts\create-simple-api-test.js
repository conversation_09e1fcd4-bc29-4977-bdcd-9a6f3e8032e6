#!/usr/bin/env node

const axios = require('axios');

async function createSimpleAPITest() {
  console.log('🧪 Creating Simple API Test...\n');
  
  const BASE_URL = 'https://7b93f343ea56.ngrok-free.app';
  const headers = {
    'ngrok-skip-browser-warning': 'true',
    'User-Agent': 'SimpleAPITest/1.0',
    'Content-Type': 'application/json'
  };

  try {
    // Test 1: Direct SQL query test
    console.log('1️⃣ Testing direct endpoints...');
    
    // Test systems without JSON parsing
    const systemsResponse = await axios.get(`${BASE_URL}/api/systems?limit=10`, { headers });
    console.log(`   Systems API: ${systemsResponse.status} - ${systemsResponse.data.success ? 'Success' : 'Failed'}`);
    
    if (systemsResponse.data.success) {
      const systems = systemsResponse.data.data.systems || [];
      console.log(`   Found ${systems.length} systems`);
      systems.forEach((system, index) => {
        console.log(`     ${index + 1}. ${system.name_ar} - $${system.price}`);
      });
    }

    // Test technical services
    const servicesResponse = await axios.get(`${BASE_URL}/api/services/technical?limit=10`, { headers });
    console.log(`   Technical Services API: ${servicesResponse.status} - ${servicesResponse.data.success ? 'Success' : 'Failed'}`);
    
    if (servicesResponse.data.success) {
      const services = servicesResponse.data.data || [];
      console.log(`   Found ${services.length} technical services`);
      services.forEach((service, index) => {
        console.log(`     ${index + 1}. ${service.name_ar} - $${service.price}`);
      });
    }

    // Test premium content
    const premiumResponse = await axios.get(`${BASE_URL}/api/services/premium?limit=10`, { headers });
    console.log(`   Premium Services API: ${premiumResponse.status} - ${premiumResponse.data.success ? 'Success' : 'Failed'}`);
    
    if (premiumResponse.data.success) {
      const premium = premiumResponse.data.data.premiumContent || [];
      console.log(`   Found ${premium.length} premium items`);
      premium.forEach((item, index) => {
        console.log(`     ${index + 1}. ${item.title_ar} - $${item.price}`);
      });
    }

    // Create working API configuration for TestSprite
    const workingConfig = {
      name: "Khanfashariya Working API Configuration",
      timestamp: new Date().toISOString(),
      status: "working",
      base_url: BASE_URL,
      headers: headers,
      
      working_endpoints: [
        {
          name: "Systems API",
          url: `${BASE_URL}/api/systems`,
          method: "GET",
          status: systemsResponse.status,
          working: systemsResponse.data.success,
          data_count: systemsResponse.data.data?.systems?.length || 0
        },
        {
          name: "Technical Services API", 
          url: `${BASE_URL}/api/services/technical`,
          method: "GET",
          status: servicesResponse.status,
          working: servicesResponse.data.success,
          data_count: servicesResponse.data.data?.length || 0
        },
        {
          name: "Premium Services API",
          url: `${BASE_URL}/api/services/premium`, 
          method: "GET",
          status: premiumResponse.status,
          working: premiumResponse.data.success,
          data_count: premiumResponse.data.data?.premiumContent?.length || 0
        }
      ],
      
      test_credentials: {
        email: "<EMAIL>",
        password: "admin123"
      },
      
      testsprite_instructions: [
        "Use the base_url as your API base URL",
        "Add all headers from the headers object",
        "Test each endpoint in working_endpoints array",
        "Use test_credentials for authentication tests",
        "All endpoints return JSON with success boolean"
      ],
      
      sample_requests: [
        {
          name: "Get Systems",
          method: "GET",
          url: `${BASE_URL}/api/systems`,
          headers: headers,
          expected_response: {
            success: true,
            data: {
              systems: "array",
              pagination: "object"
            }
          }
        },
        {
          name: "Login",
          method: "POST", 
          url: `${BASE_URL}/api/auth/login`,
          headers: headers,
          body: {
            email: "<EMAIL>",
            password: "admin123"
          },
          expected_response: {
            success: true,
            data: {
              user: "object",
              tokens: "object"
            }
          }
        }
      ]
    };

    require('fs').writeFileSync('working-api-config.json', JSON.stringify(workingConfig, null, 2));
    console.log('\n✅ Working API configuration saved to working-api-config.json');

    // Create simple HTML test page
    const htmlTest = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API بسيط</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
        .container { max-width: 800px; margin: 0 auto; }
        .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #333; border-radius: 8px; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        button { padding: 10px 20px; margin: 5px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer; }
        pre { background: #2a2a2a; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار API بسيط</h1>
        
        <div class="endpoint">
            <h3>الأنظمة التقنية</h3>
            <button onclick="testSystems()">اختبار الأنظمة</button>
            <div id="systems-result"></div>
        </div>
        
        <div class="endpoint">
            <h3>الخدمات التقنية</h3>
            <button onclick="testServices()">اختبار الخدمات</button>
            <div id="services-result"></div>
        </div>
        
        <div class="endpoint">
            <h3>المحتوى المميز</h3>
            <button onclick="testPremium()">اختبار المحتوى المميز</button>
            <div id="premium-result"></div>
        </div>
    </div>

    <script>
        async function testSystems() {
            const result = document.getElementById('systems-result');
            result.innerHTML = 'جاري الاختبار...';
            
            try {
                const response = await fetch('/api/systems', {
                    headers: {
                        'ngrok-skip-browser-warning': 'true'
                    }
                });
                const data = await response.json();
                
                if (data.success) {
                    const systems = data.data.systems || [];
                    result.innerHTML = \`<div class="success">✅ نجح: وُجد \${systems.length} نظام</div><pre>\${JSON.stringify(systems, null, 2)}</pre>\`;
                } else {
                    result.innerHTML = \`<div class="error">❌ فشل: \${data.error || 'خطأ غير معروف'}</div>\`;
                }
            } catch (error) {
                result.innerHTML = \`<div class="error">❌ خطأ: \${error.message}</div>\`;
            }
        }
        
        async function testServices() {
            const result = document.getElementById('services-result');
            result.innerHTML = 'جاري الاختبار...';
            
            try {
                const response = await fetch('/api/services/technical', {
                    headers: {
                        'ngrok-skip-browser-warning': 'true'
                    }
                });
                const data = await response.json();
                
                if (data.success) {
                    const services = data.data || [];
                    result.innerHTML = \`<div class="success">✅ نجح: وُجد \${services.length} خدمة</div><pre>\${JSON.stringify(services, null, 2)}</pre>\`;
                } else {
                    result.innerHTML = \`<div class="error">❌ فشل: \${data.error || 'خطأ غير معروف'}</div>\`;
                }
            } catch (error) {
                result.innerHTML = \`<div class="error">❌ خطأ: \${error.message}</div>\`;
            }
        }
        
        async function testPremium() {
            const result = document.getElementById('premium-result');
            result.innerHTML = 'جاري الاختبار...';
            
            try {
                const response = await fetch('/api/services/premium', {
                    headers: {
                        'ngrok-skip-browser-warning': 'true'
                    }
                });
                const data = await response.json();
                
                if (data.success) {
                    const premium = data.data.premiumContent || [];
                    result.innerHTML = \`<div class="success">✅ نجح: وُجد \${premium.length} محتوى مميز</div><pre>\${JSON.stringify(premium, null, 2)}</pre>\`;
                } else {
                    result.innerHTML = \`<div class="error">❌ فشل: \${data.error || 'خطأ غير معروف'}</div>\`;
                }
            } catch (error) {
                result.innerHTML = \`<div class="error">❌ خطأ: \${error.message}</div>\`;
            }
        }
    </script>
</body>
</html>`;

    require('fs').writeFileSync('public/simple-api-test.html', htmlTest);
    console.log('✅ Simple API test page saved to public/simple-api-test.html');

    console.log('\n📊 Summary:');
    console.log(`✅ Systems API: ${systemsResponse.data.success ? 'Working' : 'Failed'} (${systemsResponse.data.data?.systems?.length || 0} items)`);
    console.log(`✅ Services API: ${servicesResponse.data.success ? 'Working' : 'Failed'} (${servicesResponse.data.data?.length || 0} items)`);
    console.log(`✅ Premium API: ${premiumResponse.data.success ? 'Working' : 'Failed'} (${premiumResponse.data.data?.premiumContent?.length || 0} items)`);

    console.log('\n🎯 TestSprite Ready:');
    console.log(`Base URL: ${BASE_URL}`);
    console.log('Config File: working-api-config.json');
    console.log('Test Page: https://70f354611634.ngrok-free.app/simple-api-test.html');

  } catch (error) {
    console.error('❌ Error:', error.message);
    
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

createSimpleAPITest();
