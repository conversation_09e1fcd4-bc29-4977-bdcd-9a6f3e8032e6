{"timestamp": "2025-07-20T00:08:22.573Z", "schema": {"system_services": {"columns": [{"field": "id", "type": "<PERSON><PERSON><PERSON>(36)", "null": "NO", "key": "PRI", "default": null, "extra": ""}, {"field": "name_ar", "type": "text", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "name_en", "type": "text", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "description_ar", "type": "text", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "description_en", "type": "text", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "price", "type": "decimal(10,2)", "null": "NO", "key": "", "default": "0.00", "extra": ""}, {"field": "category", "type": "<PERSON><PERSON><PERSON>(100)", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "status", "type": "enum('active','inactive')", "null": "YES", "key": "", "default": "active", "extra": ""}, {"field": "created_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": ""}, {"field": "updated_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "on update CURRENT_TIMESTAMP"}, {"field": "type", "type": "enum('regular','plugin')", "null": "YES", "key": "", "default": "regular", "extra": ""}, {"field": "is_premium_addon", "type": "tinyint(1)", "null": "YES", "key": "", "default": "0", "extra": ""}, {"field": "features_ar", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "features_en", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "tech_specs_ar", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "tech_specs_en", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "video_url", "type": "var<PERSON><PERSON>(500)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "image_url", "type": "var<PERSON><PERSON>(500)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "gallery_images", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "download_url", "type": "var<PERSON><PERSON>(500)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "version", "type": "<PERSON><PERSON><PERSON>(50)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "file_size", "type": "<PERSON><PERSON><PERSON>(50)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "requirements_ar", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "requirements_en", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "installation_guide_ar", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "installation_guide_en", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "featured", "type": "tinyint(1)", "null": "YES", "key": "", "default": "0", "extra": ""}, {"field": "sort_order", "type": "int(11)", "null": "YES", "key": "", "default": "0", "extra": ""}, {"field": "download_count", "type": "int(11)", "null": "YES", "key": "", "default": "0", "extra": ""}, {"field": "rating", "type": "decimal(3,2)", "null": "YES", "key": "", "default": "0.00", "extra": ""}, {"field": "rating_count", "type": "int(11)", "null": "YES", "key": "", "default": "0", "extra": ""}], "indexes": [{"name": "PRIMARY", "column": "id", "unique": true}], "stats": {"rows": 9, "dataLength": 10216, "indexLength": 2048}}, "technical_services": {"columns": [{"field": "id", "type": "<PERSON><PERSON><PERSON>(36)", "null": "NO", "key": "PRI", "default": null, "extra": ""}, {"field": "name_ar", "type": "text", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "name_en", "type": "text", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "description_ar", "type": "text", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "description_en", "type": "text", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "price", "type": "decimal(10,2)", "null": "NO", "key": "", "default": "0.00", "extra": ""}, {"field": "category", "type": "<PERSON><PERSON><PERSON>(100)", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "status", "type": "enum('active','inactive')", "null": "YES", "key": "", "default": "active", "extra": ""}, {"field": "created_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": ""}, {"field": "updated_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "on update CURRENT_TIMESTAMP"}, {"field": "service_type", "type": "enum('development','consultation','support','customization')", "null": "YES", "key": "", "default": "development", "extra": ""}, {"field": "is_premium_addon", "type": "tinyint(1)", "null": "YES", "key": "", "default": "0", "extra": ""}, {"field": "premium_price", "type": "decimal(10,2)", "null": "YES", "key": "", "default": "0.00", "extra": ""}, {"field": "subscription_type", "type": "enum('none','monthly','yearly')", "null": "YES", "key": "", "default": "none", "extra": ""}, {"field": "features_ar", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "features_en", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "tech_specs_ar", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "tech_specs_en", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "delivery_time_ar", "type": "<PERSON><PERSON><PERSON>(100)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "delivery_time_en", "type": "<PERSON><PERSON><PERSON>(100)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "video_url", "type": "var<PERSON><PERSON>(500)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "image_url", "type": "var<PERSON><PERSON>(500)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "gallery_images", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "requirements_ar", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "requirements_en", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "process_steps_ar", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "process_steps_en", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "featured", "type": "tinyint(1)", "null": "YES", "key": "", "default": "0", "extra": ""}, {"field": "sort_order", "type": "int(11)", "null": "YES", "key": "", "default": "0", "extra": ""}, {"field": "order_count", "type": "int(11)", "null": "YES", "key": "", "default": "0", "extra": ""}, {"field": "rating", "type": "decimal(3,2)", "null": "YES", "key": "", "default": "0.00", "extra": ""}, {"field": "rating_count", "type": "int(11)", "null": "YES", "key": "", "default": "0", "extra": ""}], "indexes": [{"name": "PRIMARY", "column": "id", "unique": true}], "stats": {"rows": 11, "dataLength": 9896, "indexLength": 2048}}, "users": {"columns": [{"field": "id", "type": "<PERSON><PERSON><PERSON>(36)", "null": "NO", "key": "PRI", "default": null, "extra": ""}, {"field": "email", "type": "<PERSON><PERSON><PERSON>(191)", "null": "NO", "key": "UNI", "default": null, "extra": ""}, {"field": "username", "type": "<PERSON><PERSON><PERSON>(50)", "null": "NO", "key": "UNI", "default": null, "extra": ""}, {"field": "full_name", "type": "<PERSON><PERSON><PERSON>(191)", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "role", "type": "enum('user','admin')", "null": "YES", "key": "", "default": "user", "extra": ""}, {"field": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "status", "type": "enum('active','inactive','suspended')", "null": "YES", "key": "", "default": "active", "extra": ""}, {"field": "created_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": ""}, {"field": "updated_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "on update CURRENT_TIMESTAMP"}, {"field": "email_verified", "type": "tinyint(1)", "null": "YES", "key": "", "default": "0", "extra": ""}, {"field": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "avatar_url", "type": "var<PERSON><PERSON>(500)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "last_login", "type": "timestamp", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "login_count", "type": "int(11)", "null": "YES", "key": "", "default": "0", "extra": ""}, {"field": "password", "type": "<PERSON><PERSON><PERSON>(255)", "null": "NO", "key": "", "default": "", "extra": ""}, {"field": "email_verified_at", "type": "timestamp", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "remember_token", "type": "<PERSON><PERSON><PERSON>(100)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "two_factor_secret", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "two_factor_recovery_codes", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "profile_photo_path", "type": "<PERSON><PERSON><PERSON>(2048)", "null": "YES", "key": "", "default": null, "extra": ""}], "indexes": [{"name": "PRIMARY", "column": "id", "unique": true}, {"name": "email", "column": "email", "unique": true}, {"name": "username", "column": "username", "unique": true}], "stats": {"rows": 4, "dataLength": 740, "indexLength": 10240}}, "orders": {"columns": [{"field": "id", "type": "<PERSON><PERSON><PERSON>(36)", "null": "NO", "key": "PRI", "default": null, "extra": ""}, {"field": "user_id", "type": "<PERSON><PERSON><PERSON>(36)", "null": "NO", "key": "MUL", "default": null, "extra": ""}, {"field": "order_number", "type": "<PERSON><PERSON><PERSON>(50)", "null": "NO", "key": "UNI", "default": null, "extra": ""}, {"field": "order_type", "type": "enum('system_service','technical_service','premium_content','premium_package')", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "item_id", "type": "<PERSON><PERSON><PERSON>(36)", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "item_name_ar", "type": "text", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "item_name_en", "type": "text", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "final_price", "type": "decimal(10,2)", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "status", "type": "enum('pending','completed','cancelled')", "null": "YES", "key": "", "default": "pending", "extra": ""}, {"field": "created_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": ""}, {"field": "updated_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "on update CURRENT_TIMESTAMP"}, {"field": "quantity", "type": "int(11)", "null": "YES", "key": "", "default": "1", "extra": ""}, {"field": "unit_price", "type": "decimal(10,2)", "null": "NO", "key": "", "default": "0.00", "extra": ""}, {"field": "total_price", "type": "decimal(10,2)", "null": "NO", "key": "", "default": "0.00", "extra": ""}, {"field": "discount_amount", "type": "decimal(10,2)", "null": "YES", "key": "", "default": "0.00", "extra": ""}, {"field": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "null": "YES", "key": "", "default": "USD", "extra": ""}, {"field": "payment_status", "type": "enum('pending','paid','failed','refunded')", "null": "YES", "key": "", "default": "pending", "extra": ""}, {"field": "payment_method", "type": "<PERSON><PERSON><PERSON>(50)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "payment_reference", "type": "<PERSON><PERSON><PERSON>(255)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "notes_ar", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "notes_en", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "admin_notes", "type": "text", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "delivery_date", "type": "date", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "completion_date", "type": "timestamp", "null": "YES", "key": "", "default": null, "extra": ""}], "indexes": [{"name": "PRIMARY", "column": "id", "unique": true}, {"name": "order_number", "column": "order_number", "unique": true}, {"name": "user_id", "column": "user_id", "unique": false}], "stats": {"rows": 9, "dataLength": 2460, "indexLength": 4096}}, "premium_content": {"columns": [{"field": "id", "type": "<PERSON><PERSON><PERSON>(36)", "null": "NO", "key": "PRI", "default": null, "extra": ""}, {"field": "title_ar", "type": "text", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "title_en", "type": "text", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "description_ar", "type": "text", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "description_en", "type": "text", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "price", "type": "decimal(10,2)", "null": "NO", "key": "", "default": "0.00", "extra": ""}, {"field": "category", "type": "<PERSON><PERSON><PERSON>(100)", "null": "NO", "key": "", "default": null, "extra": ""}, {"field": "features_ar", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "features_en", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "video_url", "type": "var<PERSON><PERSON>(500)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "image_url", "type": "var<PERSON><PERSON>(500)", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "gallery_images", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "included_systems", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "included_services", "type": "json", "null": "YES", "key": "", "default": null, "extra": ""}, {"field": "status", "type": "enum('active','inactive','draft')", "null": "YES", "key": "", "default": "active", "extra": ""}, {"field": "featured", "type": "tinyint(1)", "null": "YES", "key": "", "default": "1", "extra": ""}, {"field": "sort_order", "type": "int(11)", "null": "YES", "key": "", "default": "0", "extra": ""}, {"field": "purchase_count", "type": "int(11)", "null": "YES", "key": "", "default": "0", "extra": ""}, {"field": "rating", "type": "decimal(3,2)", "null": "YES", "key": "", "default": "0.00", "extra": ""}, {"field": "rating_count", "type": "int(11)", "null": "YES", "key": "", "default": "0", "extra": ""}, {"field": "created_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": ""}, {"field": "updated_at", "type": "timestamp", "null": "YES", "key": "", "default": "CURRENT_TIMESTAMP", "extra": "on update CURRENT_TIMESTAMP"}], "indexes": [{"name": "PRIMARY", "column": "id", "unique": true}], "stats": {"rows": 1, "dataLength": 672, "indexLength": 2048}}}, "dataQuality": {"system_services": {"error": "Unexpected token 'h', \"https://im\"... is not valid JSON"}}, "performance": {"potential_indexes": [{"table_name": "system_services", "column_name": "category"}, {"table_name": "system_services", "column_name": "status"}, {"table_name": "system_services", "column_name": "type"}, {"table_name": "system_services", "column_name": "featured"}, {"table_name": "technical_services", "column_name": "category"}, {"table_name": "technical_services", "column_name": "status"}, {"table_name": "technical_services", "column_name": "featured"}], "table_sizes": [{"table_name": "activity_logs", "size_mb": "0.09"}, {"table_name": "user_sessions", "size_mb": "0.03"}, {"table_name": "orders", "size_mb": "0.01"}, {"table_name": "users", "size_mb": "0.01"}, {"table_name": "inbox_messages", "size_mb": "0.01"}, {"table_name": "user_services", "size_mb": "0.01"}, {"table_name": "technical_services", "size_mb": "0.01"}, {"table_name": "system_services", "size_mb": "0.01"}, {"table_name": "premium_editions", "size_mb": "0.01"}, {"table_name": "file_uploads", "size_mb": "0.00"}, {"table_name": "contact_messages", "size_mb": "0.00"}, {"table_name": "subscriptions", "size_mb": "0.00"}, {"table_name": "settings", "size_mb": "0.00"}, {"table_name": "premium_packages", "size_mb": "0.00"}, {"table_name": "premium_content", "size_mb": "0.00"}]}, "migrations": []}