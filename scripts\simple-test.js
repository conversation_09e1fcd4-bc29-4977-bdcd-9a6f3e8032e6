#!/usr/bin/env node

/**
 * Simple Test Script
 * 
 * Quick test to verify the website is working
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001';
const FRONTEND_URL = 'http://localhost:5173';

async function quickTest() {
  console.log('🧪 Quick Website Test');
  console.log('====================\n');
  
  // Test API
  console.log('🔌 Testing API...');
  try {
    const systemsResponse = await axios.get(`${API_BASE_URL}/api/systems`);
    if (systemsResponse.data.success && systemsResponse.data.data.systems) {
      console.log(`✅ API Working: ${systemsResponse.data.data.systems.length} systems loaded`);
    } else {
      console.log('❌ API Failed: Invalid response');
      return false;
    }
  } catch (error) {
    console.log(`❌ API Failed: ${error.message}`);
    return false;
  }
  
  // Test Frontend
  console.log('🌐 Testing Frontend...');
  try {
    const frontendResponse = await axios.get(FRONTEND_URL);
    if (frontendResponse.status === 200) {
      console.log('✅ Frontend Working: Page loads successfully');
    } else {
      console.log('❌ Frontend Failed: Invalid status');
      return false;
    }
  } catch (error) {
    console.log(`❌ Frontend Failed: ${error.message}`);
    return false;
  }
  
  console.log('\n🎉 Basic tests passed!');
  console.log('🌐 Visit: http://localhost:5173');
  console.log('🔧 Admin: http://localhost:5173/admin');
  
  return true;
}

quickTest().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Test error:', error);
  process.exit(1);
});
