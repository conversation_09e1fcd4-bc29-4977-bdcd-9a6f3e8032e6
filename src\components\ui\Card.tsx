import React from 'react';
import { designTokens } from '../../styles/designTokens';

interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  size?: 'sm' | 'md' | 'lg';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
  hoverable?: boolean;
  loading?: boolean;
}

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

interface CardBodyProps {
  children: React.ReactNode;
  className?: string;
}

interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Card component - A flexible container for content
 * 
 * Features:
 * - Multiple variants (default, elevated, outlined, filled)
 * - Responsive sizing options
 * - Customizable padding
 * - Hover effects for interactive cards
 * - Loading state support
 * - Compound components (Header, Body, Footer)
 * - RTL layout support
 * - Accessibility features
 */
const Card: React.FC<CardProps> & {
  Header: React.FC<CardHeaderProps>;
  Body: React.FC<CardBodyProps>;
  Footer: React.FC<CardFooterProps>;
} = ({
  children,
  variant = 'default',
  size = 'md',
  padding = 'md',
  className = '',
  onClick,
  hoverable = false,
  loading = false
}) => {
  const baseClasses = [
    'relative',
    'rounded-lg',
    'transition-all',
    'duration-300',
    'overflow-hidden'
  ].join(' ');

  const variantClasses = {
    default: [
      'bg-gradient-to-br',
      'from-gray-800/80',
      'to-gray-900/80',
      'border',
      'border-gray-600/40',
      'backdrop-blur-sm'
    ].join(' '),

    elevated: [
      'bg-gradient-to-br',
      'from-gray-800/90',
      'to-gray-900/90',
      'shadow-lg',
      'border',
      'border-gray-600/50',
      'backdrop-blur-sm'
    ].join(' '),

    outlined: [
      'bg-gray-800/60',
      'border-2',
      'border-accent-500/40',
      'hover:border-accent-500/60',
      'backdrop-blur-sm'
    ].join(' '),

    filled: [
      'bg-gradient-to-br',
      'from-secondary-500/10',
      'to-accent-500/10',
      'border',
      'border-secondary-500/20',
      'backdrop-blur-sm'
    ].join(' ')
  };

  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg'
  };

  const paddingClasses = {
    none: 'p-0',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  const hoverClasses = hoverable || onClick ? [
    'hover:shadow-xl',
    'hover:shadow-secondary-500/20',
    'hover:-translate-y-1',
    'hover:scale-105',
    'cursor-pointer',
    'active:scale-100'
  ].join(' ') : '';

  const interactiveClasses = onClick ? [
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-accent-500',
    'focus:ring-offset-2',
    'focus:ring-offset-background-primary'
  ].join(' ') : '';

  return (
    <div
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${paddingClasses[padding]}
        ${hoverClasses}
        ${interactiveClasses}
        ${className}
      `}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      } : undefined}
    >
      {/* Loading overlay */}
      {loading && (
        <div className="absolute inset-0 bg-background-primary/80 backdrop-blur-sm flex items-center justify-center z-10">
          <div className="w-8 h-8 border-2 border-secondary-500 border-t-transparent rounded-full animate-spin" />
        </div>
      )}

      {/* Content */}
      <div className="relative z-0">
        {children}
      </div>

      {/* Hover glow effect */}
      {(hoverable || onClick) && (
        <div className="absolute inset-0 bg-gradient-to-r from-secondary-500/0 via-secondary-500/5 to-accent-500/0 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
      )}
    </div>
  );
};

/**
 * Card Header component
 */
const CardHeader: React.FC<CardHeaderProps> = ({ children, className = '' }) => {
  return (
    <div className={`border-b border-accent-500/20 pb-4 mb-4 ${className}`}>
      {children}
    </div>
  );
};

/**
 * Card Body component
 */
const CardBody: React.FC<CardBodyProps> = ({ children, className = '' }) => {
  return (
    <div className={`flex-1 ${className}`}>
      {children}
    </div>
  );
};

/**
 * Card Footer component
 */
const CardFooter: React.FC<CardFooterProps> = ({ children, className = '' }) => {
  return (
    <div className={`border-t border-accent-500/20 pt-4 mt-4 ${className}`}>
      {children}
    </div>
  );
};

// Attach compound components
Card.Header = CardHeader;
Card.Body = CardBody;
Card.Footer = CardFooter;

export default Card;
