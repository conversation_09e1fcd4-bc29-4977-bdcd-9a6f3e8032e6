const axios = require('axios');

async function testAdminEndpoints() {
  try {
    console.log('🔐 Logging in as admin...');
    
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.data.tokens.accessToken;
    console.log('✅ Login successful!');
    
    // Test admin dashboard
    console.log('\n📊 Testing admin dashboard...');
    const dashboardResponse = await axios.get('http://localhost:3001/api/admin/dashboard', {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ Dashboard data loaded:', Object.keys(dashboardResponse.data));
    
    // Test systems admin endpoint
    console.log('\n🖥️ Testing systems admin endpoint...');
    const systemsResponse = await axios.get('http://localhost:3001/api/systems/admin', {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ Systems loaded:', systemsResponse.data.length, 'systems');
    
    // Test technical services admin endpoint
    console.log('\n🛠️ Testing technical services admin endpoint...');
    const servicesResponse = await axios.get('http://localhost:3001/api/services/admin/technical', {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ Technical services loaded:', servicesResponse.data.length, 'services');
    
    console.log('\n🎉 All admin endpoints working correctly!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testAdminEndpoints();
