/**
 * Order Status Workflow Management
 * Handles order status transitions and business logic
 */

export type OrderStatus = 
  | 'pending' 
  | 'confirmed' 
  | 'in_progress' 
  | 'testing' 
  | 'completed' 
  | 'cancelled' 
  | 'refunded' 
  | 'on_hold' 
  | 'expired';

export type OrderPriority = 'low' | 'medium' | 'high' | 'urgent';

// Define valid status transitions
export const STATUS_TRANSITIONS: Record<OrderStatus, OrderStatus[]> = {
  pending: ['confirmed', 'cancelled', 'expired'],
  confirmed: ['in_progress', 'on_hold', 'cancelled'],
  in_progress: ['testing', 'completed', 'on_hold', 'cancelled'],
  testing: ['completed', 'in_progress', 'on_hold'],
  completed: ['refunded'], // Only refunds allowed after completion
  cancelled: [], // Terminal state
  refunded: [], // Terminal state
  on_hold: ['confirmed', 'in_progress', 'cancelled'],
  expired: ['confirmed'] // Can be reactivated if customer pays
};

// Status progression weights for progress calculation
export const STATUS_PROGRESS: Record<OrderStatus, number> = {
  pending: 0,
  confirmed: 10,
  in_progress: 50,
  testing: 80,
  completed: 100,
  cancelled: 0,
  refunded: 0,
  on_hold: 0,
  expired: 0
};

// Status colors for UI
export const STATUS_COLORS: Record<OrderStatus, string> = {
  pending: 'bg-amber-500/30 text-amber-200 border-amber-400/50',
  confirmed: 'bg-cyan-500/30 text-cyan-200 border-cyan-400/50',
  in_progress: 'bg-blue-500/30 text-blue-200 border-blue-400/50',
  testing: 'bg-purple-500/30 text-purple-200 border-purple-400/50',
  completed: 'bg-emerald-500/30 text-emerald-200 border-emerald-400/50',
  cancelled: 'bg-red-500/30 text-red-200 border-red-400/50',
  refunded: 'bg-orange-500/30 text-orange-200 border-orange-400/50',
  on_hold: 'bg-gray-500/30 text-gray-200 border-gray-400/50',
  expired: 'bg-red-700/30 text-red-200 border-red-600/50'
};

// Status translations
export const STATUS_TRANSLATIONS = {
  ar: {
    pending: 'معلق',
    confirmed: 'مؤكد',
    in_progress: 'قيد التنفيذ',
    testing: 'قيد الاختبار',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    refunded: 'مسترد',
    on_hold: 'معلق مؤقتاً',
    expired: 'منتهي الصلاحية'
  },
  en: {
    pending: 'Pending',
    confirmed: 'Confirmed',
    in_progress: 'In Progress',
    testing: 'Testing',
    completed: 'Completed',
    cancelled: 'Cancelled',
    refunded: 'Refunded',
    on_hold: 'On Hold',
    expired: 'Expired'
  }
};

// Priority translations
export const PRIORITY_TRANSLATIONS = {
  ar: {
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    urgent: 'عاجل'
  },
  en: {
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    urgent: 'Urgent'
  }
};

// Priority colors
export const PRIORITY_COLORS: Record<OrderPriority, string> = {
  low: 'text-green-400',
  medium: 'text-yellow-400',
  high: 'text-orange-400',
  urgent: 'text-red-400'
};

/**
 * Check if a status transition is valid
 */
export function isValidStatusTransition(currentStatus: OrderStatus, newStatus: OrderStatus): boolean {
  return STATUS_TRANSITIONS[currentStatus]?.includes(newStatus) || false;
}

/**
 * Get next possible statuses for current status
 */
export function getNextPossibleStatuses(currentStatus: OrderStatus): OrderStatus[] {
  return STATUS_TRANSITIONS[currentStatus] || [];
}

/**
 * Calculate progress percentage based on status
 */
export function calculateProgressPercentage(status: OrderStatus): number {
  return STATUS_PROGRESS[status] || 0;
}

/**
 * Get status color class
 */
export function getStatusColor(status: OrderStatus): string {
  return STATUS_COLORS[status] || STATUS_COLORS.pending;
}

/**
 * Get status text in specified language
 */
export function getStatusText(status: OrderStatus, language: 'ar' | 'en'): string {
  return STATUS_TRANSLATIONS[language][status] || status;
}

/**
 * Get priority text in specified language
 */
export function getPriorityText(priority: OrderPriority, language: 'ar' | 'en'): string {
  return PRIORITY_TRANSLATIONS[language][priority] || priority;
}

/**
 * Get priority color class
 */
export function getPriorityColor(priority: OrderPriority): string {
  return PRIORITY_COLORS[priority] || PRIORITY_COLORS.medium;
}

/**
 * Determine if status is terminal (no further transitions allowed)
 */
export function isTerminalStatus(status: OrderStatus): boolean {
  return STATUS_TRANSITIONS[status].length === 0;
}

/**
 * Determine if status indicates active work
 */
export function isActiveStatus(status: OrderStatus): boolean {
  return ['confirmed', 'in_progress', 'testing'].includes(status);
}

/**
 * Determine if status indicates completion
 */
export function isCompletedStatus(status: OrderStatus): boolean {
  return status === 'completed';
}

/**
 * Determine if status indicates cancellation
 */
export function isCancelledStatus(status: OrderStatus): boolean {
  return ['cancelled', 'refunded', 'expired'].includes(status);
}

/**
 * Get estimated completion time based on order type and priority
 */
export function getEstimatedCompletionTime(
  orderType: string,
  priority: OrderPriority,
  hasAddons: boolean = false
): number {
  // Base completion times in hours
  const baseTimes: Record<string, number> = {
    system_service: 24,
    technical_service: 48,
    premium_content: 72,
    premium_package: 120,
    custom_request: 168
  };

  let baseTime = baseTimes[orderType] || 72;

  // Adjust for add-ons
  if (hasAddons) {
    baseTime *= 1.5;
  }

  // Adjust for priority
  const priorityMultipliers: Record<OrderPriority, number> = {
    urgent: 0.5,
    high: 0.7,
    medium: 1.0,
    low: 1.5
  };

  return baseTime * priorityMultipliers[priority];
}

/**
 * Generate status update message
 */
export function generateStatusUpdateMessage(
  oldStatus: OrderStatus,
  newStatus: OrderStatus,
  language: 'ar' | 'en',
  orderNumber: string
): { subject: string; message: string } {
  const statusText = getStatusText(newStatus, language);
  
  if (language === 'ar') {
    return {
      subject: `تحديث حالة الطلب #${orderNumber}`,
      message: `تم تحديث حالة طلبك #${orderNumber} إلى: ${statusText}`
    };
  } else {
    return {
      subject: `Order #${orderNumber} Status Update`,
      message: `Your order #${orderNumber} status has been updated to: ${statusText}`
    };
  }
}

/**
 * Validate status transition with business rules
 */
export function validateStatusTransition(
  currentStatus: OrderStatus,
  newStatus: OrderStatus,
  orderData: any
): { valid: boolean; reason?: string } {
  // Check if transition is allowed
  if (!isValidStatusTransition(currentStatus, newStatus)) {
    return {
      valid: false,
      reason: `Invalid transition from ${currentStatus} to ${newStatus}`
    };
  }

  // Business rule: Cannot complete without payment
  if (newStatus === 'completed' && orderData.payment_status !== 'paid') {
    return {
      valid: false,
      reason: 'Cannot complete order without payment confirmation'
    };
  }

  // Business rule: Cannot refund unpaid orders
  if (newStatus === 'refunded' && orderData.payment_status !== 'paid') {
    return {
      valid: false,
      reason: 'Cannot refund unpaid orders'
    };
  }

  return { valid: true };
}

/**
 * Get recommended next action for order status
 */
export function getRecommendedNextAction(
  status: OrderStatus,
  orderData: any,
  language: 'ar' | 'en'
): string {
  const actions = {
    ar: {
      pending: 'تأكيد الطلب ومراجعة التفاصيل',
      confirmed: 'بدء العمل على الطلب',
      in_progress: 'متابعة التطوير والتنفيذ',
      testing: 'إجراء الاختبارات النهائية',
      completed: 'تسليم المنتج للعميل',
      on_hold: 'حل المشاكل المعلقة',
      cancelled: 'لا توجد إجراءات مطلوبة',
      refunded: 'لا توجد إجراءات مطلوبة',
      expired: 'التواصل مع العميل لتجديد الطلب'
    },
    en: {
      pending: 'Confirm order and review details',
      confirmed: 'Start working on the order',
      in_progress: 'Continue development and implementation',
      testing: 'Perform final testing',
      completed: 'Deliver product to customer',
      on_hold: 'Resolve pending issues',
      cancelled: 'No action required',
      refunded: 'No action required',
      expired: 'Contact customer to renew order'
    }
  };

  return actions[language][status] || '';
}
