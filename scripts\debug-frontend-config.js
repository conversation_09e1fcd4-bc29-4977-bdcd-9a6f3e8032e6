#!/usr/bin/env node

const axios = require('axios');

async function debugFrontendConfig() {
  console.log('🔍 Debugging Frontend Configuration...\n');
  
  const headers = {
    'ngrok-skip-browser-warning': 'true',
    'User-Agent': 'DebugScript/1.0'
  };

  try {
    // Test 1: Check if Frontend loads
    console.log('1️⃣ Testing Frontend Loading...');
    const frontendResponse = await axios.get('https://70f354611634.ngrok-free.app', { headers });
    
    if (frontendResponse.status === 200) {
      console.log('✅ Frontend loads successfully');
      
      // Check for environment variables in the HTML
      const html = frontendResponse.data;
      if (html.includes('import.meta.env')) {
        console.log('✅ Environment variables are being used');
      } else {
        console.log('⚠️ No environment variables detected in HTML');
      }
    }

    // Test 2: Check Frontend API proxy
    console.log('\n2️⃣ Testing Frontend API Proxy...');
    try {
      const proxyResponse = await axios.get('https://70f354611634.ngrok-free.app/api/systems', { 
        headers,
        timeout: 10000
      });
      
      if (proxyResponse.data.success) {
        console.log('✅ Frontend proxy working correctly');
        console.log(`   Found ${proxyResponse.data.data.systems.length} systems via proxy`);
      } else {
        console.log('❌ Frontend proxy not returning expected data');
      }
    } catch (proxyError) {
      console.log('❌ Frontend proxy error:', proxyError.message);
      
      if (proxyError.response) {
        console.log('   Status:', proxyError.response.status);
        console.log('   Data:', proxyError.response.data);
      }
    }

    // Test 3: Direct Backend API
    console.log('\n3️⃣ Testing Direct Backend API...');
    const backendResponse = await axios.get('https://7b93f343ea56.ngrok-free.app/api/systems', { headers });
    
    if (backendResponse.data.success) {
      console.log('✅ Direct backend API working');
      console.log(`   Found ${backendResponse.data.data.systems.length} systems directly`);
    }

    // Test 4: Check CORS headers
    console.log('\n4️⃣ Testing CORS Configuration...');
    try {
      const corsResponse = await axios.options('https://7b93f343ea56.ngrok-free.app/api/systems', { 
        headers: {
          ...headers,
          'Origin': 'https://70f354611634.ngrok-free.app'
        }
      });
      
      const corsHeaders = corsResponse.headers;
      console.log('✅ CORS headers present:');
      console.log('   Access-Control-Allow-Origin:', corsHeaders['access-control-allow-origin']);
      console.log('   Access-Control-Allow-Methods:', corsHeaders['access-control-allow-methods']);
    } catch (corsError) {
      console.log('⚠️ CORS test failed:', corsError.message);
    }

    // Test 5: Check if data appears in Frontend HTML
    console.log('\n5️⃣ Checking if data appears in Frontend...');
    
    // Wait a bit for Frontend to load data
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    try {
      const frontendWithData = await axios.get('https://70f354611634.ngrok-free.app', { 
        headers,
        timeout: 15000
      });
      
      const html = frontendWithData.data;
      
      // Check for system names in HTML
      if (html.includes('نظام حروب الروابط') || html.includes('Guild War')) {
        console.log('✅ System data appears in Frontend HTML');
      } else {
        console.log('❌ System data NOT found in Frontend HTML');
        console.log('   This suggests Frontend is not fetching data correctly');
      }
      
      // Check for loading states
      if (html.includes('loading') || html.includes('تحميل')) {
        console.log('⚠️ Frontend might be stuck in loading state');
      }
      
    } catch (error) {
      console.log('❌ Error checking Frontend data:', error.message);
    }

    console.log('\n📋 Diagnosis:');
    console.log('1. Backend API: ✅ Working with real data');
    console.log('2. Frontend Loading: ✅ Working');
    console.log('3. Frontend Proxy: Need to check');
    console.log('4. Data Display: Need to verify');
    
    console.log('\n🔧 Possible Issues:');
    console.log('- Frontend environment variables not loaded correctly');
    console.log('- Vite proxy not configured for ngrok');
    console.log('- Frontend components using hardcoded localhost URLs');
    console.log('- CORS issues between Frontend and Backend');
    
    console.log('\n💡 Solutions:');
    console.log('1. Restart Frontend with correct environment variables');
    console.log('2. Update Vite config to use ngrok URLs');
    console.log('3. Check Frontend component API calls');
    console.log('4. Verify CORS configuration in Backend');

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugFrontendConfig();
