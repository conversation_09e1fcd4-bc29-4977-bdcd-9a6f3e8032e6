#!/usr/bin/env node

/**
 * Check Table Structure Script
 * 
 * Checks the structure of all tables to understand what columns exist
 */

const mysql = require('mysql2/promise');

const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khan<PERSON><PERSON><PERSON>_db',
  charset: 'utf8mb4'
};

async function checkTableStructure() {
  console.log('🔍 Checking Table Structure');
  console.log('===========================\n');
  
  let connection;
  
  try {
    connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ Connected to MySQL database\n');
    
    const tables = ['system_services', 'technical_services', 'premium_content'];
    
    for (const table of tables) {
      console.log(`📊 Table: ${table}`);
      console.log('─'.repeat(50));
      
      try {
        const [columns] = await connection.execute(`DESCRIBE ${table}`);
        
        columns.forEach(column => {
          console.log(`   ${column.Field.padEnd(20)} | ${column.Type.padEnd(15)} | ${column.Null.padEnd(5)} | ${column.Key.padEnd(5)} | ${column.Default || 'NULL'}`);
        });
        
        console.log('');
        
        // Show sample data
        const [sampleData] = await connection.execute(`SELECT * FROM ${table} LIMIT 1`);
        if (sampleData.length > 0) {
          console.log('📋 Sample Data:');
          console.log('   Keys:', Object.keys(sampleData[0]).join(', '));
          console.log('');
        }
        
      } catch (error) {
        console.log(`   ❌ Error checking table ${table}: ${error.message}\n`);
      }
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Check failed:', error.message);
    return false;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run if called directly
if (require.main === module) {
  checkTableStructure().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Check error:', error);
    process.exit(1);
  });
}

module.exports = { checkTableStructure };
