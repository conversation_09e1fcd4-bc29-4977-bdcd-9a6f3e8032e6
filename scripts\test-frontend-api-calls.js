#!/usr/bin/env node

const axios = require('axios');

async function testFrontendAPICalls() {
  console.log('🧪 Testing Frontend API Calls...\n');
  
  const tests = [
    {
      name: 'Direct Backend (3001)',
      url: 'http://localhost:3001/api/systems',
      description: 'Direct call to backend server'
    },
    {
      name: 'Frontend Proxy (5173)',
      url: 'http://localhost:5173/api/systems',
      description: 'Call through Vite proxy'
    },
    {
      name: 'Backend Technical Services',
      url: 'http://localhost:3001/api/services/technical',
      description: 'Direct technical services call'
    },
    {
      name: 'Frontend Technical Services Proxy',
      url: 'http://localhost:5173/api/services/technical',
      description: 'Technical services through proxy'
    }
  ];

  for (const test of tests) {
    console.log(`🔍 Testing: ${test.name}`);
    console.log(`   URL: ${test.url}`);
    console.log(`   Description: ${test.description}`);
    
    try {
      const response = await axios.get(test.url, {
        headers: {
          'ngrok-skip-browser-warning': 'true',
          'User-Agent': 'FrontendAPITester/1.0'
        },
        timeout: 5000
      });
      
      console.log(`   ✅ Status: ${response.status}`);
      console.log(`   ✅ Success: ${response.data.success}`);
      
      if (response.data.success) {
        const data = response.data.data;
        if (data.systems) {
          console.log(`   📊 Systems: ${data.systems.length} items`);
        } else if (Array.isArray(data)) {
          console.log(`   📊 Data: ${data.length} items`);
        } else {
          console.log(`   📊 Data: ${JSON.stringify(data).substring(0, 100)}...`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      if (error.response) {
        console.log(`   ❌ Status: ${error.response.status}`);
        console.log(`   ❌ Data: ${JSON.stringify(error.response.data).substring(0, 200)}`);
      }
    }
    
    console.log('');
  }

  // Test environment variables simulation
  console.log('🔧 Environment Variables Test:');
  console.log(`   VITE_API_BASE_URL: ${process.env.VITE_API_BASE_URL || 'undefined'}`);
  console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
  
  // Test what frontend should be using
  console.log('\n🎯 Frontend Configuration Analysis:');
  const expectedBaseURL = process.env.VITE_API_BASE_URL || 'http://localhost:3001';
  console.log(`   Expected API Base URL: ${expectedBaseURL}`);
  console.log(`   Expected Full URL: ${expectedBaseURL}/api/systems`);
  
  // Test the expected configuration
  try {
    console.log('\n🔍 Testing Expected Frontend Configuration...');
    const response = await axios.get(`${expectedBaseURL}/api/systems`, {
      headers: {
        'ngrok-skip-browser-warning': 'true',
        'User-Agent': 'ExpectedConfigTester/1.0'
      }
    });
    
    console.log(`   ✅ Expected config works: ${response.status} - ${response.data.success}`);
    if (response.data.success && response.data.data.systems) {
      console.log(`   ✅ Systems available: ${response.data.data.systems.length}`);
    }
  } catch (error) {
    console.log(`   ❌ Expected config failed: ${error.message}`);
  }

  console.log('\n📋 Summary:');
  console.log('1. Check if both backend (3001) and frontend proxy (5173) work');
  console.log('2. Verify environment variables are loaded correctly');
  console.log('3. Ensure frontend components use correct API base URL');
  console.log('4. Check browser console for actual API calls being made');
}

testFrontendAPICalls();
