@echo off
chcp 65001 >nul
title 🚀 Khanfashariya - تشغيل شامل

cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 Khanfashariya                         ║
echo ║                  تشغيل جميع الخدمات                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: تنظيف العمليات السابقة
echo 🧹 تنظيف العمليات السابقة...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im ngrok.exe >nul 2>&1
timeout /t 2 >nul

:: تشغيل الخوادم
echo.
echo 📡 تشغيل الخوادم...
echo   🔧 Backend Server...
start "🔧 Backend" cmd /k "title 🔧 Backend-3001 && color 0A && npm run start"

echo   🎨 Frontend Server...
start "🎨 Frontend" cmd /k "title 🎨 Frontend-5173 && color 0B && npm run dev"

:: انتظار الخوادم
echo.
echo ⏳ انتظار تشغيل الخوادم (8 ثواني)...
timeout /t 8 >nul

:: تشغيل ngrok
echo.
echo 🌐 تشغيل أنفاق ngrok...
echo   🔗 Backend ngrok...
start "🌐 Backend-ngrok" cmd /k "title 🌐 Backend-ngrok && color 0E && ngrok http 3001"

echo   🔗 Frontend ngrok...
start "🌐 Frontend-ngrok" cmd /k "title 🌐 Frontend-ngrok && color 0D && ngrok http 5173 --web-addr=localhost:4041"

:: انتظار ngrok
echo.
echo ⏳ انتظار تشغيل ngrok (10 ثواني)...
timeout /t 10 >nul

:: الحصول على الروابط
echo.
echo 🔗 استخراج الروابط...
node scripts/get-ngrok-urls.js

:: عرض النتائج
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      ✅ تم التشغيل!                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📋 الخدمات المشغلة:
echo   🔧 Backend:  http://localhost:3001
echo   🎨 Frontend: http://localhost:5173
echo   🌐 Ngrok:    تحقق من current-ngrok-urls.json
echo.
echo 📁 الروابط محفوظة في: current-ngrok-urls.json
echo 📄 دليل الاستخدام: START_GUIDE.md
echo.
echo ⚠️  احتفظ بجميع النوافذ مفتوحة!
echo 🚀 جاهز للاستخدام مع TestSprite!
echo.

:: فتح ملف الروابط
if exist "current-ngrok-urls.json" (
    echo 📂 فتح ملف الروابط...
    start notepad "current-ngrok-urls.json"
)

echo.
pause
