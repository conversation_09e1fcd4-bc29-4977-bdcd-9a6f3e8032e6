import React from 'react';
import { Loader2 } from 'lucide-react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  children: React.ReactNode;
}

/**
 * Enhanced button component with touch-friendly design and accessibility features
 * 
 * Features:
 * - Touch-friendly minimum sizes (44px+ touch targets)
 * - Loading states with spinner
 * - Multiple variants and sizes
 * - Proper focus indicators
 * - Disabled state handling
 * - ARIA attributes for accessibility
 */
const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled,
  children,
  className = '',
  type = 'button',
  ...props
}) => {
  const baseClasses = [
    'font-medium',
    'rounded-lg',
    'transition-all',
    'duration-200',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-offset-2',
    'focus:ring-offset-background',
    'disabled:opacity-50',
    'disabled:cursor-not-allowed',
    'touch-manipulation',
    'inline-flex',
    'items-center',
    'justify-center',
    'text-center',
    'whitespace-nowrap'
  ].join(' ');
  
  const variantClasses = {
    primary: [
      'bg-gradient-to-r',
      'from-secondary',
      'to-accent',
      'text-primary',
      'hover:shadow-lg',
      'hover:shadow-secondary/25',
      'focus:ring-secondary',
      'active:scale-95'
    ].join(' '),
    
    secondary: [
      'bg-primary',
      'border',
      'border-accent',
      'text-accent',
      'hover:bg-accent',
      'hover:text-primary',
      'focus:ring-accent',
      'active:scale-95'
    ].join(' '),
    
    outline: [
      'border-2',
      'border-gray-400',
      'text-gray-100',
      'bg-gray-800/60',
      'hover:bg-gray-700/80',
      'hover:border-gray-300',
      'hover:text-white',
      'focus:ring-gray-400/50',
      'backdrop-blur-sm',
      'font-medium',
      'tracking-wide',
      'transition-all',
      'duration-200',
      'shadow-md',
      'hover:shadow-lg',
      'active:scale-95'
    ].join(' '),
    
    ghost: [
      'text-gray-300',
      'bg-transparent',
      'hover:bg-gray-700/50',
      'hover:text-white',
      'focus:ring-gray-500/50',
      'font-medium',
      'tracking-wide',
      'transition-all',
      'duration-200',
      'active:scale-95'
    ].join(' '),
    
    danger: [
      'bg-red-600',
      'text-white',
      'hover:bg-red-700',
      'focus:ring-red-500',
      'active:scale-95'
    ].join(' ')
  };
  
  const sizeClasses = {
    sm: [
      'px-4',
      'py-2',
      'text-sm',
      'min-h-[40px]', // Minimum 40px for touch accessibility
      'gap-2'
    ].join(' '),
    
    md: [
      'px-6',
      'py-3',
      'text-base',
      'min-h-[44px]', // Minimum 44px for touch accessibility (recommended)
      'gap-2'
    ].join(' '),
    
    lg: [
      'px-8',
      'py-4',
      'text-lg',
      'min-h-[48px]', // Minimum 48px for touch accessibility
      'gap-3'
    ].join(' ')
  };
  
  const isDisabled = disabled || loading;
  
  return (
    <button
      type={type}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      disabled={isDisabled}
      aria-disabled={isDisabled}
      {...props}
    >
      {loading && (
        <Loader2 
          className="w-4 h-4 animate-spin" 
          aria-hidden="true"
        />
      )}
      {children}
    </button>
  );
};

export default Button;
