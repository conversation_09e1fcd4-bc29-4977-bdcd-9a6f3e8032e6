/**
 * Debug Messaging System
 * Investigates the messaging system between admin and users
 */

const axios = require('axios');
const mysql = require('mysql2/promise');

const API_BASE = 'http://localhost:3001/api';
const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khanfashariya_db'
};

const TEST_USER_CREDS = { email: '<EMAIL>', password: '123456' };
const ADMIN_CREDS = { email: '<EMAIL>', password: 'admin123' };

async function debugMessagingSystem() {
  console.log('💬 Debugging Messaging System...');
  
  try {
    // 1. Check database structure
    console.log('\n📊 Checking Database Structure...');
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // Check if inbox_messages table exists and its structure
    try {
      const [tableInfo] = await connection.execute('DESCRIBE inbox_messages');
      console.log('inbox_messages table structure:');
      tableInfo.forEach(col => {
        console.log(`- ${col.Field}: ${col.Type} (${col.Null === 'YES' ? 'nullable' : 'not null'})`);
      });
    } catch (error) {
      console.log('❌ inbox_messages table does not exist or has issues');
    }
    
    // Check existing messages
    const [messages] = await connection.execute('SELECT * FROM inbox_messages ORDER BY created_at DESC LIMIT 5');
    console.log(`\nMessages in database: ${messages.length}`);
    
    if (messages.length > 0) {
      console.log('Recent messages:');
      messages.forEach((msg, index) => {
        console.log(`${index + 1}. To User: ${msg.user_id} - Subject: ${msg.subject_ar || msg.subject_en} - Read: ${msg.is_read}`);
      });
    }
    
    // 2. Test user authentication
    console.log('\n👤 Testing User Authentication...');
    const userLogin = await axios.post(`${API_BASE}/auth/login`, TEST_USER_CREDS);
    
    if (userLogin.data.success) {
      const userAuth = {
        token: userLogin.data.data.tokens.accessToken,
        user: userLogin.data.data.user,
        headers: { Authorization: `Bearer ${userLogin.data.data.tokens.accessToken}` }
      };
      
      console.log(`User logged in: ${userAuth.user.email} (ID: ${userAuth.user.id})`);
      
      // Test user messages API
      const messagesResponse = await axios.get(`${API_BASE}/users/${userAuth.user.id}/messages`, { headers: userAuth.headers });
      console.log(`User messages API response:`, {
        success: messagesResponse.data.success,
        hasData: !!messagesResponse.data.data,
        hasMessages: !!messagesResponse.data.data?.messages,
        messagesCount: messagesResponse.data.data?.messages?.length || 0
      });
      
      if (messagesResponse.data.data?.messages) {
        console.log('Messages from API:');
        messagesResponse.data.data.messages.forEach((msg, index) => {
          console.log(`${index + 1}. ${msg.subject_ar || msg.subject_en} - Read: ${msg.is_read}`);
        });
      }
    } else {
      console.log('❌ User login failed');
    }
    
    // 3. Test admin authentication
    console.log('\n🛠️ Testing Admin Authentication...');
    const adminLogin = await axios.post(`${API_BASE}/auth/login`, ADMIN_CREDS);
    
    if (adminLogin.data.success) {
      const adminAuth = {
        token: adminLogin.data.data.tokens.accessToken,
        headers: { Authorization: `Bearer ${adminLogin.data.data.tokens.accessToken}` }
      };
      
      console.log('Admin logged in successfully');
      
      // Test admin users management
      const usersResponse = await axios.get(`${API_BASE}/admin/users`, { headers: adminAuth.headers });
      console.log(`Admin users API response:`, {
        success: usersResponse.data.success,
        hasData: !!usersResponse.data.data,
        hasUsers: !!usersResponse.data.data?.users,
        usersCount: usersResponse.data.data?.users?.length || 0
      });
      
      // Test if we can send a message to a user (check if endpoint exists)
      if (usersResponse.data.success && usersResponse.data.data.users.length > 0) {
        const testUser = usersResponse.data.data.users.find(u => u.email === '<EMAIL>');
        
        if (testUser) {
          console.log(`Found test user: ${testUser.email} (ID: ${testUser.id})`);
          
          // Try to send a message
          const messageData = {
            user_id: testUser.id,
            subject_ar: 'رسالة تجريبية من المدير',
            subject_en: 'Test message from admin',
            message_ar: 'هذه رسالة تجريبية لاختبار النظام',
            message_en: 'This is a test message to check the system',
            message_type: 'admin_message'
          };
          
          try {
            const sendMessageResponse = await axios.post(`${API_BASE}/admin/users/${testUser.id}/messages`, messageData, { headers: adminAuth.headers });
            console.log(`Send message response:`, {
              success: sendMessageResponse.data.success,
              message: sendMessageResponse.data.message || 'Message sent'
            });
          } catch (error) {
            console.log('❌ Send message endpoint not found or failed:', error.response?.status);
          }
        }
      }
    } else {
      console.log('❌ Admin login failed');
    }
    
    await connection.end();
    
  } catch (error) {
    console.log('❌ Error during debugging:', error.response?.data || error.message);
  }
}

debugMessagingSystem();
