import { useState, useEffect } from 'react';
import { useLanguage } from '../store/simpleStore';

type Language = 'ar' | 'en';
type TranslationData = Record<string, any>;

export const useTranslation = () => {
  const { language, setLanguage: setStoreLanguage } = useLanguage();
  const [translations, setTranslations] = useState<TranslationData>({});
  const [loading, setLoading] = useState(true);

  // Initialize language from localStorage on first load
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && ['ar', 'en'].includes(savedLanguage) && savedLanguage !== language) {
      setStoreLanguage(savedLanguage);
    }
  }, []);

  useEffect(() => {
    const loadTranslations = async () => {
      setLoading(true);
      try {
        const translationModule = await import(`../locales/${language}.json`);
        setTranslations(translationModule.default);
      } catch (error) {
        console.error('Failed to load translations:', error);
        // Fallback to Arabic if loading fails
        if (language !== 'ar') {
          const fallbackModule = await import('../locales/ar.json');
          setTranslations(fallbackModule.default);
        }
      } finally {
        setLoading(false);
      }
    };

    loadTranslations();
  }, [language]);

  // Sync with localStorage and DOM when language changes
  useEffect(() => {
    // Add language switching class to prevent layout corruption
    document.body.classList.add('language-switching');

    localStorage.setItem('language', language);
    document.documentElement.lang = language;
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';

    // Remove language switching class after transition
    const timeoutId = setTimeout(() => {
      document.body.classList.remove('language-switching');
    }, 300); // Match CSS transition duration

    return () => clearTimeout(timeoutId);
  }, [language]);

  const t = (key: string, fallback?: string): string => {
    const keys = key.split('.');
    let value = translations;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return fallback || key; // Return fallback or key if translation not found
      }
    }

    return typeof value === 'string' ? value : (fallback || key);
  };

  const changeLanguage = (newLanguage: Language) => {
    setStoreLanguage(newLanguage);
  };

  return {
    language,
    changeLanguage,
    t,
    loading
  };
};