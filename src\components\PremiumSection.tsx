import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useNotification } from '../hooks/useNotification';
import apiClient from '../lib/apiClient';
import {
  Crown,
  Star,
  Zap,
  Shield,
  ArrowRight,
  ArrowLeft,
  Play,
  Image as ImageIcon,
  CheckCircle,
  Sparkles,
  Gem,
  Award
} from 'lucide-react';

import Tooltip from './ui/Tooltip';
import VideoModal from './ui/VideoModal';
import PremiumEdition from './PremiumEdition';

interface PremiumEdition {
  id: string;
  title_ar: string;
  title_en: string;
  description_ar: string;
  description_en: string;
  detailed_description_ar?: string;
  detailed_description_en?: string;
  price: number;
  original_price?: number;
  discount_percentage?: number;
  features_ar: string[];
  features_en: string[];
  tech_specs_ar: string[];
  tech_specs_en: string[];
  video_url?: string;
  image_url?: string;
  gallery_images: string[];
  included_systems: string[];
  included_services: string[];
  status: 'active' | 'inactive' | 'draft';
  is_active_edition: boolean;
  featured: boolean;
  category: string;
  purchase_count: number;
  rating: number;
  rating_count: number;
}

/**
 * Premium Section Component - Luxurious showcase of premium content
 * Features:
 * - Elegant golden theme design
 * - Integration with admin panel data
 * - Video/image display with priority logic
 * - Interactive elements with smooth animations
 * - Responsive design following golden ratio
 */
const PremiumSection: React.FC = () => {
  const { language, t } = useTranslation();
  const { showNotification } = useNotification();
  const [premiumEdition, setPremiumEdition] = useState<PremiumEdition | null>(null);
  const [availableSystems, setAvailableSystems] = useState<any[]>([]);
  const [availableServices, setAvailableServices] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showPremiumModal, setShowPremiumModal] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);

  useEffect(() => {
    loadPremiumData();
  }, []);

  const loadPremiumData = async () => {
    try {
      // Use apiClient for consistent API calls with new endpoints
      const premiumResult = await apiClient.get('/premium');
      const systemsResult = await apiClient.get('/systems');
      const servicesResult = await apiClient.get('/services/technical');

      // Find the active Premium Edition
      if (premiumResult.success && premiumResult.data && premiumResult.data.premiumContent) {
        const premium = premiumResult.data.premiumContent;
        if (premium && premium.status === 'active') {
          // Transform the data to match our interface
          const transformedPremium: PremiumEdition = {
            id: premium.id,
            title_ar: premium.title_ar || '',
            title_en: premium.title_en || '',
            description_ar: premium.description_ar || '',
            description_en: premium.description_en || '',
            detailed_description_ar: premium.detailed_description_ar,
            detailed_description_en: premium.detailed_description_en,
            price: parseFloat(premium.price) || 0,
            original_price: premium.original_price ? parseFloat(premium.original_price) : undefined,
            discount_percentage: premium.discount_percentage || 0,
            features_ar: Array.isArray(premium.features_ar) ? premium.features_ar : [],
            features_en: Array.isArray(premium.features_en) ? premium.features_en : [],
            tech_specs_ar: Array.isArray(premium.tech_specs_ar) ? premium.tech_specs_ar : [],
            tech_specs_en: Array.isArray(premium.tech_specs_en) ? premium.tech_specs_en : [],
            video_url: premium.video_url,
            image_url: premium.image_url,
            gallery_images: Array.isArray(premium.gallery_images) ? premium.gallery_images : [],
            included_systems: Array.isArray(premium.included_systems) ? premium.included_systems : [],
            included_services: Array.isArray(premium.included_services) ? premium.included_services : [],
            status: premium.status || 'active',
            is_active_edition: premium.is_active_edition || false,
            featured: premium.featured || false,
            category: premium.category || 'premium',
            purchase_count: premium.purchase_count || 0,
            rating: parseFloat(premium.rating) || 0,
            rating_count: premium.rating_count || 0
          };
          setPremiumEdition(transformedPremium);
        }
      }

      // Load available systems and services
      if (systemsResult.success && systemsResult.data && systemsResult.data.systems) {
        const premiumEligibleSystems = systemsResult.data.systems.filter((s: any) =>
          s.status === 'active' && s.is_premium_addon === true
        );
        setAvailableSystems(premiumEligibleSystems);
      }

      if (servicesResult.success && servicesResult.data && servicesResult.data.services) {
        const premiumEligibleServices = servicesResult.data.services.filter((s: any) =>
          s.status === 'active' && s.is_premium_addon === true
        );
        setAvailableServices(premiumEligibleServices);
      }

    } catch (error) {
      console.error('Error loading premium data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenPremiumModal = () => {
    setShowPremiumModal(true);
  };

  const handleVideoClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (premiumEdition?.video_url) {
      setSelectedVideo(premiumEdition.video_url);
    }
  };

  const closeVideoModal = () => {
    setSelectedVideo(null);
  };

  if (loading || !premiumEdition) {
    return null;
  }

  return (
    <section id="premium" className="py-20 bg-gradient-to-br from-gray-900 via-black to-gray-900 relative overflow-hidden">
      {/* Luxurious Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Golden particles */}
        <div className="absolute top-20 left-20 w-2 h-2 bg-yellow-400 rounded-full animate-ping opacity-70"></div>
        <div className="absolute top-40 right-32 w-1 h-1 bg-yellow-300 rounded-full animate-pulse opacity-60"></div>
        <div className="absolute bottom-32 left-40 w-3 h-3 bg-amber-400 rounded-full animate-bounce opacity-50"></div>
        <div className="absolute bottom-20 right-20 w-2 h-2 bg-yellow-500 rounded-full animate-ping opacity-80 delay-1000"></div>

        {/* Gradient orbs */}
        <div className="absolute top-10 left-10 w-40 h-40 bg-gradient-to-br from-yellow-500/20 to-amber-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-56 h-56 bg-gradient-to-br from-amber-500/15 to-yellow-600/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-br from-yellow-400/10 to-amber-500/10 rounded-full blur-3xl animate-pulse delay-500"></div>

        {/* Luxury grid pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-yellow-500/5 to-transparent"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Luxurious Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center items-center space-x-6 rtl:space-x-reverse mb-8">
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full blur-lg opacity-75 group-hover:opacity-100 transition-opacity"></div>
              <Crown className="w-20 h-20 text-yellow-400 relative z-10 drop-shadow-2xl animate-pulse" />
              <Sparkles className="w-10 h-10 text-amber-300 absolute -top-3 -right-3 animate-ping z-10" />
              <Gem className="w-6 h-6 text-yellow-300 absolute -bottom-2 -left-2 animate-bounce z-10" />
            </div>
            <div className="text-center">
              <h2 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-yellow-400 via-amber-300 to-yellow-500 bg-clip-text text-transparent drop-shadow-2xl">
                {language === 'ar' ? premiumEdition.title_ar : premiumEdition.title_en}
              </h2>
              <div className="star-rating star-rating-center mt-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="star-icon text-yellow-400 fill-current animate-pulse" style={{ animationDelay: `${i * 200}ms` }} />
                ))}
              </div>
            </div>
          </div>
          <p className="text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-light">
            {language === 'ar' ? premiumEdition.description_ar : premiumEdition.description_en}
          </p>

          {/* Luxury divider */}
          <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mt-8">
            <div className="h-px bg-gradient-to-r from-transparent via-yellow-400 to-transparent flex-1 max-w-32"></div>
            <Gem className="w-8 h-8 text-yellow-400 animate-pulse" />
            <div className="h-px bg-gradient-to-r from-transparent via-yellow-400 to-transparent flex-1 max-w-32"></div>
          </div>
        </div>

        {/* Luxurious Premium Card */}
        <div className="max-w-7xl mx-auto premium-section">
          <div className="relative">
            {/* Luxury border effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 via-amber-500/20 to-yellow-400/20 rounded-3xl blur-xl"></div>
            <div className="relative bg-gradient-to-br from-gray-900/95 via-black/95 to-gray-900/95 backdrop-blur-xl rounded-3xl border border-yellow-400/30 overflow-hidden shadow-2xl hover:shadow-yellow-400/20 transition-all duration-700 group">

              <div className="grid lg:grid-cols-2 gap-0">
                {/* Luxurious Media Section */}
                <div className="relative h-96 lg:h-[500px] overflow-hidden premium-media">
                  {premiumEdition.video_url ? (
                    // Premium Video Display
                    <div
                      className="relative w-full h-full bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center group/video cursor-pointer"
                      onClick={handleOpenPremiumModal}
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 via-amber-600/10 to-yellow-400/10"></div>
                      {(premiumEdition.image_url || premiumEdition.gallery_images?.[0]) && (
                        <img
                          src={premiumEdition.image_url || premiumEdition.gallery_images[0]}
                          alt={language === 'ar' ? premiumEdition.title_ar : premiumEdition.title_en}
                          className="absolute inset-0 w-full h-full object-cover opacity-20"
                        />
                      )}
                      <div className="relative z-10 text-center">
                        <div
                          className="w-32 h-32 bg-gradient-to-br from-yellow-400/20 to-amber-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-6 mx-auto group-hover/video:scale-110 group-hover/video:rotate-3 transition-all duration-500 border border-yellow-400/30 cursor-pointer"
                          onClick={handleVideoClick}
                        >
                          <div className="w-24 h-24 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full flex items-center justify-center shadow-2xl">
                            <Play className="w-12 h-12 text-black ml-1 drop-shadow-lg" />
                          </div>
                        </div>
                        <p className="text-white text-xl font-bold mb-2">
                          {language === 'ar' ? 'مشاهدة العرض التوضيحي' : 'Watch Premium Demo'}
                        </p>
                        <p className="text-yellow-400 text-sm font-medium">
                          {language === 'ar' ? 'اكتشف الميزات الحصرية' : 'Discover Exclusive Features'}
                        </p>
                      </div>
                    </div>
                  ) : (premiumEdition.image_url || premiumEdition.gallery_images?.[0]) ? (
                    // Premium Image Display
                    <div
                      className="relative w-full h-full cursor-pointer group/image"
                      onClick={handleOpenPremiumModal}
                    >
                      <img
                        src={premiumEdition.image_url || premiumEdition.gallery_images[0]}
                        alt={language === 'ar' ? premiumEdition.title_ar : premiumEdition.title_en}
                        className="w-full h-full object-cover group-hover/image:scale-105 transition-transform duration-700"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-black/20"></div>
                      <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/10 to-amber-500/10"></div>
                    </div>
                  ) : (
                    // Luxury Fallback
                    <div className="w-full h-full bg-gradient-to-br from-yellow-500/20 via-amber-600/20 to-yellow-400/20 flex items-center justify-center">
                      <div className="text-center">
                        <Crown className="w-32 h-32 text-yellow-400/70 mx-auto mb-4" />
                        <p className="text-yellow-400 text-lg font-medium">
                          {language === 'ar' ? 'المحتوى المميز' : 'Premium Content'}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Luxurious Content Section */}
                <div className="p-10 lg:p-16 flex flex-col justify-center relative premium-content">
                  {/* Luxury background pattern */}
                  <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/5 to-amber-500/5"></div>

                  <div className="relative z-10">
                    <div className="mb-8">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
                        <Shield className="w-8 h-8 text-yellow-400" />
                        <span className="text-yellow-400 text-sm font-bold uppercase tracking-wider">
                          {language === 'ar' ? 'النسخة المميزة' : 'Premium Edition'}
                        </span>
                      </div>
                      <h3 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent mb-6 leading-tight">
                        {language === 'ar' ? premiumEdition.title_ar : premiumEdition.title_en}
                      </h3>
                      <p className="text-xl text-gray-300 leading-relaxed mb-8">
                        {language === 'ar' ? premiumEdition.description_ar : premiumEdition.description_en}
                      </p>
                    </div>

                    {/* Luxury Price Section */}
                    <div className="flex items-center space-x-6 rtl:space-x-reverse mb-10">
                      <div className="relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-2xl blur-lg opacity-30"></div>
                        <div className="relative bg-gradient-to-r from-yellow-400 to-amber-500 text-black px-6 py-3 rounded-2xl">
                          <span className="text-3xl font-bold">${premiumEdition.price}</span>
                        </div>
                      </div>
                      {premiumEdition.original_price && premiumEdition.original_price > premiumEdition.price && (
                        <>
                          <div className="text-xl text-gray-400 line-through">
                            ${premiumEdition.original_price}
                          </div>
                          {premiumEdition.discount_percentage && (
                            <div className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                              -{premiumEdition.discount_percentage}%
                            </div>
                          )}
                        </>
                      )}
                    </div>

                    {/* Features Preview */}
                    {premiumEdition.features && premiumEdition.features[language] && (
                      <div className="mb-8">
                        <div className="grid gap-3">
                          {(language === 'ar' ? premiumEdition.features_ar : premiumEdition.features_en).slice(0, 4).map((feature, index) => (
                            <div key={index} className="flex items-center space-x-3 rtl:space-x-reverse">
                              <div className="w-6 h-6 bg-yellow-400/20 rounded-full flex items-center justify-center flex-shrink-0">
                                <Zap className="w-3 h-3 text-yellow-400" />
                              </div>
                              <span className="text-gray-300 text-sm">{feature}</span>
                            </div>
                          ))}
                          {(language === 'ar' ? premiumEdition.features_ar : premiumEdition.features_en).length > 4 && (
                            <div className="text-yellow-400 text-sm font-medium">
                              +{(language === 'ar' ? premiumEdition.features_ar : premiumEdition.features_en).length - 4} {language === 'ar' ? 'مميزة أخرى' : 'more features'}
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* CTA Button */}
                    <button
                      onClick={handleOpenPremiumModal}
                      className="w-full bg-gradient-to-r from-yellow-400 to-amber-500 text-black font-bold py-4 px-8 rounded-xl transition-all duration-300 hover:shadow-2xl hover:shadow-yellow-400/30 hover:scale-105 flex items-center justify-center space-x-3 rtl:space-x-reverse group/btn"
                    >
                      <Crown className="w-6 h-6 group-hover/btn:scale-110 transition-transform duration-300" />
                      <span className="text-lg">{language === 'ar' ? 'استكشف النسخة المميزة' : 'Explore Premium Edition'}</span>
                      {language === 'ar' ? (
                        <ArrowLeft className="w-6 h-6 group-hover/btn:translate-x-1 transition-transform duration-300" />
                      ) : (
                        <ArrowRight className="w-6 h-6 group-hover/btn:translate-x-1 transition-transform duration-300" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Premium Modal */}
      {showPremiumModal && (
        <PremiumEdition onClose={() => setShowPremiumModal(false)} />
      )}

      {/* Video Modal */}
      <VideoModal
        isOpen={!!selectedVideo}
        onClose={closeVideoModal}
        videoUrl={selectedVideo || ''}
        title={premiumEdition ? (language === 'ar' ? premiumEdition.title_ar : premiumEdition.title_en) : ''}
      />
    </section>
  );
};

export default PremiumSection;