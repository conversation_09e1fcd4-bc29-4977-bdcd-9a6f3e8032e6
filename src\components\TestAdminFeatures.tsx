import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuth } from '../hooks/useAuth';
import {
  signIn,
  getSystemServices,
  createSystemService,
  updateSystemService,
  deleteSystemService
} from '../lib/apiServices';
import { SystemService } from '../lib/database';
import { 
  Play, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Eye,
  Edit,
  Trash2,
  Plus,
  Save,
  X
} from 'lucide-react';
import Modal from './ui/Modal';
import Button from './ui/Button';
import Input from './ui/Input';

interface TestAdminFeaturesProps {
  onClose: () => void;
}

/**
 * Component to test all admin features systematically
 */
const TestAdminFeatures: React.FC<TestAdminFeaturesProps> = ({ onClose }) => {
  const { language, t } = useTranslation();
  const { isAuthenticated, isAdmin, userProfile } = useAuth();
  const [testResults, setTestResults] = useState<Record<string, 'pending' | 'success' | 'error'>>({});
  const [testLogs, setTestLogs] = useState<string[]>([]);
  const [systems, setSystems] = useState<SystemService[]>([]);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [editingSystem, setEditingSystem] = useState<SystemService | null>(null);
  const [previewSystem, setPreviewSystem] = useState<SystemService | null>(null);

  // Test form data
  const [formData, setFormData] = useState({
    name: { ar: 'نظام تجريبي', en: 'Test System' },
    description: { ar: 'وصف النظام التجريبي', en: 'Test system description' },
    price: 50,
    category: 'general',
    features: { ar: ['ميزة 1', 'ميزة 2'], en: ['Feature 1', 'Feature 2'] },
    tech_specs: { ar: ['مواصفة 1', 'مواصفة 2'], en: ['Spec 1', 'Spec 2'] },
    video_url: '',
    image_url: '',
    status: 'active' as const
  });

  const addLog = (message: string) => {
    setTestLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const updateTestResult = (testName: string, result: 'success' | 'error') => {
    setTestResults(prev => ({ ...prev, [testName]: result }));
  };

  // Test 1: Authentication
  const testAuthentication = async () => {
    addLog('Testing authentication...');
    setTestResults(prev => ({ ...prev, auth: 'pending' }));

    try {
      if (!isAuthenticated) {
        const result = await signIn('<EMAIL>', 'admin123');
        if (result.error) {
          throw new Error(result.error.message);
        }
      }
      
      if (isAdmin) {
        addLog('✅ Authentication successful - Admin access confirmed');
        updateTestResult('auth', 'success');
      } else {
        throw new Error('User is not admin');
      }
    } catch (error: any) {
      addLog(`❌ Authentication failed: ${error.message}`);
      updateTestResult('auth', 'error');
    }
  };

  // Test 2: Data Loading
  const testDataLoading = async () => {
    addLog('Testing data loading...');
    setTestResults(prev => ({ ...prev, dataLoading: 'pending' }));

    try {
      const result = getSystemServices();
      if (result.data) {
        setSystems(result.data);
        addLog(`✅ Data loading successful - Found ${result.data.length} systems`);
        updateTestResult('dataLoading', 'success');
      } else {
        throw new Error('No data returned');
      }
    } catch (error: any) {
      addLog(`❌ Data loading failed: ${error.message}`);
      updateTestResult('dataLoading', 'error');
    }
  };

  // Test 3: Create Operation
  const testCreateOperation = async () => {
    addLog('Testing create operation...');
    setTestResults(prev => ({ ...prev, create: 'pending' }));

    try {
      const result = await createSystemService(formData);
      if (result.data) {
        addLog('✅ Create operation successful');
        updateTestResult('create', 'success');
        await testDataLoading(); // Refresh data
      } else {
        throw new Error(result.error?.message || 'Create failed');
      }
    } catch (error: any) {
      addLog(`❌ Create operation failed: ${error.message}`);
      updateTestResult('create', 'error');
    }
  };

  // Test 4: Edit Modal
  const testEditModal = () => {
    addLog('Testing edit modal...');
    setTestResults(prev => ({ ...prev, editModal: 'pending' }));

    try {
      if (systems.length > 0) {
        setEditingSystem(systems[0]);
        setShowEditModal(true);
        addLog('✅ Edit modal opened successfully');
        updateTestResult('editModal', 'success');
      } else {
        throw new Error('No systems available for editing');
      }
    } catch (error: any) {
      addLog(`❌ Edit modal failed: ${error.message}`);
      updateTestResult('editModal', 'error');
    }
  };

  // Test 5: Preview Modal
  const testPreviewModal = () => {
    addLog('Testing preview modal...');
    setTestResults(prev => ({ ...prev, previewModal: 'pending' }));

    try {
      if (systems.length > 0) {
        setPreviewSystem(systems[0]);
        setShowPreviewModal(true);
        addLog('✅ Preview modal opened successfully');
        updateTestResult('previewModal', 'success');
      } else {
        throw new Error('No systems available for preview');
      }
    } catch (error: any) {
      addLog(`❌ Preview modal failed: ${error.message}`);
      updateTestResult('previewModal', 'error');
    }
  };

  // Test 6: Update Operation
  const testUpdateOperation = async () => {
    addLog('Testing update operation...');
    setTestResults(prev => ({ ...prev, update: 'pending' }));

    try {
      if (systems.length > 0) {
        const systemToUpdate = systems[0];
        const updatedData = {
          ...systemToUpdate,
          name: { ar: 'نظام محدث', en: 'Updated System' }
        };
        
        const result = await updateSystemService(systemToUpdate.id, updatedData);
        if (result.data) {
          addLog('✅ Update operation successful');
          updateTestResult('update', 'success');
          await testDataLoading(); // Refresh data
        } else {
          throw new Error(result.error?.message || 'Update failed');
        }
      } else {
        throw new Error('No systems available for update');
      }
    } catch (error: any) {
      addLog(`❌ Update operation failed: ${error.message}`);
      updateTestResult('update', 'error');
    }
  };

  // Test 7: Delete Operation
  const testDeleteOperation = async () => {
    addLog('Testing delete operation...');
    setTestResults(prev => ({ ...prev, delete: 'pending' }));

    try {
      if (systems.length > 0) {
        const systemToDelete = systems[systems.length - 1]; // Delete last system
        const result = await deleteSystemService(systemToDelete.id);
        if (result.data) {
          addLog('✅ Delete operation successful');
          updateTestResult('delete', 'success');
          await testDataLoading(); // Refresh data
        } else {
          throw new Error(result.error?.message || 'Delete failed');
        }
      } else {
        throw new Error('No systems available for deletion');
      }
    } catch (error: any) {
      addLog(`❌ Delete operation failed: ${error.message}`);
      updateTestResult('delete', 'error');
    }
  };

  // Run all tests
  const runAllTests = async () => {
    setTestResults({});
    setTestLogs([]);
    addLog('Starting comprehensive admin features test...');
    
    await testAuthentication();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testDataLoading();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testCreateOperation();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    testEditModal();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    testPreviewModal();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testUpdateOperation();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testDeleteOperation();
    
    addLog('All tests completed!');
  };

  const getStatusIcon = (status: 'pending' | 'success' | 'error' | undefined) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'error': return <XCircle className="w-5 h-5 text-red-400" />;
      case 'pending': return <AlertCircle className="w-5 h-5 text-yellow-400 animate-pulse" />;
      default: return <div className="w-5 h-5 border-2 border-gray-400 rounded-full" />;
    }
  };

  const tests = [
    { key: 'auth', name: 'Authentication Test' },
    { key: 'dataLoading', name: 'Data Loading Test' },
    { key: 'create', name: 'Create Operation Test' },
    { key: 'editModal', name: 'Edit Modal Test' },
    { key: 'previewModal', name: 'Preview Modal Test' },
    { key: 'update', name: 'Update Operation Test' },
    { key: 'delete', name: 'Delete Operation Test' }
  ];

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 overflow-y-auto">
      <div className="min-h-screen py-8">
        <div className="max-w-6xl mx-auto bg-primary rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-secondary to-accent p-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-primary">
                {language === 'ar' ? 'اختبار خصائص الإدارة' : 'Admin Features Test'}
              </h2>
              <button
                onClick={onClose}
                className="w-10 h-10 bg-primary/20 hover:bg-primary/40 rounded-full flex items-center justify-center transition-colors"
              >
                <X className="w-6 h-6 text-white" />
              </button>
            </div>
          </div>

          <div className="p-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Test Controls */}
            <div className="space-y-6">
              <div className="bg-background/50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'التحكم في الاختبارات' : 'Test Controls'}
                </h3>
                <Button
                  onClick={runAllTests}
                  className="w-full bg-gradient-to-r from-secondary to-accent text-primary"
                >
                  <Play className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'تشغيل جميع الاختبارات' : 'Run All Tests'}
                </Button>
              </div>

              {/* Test Results */}
              <div className="bg-background/50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'نتائج الاختبارات' : 'Test Results'}
                </h3>
                <div className="space-y-2">
                  {tests.map(test => (
                    <div key={test.key} className="flex items-center justify-between p-2 bg-primary/30 rounded">
                      <span className="text-white text-sm">{test.name}</span>
                      {getStatusIcon(testResults[test.key])}
                    </div>
                  ))}
                </div>
              </div>

              {/* Current Data */}
              <div className="bg-background/50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'البيانات الحالية' : 'Current Data'}
                </h3>
                <p className="text-gray-300 text-sm">
                  {language === 'ar' ? 'عدد الأنظمة:' : 'Systems Count:'} {systems.length}
                </p>
                <p className="text-gray-300 text-sm">
                  {language === 'ar' ? 'المستخدم:' : 'User:'} {userProfile?.username || 'Not logged in'}
                </p>
                <p className="text-gray-300 text-sm">
                  {language === 'ar' ? 'الدور:' : 'Role:'} {userProfile?.role || 'None'}
                </p>
              </div>
            </div>

            {/* Test Logs */}
            <div className="bg-background/50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-4">
                {language === 'ar' ? 'سجل الاختبارات' : 'Test Logs'}
              </h3>
              <div className="bg-black/50 rounded p-3 h-96 overflow-y-auto">
                {testLogs.map((log, index) => (
                  <div key={index} className="text-sm text-gray-300 mb-1 font-mono">
                    {log}
                  </div>
                ))}
                {testLogs.length === 0 && (
                  <div className="text-gray-500 text-sm">
                    {language === 'ar' ? 'لا توجد سجلات بعد...' : 'No logs yet...'}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Edit Modal */}
      {showEditModal && editingSystem && (
        <Modal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          title={`${language === 'ar' ? 'تعديل النظام' : 'Edit System'} - ${language === 'ar' ? editingSystem.name_ar : editingSystem.name_en}`}
          size="lg"
        >
          <Modal.Body>
            <div className="space-y-4">
              <Input
                label={language === 'ar' ? 'الاسم (عربي)' : 'Name (Arabic)'}
                value={editingSystem.name.ar}
                onChange={(e) => setEditingSystem({
                  ...editingSystem,
                  name: { ...editingSystem.name, ar: e.target.value }
                })}
              />
              <Input
                label={language === 'ar' ? 'الاسم (إنجليزي)' : 'Name (English)'}
                value={editingSystem.name.en}
                onChange={(e) => setEditingSystem({
                  ...editingSystem,
                  name: { ...editingSystem.name, en: e.target.value }
                })}
              />
              <Input
                label={language === 'ar' ? 'السعر' : 'Price'}
                type="number"
                value={editingSystem.price}
                onChange={(e) => setEditingSystem({
                  ...editingSystem,
                  price: Number(e.target.value)
                })}
              />
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowEditModal(false)}>
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button variant="primary" onClick={() => {
              addLog('✅ Edit modal form is working correctly');
              setShowEditModal(false);
            }}>
              <Save className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'حفظ' : 'Save'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}

      {/* Preview Modal */}
      {showPreviewModal && previewSystem && (
        <Modal
          isOpen={showPreviewModal}
          onClose={() => setShowPreviewModal(false)}
          title={`${language === 'ar' ? 'معاينة النظام' : 'System Preview'} - ${language === 'ar' ? previewSystem.name_ar : previewSystem.name_en}`}
          size="xl"
        >
          <Modal.Body>
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    {language === 'ar' ? previewSystem.name_ar : previewSystem.name_en}
                  </h3>
                  <p className="text-gray-300 mb-4">
                    {language === 'ar' ? previewSystem.description_ar : previewSystem.description_en}
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">{language === 'ar' ? 'السعر:' : 'Price:'}</span>
                      <span className="text-secondary font-bold text-xl">${previewSystem.price}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">{language === 'ar' ? 'الفئة:' : 'Category:'}</span>
                      <span className="text-white">{previewSystem.category}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-white mb-3">
                    {language === 'ar' ? 'الميزات' : 'Features'}
                  </h4>
                  <ul className="space-y-2">
                    {previewSystem.features[language].map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                        <span className="text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowPreviewModal(false)}>
              {language === 'ar' ? 'إغلاق' : 'Close'}
            </Button>
            <Button variant="primary" onClick={() => {
              addLog('✅ Preview modal is working correctly');
              setShowPreviewModal(false);
            }}>
              <Edit className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'تعديل' : 'Edit'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}
    </div>
  );
};

export default TestAdminFeatures;
