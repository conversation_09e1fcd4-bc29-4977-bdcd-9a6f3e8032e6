/**
 * Check Users Table Structure
 * 
 * This script checks if the users table exists and has the correct structure
 */

const { executeQuery } = require('../server/config/database');
require('dotenv').config();

async function checkUsersTable() {
  console.log('🔍 Checking users table structure...\n');
  
  try {
    // Check if users table exists
    console.log('1️⃣ Checking if users table exists...');
    const { rows: tables } = await executeQuery(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users'
    `, [process.env.DB_NAME || 'khanfashariya_db']);
    
    if (tables.length === 0) {
      console.log('❌ Users table does not exist!');
      console.log('🔧 Creating users table...');
      
      await executeQuery(`
        CREATE TABLE users (
          id VARCHAR(36) PRIMARY KEY,
          email VARCHAR(255) UNIQUE NOT NULL,
          password VARCHAR(255) NOT NULL,
          name_ar VARCHAR(255),
          name_en VARCHAR(255),
          phone VARCHAR(20),
          role ENUM('user', 'admin', 'moderator') DEFAULT 'user',
          status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
          email_verified BOOLEAN DEFAULT FALSE,
          phone_verified BOOLEAN DEFAULT FALSE,
          avatar_url VARCHAR(500),
          preferences JSON,
          last_login_at TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_email (email),
          INDEX idx_role (role),
          INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
      
      console.log('✅ Users table created successfully');
    } else {
      console.log('✅ Users table exists');
    }
    
    // Check table structure
    console.log('\n2️⃣ Checking table structure...');
    const { rows: columns } = await executeQuery(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, EXTRA
      FROM information_schema.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users'
      ORDER BY ORDINAL_POSITION
    `, [process.env.DB_NAME || 'khanfashariya_db']);
    
    console.log('📋 Table structure:');
    columns.forEach(col => {
      console.log(`   ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });
    
    // Check existing users
    console.log('\n3️⃣ Checking existing users...');
    const { rows: users } = await executeQuery(`
      SELECT id, email, username, full_name, role, status, created_at
      FROM users
      ORDER BY created_at DESC
      LIMIT 10
    `);

    if (users.length === 0) {
      console.log('⚠️  No users found in database');
    } else {
      console.log(`📊 Found ${users.length} users:`);
      users.forEach(user => {
        console.log(`   👤 ${user.email} (${user.role}) - ${user.status}`);
      });
    }

    // Check for admin users specifically
    console.log('\n4️⃣ Checking admin users...');
    const { rows: admins } = await executeQuery(`
      SELECT id, email, username, full_name, role, status, created_at, password_hash
      FROM users
      WHERE role = 'admin'
    `);

    if (admins.length === 0) {
      console.log('⚠️  No admin users found');
      console.log('💡 Run: npm run create:admin');
    } else {
      console.log(`✅ Found ${admins.length} admin users:`);
      admins.forEach(admin => {
        console.log(`   🔑 ${admin.email} - ${admin.status} (created: ${admin.created_at})`);
        console.log(`      Username: ${admin.username}`);
        console.log(`      Full Name: ${admin.full_name}`);
        console.log(`      Password Hash: ${admin.password_hash ? '✅ Set' : '❌ Missing'}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error checking users table:', error.message);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  checkUsersTable()
    .then(() => {
      console.log('\n✅ Users table check completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Check failed:', error.message);
      process.exit(1);
    });
}

module.exports = { checkUsersTable };