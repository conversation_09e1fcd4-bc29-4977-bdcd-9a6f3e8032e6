import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuth } from '../hooks/useAuth';
import { useNotification } from '../hooks/useNotification';
import { useActivityLogger } from '../hooks/useActivityLogger';

import AdminSidebar from './admin/AdminSidebar';
import SystemSettings from './admin/SystemSettings';
import TechnicalSystemsManager from './admin/TechnicalSystemsManager';
import TechnicalServicesManager from './admin/TechnicalServicesManager';
import OrderManagement from './admin/OrderManagement';
import EnhancedPremiumManager from './admin/EnhancedPremiumManager';
import AdvancedPremiumManager from './admin/AdvancedPremiumManager';
import PremiumContentManager from './admin/PremiumContentManager';
import EnhancedUserManager from './admin/EnhancedUserManager';
import ComprehensiveSystemSettings from './admin/ComprehensiveSystemSettings';
import {
  getAllUsers,
  getSystemServices,
  getTechnicalServices,
  createSystemService,
  updateSystemService,
  deleteSystemService,
  createTechnicalService,
  updateTechnicalService,
  deleteTechnicalService,
  updateUserRole,
  getAllOrders,
  updateOrderStatus
} from '../lib/apiServices';
import {
  User,
  UserService,
  SystemService,
  TechnicalService,
  BaseService,
  TranslatedText,
  Order
} from '../lib/database';
import {
  Users,
  Package,
  Settings,
  Plus,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Shield,
  Star,
  Video,
  Image,
  FileText,
  Save,
  X,
  ArrowLeft,
  Home,
  BarChart3,
  DollarSign,
  TrendingUp,
  Wrench,
  Code,
  Crown
} from 'lucide-react';
import BackButton from './ui/BackButton';

interface AdminDashboardProps {
  onClose: () => void;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ onClose }) => {
  const { t, language } = useTranslation();
  const { userProfile } = useAuth();
  const { showNotification } = useNotification();
  const { getRecentActivities } = useActivityLogger();
  const [activeTab, setActiveTab] = useState('overview');
  const [users, setUsers] = useState<User[]>([]);
  const [services, setServices] = useState<UserService[]>([]);
  const [systemServices, setSystemServices] = useState<SystemService[]>([]);
  const [technicalServices, setTechnicalServices] = useState<TechnicalService[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingService, setEditingService] = useState<Partial<BaseService> | null>(null);
  const [showServiceModal, setShowServiceModal] = useState(false);
  const [serviceType, setServiceType] = useState<'system' | 'technical'>('system');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [usersResult, ordersResult, systemServicesResult, technicalServicesResult] = await Promise.all([
        getAllUsers(),
        getAllOrders(),
        getSystemServices(),
        getTechnicalServices()
      ]);

      if (usersResult.data) setUsers(usersResult.data);
      if (ordersResult.data) {
        // Handle different response formats
        let ordersArray = [];
        if (Array.isArray(ordersResult.data)) {
          ordersArray = ordersResult.data;
        } else if (ordersResult.data.orders && Array.isArray(ordersResult.data.orders)) {
          ordersArray = ordersResult.data.orders;
        } else if (ordersResult.data.data && Array.isArray(ordersResult.data.data)) {
          ordersArray = ordersResult.data.data;
        }
        
        // Convert orders to services format for compatibility
        const servicesFromOrders = ordersArray.map(order => ({
          id: order.id,
          user_id: order.user_id,
          service_name: order.service_name || order.item_name_en,
          service_type: order.order_type,
          status: order.status,
          purchase_date: order.created_at,
          completion_date: order.updated_at,
          price: order.final_price,
          notes: order.notes
        }));
        setServices(servicesFromOrders);
      }
      if (systemServicesResult.data) setSystemServices(systemServicesResult.data);
      if (technicalServicesResult.data) setTechnicalServices(technicalServicesResult.data);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateServiceStatus = async (serviceId: string, status: UserService['status']) => {
    try {
      const { error } = await updateOrderStatus(serviceId, status);
      if (!error) {
        loadData();
      }
    } catch (error) {
      console.error('Error updating service status:', error);
    }
  };

  const handleUpdateUserRole = async (userId: string, role: 'user' | 'admin') => {
    try {
      const { error } = await updateUserRole(userId, role);
      if (!error) {
        loadData();
      }
    } catch (error) {
      console.error('Error updating user role:', error);
    }
  };

  const handleSaveService = async (serviceData: Partial<BaseService>) => {
    try {
      const dataToSave = {
        name: serviceData.name!,
        description: serviceData.description!,
        price: serviceData.price!,
        category: serviceData.category!,
        features: serviceData.features!,
        video_url: serviceData.video_url,
        image_url: serviceData.image_url,
        tech_specs: serviceData.tech_specs!,
        status: serviceData.status!,
      };

      if (serviceType === 'system') {
        if (serviceData.id) {
          await updateSystemService(serviceData.id, dataToSave);
        } else {
          await createSystemService(dataToSave);
        }
      } else {
        if (serviceData.id) {
          await updateTechnicalService(serviceData.id, dataToSave);
        } else {
          await createTechnicalService(dataToSave);
        }
      }
      
      setShowServiceModal(false);
      setEditingService(null);
      await loadData();
    } catch (error) {
      console.error('Error saving service:', error);
      showNotification({ type: 'error', message: 'Failed to save service.' });
    }
  };

  const handleDeleteService = async (serviceId: string, type: 'system' | 'technical') => {
    showNotification({
      type: 'confirm',
      message: t('notifications.deleteServiceConfirm'),
      onConfirm: async () => {
        try {
          if (type === 'system') {
            await deleteSystemService(serviceId);
          } else {
            await deleteTechnicalService(serviceId);
          }
          await loadData();
        } catch (error) {
          console.error('Error deleting service:', error);
        }
      }
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'pending': return <Clock className="w-5 h-5 text-yellow-400" />;
      case 'completed': return <CheckCircle className="w-5 h-5 text-blue-400" />;
      case 'cancelled': return <XCircle className="w-5 h-5 text-red-400" />;
      default: return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  // Tabs are now managed by AdminSidebar component

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-xl p-6 border border-blue-500/30">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-400 text-sm font-medium">{t('admin.dashboard.totalUsers')}</p>
                    <p className="text-3xl font-bold text-white">{users.length}</p>
                  </div>
                  <Users className="w-8 h-8 text-blue-400" />
                </div>
              </div>

              <div className="bg-gradient-to-br from-green-500/20 to-green-600/20 rounded-xl p-6 border border-green-500/30">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-400 text-sm font-medium">{t('admin.dashboard.activeOrders')}</p>
                    <p className="text-3xl font-bold text-white">{services.filter(s => s.status === 'active').length}</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-400" />
                </div>
              </div>

              <div className="bg-gradient-to-br from-yellow-500/20 to-yellow-600/20 rounded-xl p-6 border border-yellow-500/30">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-yellow-400 text-sm font-medium">{t('admin.dashboard.pendingOrders')}</p>
                    <p className="text-3xl font-bold text-white">{services.filter(s => s.status === 'pending').length}</p>
                  </div>
                  <Clock className="w-8 h-8 text-yellow-400" />
                </div>
              </div>

              <div className="bg-gradient-to-br from-purple-500/20 to-purple-600/20 rounded-xl p-6 border border-purple-500/30">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-400 text-sm font-medium">{t('admin.dashboard.totalRevenue')}</p>
                    <p className="text-3xl font-bold text-white">${services.filter(service => service.status === 'completed').reduce((total, service) => total + service.price, 0)}</p>
                  </div>
                  <DollarSign className="w-8 h-8 text-purple-400" />
                </div>
              </div>
            </div>

            {/* Service Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gradient-to-br from-primary/50 to-background/50 rounded-xl p-6 border border-accent/20">
                <h3 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 rtl:space-x-reverse">
                  <Package className="w-6 h-6 text-secondary" />
                  <span>{language === 'ar' ? 'الأنظمة التقنية' : 'Technical Systems'}</span>
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">{language === 'ar' ? 'إجمالي الأنظمة' : 'Total Systems'}</span>
                    <span className="text-secondary font-bold">{systemServices.length}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">{language === 'ar' ? 'الأنظمة النشطة' : 'Active Systems'}</span>
                    <span className="text-green-400 font-bold">{systemServices.filter(s => s.status === 'active').length}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-primary/50 to-background/50 rounded-xl p-6 border border-accent/20">
                <h3 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 rtl:space-x-reverse">
                  <Wrench className="w-6 h-6 text-accent" />
                  <span>{language === 'ar' ? 'الخدمات التقنية' : 'Technical Services'}</span>
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">{language === 'ar' ? 'إجمالي الخدمات' : 'Total Services'}</span>
                    <span className="text-accent font-bold">{technicalServices.length}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">{language === 'ar' ? 'الخدمات النشطة' : 'Active Services'}</span>
                    <span className="text-green-400 font-bold">{technicalServices.filter(s => s.status === 'active').length}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-gradient-to-br from-primary/50 to-background/50 rounded-xl p-6 border border-accent/20">
              <h3 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 rtl:space-x-reverse">
                <TrendingUp className="w-6 h-6 text-secondary" />
                <span>{language === 'ar' ? 'النشاط الأخير' : 'Recent Activity'}</span>
              </h3>
              <div className="space-y-3">
                {getRecentActivities(8).length > 0 ? (
                  getRecentActivities(8).map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between p-3 bg-primary/30 rounded-lg">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center bg-${activity.color}-500/20`}>
                          {activity.icon === 'Plus' && <Plus className={`w-4 h-4 text-${activity.color}-400`} />}
                          {activity.icon === 'Edit' && <Edit className={`w-4 h-4 text-${activity.color}-400`} />}
                          {activity.icon === 'Trash2' && <Trash2 className={`w-4 h-4 text-${activity.color}-400`} />}
                          {activity.icon === 'FileText' && <FileText className={`w-4 h-4 text-${activity.color}-400`} />}
                          {activity.icon === 'CheckCircle' && <CheckCircle className={`w-4 h-4 text-${activity.color}-400`} />}
                        </div>
                        <div>
                          <p className="text-white font-medium">{activity.action}</p>
                          <p className="text-gray-400 text-sm">{activity.entityName}</p>
                          <p className="text-gray-500 text-xs">{new Date(activity.timestamp).toLocaleDateString()}</p>
                        </div>
                      </div>
                      <span className="text-gray-400 text-sm">{activity.user}</span>
                    </div>
                  ))
                ) : (
                  // Fallback to old service display if no activities
                  services.slice(0, 5).map((service) => (
                    <div key={service.id} className="flex items-center justify-between p-3 bg-primary/30 rounded-lg">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        {getStatusIcon(service.status)}
                        <div>
                          <p className="text-white font-medium">{service.service_name}</p>
                          <p className="text-gray-400 text-sm">{new Date(service.purchase_date).toLocaleDateString()}</p>
                        </div>
                      </div>
                      <span className="text-accent font-bold">${service.price}</span>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        );

      case 'systems':
        return <TechnicalSystemsManager onBack={() => setActiveTab('overview')} />;

      case 'technical':
        return <TechnicalServicesManager onBack={() => setActiveTab('overview')} />;

      case 'orders':
        return <OrderManagement onBack={() => setActiveTab('overview')} />;

      case 'users':
        return <EnhancedUserManager onBack={() => setActiveTab('overview')} />;

      case 'premium':
        return <PremiumContentManager onBack={() => setActiveTab('overview')} />;

      case 'settings':
        return <ComprehensiveSystemSettings onBack={() => setActiveTab('overview')} />;

      default:
        return (
          <div className="bg-gradient-to-br from-primary/50 to-background/50 rounded-xl p-6 border border-accent/20">
            <p className="text-gray-300">{language === 'ar' ? 'المحتوى قيد التطوير' : 'Content under development'}</p>
          </div>
        );
    }
  };
return (
    <div className="fixed inset-0 bg-background z-50 overflow-auto">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary to-background border-b border-accent/20 p-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-gradient-to-br from-secondary to-accent rounded-lg flex items-center justify-center">
              <Shield className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                {t('admin.dashboard.title')}
              </h1>
              <p className="text-accent">{userProfile?.full_name || userProfile?.username}</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <BackButton onClick={onClose} variant="home" size="md" />
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Enhanced Sidebar */}
        <AdminSidebar
          activeTab={activeTab}
          onTabChange={setActiveTab}
          isCollapsed={sidebarCollapsed}
          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        />

        {/* Main Content */}
        <div className={`flex-1 p-4 lg:p-8 transition-all duration-300 ${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'}`}>
          {loading ? (
            <div className="text-center py-8">
              <div className="w-8 h-8 border-2 border-secondary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-300">{language === 'ar' ? 'جاري التحميل...' : 'Loading...'}</p>
            </div>
          ) : (
            renderTabContent()
          )}
        </div>
      </div>

      {showServiceModal && (
        <ServiceModal
          service={editingService}
          serviceType={serviceType}
          onSave={handleSaveService}
          onClose={() => {
            setShowServiceModal(false);
            setEditingService(null);
          }}
          language={language}
        />
      )}
    </div>
  );
};

// Service Card Component
interface ServiceCardProps {
  service: BaseService;
  onEdit: () => void;
  onDelete: () => void;
  language: 'ar' | 'en';
}

const ServiceCard: React.FC<ServiceCardProps> = ({ service, onEdit, onDelete, language }) => {
  return (
    <div className="bg-gradient-to-br from-primary/50 to-background/50 rounded-xl p-6 border border-accent/20">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h4 className="text-xl font-bold text-white mb-2">{language === 'ar' ? service.name_ar : service.name_en}</h4>
          <p className="text-gray-300 mb-3">{language === 'ar' ? service.description_ar : service.description_en}</p>
          <div className="flex items-center space-x-4 rtl:space-x-reverse mb-3">
            <span className="text-accent font-bold text-lg">${service.price}</span>
            <span className={`px-3 py-1 rounded-full text-sm ${
              service.status === 'active' 
                ? 'bg-green-500/20 text-green-400' 
                : 'bg-red-500/20 text-red-400'
            }`}>
              {service.status === 'active' 
                ? (language === 'ar' ? 'نشط' : 'Active')
                : (language === 'ar' ? 'غير نشط' : 'Inactive')
              }
            </span>
          </div>
          
          <div className="mb-3">
            <h5 className="text-secondary font-semibold mb-2">{language === 'ar' ? 'المميزات:' : 'Features:'}</h5>
            <div className="flex flex-wrap gap-2">
              {(language === 'ar' ? service.features_ar : service.features_en)?.map((feature, index) => (
                <span key={index} className="bg-secondary/20 text-secondary px-2 py-1 rounded text-sm">
                  {feature}
                </span>
              ))}
            </div>
          </div>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            {service.video_url && (
              <div className="flex items-center space-x-1 rtl:space-x-reverse text-red-400">
                <Video className="w-4 h-4" />
                <span className="text-sm">{language === 'ar' ? 'فيديو' : 'Video'}</span>
              </div>
            )}
            {service.image_url && (
              <div className="flex items-center space-x-1 rtl:space-x-reverse text-blue-400">
                <Image className="w-4 h-4" />
                <span className="text-sm">{language === 'ar' ? 'صورة' : 'Image'}</span>
              </div>
            )}
          </div>
        </div>

        <div className="flex space-x-2 rtl:space-x-reverse">
          <button
            onClick={onEdit}
            className="p-2 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors duration-200"
          >
            <Edit className="w-5 h-5" />
          </button>
          <button
            onClick={onDelete}
            className="p-2 bg-red-500/20 text-red-400 rounded-lg hover:bg-red-500/30 transition-colors duration-200"
          >
            <Trash2 className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

// Service Modal Component
interface ServiceModalProps {
  service: Partial<BaseService> | null;
  serviceType: 'system' | 'technical';
  onSave: (service: Partial<BaseService>) => void;
  onClose: () => void;
  language: 'ar' | 'en';
}

const ServiceModal: React.FC<ServiceModalProps> = ({ service, serviceType, onSave, onClose, language }) => {
  const [formData, setFormData] = useState<Partial<BaseService>>({
    name: { ar: '', en: '' },
    description: { ar: '', en: '' },
    features: { ar: [], en: [] },
    tech_specs: { ar: [], en: [] },
    price: 0,
    category: 'general',
    video_url: '',
    image_url: '',
    status: 'active'
  });

  useEffect(() => {
    if (service) {
        setFormData({
            ...service,
            name: service.name || { ar: '', en: '' },
            description: service.description || { ar: '', en: '' },
            features: service.features || { ar: [], en: [] },
            tech_specs: service.tech_specs || { ar: [], en: [] },
        });
    }
  }, [service]);

  const handleTextChange = (lang: 'ar' | 'en', field: 'name' | 'description', value: string) => {
    setFormData(prev => {
        const currentField = prev[field] || { ar: '', en: '' };
        return { ...prev, [field]: { ...currentField, [lang]: value } };
    });
  };

  const handleArrayChange = (lang: 'ar' | 'en', field: 'features' | 'tech_specs', index: number, value: string) => {
    setFormData(prev => {
        const currentField = prev[field] || { ar: [], en: [] };
        const newLangArray = [...(currentField[lang] || [])];
        newLangArray[index] = value;
        return {
            ...prev,
            [field]: { ...currentField, [lang]: newLangArray }
        };
    });
  };

  const addArrayItem = (lang: 'ar' | 'en', field: 'features' | 'tech_specs') => {
    setFormData(prev => {
        const currentField = prev[field] || { ar: [], en: [] };
        const currentLangArray = currentField[lang] || [];
        return {
            ...prev,
            [field]: { ...currentField, [lang]: [...currentLangArray, ''] }
        };
    });
  };
  
  const removeArrayItem = (lang: 'ar' | 'en', field: 'features' | 'tech_specs', index: number) => {
    setFormData(prev => {
        const currentField = prev[field] || { ar: [], en: [] };
        const currentLangArray = currentField[lang] || [];
        return {
            ...prev,
            [field]: { ...currentField, [lang]: currentLangArray.filter((_, i) => i !== index) }
        };
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };
  
  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-primary to-background border border-accent/30 rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-auto">
        <div className="p-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-white">
              {service?.id
                ? (language === 'ar' ? `تعديل ${serviceType === 'system' ? 'النظام' : 'الخدمة'}` : `Edit ${serviceType === 'system' ? 'System' : 'Service'}`)
                : (language === 'ar' ? `إضافة ${serviceType === 'system' ? 'نظام' : 'خدمة'} جديد` : `Add New ${serviceType === 'system' ? 'System' : 'Service'}`)
              }
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors duration-200"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-secondary mb-2">اسم الخدمة (العربية)</label>
                <input
                  type="text"
                  value={formData.name?.ar || ''}
                  onChange={(e) => handleTextChange('ar', 'name', e.target.value)}
                  required
                  className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary mb-2">Service Name (English)</label>
                <input
                  type="text"
                  value={formData.name?.en || ''}
                  onChange={(e) => handleTextChange('en', 'name', e.target.value)}
                  required
                  className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-secondary mb-2">الوصف (العربية)</label>
                <textarea
                  value={formData.description?.ar || ''}
                  onChange={(e) => handleTextChange('ar', 'description', e.target.value)}
                  rows={4}
                  className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary mb-2">Description (English)</label>
                <textarea
                  value={formData.description?.en || ''}
                  onChange={(e) => handleTextChange('en', 'description', e.target.value)}
                  rows={4}
                  className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                />
              </div>
            </div>

            {/* Features (Arabic) */}
            <div>
              <div className="flex justify-between items-center mb-3">
                <label className="block text-sm font-medium text-secondary">المميزات (العربية)</label>
                <button type="button" onClick={() => addArrayItem('ar', 'features')} className="text-secondary hover:text-secondary/80 text-sm flex items-center space-x-1 rtl:space-x-reverse">
                  <Plus className="w-4 h-4" /> <span>إضافة</span>
                </button>
              </div>
              <div className="space-y-2">
                {formData.features?.ar?.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                    <input type="text" value={feature} onChange={(e) => handleArrayChange('ar', 'features', index, e.target.value)} className="flex-1 px-4 py-2 bg-primary/50 border border-accent/30 rounded-lg text-white" />
                    <button type="button" onClick={() => removeArrayItem('ar', 'features', index)} className="text-red-400 hover:text-red-300"><Trash2 className="w-4 h-4"/></button>
                  </div>
                ))}
              </div>
            </div>

            {/* Features (English) */}
            <div>
              <div className="flex justify-between items-center mb-3">
                <label className="block text-sm font-medium text-secondary">Features (English)</label>
                <button type="button" onClick={() => addArrayItem('en', 'features')} className="text-secondary hover:text-secondary/80 text-sm flex items-center space-x-1">
                  <Plus className="w-4 h-4" /> <span>Add</span>
                </button>
              </div>
              <div className="space-y-2">
                {formData.features?.en?.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input type="text" value={feature} onChange={(e) => handleArrayChange('en', 'features', index, e.target.value)} className="flex-1 px-4 py-2 bg-primary/50 border border-accent/30 rounded-lg text-white" />
                    <button type="button" onClick={() => removeArrayItem('en', 'features', index)} className="text-red-400 hover:text-red-300"><Trash2 className="w-4 h-4"/></button>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="flex justify-end space-x-4 rtl:space-x-reverse pt-6 border-t border-gray-700">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-3 border border-accent/30 text-accent rounded-lg hover:bg-accent/10 transition-all duration-300"
              >
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </button>
              <button
                type="submit"
                className="bg-gradient-to-r from-secondary to-accent text-primary font-bold px-6 py-3 rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-secondary/25 flex items-center space-x-2 rtl:space-x-reverse"
              >
                <Save className="w-5 h-5" />
                <span>{language === 'ar' ? 'حفظ' : 'Save'}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;