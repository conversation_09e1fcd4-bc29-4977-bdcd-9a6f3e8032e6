import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { 
  getSystemServices,
  createSystemService,
  updateSystemService,
  deleteSystemService,
  SystemService 
} from '../lib/database';
import { X, Play, CheckCircle, XCircle } from 'lucide-react';

interface QuickTestProps {
  onClose: () => void;
}

/**
 * Quick test for debugging CRUD operations
 */
const QuickTest: React.FC<QuickTestProps> = ({ onClose }) => {
  const { language } = useTranslation();
  const [logs, setLogs] = useState<string[]>([]);
  const [testSystem, setTestSystem] = useState<SystemService | null>(null);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `${timestamp}: ${message}`]);
    console.log(`QuickTest: ${message}`);
  };

  const runQuickTest = () => {
    setLogs([]);
    addLog('Starting quick test...');

    // Test 1: Get systems
    addLog('1. Testing getSystemServices...');
    const systems = getSystemServices();
    addLog(`Found ${systems.data?.length || 0} systems`);

    // Test 2: Create system
    addLog('2. Testing createSystemService...');
    const testData = {
      name: { ar: 'نظام اختبار سريع', en: 'Quick Test System' },
      description: { ar: 'وصف الاختبار السريع', en: 'Quick test description' },
      price: 99,
      category: 'test',
      features: { ar: ['ميزة 1', 'ميزة 2'], en: ['Feature 1', 'Feature 2'] },
      tech_specs: { ar: ['مواصفة 1'], en: ['Spec 1'] },
      video_url: '',
      image_url: '',
      status: 'active' as const
    };

    const createResult = createSystemService(testData);
    if (createResult.data) {
      addLog(`✅ Created system: ${createResult.data.name.en} (ID: ${createResult.data.id})`);
      setTestSystem(createResult.data);
      
      // Test 3: Update system
      addLog('3. Testing updateSystemService...');
      const updateData = {
        name: { ar: 'نظام محدث سريع', en: 'Updated Quick Test System' },
        price: 150
      };
      
      const updateResult = updateSystemService(createResult.data.id, updateData);
      if (updateResult.data) {
        addLog(`✅ Updated system: ${updateResult.data.name.en}`);
        setTestSystem(updateResult.data);
        
        // Test 4: Delete system
        addLog('4. Testing deleteSystemService...');
        const deleteResult = deleteSystemService(createResult.data.id);
        if (deleteResult.data) {
          addLog(`✅ Deleted system successfully`);
          setTestSystem(null);
        } else {
          addLog(`❌ Delete failed: ${deleteResult.error?.message}`);
        }
      } else {
        addLog(`❌ Update failed: ${updateResult.error?.message}`);
      }
    } else {
      addLog(`❌ Create failed: ${createResult.error?.message}`);
    }

    addLog('Quick test completed!');
  };

  const testModalFunctionality = () => {
    addLog('Testing modal functionality...');
    
    // Simulate modal operations
    if (testSystem) {
      addLog(`✅ Test system available: ${testSystem.name.en}`);
      addLog(`✅ Edit modal would work with system ID: ${testSystem.id}`);
      addLog(`✅ Preview modal would work with system data`);
    } else {
      addLog(`❌ No test system available for modal testing`);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 overflow-y-auto">
      <div className="min-h-screen py-8">
        <div className="max-w-4xl mx-auto bg-primary rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-green-600 to-teal-600 p-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-white">
                {language === 'ar' ? 'اختبار سريع' : 'Quick Test'}
              </h2>
              <button
                onClick={onClose}
                className="w-10 h-10 bg-white/20 hover:bg-white/40 rounded-full flex items-center justify-center transition-colors"
              >
                <X className="w-6 h-6 text-white" />
              </button>
            </div>
          </div>

          <div className="p-6">
            {/* Controls */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <button
                onClick={runQuickTest}
                className="flex items-center justify-center space-x-2 rtl:space-x-reverse px-6 py-3 bg-green-500/20 text-green-400 border border-green-500/30 rounded-lg hover:bg-green-500/30 transition-colors"
              >
                <Play className="w-5 h-5" />
                <span>{language === 'ar' ? 'تشغيل الاختبار السريع' : 'Run Quick Test'}</span>
              </button>

              <button
                onClick={testModalFunctionality}
                className="flex items-center justify-center space-x-2 rtl:space-x-reverse px-6 py-3 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded-lg hover:bg-blue-500/30 transition-colors"
              >
                <CheckCircle className="w-5 h-5" />
                <span>{language === 'ar' ? 'اختبار النوافذ' : 'Test Modals'}</span>
              </button>
            </div>

            {/* Test System Info */}
            {testSystem && (
              <div className="mb-6 bg-background/50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-3">
                  {language === 'ar' ? 'النظام المختبر' : 'Test System'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">{language === 'ar' ? 'المعرف:' : 'ID:'}</span>
                    <span className="text-white ml-2">{testSystem.id}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">{language === 'ar' ? 'الاسم:' : 'Name:'}</span>
                    <span className="text-white ml-2">{testSystem.name[language]}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">{language === 'ar' ? 'السعر:' : 'Price:'}</span>
                    <span className="text-white ml-2">${testSystem.price}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">{language === 'ar' ? 'الحالة:' : 'Status:'}</span>
                    <span className="text-white ml-2">{testSystem.status}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Logs */}
            <div className="bg-background/50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-4">
                {language === 'ar' ? 'سجل الاختبار' : 'Test Logs'}
              </h3>
              <div className="bg-black/50 rounded p-3 h-64 overflow-y-auto">
                {logs.map((log, index) => (
                  <div key={index} className="text-sm text-gray-300 mb-1 font-mono">
                    {log}
                  </div>
                ))}
                {logs.length === 0 && (
                  <div className="text-gray-500 text-sm">
                    {language === 'ar' ? 'لا توجد سجلات بعد...' : 'No logs yet...'}
                  </div>
                )}
              </div>
            </div>

            {/* Debug Info */}
            <div className="mt-6 bg-background/50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-3">
                {language === 'ar' ? 'معلومات التشخيص' : 'Debug Info'}
              </h3>
              <div className="text-sm space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-400">{language === 'ar' ? 'النظام المختبر:' : 'Test System:'}</span>
                  <span className="text-white">{testSystem ? 'Available' : 'None'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">{language === 'ar' ? 'معرف النظام:' : 'System ID:'}</span>
                  <span className="text-white">{testSystem?.id || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">{language === 'ar' ? 'اسم النظام:' : 'System Name:'}</span>
                  <span className="text-white">{testSystem?.name.en || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">{language === 'ar' ? 'وقت الإنشاء:' : 'Created At:'}</span>
                  <span className="text-white">{testSystem?.created_at || 'N/A'}</span>
                </div>
              </div>
            </div>

            {/* Instructions */}
            <div className="mt-6 p-4 bg-gray-800/50 border border-gray-600 rounded-lg">
              <h4 className="text-lg font-semibold text-white mb-2">
                {language === 'ar' ? 'التعليمات' : 'Instructions'}
              </h4>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• {language === 'ar' ? 'انقر على "تشغيل الاختبار السريع" لاختبار عمليات CRUD' : 'Click "Run Quick Test" to test CRUD operations'}</li>
                <li>• {language === 'ar' ? 'انقر على "اختبار النوافذ" لفحص وظائف النوافذ المنبثقة' : 'Click "Test Modals" to check modal functionality'}</li>
                <li>• {language === 'ar' ? 'راقب السجلات لمعرفة نتائج الاختبارات' : 'Watch the logs to see test results'}</li>
                <li>• {language === 'ar' ? 'تحقق من معلومات التشخيص لفهم حالة النظام' : 'Check debug info to understand system state'}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickTest;
