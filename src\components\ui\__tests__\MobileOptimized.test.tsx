/**
 * Mobile Optimized Components Tests
 * 
 * Tests for mobile-optimized UI components
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '../../../test/utils'
import { 
  TouchButton, 
  MobileNav, 
  MobileCard, 
  MobileInput, 
  MobileListItem,
  MobileBottomNav,
  MobileModal,
  useSwipeGesture 
} from '../MobileOptimized'
import React from 'react'

describe('TouchButton', () => {
  it('renders with default props', () => {
    render(<TouchButton>Click me</TouchButton>)
    
    const button = screen.getByRole('button', { name: 'Click me' })
    expect(button).toBeInTheDocument()
    expect(button).toHaveClass('min-h-[48px]') // default md size
  })

  it('applies correct size classes', () => {
    const { rerender } = render(<TouchButton size="sm">Small</TouchButton>)
    expect(screen.getByRole('button')).toHaveClass('min-h-[44px]')

    rerender(<TouchButton size="lg">Large</TouchButton>)
    expect(screen.getByRole('button')).toHaveClass('min-h-[52px]')
  })

  it('applies variant classes correctly', () => {
    const { rerender } = render(<TouchButton variant="primary">Primary</TouchButton>)
    expect(screen.getByRole('button')).toHaveClass('bg-interactive-primary')

    rerender(<TouchButton variant="secondary">Secondary</TouchButton>)
    expect(screen.getByRole('button')).toHaveClass('bg-interactive-secondary')

    rerender(<TouchButton variant="ghost">Ghost</TouchButton>)
    expect(screen.getByRole('button')).toHaveClass('bg-transparent')
  })

  it('handles click events', () => {
    const handleClick = vi.fn()
    render(<TouchButton onClick={handleClick}>Click me</TouchButton>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('supports full width', () => {
    render(<TouchButton fullWidth>Full Width</TouchButton>)
    expect(screen.getByRole('button')).toHaveClass('w-full')
  })

  it('handles disabled state', () => {
    const handleClick = vi.fn()
    render(<TouchButton disabled onClick={handleClick}>Disabled</TouchButton>)
    
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(button).toHaveClass('disabled:opacity-50')
    
    fireEvent.click(button)
    expect(handleClick).not.toHaveBeenCalled()
  })
})

describe('MobileNav', () => {
  it('renders menu button when closed', () => {
    render(
      <MobileNav isOpen={false} onToggle={vi.fn()}>
        <div>Nav content</div>
      </MobileNav>
    )
    
    expect(screen.getByLabelText('Toggle navigation menu')).toBeInTheDocument()
    expect(screen.queryByText('Nav content')).not.toBeInTheDocument()
  })

  it('renders close button and content when open', () => {
    render(
      <MobileNav isOpen={true} onToggle={vi.fn()}>
        <div>Nav content</div>
      </MobileNav>
    )
    
    expect(screen.getByText('Nav content')).toBeInTheDocument()
  })

  it('calls onToggle when menu button is clicked', () => {
    const handleToggle = vi.fn()
    render(
      <MobileNav isOpen={false} onToggle={handleToggle}>
        <div>Nav content</div>
      </MobileNav>
    )
    
    fireEvent.click(screen.getByLabelText('Toggle navigation menu'))
    expect(handleToggle).toHaveBeenCalledTimes(1)
  })

  it('has proper touch target size', () => {
    render(
      <MobileNav isOpen={false} onToggle={vi.fn()}>
        <div>Nav content</div>
      </MobileNav>
    )
    
    const button = screen.getByLabelText('Toggle navigation menu')
    expect(button).toHaveClass('min-h-[44px]', 'min-w-[44px]')
  })
})

describe('MobileCard', () => {
  it('renders content correctly', () => {
    render(
      <MobileCard>
        <div>Card content</div>
      </MobileCard>
    )
    
    expect(screen.getByText('Card content')).toBeInTheDocument()
  })

  it('applies padding classes correctly', () => {
    const { rerender } = render(<MobileCard padding="sm">Content</MobileCard>)
    expect(screen.getByText('Content').parentElement).toHaveClass('p-3')

    rerender(<MobileCard padding="lg">Content</MobileCard>)
    expect(screen.getByText('Content').parentElement).toHaveClass('p-6')
  })

  it('handles clickable state', () => {
    const handleClick = vi.fn()
    render(
      <MobileCard clickable onClick={handleClick}>
        Clickable card
      </MobileCard>
    )
    
    const card = screen.getByRole('button')
    expect(card).toHaveClass('cursor-pointer', 'min-h-[44px]')
    
    fireEvent.click(card)
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('renders as div when not clickable', () => {
    render(<MobileCard>Non-clickable card</MobileCard>)
    
    expect(screen.queryByRole('button')).not.toBeInTheDocument()
    expect(screen.getByText('Non-clickable card')).toBeInTheDocument()
  })
})

describe('MobileInput', () => {
  it('renders input with label', () => {
    render(<MobileInput label="Test Label" placeholder="Enter text" />)
    
    expect(screen.getByLabelText('Test Label')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Enter text')).toBeInTheDocument()
  })

  it('shows required indicator', () => {
    render(<MobileInput label="Required Field" required />)
    
    expect(screen.getByText('*')).toBeInTheDocument()
  })

  it('displays error message', () => {
    render(<MobileInput label="Field" error="This field is required" />)
    
    expect(screen.getByText('This field is required')).toBeInTheDocument()
    expect(screen.getByRole('textbox')).toHaveClass('border-status-error')
  })

  it('handles value changes', () => {
    const handleChange = vi.fn()
    render(<MobileInput onChange={handleChange} />)
    
    const input = screen.getByRole('textbox')
    fireEvent.change(input, { target: { value: 'test value' } })
    
    expect(handleChange).toHaveBeenCalledWith('test value')
  })

  it('has proper touch target size', () => {
    render(<MobileInput />)
    
    expect(screen.getByRole('textbox')).toHaveClass('min-h-[48px]')
  })

  it('handles disabled state', () => {
    render(<MobileInput disabled />)
    
    const input = screen.getByRole('textbox')
    expect(input).toBeDisabled()
    expect(input).toHaveClass('disabled:opacity-50')
  })
})

describe('MobileListItem', () => {
  it('renders content correctly', () => {
    render(
      <MobileListItem>
        <div>List item content</div>
      </MobileListItem>
    )
    
    expect(screen.getByText('List item content')).toBeInTheDocument()
  })

  it('shows arrow when specified', () => {
    render(
      <MobileListItem showArrow>
        Item with arrow
      </MobileListItem>
    )
    
    // Check for chevron icon (arrow)
    expect(document.querySelector('svg')).toBeInTheDocument()
  })

  it('handles click events when clickable', () => {
    const handleClick = vi.fn()
    render(
      <MobileListItem onClick={handleClick}>
        Clickable item
      </MobileListItem>
    )
    
    const item = screen.getByRole('button')
    expect(item).toHaveClass('cursor-pointer')
    
    fireEvent.click(item)
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('has proper touch target size', () => {
    render(<MobileListItem>Item</MobileListItem>)
    
    expect(screen.getByText('Item').closest('div')).toHaveClass('min-h-[56px]')
  })
})

describe('MobileBottomNav', () => {
  const mockItems = [
    {
      id: 'home',
      label: 'Home',
      icon: <div>Home Icon</div>,
      active: true,
      onClick: vi.fn()
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: <div>Profile Icon</div>,
      active: false,
      onClick: vi.fn()
    }
  ]

  it('renders all navigation items', () => {
    render(<MobileBottomNav items={mockItems} />)
    
    expect(screen.getByText('Home')).toBeInTheDocument()
    expect(screen.getByText('Profile')).toBeInTheDocument()
    expect(screen.getByText('Home Icon')).toBeInTheDocument()
    expect(screen.getByText('Profile Icon')).toBeInTheDocument()
  })

  it('handles item clicks', () => {
    render(<MobileBottomNav items={mockItems} />)
    
    fireEvent.click(screen.getByText('Profile'))
    expect(mockItems[1].onClick).toHaveBeenCalledTimes(1)
  })

  it('applies active state styling', () => {
    render(<MobileBottomNav items={mockItems} />)
    
    const homeButton = screen.getByText('Home').closest('button')
    const profileButton = screen.getByText('Profile').closest('button')
    
    expect(homeButton).toHaveClass('text-interactive-primary')
    expect(profileButton).toHaveClass('text-text-tertiary')
  })

  it('has proper touch target size', () => {
    render(<MobileBottomNav items={mockItems} />)
    
    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      expect(button).toHaveClass('min-h-[60px]')
    })
  })
})

describe('MobileModal', () => {
  it('does not render when closed', () => {
    render(
      <MobileModal isOpen={false} onClose={vi.fn()}>
        Modal content
      </MobileModal>
    )
    
    expect(screen.queryByText('Modal content')).not.toBeInTheDocument()
  })

  it('renders when open', () => {
    render(
      <MobileModal isOpen={true} onClose={vi.fn()}>
        Modal content
      </MobileModal>
    )
    
    expect(screen.getByText('Modal content')).toBeInTheDocument()
  })

  it('renders title when provided', () => {
    render(
      <MobileModal isOpen={true} onClose={vi.fn()} title="Test Modal">
        Modal content
      </MobileModal>
    )
    
    expect(screen.getByText('Test Modal')).toBeInTheDocument()
  })

  it('calls onClose when backdrop is clicked', () => {
    const handleClose = vi.fn()
    render(
      <MobileModal isOpen={true} onClose={handleClose}>
        Modal content
      </MobileModal>
    )
    
    // Click backdrop
    const backdrop = document.querySelector('.bg-black')
    if (backdrop) {
      fireEvent.click(backdrop)
      expect(handleClose).toHaveBeenCalledTimes(1)
    }
  })

  it('calls onClose when close button is clicked', () => {
    const handleClose = vi.fn()
    render(
      <MobileModal isOpen={true} onClose={handleClose} title="Test Modal">
        Modal content
      </MobileModal>
    )
    
    // Find and click close button (X icon)
    const closeButton = document.querySelector('button svg')?.closest('button')
    if (closeButton) {
      fireEvent.click(closeButton)
      expect(handleClose).toHaveBeenCalledTimes(1)
    }
  })
})

describe('useSwipeGesture', () => {
  it('detects left swipe', () => {
    const onSwipeLeft = vi.fn()
    const onSwipeRight = vi.fn()
    
    const TestComponent = () => {
      const swipeHandlers = useSwipeGesture(onSwipeLeft, onSwipeRight, 50)
      return <div {...swipeHandlers}>Swipe me</div>
    }
    
    render(<TestComponent />)
    
    const element = screen.getByText('Swipe me')
    
    // Simulate left swipe (start at 100, end at 0)
    fireEvent.touchStart(element, {
      touches: [{ clientX: 100, clientY: 0 }]
    })
    fireEvent.touchMove(element, {
      touches: [{ clientX: 0, clientY: 0 }]
    })
    fireEvent.touchEnd(element)
    
    expect(onSwipeLeft).toHaveBeenCalledTimes(1)
    expect(onSwipeRight).not.toHaveBeenCalled()
  })

  it('detects right swipe', () => {
    const onSwipeLeft = vi.fn()
    const onSwipeRight = vi.fn()
    
    const TestComponent = () => {
      const swipeHandlers = useSwipeGesture(onSwipeLeft, onSwipeRight, 50)
      return <div {...swipeHandlers}>Swipe me</div>
    }
    
    render(<TestComponent />)
    
    const element = screen.getByText('Swipe me')
    
    // Simulate right swipe (start at 0, end at 100)
    fireEvent.touchStart(element, {
      touches: [{ clientX: 0, clientY: 0 }]
    })
    fireEvent.touchMove(element, {
      touches: [{ clientX: 100, clientY: 0 }]
    })
    fireEvent.touchEnd(element)
    
    expect(onSwipeRight).toHaveBeenCalledTimes(1)
    expect(onSwipeLeft).not.toHaveBeenCalled()
  })

  it('respects threshold setting', () => {
    const onSwipeLeft = vi.fn()
    
    const TestComponent = () => {
      const swipeHandlers = useSwipeGesture(onSwipeLeft, undefined, 100)
      return <div {...swipeHandlers}>Swipe me</div>
    }
    
    render(<TestComponent />)
    
    const element = screen.getByText('Swipe me')
    
    // Simulate small swipe (below threshold)
    fireEvent.touchStart(element, {
      touches: [{ clientX: 50, clientY: 0 }]
    })
    fireEvent.touchMove(element, {
      touches: [{ clientX: 25, clientY: 0 }]
    })
    fireEvent.touchEnd(element)
    
    expect(onSwipeLeft).not.toHaveBeenCalled()
  })
})
