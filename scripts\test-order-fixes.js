/**
 * Test Order System Fixes
 * Tests the enhanced order system with proper pricing and date display
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

// Test credentials
const adminCredentials = {
  email: '<EMAIL>',
  password: 'admin123'
};

const userCredentials = {
  email: '<EMAIL>',
  password: '123456'
};

let adminAuth = null;
let userAuth = null;

const logTest = (testName, status, details = '') => {
  const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
  console.log(`${statusIcon} ${testName}: ${status} ${details ? `- ${details}` : ''}`);
};

const logSection = (sectionName) => {
  console.log(`\n🔧 ${sectionName}`);
  console.log('='.repeat(50));
};

async function authenticateAdmin() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, adminCredentials);
    if (response.data.success && response.data.data.token) {
      adminAuth = {
        headers: { Authorization: `Bearer ${response.data.data.token}` }
      };
      logTest('Admin Authentication', 'PASS', 'Admin logged in successfully');
      return true;
    }
  } catch (error) {
    logTest('Admin Authentication', 'FAIL', error.response?.data?.error || error.message);
    return false;
  }
}

async function authenticateUser() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, userCredentials);
    if (response.data.success && response.data.data.token) {
      userAuth = {
        headers: { Authorization: `Bearer ${response.data.data.token}` }
      };
      logTest('User Authentication', 'PASS', 'User logged in successfully');
      return true;
    }
  } catch (error) {
    logTest('User Authentication', 'FAIL', error.response?.data?.error || error.message);
    return false;
  }
}

async function testOrderDataIntegrity() {
  logSection('ORDER DATA INTEGRITY TESTS');
  
  try {
    // Test admin orders endpoint
    const adminOrdersResponse = await axios.get(`${API_BASE}/admin/orders`, adminAuth);
    if (adminOrdersResponse.data.success) {
      const orders = adminOrdersResponse.data.data.orders || [];
      logTest('Admin Orders Fetch', 'PASS', `Found ${orders.length} orders`);
      
      if (orders.length > 0) {
        const testOrder = orders[0];
        
        // Test pricing data
        const hasValidPrice = testOrder.final_price > 0 || testOrder.total_price > 0 || testOrder.base_price > 0;
        logTest('Order Pricing Data', hasValidPrice ? 'PASS' : 'FAIL', 
          `Final: $${testOrder.final_price || 0}, Total: $${testOrder.total_price || 0}, Base: $${testOrder.base_price || 0}`);
        
        // Test date data
        const hasValidDate = testOrder.created_at && testOrder.created_at !== 'Invalid Date';
        logTest('Order Date Data', hasValidDate ? 'PASS' : 'FAIL', 
          `Created: ${testOrder.created_at}, Updated: ${testOrder.updated_at}`);
        
        // Test item names
        const hasValidNames = testOrder.item_name_ar || testOrder.item_name_en;
        logTest('Order Item Names', hasValidNames ? 'PASS' : 'FAIL', 
          `AR: ${testOrder.item_name_ar || 'N/A'}, EN: ${testOrder.item_name_en || 'N/A'}`);
        
        // Test user data
        const hasUserData = testOrder.username || testOrder.email;
        logTest('Order User Data', hasUserData ? 'PASS' : 'FAIL', 
          `User: ${testOrder.username || 'N/A'}, Email: ${testOrder.email || 'N/A'}`);
        
        // Test premium orders specifically
        if (testOrder.order_type === 'premium_package' || testOrder.order_type === 'premium_content') {
          logTest('Premium Order Detection', 'PASS', 
            `Type: ${testOrder.order_type}, Category: ${testOrder.order_category || 'N/A'}`);
          
          // Test order details
          if (testOrder.order_details || testOrder.customer_requirements) {
            try {
              const details = JSON.parse(testOrder.order_details || testOrder.customer_requirements || '{}');
              logTest('Premium Order Details', 'PASS', 
                `Has details: ${Object.keys(details).length} fields`);
            } catch (e) {
              logTest('Premium Order Details', 'WARN', 'Details not in JSON format');
            }
          }
        }
      }
    } else {
      logTest('Admin Orders Fetch', 'FAIL', 'Failed to fetch admin orders');
    }
    
    // Test user orders endpoint
    const userOrdersResponse = await axios.get(`${API_BASE}/orders`, userAuth);
    if (userOrdersResponse.data.success) {
      const userOrders = userOrdersResponse.data.data.orders || [];
      logTest('User Orders Fetch', 'PASS', `Found ${userOrders.length} user orders`);
      
      if (userOrders.length > 0) {
        const testUserOrder = userOrders[0];
        
        // Test enhanced user order data
        const hasEnhancedData = testUserOrder.username || testUserOrder.email;
        logTest('Enhanced User Order Data', hasEnhancedData ? 'PASS' : 'FAIL', 
          `User: ${testUserOrder.username || 'N/A'}`);
      }
    } else {
      logTest('User Orders Fetch', 'FAIL', 'Failed to fetch user orders');
    }
    
  } catch (error) {
    logTest('Order Data Integrity', 'FAIL', error.response?.data?.error || error.message);
  }
}

async function testPremiumOrderCreation() {
  logSection('PREMIUM ORDER CREATION TEST');
  
  try {
    // Get premium content first
    const premiumResponse = await axios.get(`${API_BASE}/premium-content`);
    if (premiumResponse.data.success && premiumResponse.data.data.length > 0) {
      const premiumItem = premiumResponse.data.data[0];
      
      // Create a test premium order
      const orderData = {
        order_type: 'premium_package',
        order_category: 'premium_custom',
        item_id: premiumItem.id,
        quantity: 1,
        selected_systems: ['system-1', 'system-2'],
        selected_services: ['service-1'],
        subscription_type: 'monthly',
        maintenance_included: true,
        installation_included: true,
        support_level: 'premium',
        notes_ar: 'طلب اختبار للنسخة المميزة مع أنظمة إضافية',
        notes_en: 'Test premium edition order with additional systems',
        customer_requirements: JSON.stringify({
          is_premium_edition: true,
          base_package: {
            id: premiumItem.id,
            name_ar: premiumItem.title_ar,
            name_en: premiumItem.title_en,
            price: premiumItem.price
          },
          selected_systems: ['system-1', 'system-2'],
          selected_services: ['service-1'],
          pricing_breakdown: {
            base_price: parseFloat(premiumItem.price),
            addons_price: 200,
            total_price: parseFloat(premiumItem.price) + 200
          }
        })
      };
      
      const createResponse = await axios.post(`${API_BASE}/orders`, orderData, userAuth);
      if (createResponse.data.success) {
        logTest('Premium Order Creation', 'PASS', 
          `Order created: ${createResponse.data.data.order_number}`);
        
        // Verify the created order
        const orderId = createResponse.data.data.id;
        const verifyResponse = await axios.get(`${API_BASE}/admin/orders`, adminAuth);
        if (verifyResponse.data.success) {
          const createdOrder = verifyResponse.data.data.orders.find(o => o.id === orderId);
          if (createdOrder) {
            logTest('Premium Order Verification', 'PASS', 
              `Found created order with price: $${createdOrder.final_price || createdOrder.total_price || 0}`);
          } else {
            logTest('Premium Order Verification', 'FAIL', 'Created order not found');
          }
        }
      } else {
        logTest('Premium Order Creation', 'FAIL', 
          createResponse.data.error || 'Unknown error');
      }
    } else {
      logTest('Premium Content Fetch', 'FAIL', 'No premium content available for testing');
    }
  } catch (error) {
    logTest('Premium Order Creation', 'FAIL', error.response?.data?.error || error.message);
  }
}

async function testOrderStatusWorkflow() {
  logSection('ORDER STATUS WORKFLOW TEST');
  
  try {
    // Get first order for testing
    const ordersResponse = await axios.get(`${API_BASE}/admin/orders`, adminAuth);
    if (ordersResponse.data.success && ordersResponse.data.data.orders.length > 0) {
      const testOrder = ordersResponse.data.data.orders[0];
      const originalStatus = testOrder.status;
      
      // Test status update
      const updateData = {
        status: 'confirmed',
        admin_notes: 'Test status update',
        priority: 'high'
      };
      
      const updateResponse = await axios.put(`${API_BASE}/admin/orders/${testOrder.id}`, updateData, adminAuth);
      if (updateResponse.data.success) {
        logTest('Order Status Update', 'PASS', 
          `Updated from ${originalStatus} to confirmed`);
        
        // Revert back to original status
        const revertResponse = await axios.put(`${API_BASE}/admin/orders/${testOrder.id}`, 
          { status: originalStatus }, adminAuth);
        if (revertResponse.data.success) {
          logTest('Order Status Revert', 'PASS', 
            `Reverted back to ${originalStatus}`);
        }
      } else {
        logTest('Order Status Update', 'FAIL', 
          updateResponse.data.error || 'Unknown error');
      }
    } else {
      logTest('Order Status Workflow', 'SKIP', 'No orders available for testing');
    }
  } catch (error) {
    logTest('Order Status Workflow', 'FAIL', error.response?.data?.error || error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting Order System Fix Tests');
  console.log('=====================================\n');
  
  // Authenticate
  const adminAuth = await authenticateAdmin();
  const userAuth = await authenticateUser();
  
  if (!adminAuth || !userAuth) {
    console.log('\n❌ Authentication failed. Cannot proceed with tests.');
    return;
  }
  
  // Run tests
  await testOrderDataIntegrity();
  await testPremiumOrderCreation();
  await testOrderStatusWorkflow();
  
  console.log('\n🎉 Order System Fix Tests Completed!');
  console.log('=====================================');
}

// Run the tests
runTests().catch(console.error);
