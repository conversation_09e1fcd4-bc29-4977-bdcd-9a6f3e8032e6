/**
 * Enhanced Error Boundary Component
 * 
 * Provides comprehensive error handling with user-friendly messages
 * and error reporting capabilities
 */

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Alert<PERSON>riangle, RefreshCw, Home, Bug } from 'lucide-react'

interface Props {
  children: ReactNode
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  showDetails?: boolean
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string
}

class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0
  private maxRetries = 3

  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Generate unique error ID for tracking
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    return {
      hasError: true,
      error,
      errorId
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    // Update state with error info
    this.setState({
      error,
      errorInfo
    })

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // Report error to monitoring service (if available)
    this.reportError(error, errorInfo)
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // Here you would integrate with error reporting services like Sentry
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    // For now, just log to console
    console.error('Error Report:', errorReport)
    
    // In production, send to error reporting service:
    // errorReportingService.captureException(error, { extra: errorReport })
  }

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: ''
      })
    }
  }

  private handleReload = () => {
    window.location.reload()
  }

  private handleGoHome = () => {
    window.location.href = '/'
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return (
          <FallbackComponent 
            error={this.state.error!} 
            resetError={this.handleRetry}
          />
        )
      }

      // Default error UI
      return (
        <div className="min-h-screen bg-background-primary flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-background-card rounded-lg border border-background-border p-6 text-center">
            {/* Error Icon */}
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-status-errorBg rounded-full flex items-center justify-center">
                <AlertTriangle className="w-8 h-8 text-status-error" />
              </div>
            </div>

            {/* Error Title */}
            <h1 className="text-xl font-bold text-text-primary mb-2">
              عذراً، حدث خطأ غير متوقع
            </h1>
            <p className="text-sm text-text-secondary mb-6">
              Sorry, something went wrong
            </p>

            {/* Error Message */}
            <div className="bg-background-tertiary rounded-md p-3 mb-6 text-left">
              <p className="text-sm text-text-tertiary">
                <strong>خطأ:</strong> {this.state.error?.message || 'Unknown error'}
              </p>
              {this.props.showDetails && this.state.errorInfo && (
                <details className="mt-2">
                  <summary className="text-xs text-text-tertiary cursor-pointer hover:text-text-secondary">
                    تفاصيل تقنية
                  </summary>
                  <pre className="text-xs text-text-tertiary mt-2 overflow-auto max-h-32">
                    {this.state.error?.stack}
                  </pre>
                </details>
              )}
            </div>

            {/* Error ID */}
            <p className="text-xs text-text-tertiary mb-6">
              معرف الخطأ: {this.state.errorId}
            </p>

            {/* Action Buttons */}
            <div className="space-y-3">
              {/* Retry Button */}
              {this.retryCount < this.maxRetries && (
                <button
                  onClick={this.handleRetry}
                  className="w-full bg-interactive-primary hover:bg-interactive-primaryHover text-white py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  إعادة المحاولة ({this.maxRetries - this.retryCount} محاولات متبقية)
                </button>
              )}

              {/* Reload Page Button */}
              <button
                onClick={this.handleReload}
                className="w-full bg-interactive-secondary hover:bg-interactive-secondaryHover text-text-primary py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                إعادة تحميل الصفحة
              </button>

              {/* Go Home Button */}
              <button
                onClick={this.handleGoHome}
                className="w-full bg-background-tertiary hover:bg-background-hover text-text-secondary py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center gap-2"
              >
                <Home className="w-4 h-4" />
                العودة للصفحة الرئيسية
              </button>
            </div>

            {/* Report Bug Link */}
            <div className="mt-6 pt-4 border-t border-background-border">
              <button
                onClick={() => {
                  // Here you could open a bug report form or email
                  const subject = encodeURIComponent(`خطأ في الموقع - ${this.state.errorId}`)
                  const body = encodeURIComponent(`
تفاصيل الخطأ:
- معرف الخطأ: ${this.state.errorId}
- رسالة الخطأ: ${this.state.error?.message}
- الصفحة: ${window.location.href}
- المتصفح: ${navigator.userAgent}
- الوقت: ${new Date().toISOString()}

يرجى وصف ما كنت تفعله عندما حدث الخطأ:
                  `)
                  window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`)
                }}
                className="text-xs text-text-accent hover:text-interactive-accentHover transition-colors duration-200 flex items-center justify-center gap-1"
              >
                <Bug className="w-3 h-3" />
                الإبلاغ عن هذا الخطأ
              </button>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// Higher-order component for easier usage
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Hook for error reporting in functional components
export const useErrorHandler = () => {
  const reportError = (error: Error, context?: string) => {
    console.error('Manual error report:', error, context)
    
    // Report to error service
    const errorReport = {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
    
    console.error('Error Report:', errorReport)
  }

  return { reportError }
}

export default ErrorBoundary
