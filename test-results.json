{"total": 8, "passed": 8, "failed": 0, "tests": [{"name": "POST /api/auth/login - Valid Admin Login", "status": "PASSED", "response": {"status": 200, "data": {"success": true, "message": "Login successful", "data": {"user": {"id": "admin-user-id-2024", "email": "<EMAIL>", "username": "admin", "full_name": "Updated Admin User", "role": "admin", "status": "active", "login_count": 78}, "tokens": {"accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJhZG1pbi11c2VyLWlkLTIwMjQiLCJpYXQiOjE3NTMxMzczMjksImV4cCI6MTc1MzIyMzcyOX0.D4GYcjAL2UpvEX0RNg9HgKUnoS94xfBkXqyLoH-Xd_4", "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJhZG1pbi11c2VyLWlkLTIwMjQiLCJpYXQiOjE3NTMxMzczMjksImV4cCI6MTc1Mzc0MjEyOX0.0OEXqNZ0CAQa4CBSvOoIyJsl_4DFRzVIi5S_SKxT_mw", "expiresIn": "24h"}, "session": {"id": "3b115952-b0ed-42dd-bbf4-b13bde375c8f"}}}}}, {"name": "POST /api/auth/login - Invalid Password", "status": "PASSED", "response": {"status": 401, "data": {"success": false, "error": "Invalid email or password", "code": "AUTH_ERROR", "timestamp": "2025-07-21T22:35:30.291Z", "details": {"name": "APIError", "statusCode": 401, "request": {"method": "POST", "url": "/api/auth/login", "ip": "::1", "userAgent": "LocalAPITester/1.0"}}, "requestId": "req_1753137329936_deqza489y"}}}, {"name": "POST /api/auth/login - Missing Credentials", "status": "PASSED", "response": {"status": 400, "data": {"success": false, "error": "Email and password are required", "code": "VALIDATION_ERROR", "timestamp": "2025-07-21T22:35:30.303Z", "details": {"name": "APIError", "statusCode": 400, "request": {"method": "POST", "url": "/api/auth/login", "ip": "::1", "userAgent": "LocalAPITester/1.0"}}, "requestId": "req_1753137330298_fp6bggu6g"}}}, {"name": "POST /api/auth/register - Valid Registration or User Exists", "status": "PASSED", "response": {"status": 400, "data": {"success": false, "error": "Username must be 3-20 characters long and contain only letters, numbers, and underscores", "code": "VALIDATION_ERROR", "timestamp": "2025-07-21T22:35:30.313Z", "details": {"name": "APIError", "statusCode": 400, "request": {"method": "POST", "url": "/api/auth/register", "ip": "::1", "userAgent": "LocalAPITester/1.0"}}, "requestId": "req_1753137330309_we3mst6jc"}}}, {"name": "POST /api/auth/register - Missing Fields", "status": "PASSED", "response": {"status": 400, "data": {"success": false, "error": "All fields are required: email, username, full_name, password", "code": "VALIDATION_ERROR", "timestamp": "2025-07-21T22:35:30.323Z", "details": {"name": "APIError", "statusCode": 400, "request": {"method": "POST", "url": "/api/auth/register", "ip": "::1", "userAgent": "LocalAPITester/1.0"}}, "requestId": "req_1753137330319_0m3effpid"}}}, {"name": "GET /api/systems - Get All Systems", "status": "PASSED", "response": {"status": 200, "data": {"success": true, "data": {"systems": [{"id": "guildWar", "name_ar": "نظام حروب الروابط التلقائي", "name_en": "Automatic Guild War System", "description_ar": "نظام حروب الروابط التلقائي مع ذكاء اصطناعي متطور لإدارة المعارك والتحكم في الاستراتيجيات", "description_en": "Automatic Guild War system with advanced AI for battle management and strategic control", "price": "299.00", "category": "combat", "type": "regular", "features_ar": ["أحداث تلقائية", "مكافآت يومية", "تحديات أسبوعية", "نظام نقاط"], "features_en": ["Automatic events", "Daily rewards", "Weekly challenges", "Points system"], "tech_specs_ar": ["جدولة ذكية", "إدارة الموارد", "تسجيل شامل", "واجهة إدارية"], "tech_specs_en": ["Smart scheduling", "Resource management", "Comprehensive logging", "Admin interface"], "video_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "image_url": "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg", "gallery_images": ["https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg", "https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg", "https://images.pexels.com/photos/442150/pexels-photo-442150.jpeg", "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg"], "version": null, "file_size": null, "status": "active", "featured": 0, "download_count": 0, "rating": "0.00", "rating_count": 0, "is_premium_addon": 1, "created_at": "2025-07-16T21:08:45.000Z", "updated_at": "2025-07-21T08:27:49.000Z"}, {"id": "petLevel", "name_ar": "نظام الرفيق المتطور", "name_en": "Advanced Companion System", "description_ar": "نظام رفيق متطور مع تقنيات التطوير الذاتي والذكاء التكيفي", "description_en": "Advanced companion system with self-development technologies and adaptive intelligence", "price": "299.00", "category": "gameplay", "type": "regular", "features_ar": ["تطوير ذاتي", "مهارات متقدمة", "ذكاء تكيفي", "نظام ولاء"], "features_en": ["Self-development", "Advanced skills", "Adaptive intelligence", "Loyalty system"], "tech_specs_ar": ["ميزة"], "tech_specs_en": ["Adaptive learning", "Customizable skills", "Emotion system", "Automatic development"], "video_url": "https://www.youtube.com/watch?v=MBB0RL-rWiY", "image_url": "https://image.board.gameforge.com/uploads/metin2/pt/announcement_metin2_pt_dadf72ef2e3819840e2569752a09e3f2.jpg", "gallery_images": [], "version": null, "file_size": null, "status": "active", "featured": 0, "download_count": 0, "rating": "0.00", "rating_count": 0, "is_premium_addon": 0, "created_at": "2025-07-16T21:08:45.000Z", "updated_at": "2025-07-20T03:26:09.000Z"}], "pagination": {"page": 1, "limit": 12, "total": "2", "totalPages": 1, "hasNext": false, "hasPrev": false}, "filters": {"categories": [{"name": "combat", "count": "1"}, {"name": "gameplay", "count": "1"}]}}}}}, {"name": "GET /api/services/technical - Get All Technical Services", "status": "PASSED", "response": {"status": 200, "data": {"success": true, "data": {"services": [{"id": "freebsd", "name_ar": "إعداد خوادم FreeBSD المحسنة", "name_en": "Optimized FreeBSD Server Setup", "description_ar": "إعداد وتحصين خوادم FreeBSD للحماية القصوى", "description_en": "Setup and hardening of FreeBSD servers for maximum protection", "price": "250.00", "category": "infrastructure", "service_type": "development", "features_ar": ["<PERSON><PERSON><PERSON> عالي", "<PERSON><PERSON><PERSON><PERSON> محسن", "استقرار عالي", "مراقبة مستمرة"], "features_en": ["High Security", "Optimized Performance", "High Stability", "Continuous Monitoring"], "tech_specs_ar": ["ØªØ­ØµÙŠÙ† Ù…ØªÙ‚Ø¯Ù…", "ØªØ­Ø³ÙŠÙ† Ø§Ù„Ø£Ø¯Ø§Ø¡", "Ù…Ø±Ø§Ù‚Ø¨Ø© Ø§Ù„Ù†Ø¸Ø§Ù…", "Ù†Ø³Ø® Ø§Ø­ØªÙŠØ§Ø·ÙŠØ©"], "tech_specs_en": ["Advanced Hardening", "Performance Tuning", "System Monitoring", "Backups"], "delivery_time_ar": "", "delivery_time_en": "", "is_premium_addon": "1", "premium_price": "0.00", "subscription_type": "none", "video_url": null, "image_url": null, "gallery_images": [], "status": "active", "featured": "0", "order_count": "0", "rating": "0.00", "rating_count": "0", "created_at": "2025-07-16T21:08:45.000Z", "updated_at": "2025-07-20T00:14:58.000Z"}, {"id": "monthlyMaintenance", "name_ar": "صيانة شهرية للخوادم", "name_en": "Monthly Server Maintenance", "description_ar": "خدمة صيانة شهرية شاملة للخوادم مع مراقبة مستمرة وتحديثات أمنية", "description_en": "Comprehensive monthly server maintenance with continuous monitoring and security updates", "price": "50.00", "category": "maintenance", "service_type": "development", "features_ar": ["مراقبة 24/7", "تحديثات أمنية", "نسخ احتياطية", "تقارير شهرية"], "features_en": ["24/7 Monitoring", "Security Updates", "Backups", "Monthly Reports"], "tech_specs_ar": ["Ù…Ø±Ø§Ù‚Ø¨Ø© Ù…Ø³ØªÙ…Ø±Ø©", "ØªØ­Ø¯ÙŠØ«Ø§Øª ØªÙ„Ù‚Ø§Ø¦ÙŠØ©", "Ù†Ø³Ø® Ø§Ø­ØªÙŠØ§Ø·ÙŠØ© ÙŠÙˆÙ…ÙŠØ©", "Ø¯Ø¹Ù… ÙÙ†ÙŠ"], "tech_specs_en": ["Continuous Monitoring", "Automatic Updates", "Daily Backups", "Technical Support"], "delivery_time_ar": "", "delivery_time_en": "", "is_premium_addon": "1", "premium_price": "30.00", "subscription_type": "monthly", "video_url": null, "image_url": null, "gallery_images": [], "status": "active", "featured": "0", "order_count": "0", "rating": "0.00", "rating_count": "0", "created_at": "2025-07-16T21:08:45.000Z", "updated_at": "2025-07-20T00:14:58.000Z"}, {"id": "yearlySupport", "name_ar": "دعم فني سنوي متقدم", "name_en": "Advanced Annual Technical Support", "description_ar": "دعم فني متقدم على مدار السنة مع أولوية في الاستجابة وحلول مخصصة", "description_en": "Advanced technical support throughout the year with priority response and custom solutions", "price": "500.00", "category": "support", "service_type": "development", "features_ar": ["دعم أولوية", "استجابة سريعة", "حلول مخصصة", "تدريب مجاني"], "features_en": ["Priority Support", "Fast Response", "Custom Solutions", "Free Training"], "tech_specs_ar": ["Ø§Ø³ØªØ¬Ø§Ø¨Ø© Ø®Ù„Ø§Ù„ Ø³Ø§Ø¹Ø©", "Ø¯Ø¹Ù… Ù…ØªØ¹Ø¯Ø¯ Ø§Ù„Ù‚Ù†ÙˆØ§Øª", "Ø¬Ù„Ø³Ø§Øª ØªØ¯Ø±ÙŠØ¨", "ØªÙ‚Ø§Ø±ÙŠØ± Ø±Ø¨Ø¹ Ø³Ù†ÙˆÙŠØ©"], "tech_specs_en": ["1-hour Response", "Multi-channel Support", "Training Sessions", "Quarterly Reports"], "delivery_time_ar": "", "delivery_time_en": "", "is_premium_addon": "1", "premium_price": "300.00", "subscription_type": "yearly", "video_url": null, "image_url": null, "gallery_images": [], "status": "active", "featured": "0", "order_count": "0", "rating": "0.00", "rating_count": "0", "created_at": "2025-07-16T21:08:45.000Z", "updated_at": "2025-07-20T00:14:58.000Z"}], "pagination": {"page": 1, "limit": 12, "total": "3", "totalPages": 1, "hasNext": false, "hasPrev": false}, "filters": {"categories": [{"name": "infrastructure", "count": "1"}, {"name": "maintenance", "count": "1"}, {"name": "support", "count": "1"}]}}}}}, {"name": "GET /api/services/premium - Get All Premium Services", "status": "PASSED", "response": {"status": 200, "data": {"success": true, "data": {"premiumContent": [{"id": "premium_advanced_systems", "title_ar": "حزمة الأنظمة المتقدمة", "title_en": "Advanced Systems Package", "description_ar": "مجموعة من أقوى الأنظمة المتقدمة للخوادم الاحترافية", "description_en": "Collection of the most powerful advanced systems for professional servers", "price": "999.00", "category": "systems_package", "features_ar": ["أنظمة PvP متقدمة", "أنظمة حماية", "أنظمة إدارة", "أنظمة مراقبة"], "features_en": ["Advanced PvP systems", "Protection systems", "Management systems", "Monitoring systems"], "video_url": null, "image_url": null, "gallery_images": [], "included_systems": "[]", "included_services": "[]", "status": "active", "featured": "0", "purchase_count": "0", "rating": "0.00", "rating_count": "0", "created_at": "2025-07-21T01:57:26.000Z", "updated_at": "2025-07-21T01:57:26.000Z"}, {"id": "premium_technical_services", "title_ar": "حزمة الخدمات التقنية المميزة", "title_en": "Premium Technical Services Package", "description_ar": "خدمات تقنية شاملة مع دعم مستمر وصيانة دورية", "description_en": "Comprehensive technical services with continuous support and regular maintenance", "price": "1499.00", "category": "services_package", "features_ar": ["دعم فني 24/7", "صيانة دورية", "تحديثات مجانية", "استشارات تقنية"], "features_en": ["24/7 technical support", "Regular maintenance", "Free updates", "Technical consultations"], "video_url": null, "image_url": null, "gallery_images": [], "included_systems": "[]", "included_services": "[]", "status": "active", "featured": "0", "purchase_count": "0", "rating": "0.00", "rating_count": "0", "created_at": "2025-07-21T01:57:26.000Z", "updated_at": "2025-07-21T01:57:26.000Z"}, {"id": "premium_complete_package", "title_ar": "الحزمة المميزة الشاملة", "title_en": "Complete Premium Package", "description_ar": "نسخة شاملة تحتوي على جميع الأنظمة والملفات", "description_en": "A comprehensive version containing all systems and files", "price": "1999.00", "category": "complete_package", "features_ar": [], "features_en": [], "video_url": null, "image_url": null, "gallery_images": [], "included_systems": "[]", "included_services": "[]", "status": "active", "featured": "1", "purchase_count": "0", "rating": "0.00", "rating_count": "0", "created_at": "2025-07-16T21:08:45.000Z", "updated_at": "2025-07-21T08:26:23.000Z"}], "pagination": {"page": 1, "limit": 12, "total": "3", "totalPages": 1, "hasNext": false, "hasPrev": false}}}}}]}