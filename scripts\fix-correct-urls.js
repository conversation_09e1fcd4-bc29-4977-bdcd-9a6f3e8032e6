#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const fs = require('fs');

async function fixCorrectURLs() {
  console.log('🔧 Fixing URLs to match TestSprite requirements...\n');
  
  // Target URLs from TestSprite
  const targetFrontendURL = 'https://720f35b15ec2.ngrok-free.app';
  const targetBackendURL = 'https://720f35b15ec2.ngrok-free.app'; // Same URL for both
  
  console.log(`🎯 Target Frontend URL: ${targetFrontendURL}`);
  console.log(`🎯 Target Backend URL: ${targetBackendURL}`);
  
  try {
    // Step 1: Kill all ngrok processes
    console.log('\n🔪 Stopping all ngrok processes...');
    await new Promise((resolve) => {
      exec('powershell "Get-Process ngrok -ErrorAction SilentlyContinue | Stop-Process -Force"', () => {
        setTimeout(resolve, 3000);
      });
    });
    
    // Step 2: Check if frontend is running
    console.log('\n🔍 Checking frontend server...');
    const frontendRunning = await new Promise((resolve) => {
      exec('curl -s http://localhost:5173', (error) => {
        resolve(!error);
      });
    });
    
    if (!frontendRunning) {
      console.log('⚠️ Frontend not running, starting it...');
      // Start frontend in background
      spawn('npm', ['run', 'dev'], {
        stdio: 'ignore',
        detached: true,
        shell: true
      });
      
      // Wait for frontend to start
      console.log('⏳ Waiting for frontend to start...');
      await new Promise(resolve => setTimeout(resolve, 10000));
    } else {
      console.log('✅ Frontend is running');
    }
    
    // Step 3: Check backend
    console.log('\n🔍 Checking backend server...');
    const backendRunning = await new Promise((resolve) => {
      exec('curl -s http://localhost:3001/health', (error) => {
        resolve(!error);
      });
    });
    
    if (!backendRunning) {
      throw new Error('Backend server is not running. Please start it first.');
    }
    console.log('✅ Backend is running');
    
    // Step 4: Start ngrok for frontend (port 5173)
    console.log('\n🚇 Starting ngrok for frontend (port 5173)...');
    const frontendNgrok = spawn('npx', ['ngrok', 'http', '5173', '--log=stdout'], {
      stdio: ['ignore', 'pipe', 'pipe'],
      shell: true
    });
    
    let frontendURL = null;
    
    await new Promise((resolve) => {
      const processOutput = (data) => {
        const output = data.toString();
        const urlMatch = output.match(/https:\/\/[a-z0-9]+\.ngrok-free\.app/);
        if (urlMatch && !frontendURL) {
          frontendURL = urlMatch[0];
          console.log(`🔗 Frontend URL: ${frontendURL}`);
          resolve();
        }
      };
      
      frontendNgrok.stdout.on('data', processOutput);
      frontendNgrok.stderr.on('data', processOutput);
      
      setTimeout(() => {
        if (!frontendURL) {
          console.log('⏰ Frontend ngrok timeout');
          resolve();
        }
      }, 20000);
    });
    
    // Step 5: Start ngrok for backend (port 3001) 
    console.log('\n🚇 Starting ngrok for backend (port 3001)...');
    const backendNgrok = spawn('npx', ['ngrok', 'http', '3001', '--log=stdout'], {
      stdio: ['ignore', 'pipe', 'pipe'],
      shell: true
    });
    
    let backendURL = null;
    
    await new Promise((resolve) => {
      const processOutput = (data) => {
        const output = data.toString();
        const urlMatch = output.match(/https:\/\/[a-z0-9]+\.ngrok-free\.app/);
        if (urlMatch && !backendURL) {
          backendURL = urlMatch[0];
          console.log(`🔗 Backend URL: ${backendURL}`);
          resolve();
        }
      };
      
      backendNgrok.stdout.on('data', processOutput);
      backendNgrok.stderr.on('data', processOutput);
      
      setTimeout(() => {
        if (!backendURL) {
          console.log('⏰ Backend ngrok timeout');
          resolve();
        }
      }, 20000);
    });
    
    // Step 6: Wait for ngrok API
    console.log('\n⏳ Waiting for ngrok API...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Step 7: Get all tunnels
    const tunnels = await new Promise((resolve) => {
      exec('curl -s http://127.0.0.1:4040/api/tunnels', (error, stdout) => {
        if (error) {
          resolve([]);
          return;
        }
        try {
          const data = JSON.parse(stdout);
          resolve(data.tunnels || []);
        } catch {
          resolve([]);
        }
      });
    });
    
    console.log('\n🔗 Active Tunnels:');
    tunnels.forEach((tunnel, index) => {
      const port = tunnel.config.addr.includes('5173') ? '(Frontend)' : 
                   tunnel.config.addr.includes('3001') ? '(Backend)' : '';
      console.log(`  ${index + 1}. ${tunnel.public_url} ${port}`);
    });
    
    // Step 8: Create corrected TestSprite config
    const frontendTunnel = tunnels.find(t => t.config.addr.includes('5173'));
    const backendTunnel = tunnels.find(t => t.config.addr.includes('3001'));
    
    const correctedConfig = {
      name: "Khanfashariya - Corrected URLs",
      timestamp: new Date().toISOString(),
      frontend: {
        url: frontendTunnel ? frontendTunnel.public_url : frontendURL,
        port: 5173,
        description: "React Frontend Application"
      },
      backend: {
        url: backendTunnel ? backendTunnel.public_url : backendURL,
        port: 3001,
        description: "Node.js Backend API"
      },
      testsprite_config: {
        baseUrl: backendTunnel ? backendTunnel.public_url : backendURL,
        headers: {
          "ngrok-skip-browser-warning": "true",
          "User-Agent": "TestSprite/1.0",
          "Content-Type": "application/json"
        },
        endpoints: [
          {
            path: "/health",
            method: "GET",
            description: "Health check"
          },
          {
            path: "/api/auth/login",
            method: "POST",
            description: "Authentication",
            body: {
              email: "<EMAIL>",
              password: "admin123"
            }
          },
          {
            path: "/api/systems",
            method: "GET",
            description: "Technical systems"
          }
        ]
      }
    };
    
    fs.writeFileSync('corrected-urls.json', JSON.stringify(correctedConfig, null, 2));
    
    console.log('\n✅ URLs corrected and saved to corrected-urls.json');
    console.log('\n🎯 For TestSprite:');
    console.log(`Frontend URL: ${correctedConfig.frontend.url}`);
    console.log(`Backend API URL: ${correctedConfig.backend.url}`);
    
    console.log('\n⏳ Keeping tunnels alive... Press Ctrl+C to stop');
    
    // Keep alive
    setInterval(() => {
      console.log(`✅ Tunnels active - ${new Date().toLocaleTimeString()}`);
    }, 60000);
    
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    process.exit(1);
  }
}

fixCorrectURLs();
