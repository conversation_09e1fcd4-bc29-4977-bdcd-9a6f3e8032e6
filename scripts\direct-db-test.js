/**
 * Direct Database Test
 * Test order data directly from database
 */

const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khan<PERSON><PERSON><PERSON>_db',
  charset: 'utf8mb4'
};

async function testOrderDatabase() {
  let connection;
  
  try {
    console.log('🔍 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connected');
    
    // Test orders table structure
    console.log('\n📋 Testing Orders Table Structure...');
    const [columns] = await connection.execute(`
      SHOW COLUMNS FROM orders
    `);
    
    console.log(`✅ Orders table has ${columns.length} columns`);
    
    // Test order data
    console.log('\n📊 Testing Order Data...');
    const [orders] = await connection.execute(`
      SELECT 
        o.*,
        u.username,
        u.email,
        u.full_name
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      ORDER BY o.created_at DESC
      LIMIT 5
    `);
    
    console.log(`✅ Found ${orders.length} orders`);
    
    if (orders.length > 0) {
      const testOrder = orders[0];
      console.log('\n📋 Sample Order Data:');
      console.log(`   ID: ${testOrder.id}`);
      console.log(`   Order Number: ${testOrder.order_number || 'N/A'}`);
      console.log(`   Type: ${testOrder.order_type}`);
      console.log(`   Category: ${testOrder.order_category || 'N/A'}`);
      console.log(`   Status: ${testOrder.status}`);
      console.log(`   Item Name AR: ${testOrder.item_name_ar || 'N/A'}`);
      console.log(`   Item Name EN: ${testOrder.item_name_en || 'N/A'}`);
      console.log(`   Username: ${testOrder.username || 'N/A'}`);
      console.log(`   Email: ${testOrder.email || 'N/A'}`);
      console.log(`   Created: ${testOrder.created_at}`);
      console.log(`   Updated: ${testOrder.updated_at}`);
      
      console.log('\n💰 Pricing Data:');
      console.log(`   Final Price: $${testOrder.final_price || 0}`);
      console.log(`   Total Price: $${testOrder.total_price || 0}`);
      console.log(`   Base Price: $${testOrder.base_price || 0}`);
      console.log(`   Add-ons Price: $${testOrder.addons_price || 0}`);
      console.log(`   Unit Price: $${testOrder.unit_price || 0}`);
      
      // Analysis
      const hasValidPrice = (testOrder.final_price > 0) || (testOrder.total_price > 0) || (testOrder.base_price > 0) || (testOrder.unit_price > 0);
      const hasValidDate = testOrder.created_at && testOrder.created_at !== 'Invalid Date';
      const hasValidName = testOrder.item_name_ar || testOrder.item_name_en;
      const hasUserData = testOrder.username || testOrder.email;
      
      console.log('\n🔍 Analysis:');
      console.log(`   Price Status: ${hasValidPrice ? '✅ Valid' : '❌ Zero Price Issue'}`);
      console.log(`   Date Status: ${hasValidDate ? '✅ Valid' : '❌ Invalid Date'}`);
      console.log(`   Name Status: ${hasValidName ? '✅ Valid' : '❌ Missing Names'}`);
      console.log(`   User Data: ${hasUserData ? '✅ Valid' : '❌ Missing User Data'}`);
      
      // Check for premium orders
      const premiumOrders = orders.filter(o => o.order_type === 'premium_package' || o.order_type === 'premium_content');
      console.log(`\n👑 Premium Orders: ${premiumOrders.length} found`);
      
      if (premiumOrders.length > 0) {
        const premiumOrder = premiumOrders[0];
        console.log(`   Premium Order ID: ${premiumOrder.id}`);
        console.log(`   Order Details: ${premiumOrder.order_details ? '✅ Present' : '❌ Missing'}`);
        console.log(`   Customer Requirements: ${premiumOrder.customer_requirements ? '✅ Present' : '❌ Missing'}`);
        console.log(`   Selected Add-ons: ${premiumOrder.selected_addons ? '✅ Present' : '❌ Missing'}`);
      }
    }
    
    // Test zero price orders
    console.log('\n🔍 Checking for Zero Price Orders...');
    const [zeroPriceOrders] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM orders 
      WHERE (final_price = 0 OR final_price IS NULL) 
        AND (total_price = 0 OR total_price IS NULL)
        AND (base_price = 0 OR base_price IS NULL)
        AND (unit_price = 0 OR unit_price IS NULL)
    `);
    
    console.log(`❌ Zero price orders: ${zeroPriceOrders[0].count}`);
    
    // Test missing names
    console.log('\n🔍 Checking for Missing Names...');
    const [missingNames] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM orders 
      WHERE (item_name_ar IS NULL OR item_name_ar = '') 
        AND (item_name_en IS NULL OR item_name_en = '')
    `);
    
    console.log(`❌ Orders with missing names: ${missingNames[0].count}`);
    
    console.log('\n🎉 Database test completed!');
    
  } catch (error) {
    console.log('❌ Database test failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

testOrderDatabase();
