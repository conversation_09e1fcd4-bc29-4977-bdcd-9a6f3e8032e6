#!/usr/bin/env node

/**
 * Frontend MySQL Integration Test
 * 
 * This script tests that the frontend at http://localhost:5173
 * properly loads data from MySQL via API calls.
 */

const puppeteer = require('puppeteer');
const axios = require('axios');

const FRONTEND_URL = 'http://localhost:5173';
const API_BASE_URL = 'http://localhost:3001';

async function testAPIConnection() {
  console.log('🔌 Testing API connection...\n');
  
  try {
    // Test health endpoint
    const healthResponse = await axios.get(`${API_BASE_URL}/health`);
    if (healthResponse.data.status === 'healthy') {
      console.log('✅ API Health Check: Passed');
    } else {
      console.log('❌ API Health Check: Failed');
      return false;
    }
    
    // Test systems endpoint
    const systemsResponse = await axios.get(`${API_BASE_URL}/api/systems`);
    if (systemsResponse.data.success && Array.isArray(systemsResponse.data.data)) {
      console.log(`✅ Systems API: ${systemsResponse.data.data.length} systems loaded`);
    } else {
      console.log('❌ Systems API: Failed');
      return false;
    }
    
    // Test services endpoint
    const servicesResponse = await axios.get(`${API_BASE_URL}/api/services/technical`);
    if (servicesResponse.data.success && Array.isArray(servicesResponse.data.data)) {
      console.log(`✅ Services API: ${servicesResponse.data.data.length} services loaded`);
    } else {
      console.log('❌ Services API: Failed');
      return false;
    }
    
    console.log('');
    return true;
    
  } catch (error) {
    console.log('❌ API Connection Failed:', error.message);
    return false;
  }
}

async function testFrontendDataLoading() {
  console.log('🌐 Testing frontend data loading...\n');
  
  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Monitor network requests
    const apiCalls = [];
    const localStorageAccess = [];
    
    page.on('request', request => {
      const url = request.url();
      if (url.includes('/api/')) {
        apiCalls.push(url);
      }
    });
    
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('localStorage') && text.includes('intercepted')) {
        localStorageAccess.push(text);
      }
    });
    
    // Navigate to the site
    console.log('📱 Loading frontend...');
    await page.goto(FRONTEND_URL, { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });
    
    // Wait for data to load
    await page.waitForTimeout(3000);
    
    // Check if systems are loaded
    const systemsLoaded = await page.evaluate(() => {
      const systemElements = document.querySelectorAll('[data-testid="system-card"], .system-card, .grid > div');
      return systemElements.length > 0;
    });
    
    // Check if services are loaded
    const servicesLoaded = await page.evaluate(() => {
      const serviceElements = document.querySelectorAll('[data-testid="service-card"], .service-card');
      return serviceElements.length > 0;
    });
    
    // Check for error messages
    const hasErrors = await page.evaluate(() => {
      const errorElements = document.querySelectorAll('.error, [class*="error"], .notification.error');
      return errorElements.length > 0;
    });
    
    // Check data source indicator
    const dataSource = await page.evaluate(() => {
      const indicator = document.querySelector('.data-source-indicator, [data-testid="data-source"]');
      return indicator ? indicator.textContent : null;
    });
    
    console.log('📊 Frontend Test Results:');
    console.log('─'.repeat(25));
    console.log(`   Systems Loaded: ${systemsLoaded ? '✅ Yes' : '❌ No'}`);
    console.log(`   Services Loaded: ${servicesLoaded ? '✅ Yes' : '❌ No'}`);
    console.log(`   Has Errors: ${hasErrors ? '❌ Yes' : '✅ No'}`);
    console.log(`   Data Source: ${dataSource || 'Not detected'}`);
    console.log(`   API Calls Made: ${apiCalls.length}`);
    console.log(`   localStorage Blocked: ${localStorageAccess.length}`);
    
    if (apiCalls.length > 0) {
      console.log('\n🔗 API Calls Detected:');
      apiCalls.forEach(call => {
        const endpoint = call.replace(API_BASE_URL, '');
        console.log(`   • ${endpoint}`);
      });
    }
    
    if (localStorageAccess.length > 0) {
      console.log('\n🚫 localStorage Access Blocked:');
      localStorageAccess.slice(0, 3).forEach(access => {
        console.log(`   • ${access}`);
      });
      if (localStorageAccess.length > 3) {
        console.log(`   • ... and ${localStorageAccess.length - 3} more`);
      }
    }
    
    console.log('');
    
    return {
      systemsLoaded,
      servicesLoaded,
      hasErrors: !hasErrors,
      apiCallsCount: apiCalls.length,
      dataSourceCorrect: dataSource === 'API' || apiCalls.length > 0
    };
    
  } catch (error) {
    console.log('❌ Frontend test failed:', error.message);
    return {
      systemsLoaded: false,
      servicesLoaded: false,
      hasErrors: false,
      apiCallsCount: 0,
      dataSourceCorrect: false
    };
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

async function generateReport(apiTest, frontendTest) {
  console.log('📋 MySQL Integration Test Report');
  console.log('================================\n');
  
  const overallSuccess = 
    apiTest && 
    frontendTest.systemsLoaded && 
    frontendTest.hasErrors && 
    frontendTest.apiCallsCount > 0;
  
  console.log(`🎯 Overall Status: ${overallSuccess ? '✅ PASSED' : '❌ FAILED'}\n`);
  
  console.log('📊 Test Results:');
  console.log('─'.repeat(16));
  console.log(`   API Connection: ${apiTest ? '✅ Working' : '❌ Failed'}`);
  console.log(`   Data Loading: ${frontendTest.systemsLoaded ? '✅ Working' : '❌ Failed'}`);
  console.log(`   Error Free: ${frontendTest.hasErrors ? '✅ Yes' : '❌ No'}`);
  console.log(`   Using API: ${frontendTest.dataSourceCorrect ? '✅ Yes' : '❌ No'}`);
  
  if (overallSuccess) {
    console.log('\n🎉 Success!');
    console.log('✅ Frontend is successfully loading data from MySQL via API');
    console.log('✅ No localStorage usage detected for main data');
    console.log('✅ All systems working correctly');
    console.log('\n🚀 The website is ready for production use!');
  } else {
    console.log('\n⚠️ Issues Found:');
    if (!apiTest) console.log('❌ API server is not responding');
    if (!frontendTest.systemsLoaded) console.log('❌ Frontend is not loading data');
    if (!frontendTest.hasErrors) console.log('❌ Frontend has errors');
    if (!frontendTest.dataSourceCorrect) console.log('❌ Frontend is not using API');
    console.log('\n🔧 Please fix the issues above before proceeding');
  }
  
  return overallSuccess;
}

async function main() {
  console.log('🧪 MySQL Integration Test for Frontend');
  console.log('======================================\n');
  
  // Test API connection
  const apiTest = await testAPIConnection();
  
  if (!apiTest) {
    console.log('❌ API test failed. Make sure the server is running on port 3001');
    process.exit(1);
  }
  
  // Test frontend
  const frontendTest = await testFrontendDataLoading();
  
  // Generate report
  const success = await generateReport(apiTest, frontendTest);
  
  process.exit(success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Test error:', error);
    process.exit(1);
  });
}

module.exports = { main };
