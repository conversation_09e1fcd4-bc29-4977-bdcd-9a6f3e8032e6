#!/usr/bin/env node

/**
 * Database Check Script for Khanfashariya.com
 * 
 * This script checks the MySQL database structure and data
 * to ensure everything is properly set up for the API migration.
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'khanfashariya_db',
  charset: 'utf8mb4'
};

console.log('🔍 Checking MySQL Database...');
console.log(`📊 Database: ${dbConfig.database}@${dbConfig.host}:${dbConfig.port}`);

/**
 * Check if table exists and get its structure
 */
async function checkTable(connection, tableName) {
  try {
    // Check if table exists
    const [tables] = await connection.execute(
      `SHOW TABLES LIKE '${tableName}'`
    );
    
    if (tables.length === 0) {
      return { exists: false, structure: null, count: 0 };
    }
    
    // Get table structure
    const [structure] = await connection.execute(`DESCRIBE ${tableName}`);
    
    // Get row count
    const [countResult] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
    const count = countResult[0].count;
    
    return { exists: true, structure, count };
  } catch (error) {
    return { exists: false, error: error.message, count: 0 };
  }
}

/**
 * Get sample data from table
 */
async function getSampleData(connection, tableName, limit = 3) {
  try {
    const [rows] = await connection.execute(`SELECT * FROM ${tableName} LIMIT ?`, [limit]);
    return rows;
  } catch (error) {
    return [];
  }
}

/**
 * Main check function
 */
async function checkDatabase() {
  let connection;
  
  try {
    console.log('🔌 Connecting to MySQL...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');
    
    // Required tables
    const requiredTables = [
      'users',
      'system_services',
      'technical_services',
      'orders',
      'premium_content',
      'premium_packages',
      'user_services',
      'subscriptions',
      'contact_messages',
      'inbox_messages'
    ];
    
    console.log('\n📋 Checking required tables...');
    
    const tableStatus = {};
    
    for (const tableName of requiredTables) {
      console.log(`\n🔍 Checking table: ${tableName}`);
      const result = await checkTable(connection, tableName);
      
      tableStatus[tableName] = result;
      
      if (result.exists) {
        console.log(`✅ Table exists with ${result.count} rows`);
        
        // Show sample data for important tables
        if (['users', 'system_services', 'technical_services'].includes(tableName)) {
          const sampleData = await getSampleData(connection, tableName, 2);
          if (sampleData.length > 0) {
            console.log(`📄 Sample data:`, JSON.stringify(sampleData[0], null, 2));
          }
        }
      } else {
        console.log(`❌ Table missing${result.error ? ': ' + result.error : ''}`);
      }
    }
    
    // Summary
    console.log('\n📊 Database Summary:');
    const existingTables = Object.keys(tableStatus).filter(t => tableStatus[t].exists);
    const missingTables = Object.keys(tableStatus).filter(t => !tableStatus[t].exists);
    
    console.log(`✅ Existing tables: ${existingTables.length}/${requiredTables.length}`);
    console.log(`❌ Missing tables: ${missingTables.length}`);
    
    if (missingTables.length > 0) {
      console.log(`Missing: ${missingTables.join(', ')}`);
    }
    
    // Check specific data
    console.log('\n🔍 Checking specific data...');
    
    // Check admin user
    if (tableStatus.users?.exists) {
      const [adminUsers] = await connection.execute(
        'SELECT id, email, username, role FROM users WHERE role = "admin"'
      );
      console.log(`👤 Admin users: ${adminUsers.length}`);
      if (adminUsers.length > 0) {
        console.log(`Admin: ${adminUsers[0].email} (${adminUsers[0].username})`);
      }
    }
    
    // Check system services
    if (tableStatus.system_services?.exists) {
      const [activeSystems] = await connection.execute(
        'SELECT COUNT(*) as count FROM system_services WHERE status = "active"'
      );
      console.log(`🖥️ Active system services: ${activeSystems[0].count}`);
    }
    
    // Check technical services
    if (tableStatus.technical_services?.exists) {
      const [activeServices] = await connection.execute(
        'SELECT COUNT(*) as count FROM technical_services WHERE status = "active"'
      );
      console.log(`🛠️ Active technical services: ${activeServices[0].count}`);
    }
    
    // Overall status
    const allTablesExist = missingTables.length === 0;
    const hasData = existingTables.some(t => tableStatus[t].count > 0);
    
    console.log('\n🎯 Overall Status:');
    console.log(`Database Structure: ${allTablesExist ? '✅ Complete' : '❌ Incomplete'}`);
    console.log(`Database Data: ${hasData ? '✅ Has Data' : '❌ Empty'}`);
    
    if (allTablesExist && hasData) {
      console.log('🎉 Database is ready for API usage!');
      return true;
    } else {
      console.log('⚠️ Database needs setup or data population');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
    return false;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run if called directly
if (require.main === module) {
  checkDatabase().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { checkDatabase };
