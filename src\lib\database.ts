import { hashPassword, comparePassword } from '../utils/crypto';

// --- TYPE DEFINITIONS ---

// Helper type for multilingual text fields
export interface TranslatedText {
  ar: string;
  en: string;
}

export interface User {
  id: string;
  email: string;
  username: string;
  full_name: string;
  role: 'user' | 'admin';
  password_hash: string;
  created_at: string;
  updated_at: string;
}

// Enhanced Order interface (renamed from UserService for clarity)
export interface Order {
  id: string;
  user_id: string;
  service_name: string; // This is a snapshot of the name at time of purchase
  service_type: string;
  type: 'standard' | 'premium_custom' | 'custom_request'; // Order type classification
  status: 'pending' | 'confirmed' | 'in_progress' | 'testing' | 'completed' | 'cancelled' | 'refunded' | 'on_hold';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  price: number;
  details: string; // JSON representation of complex orders (base product + add-ons)
  subscriptionId: string | null; // Link to subscription record if applicable
  purchase_date: string;
  start_date?: string;
  completion_date?: string;
  estimated_completion?: string;
  notes?: string;
  admin_notes?: string;
  progress_percentage?: number;
  files_attached?: string[];
  communication_log?: Array<{
    date: string;
    message: string;
    sender: 'admin' | 'user';
  }>;
}

// Legacy alias for backward compatibility during transition
export type UserService = Order;

export interface ContactMessage {
  id: string;
  name: string;
  email: string;
  message: string;
  status: 'new' | 'read' | 'replied';
  created_at: string;
}

// New Subscription interface for recurring services
export interface Subscription {
  id: string;
  userId: string;
  orderId: string; // Link to the original order
  serviceId: string; // ID of the technical service
  status: 'active' | 'cancelled' | 'paused';
  nextBillingDate: string;
  price: number;
  billingCycle: 'monthly' | 'yearly';
  created_at: string;
  updated_at: string;
}

// New InboxMessage interface for customer messaging
export interface InboxMessage {
  id: string;
  userId: string;
  title: TranslatedText;
  content: TranslatedText;
  isRead: boolean;
  type: 'billing' | 'alert' | 'promotion';
  createdAt: string;
}

// Base interface for all purchasable services/systems
export interface BaseService {
  id: string;
  name: TranslatedText;
  description: TranslatedText;
  price: number;
  category: string;
  features: { ar: string[]; en: string[] };
  video_url?: string;
  image_url?: string;
  gallery_images?: string[]; // New field for image gallery
  tech_specs: { ar: string[]; en: string[] };
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

// Enhanced SystemService interface with type distinction
export interface SystemService extends BaseService {
  type: 'regular' | 'plugin'; // Distinguishes between standard systems and special plugins
  isPremiumAddon?: boolean; // Flag for systems eligible for Premium Edition
}

// Enhanced TechnicalService interface with Premium Edition and subscription support
export interface TechnicalService extends BaseService {
  isPremiumAddon: boolean; // Flag for services eligible for Premium Edition
  premiumPrice: number; // Special price when bundled with Premium Edition (can be 0 for free)
  subscriptionType: 'none' | 'monthly' | 'yearly'; // Identifies recurring services
}

export interface PremiumContent {
  id: string;
  title_ar: string;
  title_en: string;
  description_ar: string;
  description_en: string;
  detailed_description_ar?: string;
  detailed_description_en?: string;
  price: number;
  original_price?: number;
  discount_percentage?: number;
  category: string;
  features_ar: string[];
  features_en: string[];
  tech_specs_ar: string[];
  tech_specs_en: string[];
  video_url?: string;
  image_url?: string;
  gallery_images: string[];
  included_systems: string[]; // Array of system IDs
  included_services: string[]; // Array of service IDs
  installation_guide_ar?: string;
  installation_guide_en?: string;
  support_info_ar?: string;
  support_info_en?: string;
  status: 'active' | 'inactive' | 'draft';
  is_active_edition: boolean; // Only one premium edition can be active at a time
  featured: boolean;
  sort_order: number;
  purchase_count: number;
  rating: number;
  rating_count: number;
  created_at: string;
  updated_at: string;
}

// Premium pricing interfaces
export interface PremiumSystemPricing {
  id: string;
  system_id: string;
  premium_price: number;
  installation_included: boolean;
  maintenance_included: boolean;
  is_available_for_premium: boolean;
  description_ar?: string;
  description_en?: string;
  created_at: string;
  updated_at: string;
}

export interface PremiumServicePricing {
  id: string;
  service_id: string;
  premium_price: number;
  installation_included: boolean;
  maintenance_included: boolean;
  is_available_for_premium: boolean;
  subscription_discount_percentage: number;
  description_ar?: string;
  description_en?: string;
  created_at: string;
  updated_at: string;
}

export interface PremiumPackage {
  id: string;
  name: TranslatedText;
  description: TranslatedText;
  systems: string[]; // IDs of premium content
  price: number;
  original_price?: number;
  discount_percentage?: number;
  features: { ar: string[]; en: string[] };
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

type DbTables = {
  currentUser: User | null;
  users: User[];
  userServices: UserService[]; // Legacy field name for backward compatibility
  orders: Order[]; // New orders table
  subscriptions: Subscription[]; // New subscriptions table
  inboxMessages: InboxMessage[]; // New inbox messages table
  contactMessages: ContactMessage[];
  systemServices: SystemService[];
  technicalServices: TechnicalService[];
  premiumContent: PremiumContent[];
  premiumPackages: PremiumPackage[];
};

class LocalDatabase {
  private db: DbTables = {
    currentUser: null,
    users: [],
    userServices: [], // Legacy field for backward compatibility
    orders: [], // New orders table
    subscriptions: [], // New subscriptions table
    inboxMessages: [], // New inbox messages table
    contactMessages: [],
    systemServices: [],
    technicalServices: [],
    premiumContent: [],
    premiumPackages: [],
  };

  constructor() {
    this.loadFromStorage();
    this.initializeAdminUser();
    this.initializeDefaultData();
  }

  private loadFromStorage() {
    // localStorage functionality removed - now using MySQL database
    // Data is loaded from API endpoints instead
  }

  private saveToStorage() {
    // localStorage functionality removed - now using MySQL database
    // Data is saved through API endpoints instead
  }

  private async initializeAdminUser() {
    // إزالة المستخدم الإداري الموجود إن وجد لإعادة إنشائه بكلمة مرور صحيحة
    this.db.users = this.db.users.filter(user => user.email !== '<EMAIL>');

    // إنشاء المستخدم الإداري الجديد
    const adminUser: User = {
      id: this.generateId('user'),
      email: '<EMAIL>',
      username: 'admin',
      full_name: 'مدير النظام',
      role: 'admin',
      password_hash: await hashPassword('admin123'),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    this.db.users.push(adminUser);
    console.log('Admin user created with hash:', adminUser.password_hash);

    const testUserExists = this.db.users.some(user => user.email === '<EMAIL>');
    if (!testUserExists) {
        const testUser: User = {
            id: this.generateId('user'),
            email: '<EMAIL>',
            username: 'testuser',
            full_name: 'مستخدم تجريبي',
            role: 'user',
            password_hash: await hashPassword('test123'),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        this.db.users.push(testUser);
    }
    this.saveToStorage();
  }

  private initializeDefaultData() {
    if (this.db.premiumContent.length === 0) {
      this.db.premiumContent = [
        {
          id: 'premium_complete_package',
          name: { ar: 'النسخة الشاملة المميزة', en: 'Premium Complete Package' },
          description: { ar: 'نسخة شاملة تحتوي على جميع الأنظمة والملفات', en: 'A comprehensive version containing all systems and files' },
          detailed_description: { ar: 'النسخة الشاملة المميزة تشمل ملفات الكلاينت والسيرفر مع تنصيب اللعبة على نظام FreeBSD محسن للأداء العالي والاستقرار. تحتوي على جميع الأنظمة المتقدمة مع حماية قصوى ضد الاختراق.', en: 'The premium comprehensive version includes client and server files with the game installed on a FreeBSD system optimized for high performance and stability. It contains all advanced systems with maximum protection against hacking.' },
          features: {
            ar: ['ملفات كلاينت كاملة', 'ملفات سيرفر محسنة', 'تنصيب على FreeBSD', 'جميع الأنظمة المتقدمة', 'حماية قصوى', 'دعم فني مدى الحياة'],
            en: ['Full client files', 'Optimized server files', 'Installation on FreeBSD', 'All advanced systems', 'Maximum protection', 'Lifetime technical support']
          },
          technical_specs: {
            ar: ['نظام FreeBSD 13.2', 'MySQL 8.0 محسن', 'Python 3.11 للأنظمة', 'حماية DDoS متقدمة'],
            en: ['FreeBSD 13.2 system', 'Optimized MySQL 8.0', 'Python 3.11 for systems', 'Advanced DDoS protection']
          },
          images: [
            'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=800',
            'https://images.pexels.com/photos/1181677/pexels-photo-1181677.jpeg?auto=compress&cs=tinysrgb&w=800'
          ],
          videos: [
            'https://www.youtube.com/watch?v=example1',
            'https://www.youtube.com/watch?v=example2'
          ],
          price: 1999,
          original_price: 2999,
          category: 'complete_package',
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];
    }
    if (this.db.systemServices.length === 0) {
      this.db.systemServices = [
        {
          id: 'guildWar',
          name: { ar: 'نظام حروب الروابط التلقائي', en: 'Automatic Guild War System' },
          description: { ar: 'نظام حروب الروابط التلقائي مع ذكاء اصطناعي متطور لإدارة المعارك والتحكم في الاستراتيجيات', en: 'Automatic Guild War system with advanced AI for battle management and strategic control' },
          price: 299,
          category: 'combat',
          type: 'regular', // Standard system
          features: { ar: ['ذكاء اصطناعي متطور', 'إدارة تلقائية', 'تحليل المعارك', 'نظام إنذار متقدم'], en: ['Advanced AI', 'Automatic management', 'Battle analysis', 'Advanced alert system'] },
          image_url: 'https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg?auto=compress&cs=tinysrgb&w=800',
          video_url: 'https://www.youtube.com/watch?v=example1',
          tech_specs: { ar: ['دعم خوادم متعددة', 'واجهة إدارة متقدمة', 'تقارير مفصلة', 'نظام حماية متطور'], en: ['Multi-server support', 'Advanced management interface', 'Detailed reports', 'Advanced protection system'] },
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'duel',
          name: { ar: 'نظام المبارزات التلقائي', en: 'Automatic Duel System' },
          description: { ar: 'نظام مبارزات تقني متطور مع خوارزميات القتال المستقبلية وتحليل الأداء', en: 'Advanced technical duel system with futuristic combat algorithms and performance analysis' },
          price: 249,
          category: 'combat',
          type: 'regular', // Standard system
          features: { ar: ['خوارزميات قتال', 'نظام ترتيب ذكي', 'مكافآت تلقائية', 'تحليل الأداء'], en: ['Combat algorithms', 'Smart ranking system', 'Automatic rewards', 'Performance analysis'] },
          image_url: 'https://images.pexels.com/photos/442576/pexels-photo-442576.jpeg?auto=compress&cs=tinysrgb&w=800',
          tech_specs: { ar: ['نظام مطابقة ذكي', 'إحصائيات مفصلة', 'مكافآت متدرجة', 'حماية من الغش'], en: ['Smart matching system', 'Detailed statistics', 'Progressive rewards', 'Anti-cheat protection'] },
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'contest',
          name: { ar: 'نظام المسابقات التلقائي', en: 'Automatic Contest System' },
          description: { ar: 'نظام مسابقات متطور مع تقنيات المراقبة والتحكم المتقدمة وإدارة الجوائز', en: 'Advanced contest system with monitoring and control technologies and prize management' },
          price: 279,
          category: 'events',
          type: 'regular', // Standard system
          features: { ar: ['مراقبة متقدمة', 'إدارة المشاركين', 'نظام جوائز ذكي', 'تقارير مفصلة'], en: ['Advanced monitoring', 'Participant management', 'Smart prize system', 'Detailed reports'] },
          image_url: 'https://images.pexels.com/photos/1591056/pexels-photo-1591056.jpeg?auto=compress&cs=tinysrgb&w=800',
          tech_specs: { ar: ['أنواع مسابقات متعددة', 'نظام تسجيل تلقائي', 'إدارة الجوائز', 'تقارير في الوقت الفعلي'], en: ['Multiple contest types', 'Automatic registration', 'Prize management', 'Real-time reports'] },
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'wiki',
          name: { ar: 'نظام الويكي التقني', en: 'Technical Wiki System' },
          description: { ar: 'نظام ويكي تقني داخل اللعبة بواجهة عربية مستقبلية ومحرر متطور', en: 'Technical wiki system in-game with futuristic Arabic interface and advanced editor' },
          price: 399,
          category: 'utility',
          type: 'plugin', // Special plugin system
          features: { ar: ['واجهة عربية', 'محرر متقدم', 'بحث ذكي', 'تصنيف تلقائي'], en: ['Arabic interface', 'Advanced editor', 'Smart search', 'Automatic categorization'] },
          image_url: 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=800',
          tech_specs: { ar: ['محرر WYSIWYG', 'دعم الوسائط المتعددة', 'نظام صلاحيات', 'تاريخ التعديلات'], en: ['WYSIWYG editor', 'Multimedia support', 'Permission system', 'Edit history'] },
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'autoShop',
          name: { ar: 'نظام المتجر الآلي', en: 'Automatic Shop System' },
          description: { ar: 'متجر آلي مع ذكاء اصطناعي لإدارة المخزون والأسعار والمبيعات', en: 'Automatic shop with AI for inventory, pricing and sales management' },
          price: 349,
          category: 'economy',
          type: 'plugin', // Special plugin system
          features: { ar: ['ذكاء اصطناعي', 'إدارة مخزون', 'تسعير ديناميكي', 'تحليل مبيعات'], en: ['Artificial intelligence', 'Inventory management', 'Dynamic pricing', 'Sales analysis'] },
          image_url: 'https://images.pexels.com/photos/230544/pexels-photo-230544.jpeg?auto=compress&cs=tinysrgb&w=800',
          tech_specs: { ar: ['تحليل السوق', 'تنبيهات المخزون', 'تقارير المبيعات', 'إدارة العملاء'], en: ['Market analysis', 'Inventory alerts', 'Sales reports', 'Customer management'] },
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'petLevel',
          name: { ar: 'نظام الرفيق المتطور', en: 'Advanced Companion System' },
          description: { ar: 'نظام رفيق متطور مع تقنيات التطوير الذاتي والذكاء التكيفي', en: 'Advanced companion system with self-development technologies and adaptive intelligence' },
          price: 299,
          category: 'gameplay',
          type: 'regular', // Standard system
          features: { ar: ['تطوير ذاتي', 'مهارات متقدمة', 'ذكاء تكيفي', 'نظام ولاء'], en: ['Self-development', 'Advanced skills', 'Adaptive intelligence', 'Loyalty system'] },
          image_url: 'https://images.pexels.com/photos/33053/dog-young-dog-small-dog-maltese.jpg?auto=compress&cs=tinysrgb&w=800',
          tech_specs: { ar: ['تعلم تكيفي', 'مهارات قابلة للتخصيص', 'نظام عواطف', 'تطوير تلقائي'], en: ['Adaptive learning', 'Customizable skills', 'Emotion system', 'Automatic development'] },
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'autoAttack',
          name: { ar: 'نظام الهجوم التلقائي', en: 'Auto Attack System' },
          description: { ar: 'نظام هجوم تلقائي ذكي مع تقنيات التعلم الآلي والتكيف', en: 'Intelligent auto-attack system with machine learning and adaptation technologies' },
          price: 269,
          category: 'combat',
          features: { ar: ['تعلم آلي', 'هجوم ذكي', 'تكيف تلقائي', 'تحسين مستمر'], en: ['Machine learning', 'Smart attack', 'Auto adaptation', 'Continuous improvement'] },
          image_url: 'https://images.pexels.com/photos/1670977/pexels-photo-1670977.jpeg?auto=compress&cs=tinysrgb&w=800',
          tech_specs: { ar: ['تحليل الأعداء', 'استراتيجيات متكيفة', 'تحسين الأداء', 'حماية من الاكتشاف'], en: ['Enemy analysis', 'Adaptive strategies', 'Performance optimization', 'Detection protection'] },
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'quickSwitch',
          name: { ar: 'نظام تبديل الأدوات السريع', en: 'Quick Tool Switch System' },
          description: { ar: 'نظام تبديل أدوات فوري مع تقنيات الاستجابة السريعة والذاكرة التكيفية', en: 'Instant tool switching system with fast response technologies and adaptive memory' },
          price: 199,
          category: 'utility',
          features: { ar: ['استجابة فورية', 'تبديل ذكي', 'ذاكرة تكيفية', 'واجهة سهلة'], en: ['Instant response', 'Smart switching', 'Adaptive memory', 'Easy interface'] },
          image_url: 'https://images.pexels.com/photos/414102/pexels-photo-414102.jpeg?auto=compress&cs=tinysrgb&w=800',
          tech_specs: { ar: ['تبديل فوري', 'حفظ التفضيلات', 'واجهة قابلة للتخصيص', 'دعم الاختصارات'], en: ['Instant switching', 'Preference saving', 'Customizable interface', 'Shortcut support'] },
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'itemFilter',
          name: { ar: 'نظام فلتر المعدات المتقدم', en: 'Advanced Equipment Filter' },
          description: { ar: 'نظام فلتر معدات متقدم مع خوارزميات التصنيف الذكية والبحث السريع', en: 'Advanced equipment filter system with smart sorting algorithms and quick search' },
          price: 229,
          category: 'utility',
          features: { ar: ['تصنيف ذكي', 'فلترة متقدمة', 'بحث سريع', 'حفظ تفضيلات'], en: ['Smart sorting', 'Advanced filtering', 'Quick search', 'Save preferences'] },
          image_url: 'https://images.pexels.com/photos/669619/pexels-photo-669619.jpeg?auto=compress&cs=tinysrgb&w=800',
          tech_specs: { ar: ['فلاتر متعددة', 'بحث متقدم', 'ترتيب تلقائي', 'حفظ عمليات البحث'], en: ['Multiple filters', 'Advanced search', 'Auto-sorting', 'Save searches'] },
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];
    }
    if (this.db.technicalServices.length === 0) {
        this.db.technicalServices = [
            {
                id: 'crashFix',
                name: { ar: 'إصلاح الأعطال والكراشات', en: 'Bug Fixes and Crash Resolution' },
                description: { ar: 'إصلاح الأعطال الحرجة والثغرات الأمنية باستخدام تقنيات الحماية المتقدمة', en: 'Fixing critical bugs and security vulnerabilities using advanced protection techniques' },
                price: 150,
                category: 'maintenance',
                isPremiumAddon: true, // Available as Premium Edition add-on
                premiumPrice: 100, // Discounted price when bundled with Premium Edition
                subscriptionType: 'none', // One-time service
                features: { ar: ['إصلاح سريع', 'تحليل الأخطاء', 'حماية متقدمة', 'ضمان الجودة'], en: ['Quick Fixes', 'Error Analysis', 'Advanced Protection', 'Quality Assurance'] },
                image_url: 'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=800',
                tech_specs: { ar: ['تشخيص متقدم', 'إصلاح فوري', 'اختبار شامل', 'دعم مستمر'], en: ['Advanced Diagnostics', 'Immediate Fixes', 'Comprehensive Testing', 'Ongoing Support'] },
                status: 'active',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
              id: 'customization',
              name: { ar: 'تعديل وتخصيص أكواد النظام', en: 'System Code Modification' },
              description: { ar: 'تعديل وتخصيص أكواد النظام والأنظمة حسب متطلبات المشروع', en: 'Modification and customization of system codes according to project requirements' },
              price: 200,
              category: 'development',
              isPremiumAddon: true, // Available as Premium Edition add-on
              premiumPrice: 150, // Discounted price when bundled with Premium Edition
              subscriptionType: 'none', // One-time service
              features: { ar: ['تخصيص كامل', 'برمجة متقدمة', 'تحسين الأداء', 'دعم فني'], en: ['Full Customization', 'Advanced Programming', 'Performance Optimization', 'Technical Support'] },
              image_url: 'https://images.pexels.com/photos/1181677/pexels-photo-1181677.jpeg?auto=compress&cs=tinysrgb&w=800',
              tech_specs: { ar: ['كود نظيف', 'توثيق شامل', 'اختبار متقدم', 'صيانة مستمرة'], en: ['Clean Code', 'Comprehensive Documentation', 'Advanced Testing', 'Continuous Maintenance'] },
              status: 'active',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            {
              id: 'customSystems',
              name: { ar: 'تصميم أنظمة تقنية خاصة', en: 'Custom System Design' },
              description: { ar: 'تصميم وتطوير أنظمة تقنية مخصصة بأحدث التقنيات', en: 'Design and development of custom technical systems with the latest technologies' },
              price: 500,
              category: 'development',
              isPremiumAddon: false, // Not available as Premium Edition add-on (too complex)
              premiumPrice: 0, // Not applicable
              subscriptionType: 'none', // One-time service
              features: { ar: ['تصميم مخصص', 'تقنيات حديثة', 'أداء عالي', 'قابلية التوسع'], en: ['Custom Design', 'Modern Technologies', 'High Performance', 'Scalability'] },
              image_url: 'https://images.pexels.com/photos/1181244/pexels-photo-1181244.jpeg?auto=compress&cs=tinysrgb&w=800',
              tech_specs: { ar: ['هندسة متقدمة', 'أمان عالي', 'واجهات حديثة', 'تكامل سلس'], en: ['Advanced Architecture', 'High Security', 'Modern Interfaces', 'Seamless Integration'] },
              status: 'active',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            {
              id: 'installation',
              name: { ar: 'تنصيب المحتويات والأدوات', en: 'Content and Tool Installation' },
              description: { ar: 'تنصيب المعدات والأدوات الرقمية بسرعة البرق واحترافية عالية', en: 'Installation of digital content and tools with lightning speed and high professionalism' },
              price: 100,
              category: 'installation',
              isPremiumAddon: true, // Available as Premium Edition add-on
              premiumPrice: 0, // Free when bundled with Premium Edition
              subscriptionType: 'none', // One-time service
              features: { ar: ['تنصيب سريع', 'إعداد شامل', 'اختبار كامل', 'دعم فوري'], en: ['Fast Installation', 'Comprehensive Setup', 'Full Testing', 'Immediate Support'] },
              image_url: 'https://images.pexels.com/photos/1181263/pexels-photo-1181263.jpeg?auto=compress&cs=tinysrgb&w=800',
              tech_specs: { ar: ['تنصيب آمن', 'إعداد مُحسن', 'توثيق مفصل', 'دعم تقني'], en: ['Secure Installation', 'Optimized Setup', 'Detailed Documentation', 'Technical Support'] },
              status: 'active',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            {
              id: 'translation',
              name: { ar: 'ترجمة أنظمة النظام إلى العربية', en: 'System Translation to Arabic' },
              description: { ar: 'ترجمة أنظمة النظام والواجهات إلى العربية مع الحفاظ على الطابع التقني', en: 'Translation of system interfaces to Arabic while maintaining the technical character' },
              price: 120,
              category: 'localization',
              isPremiumAddon: true, // Available as Premium Edition add-on
              premiumPrice: 80, // Discounted price when bundled with Premium Edition
              subscriptionType: 'none', // One-time service
              features: { ar: ['ترجمة احترافية', 'دعم RTL', 'خطوط عربية', 'واجهات محلية'], en: ['Professional Translation', 'RTL Support', 'Arabic Fonts', 'Localized Interfaces'] },
              image_url: 'https://images.pexels.com/photos/1181406/pexels-photo-1181406.jpeg?auto=compress&cs=tinysrgb&w=800',
              tech_specs: { ar: ['ترجمة دقيقة', 'تخطيط RTL', 'خطوط محسنة', 'اختبار شامل'], en: ['Accurate Translation', 'RTL Layout', 'Optimized Fonts', 'Comprehensive Testing'] },
              status: 'active',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            {
              id: 'questDev',
              name: { ar: 'تطوير المهام والأنظمة التفاعلية', en: 'Quest & Interactive Systems Dev' },
              description: { ar: 'تطوير المهام والأنظمة التفاعلية المتقدمة', en: 'Development of advanced quests and interactive systems' },
              price: 300,
              category: 'development',
              isPremiumAddon: false, // Not available as Premium Edition add-on (too complex)
              premiumPrice: 0, // Not applicable
              subscriptionType: 'none', // One-time service
              features: { ar: ['مهام تفاعلية', 'قصص متطورة', 'مكافآت ذكية', 'تجربة غامرة'], en: ['Interactive Quests', 'Advanced Storylines', 'Smart Rewards', 'Immersive Experience'] },
              image_url: 'https://images.pexels.com/photos/1181354/pexels-photo-1181354.jpeg?auto=compress&cs=tinysrgb&w=800',
              tech_specs: { ar: ['محرك مهام', 'نظام مكافآت', 'واجهات تفاعلية', 'تتبع التقدم'], en: ['Quest Engine', 'Reward System', 'Interactive UIs', 'Progress Tracking'] },
              status: 'active',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            {
              id: 'freebsd',
              name: { ar: 'إعداد خوادم FreeBSD المحسنة', en: 'Optimized FreeBSD Server Setup' },
              description: { ar: 'إعداد وتحصين خوادم FreeBSD للحماية القصوى', en: 'Setup and hardening of FreeBSD servers for maximum protection' },
              price: 250,
              category: 'infrastructure',
              isPremiumAddon: true, // Available as Premium Edition add-on
              premiumPrice: 0, // Free when bundled with Premium Edition
              subscriptionType: 'none', // One-time service
              features: { ar: ['أمان عالي', 'أداء محسن', 'استقرار عالي', 'مراقبة مستمرة'], en: ['High Security', 'Optimized Performance', 'High Stability', 'Continuous Monitoring'] },
              image_url: 'https://images.pexels.com/photos/1181316/pexels-photo-1181316.jpeg?auto=compress&cs=tinysrgb&w=800',
              tech_specs: { ar: ['تحصين متقدم', 'تحسين الأداء', 'مراقبة النظام', 'نسخ احتياطية'], en: ['Advanced Hardening', 'Performance Tuning', 'System Monitoring', 'Backups'] },
              status: 'active',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            {
              id: 'scripts',
              name: { ar: 'سكربتات الحماية والمراقبة', en: 'Protection & Monitoring Scripts' },
              description: { ar: 'سكربتات حماية ومراقبة متقدمة للأمان الرقمي', en: 'Advanced protection and monitoring scripts for digital security' },
              price: 180,
              category: 'security',
              isPremiumAddon: true, // Available as Premium Edition add-on
              premiumPrice: 120, // Discounted price when bundled with Premium Edition
              subscriptionType: 'none', // One-time service
              features: { ar: ['حماية متقدمة', 'مراقبة فورية', 'تنبيهات ذكية', 'تقارير مفصلة'], en: ['Advanced Protection', 'Real-time Monitoring', 'Smart Alerts', 'Detailed Reports'] },
              image_url: 'https://images.pexels.com/photos/1181467/pexels-photo-1181467.jpeg?auto=compress&cs=tinysrgb&w=800',
              tech_specs: { ar: ['كشف التهديدات', 'حماية تلقائية', 'تحليل السلوك', 'استجابة سريعة'], en: ['Threat Detection', 'Automatic Protection', 'Behavior Analysis', 'Fast Response'] },
              status: 'active',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            {
              id: 'sourceCode',
              name: { ar: 'أكواد مصدرية محسنة ومضمونة', en: 'Optimized & Guaranteed Source Code' },
              description: { ar: 'أكواد مصدرية محسنة ومضمونة العمل بأعلى معايير الأمان', en: 'Optimized source code guaranteed to work with the highest security standards' },
              price: 400,
              category: 'development',
              isPremiumAddon: false, // Not available as Premium Edition add-on (too complex)
              premiumPrice: 0, // Not applicable
              subscriptionType: 'none', // One-time service
              features: { ar: ['كود محسن', 'أمان عالي', 'أداء ممتاز', 'ضمان الجودة'], en: ['Optimized Code', 'High Security', 'Excellent Performance', 'Quality Assurance'] },
              image_url: 'https://images.pexels.com/photos/1181373/pexels-photo-1181373.jpeg?auto=compress&cs=tinysrgb&w=800',
              tech_specs: { ar: ['معايير عالية', 'اختبار شامل', 'توثيق كامل', 'دعم مستمر'], en: ['High Standards', 'Comprehensive Testing', 'Full Documentation', 'Ongoing Support'] },
              status: 'active',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            // New subscription-based services
            {
              id: 'monthlyMaintenance',
              name: { ar: 'صيانة شهرية للخوادم', en: 'Monthly Server Maintenance' },
              description: { ar: 'خدمة صيانة شهرية شاملة للخوادم مع مراقبة مستمرة وتحديثات أمنية', en: 'Comprehensive monthly server maintenance with continuous monitoring and security updates' },
              price: 50,
              category: 'maintenance',
              isPremiumAddon: true, // Available as Premium Edition add-on
              premiumPrice: 30, // Discounted price when bundled with Premium Edition
              subscriptionType: 'monthly', // Recurring monthly service
              features: { ar: ['مراقبة 24/7', 'تحديثات أمنية', 'نسخ احتياطية', 'تقارير شهرية'], en: ['24/7 Monitoring', 'Security Updates', 'Backups', 'Monthly Reports'] },
              image_url: 'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=800',
              tech_specs: { ar: ['مراقبة مستمرة', 'تحديثات تلقائية', 'نسخ احتياطية يومية', 'دعم فني'], en: ['Continuous Monitoring', 'Automatic Updates', 'Daily Backups', 'Technical Support'] },
              status: 'active',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            {
              id: 'yearlySupport',
              name: { ar: 'دعم فني سنوي متقدم', en: 'Advanced Annual Technical Support' },
              description: { ar: 'دعم فني متقدم على مدار السنة مع أولوية في الاستجابة وحلول مخصصة', en: 'Advanced technical support throughout the year with priority response and custom solutions' },
              price: 500,
              category: 'support',
              isPremiumAddon: true, // Available as Premium Edition add-on
              premiumPrice: 300, // Discounted price when bundled with Premium Edition
              subscriptionType: 'yearly', // Recurring yearly service
              features: { ar: ['دعم أولوية', 'استجابة سريعة', 'حلول مخصصة', 'تدريب مجاني'], en: ['Priority Support', 'Fast Response', 'Custom Solutions', 'Free Training'] },
              image_url: 'https://images.pexels.com/photos/1181677/pexels-photo-1181677.jpeg?auto=compress&cs=tinysrgb&w=800',
              tech_specs: { ar: ['استجابة خلال ساعة', 'دعم متعدد القنوات', 'جلسات تدريب', 'تقارير ربع سنوية'], en: ['1-hour Response', 'Multi-channel Support', 'Training Sessions', 'Quarterly Reports'] },
              status: 'active',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
        ];
    }
    this.saveToStorage();
  }

  private generateId(prefix: string = 'id'): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // --- Auth methods ---
  public async signUp(email: string, password: string, userData: { username: string; full_name: string }) {
    if (this.db.users.some(u => u.email === email || u.username === userData.username)) {
      return { data: null, error: { message: 'User already exists' } };
    }
    const password_hash = await hashPassword(password);
    const newUser: User = {
      id: this.generateId('user'),
      email,
      password_hash,
      ...userData,
      role: 'user',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    this.db.users.push(newUser);
    this.db.currentUser = newUser;
    this.saveToStorage();
    return { data: { user: newUser }, error: null };
  }

  public async signIn(email: string, password: string) {
    console.log('SignIn attempt for:', email);
    const user = this.db.users.find(u => u.email === email);
    console.log('User found:', !!user);

    if (user) {
      console.log('Stored hash:', user.password_hash);
      const inputHash = await hashPassword(password);
      console.log('Input hash:', inputHash);
      const passwordMatch = await comparePassword(password, user.password_hash);
      console.log('Password match:', passwordMatch);

      if (passwordMatch) {
        this.db.currentUser = user;
        this.saveToStorage();
        console.log('Login successful');
        return { data: { user }, error: null };
      }
    }

    console.log('Login failed');
    return { data: null, error: { message: 'Invalid email or password' } };
  }

  public signOut() {
    this.db.currentUser = null;
    this.saveToStorage();
    return { error: null };
  }

  public getCurrentUser = () => this.db.currentUser;
  
  // Generic CRUD
  private createItem<T extends { id: string; created_at: string; updated_at: string }>(table: keyof DbTables, itemData: Omit<T, 'id'|'created_at'|'updated_at'>) {
    const newItem = { ...itemData, id: this.generateId(table.toString()), created_at: new Date().toISOString(), updated_at: new Date().toISOString() } as T;
    (this.db[table] as T[]).push(newItem);
    this.saveToStorage();
    return { data: newItem, error: null };
  }

  private updateItem<T extends { id: string; updated_at: string }>(table: keyof DbTables, itemId: string, itemData: Partial<T>) {
    const items = this.db[table] as T[];
    const itemIndex = items.findIndex(i => i.id === itemId);
    if (itemIndex !== -1) {
      items[itemIndex] = { ...items[itemIndex], ...itemData, updated_at: new Date().toISOString() };
      this.saveToStorage();
      return { data: items[itemIndex], error: null };
    }
    return { data: null, error: { message: 'Item not found' } };
  }

  private deleteItem<T extends { id: string }>(table: keyof DbTables, itemId: string) {
    const items = this.db[table] as T[];
    const itemIndex = items.findIndex(i => i.id === itemId);
    if (itemIndex !== -1) {
      const deletedItem = items.splice(itemIndex, 1)[0];
      this.saveToStorage();
      return { data: deletedItem, error: null };
    }
    return { data: null, error: { message: 'Item not found' } };
  }

  // --- Data Access Methods ---
  public getAllUsers = () => ({ data: this.db.users, error: null });
  public getAllServices = () => ({ data: this.db.userServices, error: null });
  public getSystemServices = () => ({ data: this.db.systemServices, error: null });
  public getTechnicalServices = () => ({ data: this.db.technicalServices, error: null });
  public getPremiumContent = () => ({ data: this.db.premiumContent, error: null });
  public getPremiumPackages = () => ({ data: this.db.premiumPackages, error: null });
  public getPremiumEditions = () => ({ data: this.db.premiumEditions, error: null });
  public getUserServices = (userId: string) => ({ data: this.db.userServices.filter(s => s.user_id === userId), error: null });

  // Enhanced order creation function with full business logic
  public createOrder = (userId: string, orderData: Omit<Order, 'id' | 'user_id' | 'purchase_date'>) => {
    const orderId = this.generateId();
    const now = new Date().toISOString();

    const order: Order = {
      id: orderId,
      user_id: userId,
      purchase_date: now,
      ...orderData
    };

    // Create the order record
    const orderResult = this.createItem('orders', order);

    // Also add to legacy userServices for backward compatibility
    if (orderResult.data) {
      this.createItem('userServices', {
        user_id: userId,
        purchase_date: now,
        ...orderData
      });

      // Handle subscription services for premium_custom orders
      if (orderData.type === 'premium_custom' && orderData.details) {
        try {
          const orderDetails = JSON.parse(orderData.details);

          // Check for subscription services in the order
          if (orderDetails.selectedServices) {
            orderDetails.selectedServices.forEach((service: any) => {
              if (service.subscriptionType && service.subscriptionType !== 'none') {
                // Calculate next billing date
                const nextBillingDate = new Date();
                if (service.subscriptionType === 'monthly') {
                  nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
                } else if (service.subscriptionType === 'yearly') {
                  nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1);
                }

                // Create subscription record
                const subscription: Subscription = {
                  id: this.generateId(),
                  userId: userId,
                  orderId: orderId,
                  serviceId: service.id,
                  status: 'active',
                  nextBillingDate: nextBillingDate.toISOString(),
                  price: service.price,
                  billingCycle: service.subscriptionType,
                  created_at: now,
                  updated_at: now
                };

                this.createItem('subscriptions', subscription);
              }
            });
          }
        } catch (error) {
          console.error('Error processing subscription services:', error);
        }
      }
    }

    return orderResult;
  };

  // Legacy function for backward compatibility
  public purchaseService = (userId: string, serviceData: Omit<UserService, 'id' | 'user_id' | 'purchase_date'>) => this.createItem('userServices', { user_id: userId, purchase_date: new Date().toISOString(), ...serviceData });
  public createContactMessage = (data: Omit<ContactMessage, 'id'|'created_at'|'status'>) => this.createItem('contactMessages', {...data, status: 'new'});

  // New CRUD functions for Orders
  public getAllOrders = () => ({ data: this.db.orders || [], error: null });
  public getOrdersByUser = (userId: string) => ({ data: (this.db.orders || []).filter(o => o.user_id === userId), error: null });
  public updateOrder = (orderId: string, orderData: Partial<Order>) => this.updateItem('orders', orderId, orderData);
  public deleteOrder = (orderId: string) => this.deleteItem('orders', orderId);

  // New CRUD functions for Subscriptions
  public getAllSubscriptions = () => ({ data: this.db.subscriptions || [], error: null });
  public getSubscriptionsByUser = (userId: string) => ({ data: (this.db.subscriptions || []).filter(s => s.userId === userId), error: null });
  public createSubscription = (data: Omit<Subscription, 'id'|'created_at'|'updated_at'>) => this.createItem('subscriptions', data);
  public updateSubscription = (subscriptionId: string, data: Partial<Subscription>) => this.updateItem('subscriptions', subscriptionId, data);
  public deleteSubscription = (subscriptionId: string) => this.deleteItem('subscriptions', subscriptionId);

  // New CRUD functions for InboxMessages
  public getAllInboxMessages = () => ({ data: this.db.inboxMessages || [], error: null });
  public getInboxMessagesByUser = (userId: string) => ({ data: (this.db.inboxMessages || []).filter(m => m.userId === userId), error: null });
  public createInboxMessage = (data: Omit<InboxMessage, 'id'>) => this.createItem('inboxMessages', data);
  public updateInboxMessage = (messageId: string, data: Partial<InboxMessage>) => this.updateItem('inboxMessages', messageId, data);
  public deleteInboxMessage = (messageId: string) => this.deleteItem('inboxMessages', messageId);

  // Initialize test data for development - moved to admin panel
  public initializeTestData = () => {
    // Initialize empty arrays if they don't exist
    if (!this.db.premiumEditions) this.db.premiumEditions = [];
    if (!this.db.systemServices) this.db.systemServices = [];
    if (!this.db.technicalServices) this.db.technicalServices = [];
    if (!this.db.orders) this.db.orders = [];
    if (!this.db.subscriptions) this.db.subscriptions = [];
    if (!this.db.inboxMessages) this.db.inboxMessages = [];

    // Create sample data if arrays are empty
    this.createSampleData();

    this.saveToStorage();
    return { success: true, message: 'Database structure initialized' };
  };

  // Create sample test data - to be called from admin panel
  public createSampleData = () => {
    try {
      // Create test premium edition if not exists
      const premiumEditions = this.db.premiumEditions || [];
      if (premiumEditions.length === 0) {
        const testPremiumEdition = {
          id: 'premium-edition-1',
          name: { ar: 'النسخة المميزة', en: 'Premium Edition' },
          description: { ar: 'نسخة مميزة مع جميع الميزات المتقدمة', en: 'Premium edition with all advanced features' },
          price: 299,
          original_price: 399,
          discount_percentage: 25,
          features: {
            ar: ['دعم فني 24/7', 'تحديثات مجانية', 'تخصيص كامل', 'أولوية في الدعم'],
            en: ['24/7 Technical Support', 'Free Updates', 'Full Customization', 'Priority Support']
          },
          image_url: 'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg',
          gallery_images: [
            'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg',
            'https://images.pexels.com/photos/1181263/pexels-photo-1181263.jpeg',
            'https://images.pexels.com/photos/1181244/pexels-photo-1181244.jpeg'
          ],
          video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
          is_active: true,
          is_available: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        this.createItem('premiumEditions', testPremiumEdition);
      }

      // Create test systems if not exists
      const systems = this.db.systemServices || [];
      if (systems.length === 0) {
        const testSystems = [
          {
            id: 'system-1',
            name: { ar: 'نظام إدارة المحتوى', en: 'Content Management System' },
            description: { ar: 'نظام متقدم لإدارة المحتوى', en: 'Advanced content management system' },
            price: 150,
            type: 'regular' as const,
            features: { ar: ['إدارة سهلة', 'واجهة حديثة'], en: ['Easy Management', 'Modern Interface'] },
            images: ['https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg'],
            is_available: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 'system-2',
            name: { ar: 'إضافة الدفع المتقدمة', en: 'Advanced Payment Plugin' },
            description: { ar: 'إضافة خاصة للدفع المتقدم', en: 'Special plugin for advanced payments' },
            price: 200,
            type: 'plugin' as const,
            features: { ar: ['دفع آمن', 'عدة طرق دفع'], en: ['Secure Payment', 'Multiple Payment Methods'] },
            images: ['https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg'],
            is_available: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ];
        testSystems.forEach(system => this.createItem('systemServices', system));
      }

      // Create test technical services if not exists
      const services = this.db.technicalServices || [];
      if (services.length === 0) {
        const testServices = [
          {
            id: 'service-1',
            name: { ar: 'خدمة الصيانة الشهرية', en: 'Monthly Maintenance Service' },
            description: { ar: 'خدمة صيانة شهرية للموقع', en: 'Monthly website maintenance service' },
            price: 100,
            premiumPrice: 50,
            subscriptionType: 'monthly' as const,
            isPremiumAddon: true,
            features: { ar: ['صيانة دورية', 'تحديثات أمنية'], en: ['Regular Maintenance', 'Security Updates'] },
            is_available: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 'service-2',
            name: { ar: 'خدمة التطوير المخصص', en: 'Custom Development Service' },
            description: { ar: 'خدمة تطوير مخصصة حسب الطلب', en: 'Custom development service on demand' },
            price: 500,
            premiumPrice: 0,
            subscriptionType: 'none' as const,
            isPremiumAddon: true,
            features: { ar: ['تطوير مخصص', 'دعم مباشر'], en: ['Custom Development', 'Direct Support'] },
            is_available: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ];
        testServices.forEach(service => this.createItem('technicalServices', service));
      }

      return { success: true, message: 'Sample data created successfully' };
    } catch (error) {
      console.error('Error creating sample data:', error);
      return { success: false, error: 'Failed to create sample data' };
    }
  };

  // Test function to create sample subscription data for testing
  public createTestSubscription = (userId: string) => {
    const nextBillingDate = new Date();
    nextBillingDate.setDate(nextBillingDate.getDate() + 2); // 2 days from now

    const testSubscription: Subscription = {
      id: this.generateId(),
      userId: userId,
      orderId: 'test-order-' + Date.now(),
      serviceId: 'test-service-' + Date.now(),
      status: 'active',
      nextBillingDate: nextBillingDate.toISOString(),
      price: 29.99,
      billingCycle: 'monthly',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    this.createItem('subscriptions', testSubscription);
    return { data: testSubscription, error: null };
  };

  // Generate billing alerts for upcoming subscription renewals
  public generateBillingAlerts = () => {
    try {
      const now = new Date();
      const alertThreshold = new Date();
      alertThreshold.setDate(now.getDate() + 3); // Alert 3 days before billing

      const subscriptions = this.db.subscriptions || [];
      const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');

      let alertsCreated = 0;

      activeSubscriptions.forEach(subscription => {
        const billingDate = new Date(subscription.nextBillingDate);

        // Check if billing date is within alert threshold
        if (billingDate <= alertThreshold && billingDate > now) {
          // Check if alert already exists for this billing cycle
          const existingAlerts = (this.db.inboxMessages || []).filter(msg =>
            msg.userId === subscription.userId &&
            msg.type === 'billing' &&
            msg.content.en.includes(subscription.id)
          );

          if (existingAlerts.length === 0) {
            // Create billing alert message
            const alertMessage: InboxMessage = {
              id: this.generateId(),
              userId: subscription.userId,
              title: {
                ar: 'تنبيه تجديد الاشتراك',
                en: 'Subscription Renewal Alert'
              },
              content: {
                ar: `سيتم تجديد اشتراكك قريباً في ${billingDate.toLocaleDateString('ar-SA')} بمبلغ $${subscription.price}. معرف الاشتراك: ${subscription.id}`,
                en: `Your subscription will be renewed on ${billingDate.toLocaleDateString('en-US')} for $${subscription.price}. Subscription ID: ${subscription.id}`
              },
              isRead: false,
              type: 'billing',
              createdAt: now.toISOString()
            };

            this.createItem('inboxMessages', alertMessage);
            alertsCreated++;
          }
        }
      });

      return {
        success: true,
        alertsCreated,
        message: `Generated ${alertsCreated} billing alerts`
      };
    } catch (error) {
      console.error('Error generating billing alerts:', error);
      return {
        success: false,
        error: 'Failed to generate billing alerts'
      };
    }
  };

  public updateUserRole = (userId: string, role: 'user' | 'admin') => this.updateItem('users', userId, { role });
  public updateServiceStatus = (serviceId: string, status: UserService['status']) => this.updateItem('userServices', serviceId, { status, ...(status === 'completed' && { completion_date: new Date().toISOString() }) });

  // إعادة تعيين كلمة مرور المستخدم الإداري
  public async resetAdminPassword() {
    const adminUser = this.db.users.find(u => u.email === '<EMAIL>');
    if (adminUser) {
      const newPasswordHash = await hashPassword('admin123');
      adminUser.password_hash = newPasswordHash;
      adminUser.updated_at = new Date().toISOString();
      this.saveToStorage();
      console.log('Admin password reset. New hash:', newPasswordHash);
      return { success: true };
    }
    return { success: false, error: 'Admin user not found' };
  }
  
  public createSystemService = (data: Omit<SystemService, 'id'|'created_at'|'updated_at'>) => this.createItem('systemServices', data);
  public updateSystemService = (id: string, data: Partial<SystemService>) => this.updateItem('systemServices', id, data);
  public deleteSystemService = (id: string) => this.deleteItem('systemServices', id);
  
  public createTechnicalService = (data: Omit<TechnicalService, 'id'|'created_at'|'updated_at'>) => this.createItem('technicalServices', data);
  public updateTechnicalService = (id: string, data: Partial<TechnicalService>) => this.updateItem('technicalServices', id, data);
  public deleteTechnicalService = (id: string) => this.deleteItem('technicalServices', id);
  
  public createPremiumContent = (data: Omit<PremiumContent, 'id'|'created_at'|'updated_at'>) => this.createItem('premiumContent', data);
  public updatePremiumContent = (id: string, data: Partial<PremiumContent>) => this.updateItem('premiumContent', id, data);
  public deletePremiumContent = (id: string) => this.deleteItem('premiumContent', id);

  public createPremiumPackage = (data: Omit<PremiumPackage, 'id'|'created_at'|'updated_at'>) => this.createItem('premiumPackages', data);
  public updatePremiumPackage = (id: string, data: Partial<PremiumPackage>) => this.updateItem('premiumPackages', id, data);
  public deletePremiumPackage = (id: string) => this.deleteItem('premiumPackages', id);
}

const localDB = new LocalDatabase();

// --- Corrected Export Method ---
export const signUp = (email: string, password: string, userData: { username: string; full_name: string }) => localDB.signUp(email, password, userData);
export const signIn = (email: string, password: string) => localDB.signIn(email, password);
export const signOut = () => localDB.signOut();
export const getCurrentUser = () => localDB.getCurrentUser();
export const getUserProfile = (userId: string) => localDB.getAllUsers().data.find(u => u.id === userId);
export const createUserProfile = (userId: string, userData: Partial<User>) => localDB.updateItem('users', userId, userData);
export const getUserServices = (userId: string) => localDB.getUserServices(userId);
export const purchaseService = (userId: string, serviceData: Omit<UserService, 'id' | 'user_id' | 'purchase_date'>) => localDB.purchaseService(userId, serviceData);
export const createContactMessage = (messageData: Omit<ContactMessage, 'id' | 'created_at' | 'status'>) => localDB.createContactMessage(messageData);
export const getContactMessages = () => ({ data: (localDB as any).db.contactMessages, error: null });

// New Order Management Functions
export const createOrder = (userId: string, orderData: Omit<Order, 'id' | 'user_id' | 'purchase_date'>) => localDB.createOrder(userId, orderData);
export const getAllOrders = () => localDB.getAllOrders();
export const getOrdersByUser = (userId: string) => localDB.getOrdersByUser(userId);
export const updateOrder = (orderId: string, orderData: Partial<Order>) => localDB.updateOrder(orderId, orderData);
export const deleteOrder = (orderId: string) => localDB.deleteOrder(orderId);

// New Subscription Management Functions
export const getAllSubscriptions = () => localDB.getAllSubscriptions();
export const getSubscriptionsByUser = (userId: string) => localDB.getSubscriptionsByUser(userId);
export const createSubscription = (data: Omit<Subscription, 'id'|'created_at'|'updated_at'>) => localDB.createSubscription(data);
export const updateSubscription = (subscriptionId: string, data: Partial<Subscription>) => localDB.updateSubscription(subscriptionId, data);
export const deleteSubscription = (subscriptionId: string) => localDB.deleteSubscription(subscriptionId);

// New Inbox Message Management Functions
export const getAllInboxMessages = () => localDB.getAllInboxMessages();
export const getInboxMessagesByUser = (userId: string) => localDB.getInboxMessagesByUser(userId);
export const createInboxMessage = (data: Omit<InboxMessage, 'id'>) => localDB.createInboxMessage(data);
export const updateInboxMessage = (messageId: string, data: Partial<InboxMessage>) => localDB.updateInboxMessage(messageId, data);
export const deleteInboxMessage = (messageId: string) => localDB.deleteInboxMessage(messageId);

// Utility Functions
export const generateBillingAlerts = () => localDB.generateBillingAlerts();
export const createTestSubscription = (userId: string) => localDB.createTestSubscription(userId);
export const initializeTestData = () => localDB.initializeTestData();
export const createSampleData = () => localDB.createSampleData();

// Admin functions
export const getAllUsers = () => localDB.getAllUsers();
export const getAllServices = () => localDB.getAllServices();
export const updateServiceStatus = (serviceId: string, status: UserService['status']) => localDB.updateServiceStatus(serviceId, status);
export const updateUserRole = (userId: string, role: 'user' | 'admin') => localDB.updateUserRole(userId, role);
export const resetAdminPassword = () => localDB.resetAdminPassword();

// System Services Management
export const createSystemService = (serviceData: Omit<SystemService, 'id'|'created_at'|'updated_at'>) => localDB.createSystemService(serviceData);
export const updateSystemService = (serviceId: string, serviceData: Partial<SystemService>) => localDB.updateSystemService(serviceId, serviceData);
export const deleteSystemService = (serviceId: string) => localDB.deleteSystemService(serviceId);
export const getSystemServices = () => localDB.getSystemServices();

// Technical Services Management
export const createTechnicalService = (serviceData: Omit<TechnicalService, 'id'|'created_at'|'updated_at'>) => localDB.createTechnicalService(serviceData);
export const updateTechnicalService = (serviceId: string, serviceData: Partial<TechnicalService>) => localDB.updateTechnicalService(serviceId, serviceData);
export const deleteTechnicalService = (serviceId: string) => localDB.deleteTechnicalService(serviceId);
export const getTechnicalServices = () => localDB.getTechnicalServices();

// Premium Content Management
export const createPremiumContent = (contentData: Omit<PremiumContent, 'id'|'created_at'|'updated_at'>) => localDB.createPremiumContent(contentData);
export const updatePremiumContent = (contentId: string, contentData: Partial<PremiumContent>) => localDB.updatePremiumContent(contentId, contentData);
export const deletePremiumContent = (contentId: string) => localDB.deletePremiumContent(contentId);
export const getPremiumContent = () => localDB.getPremiumContent();

export const createPremiumPackage = (packageData: Omit<PremiumPackage, 'id'|'created_at'|'updated_at'>) => localDB.createPremiumPackage(packageData);
export const updatePremiumPackage = (packageId: string, packageData: Partial<PremiumPackage>) => localDB.updatePremiumPackage(packageId, packageData);
export const deletePremiumPackage = (packageId: string) => localDB.deletePremiumPackage(packageId);
export const getPremiumPackages = () => localDB.getPremiumPackages();

// Premium Edition Management
export const getPremiumEditions = () => localDB.getPremiumEditions();
export const createPremiumEdition = (editionData: any) => localDB.createItem('premiumEditions', editionData);
export const updatePremiumEdition = (editionId: string, editionData: any) => localDB.updateItem('premiumEditions', editionId, editionData);
export const deletePremiumEdition = (editionId: string) => localDB.deleteItem('premiumEditions', editionId);

// Activate Premium Edition (deactivates all others)
export const activatePremiumEdition = (editionId: string) => {
  try {
    const result = localDB.getPremiumEditions();
    if (!result.success || !result.data) {
      return { success: false, error: 'Failed to get premium editions' };
    }

    // Deactivate all editions first
    result.data.forEach(edition => {
      localDB.updateItem('premiumEditions', edition.id, { ...edition, is_active: false });
    });

    // Activate the selected edition
    const targetEdition = result.data.find(edition => edition.id === editionId);
    if (!targetEdition) {
      return { success: false, error: 'Premium edition not found' };
    }

    const updateResult = localDB.updateItem('premiumEditions', editionId, { ...targetEdition, is_active: true });
    return updateResult;
  } catch (error) {
    return { success: false, error: 'Failed to activate premium edition' };
  }
};