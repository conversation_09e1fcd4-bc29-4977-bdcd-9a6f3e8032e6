#!/usr/bin/env node

const mysql = require('mysql2/promise');

async function addInactiveSystem() {
  console.log('🔧 Adding Inactive System for Testing...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '123456',
      database: 'khan<PERSON>shariya_db'
    });

    console.log('✅ Connected to MySQL database\n');

    // Update existing system to inactive for testing
    console.log('1️⃣ Setting one system to inactive...');
    
    await connection.execute(`
      UPDATE system_services 
      SET status = 'inactive', updated_at = NOW()
      WHERE id = 'quickSwitch'
    `);
    
    console.log('   ✅ Set quickSwitch system to inactive');

    // Add a new inactive system
    console.log('\n2️⃣ Adding new inactive system...');
    
    const inactiveSystemData = {
      id: 'testInactive',
      name_ar: 'نظام اختبار غير نشط',
      name_en: 'Inactive Test System',
      description_ar: 'هذا نظام للاختبار وهو غير نشط حالياً',
      description_en: 'This is a test system that is currently inactive',
      price: 199.00,
      category: 'testing',
      type: 'plugin',
      features_ar: [
        'ميزة اختبار 1',
        'ميزة اختبار 2',
        'ميزة اختبار 3'
      ],
      features_en: [
        'Test feature 1',
        'Test feature 2', 
        'Test feature 3'
      ],
      tech_specs_ar: [
        'متطلب تقني 1',
        'متطلب تقني 2'
      ],
      tech_specs_en: [
        'Technical requirement 1',
        'Technical requirement 2'
      ],
      image_url: 'https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg',
      video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      status: 'inactive'
    };

    await connection.execute(`
      INSERT INTO system_services (
        id, name_ar, name_en, description_ar, description_en, price, category, type,
        features_ar, features_en, tech_specs_ar, tech_specs_en,
        video_url, image_url, gallery_images, status, featured, is_premium_addon,
        download_count, rating, rating_count, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      inactiveSystemData.id,
      inactiveSystemData.name_ar,
      inactiveSystemData.name_en,
      inactiveSystemData.description_ar,
      inactiveSystemData.description_en,
      inactiveSystemData.price,
      inactiveSystemData.category,
      inactiveSystemData.type,
      JSON.stringify(inactiveSystemData.features_ar),
      JSON.stringify(inactiveSystemData.features_en),
      JSON.stringify(inactiveSystemData.tech_specs_ar),
      JSON.stringify(inactiveSystemData.tech_specs_en),
      inactiveSystemData.video_url,
      inactiveSystemData.image_url,
      JSON.stringify([]), // empty gallery
      inactiveSystemData.status,
      0, // not featured
      0, // not premium addon
      0, // download count
      0, // rating
      0  // rating count
    ]);
    
    console.log('   ✅ Added new inactive test system');

    // Test the results
    console.log('\n3️⃣ Testing system status distribution...');
    
    const [statusCount] = await connection.execute(`
      SELECT status, COUNT(*) as count 
      FROM system_services 
      GROUP BY status
    `);
    
    console.log('System status distribution:');
    statusCount.forEach(row => {
      console.log(`   ${row.status}: ${row.count} systems`);
    });

    const [allSystems] = await connection.execute(`
      SELECT id, name_ar, status 
      FROM system_services 
      ORDER BY status DESC, created_at DESC
    `);
    
    console.log('\nAll systems:');
    allSystems.forEach(system => {
      const statusIcon = system.status === 'active' ? '🟢' : '🔴';
      console.log(`   ${statusIcon} ${system.name_ar} (${system.status})`);
    });

    await connection.end();

    console.log('\n✅ Inactive system testing setup completed!');
    console.log('\n🔄 Expected results in admin panel:');
    console.log('- Active systems should show with green icon and "نشط" status');
    console.log('- Inactive systems should show with red icon and "غير نشط" status');
    console.log('- All systems (active + inactive) should be visible in admin');
    console.log('- Only active systems should show in public homepage');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

addInactiveSystem();
