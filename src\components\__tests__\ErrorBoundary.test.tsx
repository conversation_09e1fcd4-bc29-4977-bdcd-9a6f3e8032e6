/**
 * ErrorBoundary Component Tests
 * 
 * Tests for the enhanced error boundary component
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '../../test/utils'
import ErrorBoundary, { withErrorBoundary, useError<PERSON>and<PERSON> } from '../ErrorBoundary'
import React from 'react'

// Mock component that throws an error
const ThrowError = ({ shouldThrow = false }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error')
  }
  return <div>No error</div>
}

// Mock component for testing HOC
const TestComponent = () => <div>Test Component</div>

describe('ErrorBoundary', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Suppress console.error for error boundary tests
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  describe('Normal Operation', () => {
    it('renders children when no error occurs', () => {
      render(
        <ErrorBoundary>
          <div>Test content</div>
        </ErrorBoundary>
      )

      expect(screen.getByText('Test content')).toBeInTheDocument()
    })

    it('does not show error UI when no error', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      )

      expect(screen.getByText('No error')).toBeInTheDocument()
      expect(screen.queryByText('عذراً، حدث خطأ غير متوقع')).not.toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('catches and displays error when child component throws', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText('عذراً، حدث خطأ غير متوقع')).toBeInTheDocument()
      expect(screen.getByText('Sorry, something went wrong')).toBeInTheDocument()
    })

    it('displays error message in error UI', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText(/Test error/)).toBeInTheDocument()
    })

    it('generates unique error ID', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      const errorIdElement = screen.getByText(/معرف الخطأ:/)
      expect(errorIdElement).toBeInTheDocument()
      expect(errorIdElement.textContent).toMatch(/error_\d+_[a-z0-9]+/)
    })

    it('calls onError callback when error occurs', () => {
      const onError = vi.fn()
      
      render(
        <ErrorBoundary onError={onError}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(onError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String)
        })
      )
    })
  })

  describe('Error UI Interactions', () => {
    it('shows retry button when retries are available', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      const retryButton = screen.getByText(/إعادة المحاولة/)
      expect(retryButton).toBeInTheDocument()
      expect(retryButton.textContent).toContain('3 محاولات متبقية')
    })

    it('handles retry button click', () => {
      const TestComponentWithState = () => {
        const [shouldThrow, setShouldThrow] = React.useState(true)
        
        React.useEffect(() => {
          const timer = setTimeout(() => setShouldThrow(false), 100)
          return () => clearTimeout(timer)
        }, [])
        
        return <ThrowError shouldThrow={shouldThrow} />
      }

      render(
        <ErrorBoundary>
          <TestComponentWithState />
        </ErrorBoundary>
      )

      const retryButton = screen.getByText(/إعادة المحاولة/)
      fireEvent.click(retryButton)

      // After retry, should show the component again
      setTimeout(() => {
        expect(screen.getByText('No error')).toBeInTheDocument()
      }, 200)
    })

    it('shows reload page button', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText('إعادة تحميل الصفحة')).toBeInTheDocument()
    })

    it('shows go home button', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText('العودة للصفحة الرئيسية')).toBeInTheDocument()
    })

    it('shows bug report button', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText('الإبلاغ عن هذا الخطأ')).toBeInTheDocument()
    })
  })

  describe('Custom Fallback', () => {
    it('uses custom fallback component when provided', () => {
      const CustomFallback = ({ error }: { error: Error }) => (
        <div>Custom error: {error.message}</div>
      )

      render(
        <ErrorBoundary fallback={CustomFallback}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText('Custom error: Test error')).toBeInTheDocument()
      expect(screen.queryByText('عذراً، حدث خطأ غير متوقع')).not.toBeInTheDocument()
    })
  })

  describe('Development Mode', () => {
    it('shows error details in development mode', () => {
      render(
        <ErrorBoundary showDetails={true}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText('تفاصيل تقنية')).toBeInTheDocument()
    })

    it('hides error details in production mode', () => {
      render(
        <ErrorBoundary showDetails={false}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.queryByText('تفاصيل تقنية')).not.toBeInTheDocument()
    })
  })
})

describe('withErrorBoundary HOC', () => {
  it('wraps component with error boundary', () => {
    const WrappedComponent = withErrorBoundary(TestComponent)
    
    render(<WrappedComponent />)
    
    expect(screen.getByText('Test Component')).toBeInTheDocument()
  })

  it('passes props to wrapped component', () => {
    const ComponentWithProps = ({ message }: { message: string }) => (
      <div>{message}</div>
    )
    const WrappedComponent = withErrorBoundary(ComponentWithProps)
    
    render(<WrappedComponent message="Hello World" />)
    
    expect(screen.getByText('Hello World')).toBeInTheDocument()
  })

  it('catches errors in wrapped component', () => {
    const ErrorComponent = () => {
      throw new Error('HOC test error')
    }
    const WrappedComponent = withErrorBoundary(ErrorComponent)
    
    render(<WrappedComponent />)
    
    expect(screen.getByText('عذراً، حدث خطأ غير متوقع')).toBeInTheDocument()
  })
})

describe('useErrorHandler hook', () => {
  it('provides reportError function', () => {
    const TestComponent = () => {
      const { reportError } = useErrorHandler()
      
      const handleClick = () => {
        reportError(new Error('Manual error'), 'Test context')
      }
      
      return <button onClick={handleClick}>Report Error</button>
    }
    
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    render(<TestComponent />)
    
    fireEvent.click(screen.getByText('Report Error'))
    
    expect(consoleSpy).toHaveBeenCalledWith(
      'Manual error report:',
      expect.any(Error),
      'Test context'
    )
  })
})
