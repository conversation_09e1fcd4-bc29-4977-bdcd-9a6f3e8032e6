import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuth } from '../hooks/useAuth';
import { useNotification } from '../hooks/useNotification';
import { useButtonActions } from '../utils/buttonActions';
import { getTechnicalServices, createOrder } from '../lib/apiServices';
import { TechnicalService } from '../lib/database';
import { LoadingWithText, CardSkeleton, useLoadingState } from './ui/LoadingStates';
import { TouchButton, MobileCard } from './ui/MobileOptimized';

import {
  Wrench,
  Settings,
  Palette,
  Download,
  Globe,
  Code,
  Server,
  FileText,
  Shield,
  Zap,
  Target,
  Lock,
  ArrowRight,
  ArrowLeft,
  CheckCircle,
  Play,
  Image as ImageIcon,
  Grid3X3,
  List,
  Eye,
  Info,
  DollarSign,
  Calendar,
  Crown,
  X,
  Star,
  RefreshCw
} from 'lucide-react';


const Services: React.FC = () => {
  const { t, language } = useTranslation();
  const { isAuthenticated, userProfile } = useAuth();
  const { showNotification } = useNotification();
  const buttonActions = useButtonActions();
  const [services, setServices] = useState<TechnicalService[]>([]);
  const { isLoading, startLoading, stopLoading, setLoadingError } = useLoadingState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedService, setSelectedService] = useState<TechnicalService | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [hoveredService, setHoveredService] = useState<string | null>(null);

  useEffect(() => {
    loadServices();
  }, []);

  const loadServices = async () => {
    startLoading();
    try {
      const result = await getTechnicalServices();
      console.log('Services - API result:', result); // Debug log

      // Check if data is in result.data.services or result.data directly
      const servicesData = result.data?.services || result.data;
      if (servicesData && Array.isArray(servicesData)) {
        setServices(servicesData.filter(s => s.status === 'active'));
        console.log('Services loaded successfully:', servicesData.length);
      } else {
        console.warn('Invalid services data:', result);
        setServices([]);
      }
      stopLoading();
    } catch (error) {
      console.error('Error loading services:', error);
      setLoadingError(t('services.loadError'));
    }
  };

  const handleRequestService = async (service: TechnicalService) => {
    if (!isAuthenticated) {
      showNotification({ type: 'error', message: t('notifications.loginRequired') });
      return;
    }

    if (!userProfile) {
      showNotification({ type: 'error', message: t('notifications.profileError') });
      return;
    }

    try {
      const { error } = await createOrder({
        order_type: 'technical_service',
        item_id: service.id,
        quantity: 1,
        notes_ar: `شراء خدمة: ${service.name_ar}`,
        notes_en: `Purchased service: ${service.name_en}`
      });

      if (error) {
        showNotification({ type: 'error', message: t('notifications.purchaseError') });
      } else {
        showNotification({ type: 'success', message: t('notifications.purchaseSuccess') });
      }
    } catch (error) {
      console.error('Error:', error);
      showNotification({ type: 'error', message: t('notifications.purchaseError') });
    }
  };

  const handleContactTeam = () => {
    const contactElement = document.getElementById('contact');
    if (contactElement) {
      contactElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleViewDetails = (service: TechnicalService) => {
    setSelectedService(service);
    setShowDetailsModal(true);
  };

  const closeDetailsModal = () => {
    setSelectedService(null);
    setShowDetailsModal(false);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'maintenance': return Shield;
      case 'development': return Code;
      case 'installation': return Download;
      case 'localization': return Globe;
      case 'infrastructure': return Server;
      case 'security': return Lock;
      default: return Wrench;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'maintenance': return {
        color: 'from-red-500 to-red-600',
        accentColor: 'text-red-400',
        borderColor: 'border-red-500/30',
        bgColor: 'bg-red-500/10'
      };
      case 'development': return {
        color: 'from-blue-500 to-blue-600',
        accentColor: 'text-blue-400',
        borderColor: 'border-blue-500/30',
        bgColor: 'bg-blue-500/10'
      };
      case 'installation': return {
        color: 'from-green-500 to-green-600',
        accentColor: 'text-green-400',
        borderColor: 'border-green-500/30',
        bgColor: 'bg-green-500/10'
      };
      case 'localization': return {
        color: 'from-orange-500 to-orange-600',
        accentColor: 'text-orange-400',
        borderColor: 'border-orange-500/30',
        bgColor: 'bg-orange-500/10'
      };
      case 'infrastructure': return {
        color: 'from-gray-500 to-gray-600',
        accentColor: 'text-gray-400',
        borderColor: 'border-gray-500/30',
        bgColor: 'bg-gray-500/10'
      };
      case 'security': return {
        color: 'from-yellow-500 to-yellow-600',
        accentColor: 'text-yellow-400',
        borderColor: 'border-yellow-500/30',
        bgColor: 'bg-yellow-500/10'
      };
      default: return {
        color: 'from-purple-500 to-purple-600',
        accentColor: 'text-purple-400',
        borderColor: 'border-purple-500/30',
        bgColor: 'bg-purple-500/10'
      };
    }
  };

  if (isLoading) {
    return (
      <section id="services" className="py-20 bg-gradient-to-br from-background-primary to-background-secondary">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-8">
            <LoadingWithText
              text="جاري تحميل الخدمات..."
              textEn="Loading services..."
              size="lg"
              direction="vertical"
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
            {[...Array(6)].map((_, index) => (
              <CardSkeleton key={index} />
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="services" className="py-20 bg-gradient-to-br from-primary to-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center items-center space-x-4 rtl:space-x-reverse mb-6">
            <Shield className="w-8 h-8 text-secondary" />
            <h2 className="text-4xl md:text-5xl font-bold text-white">
              <span className="bg-gradient-to-r from-secondary to-accent bg-clip-text text-transparent">
                {t('services.title')}
              </span>
            </h2>
            <Zap className="w-8 h-8 text-accent" />
          </div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            {t('services.subtitle')}
          </p>

          {/* View Mode Toggle */}
          <div className="flex justify-center items-center space-x-4 rtl:space-x-reverse">
            <span className="text-gray-400 text-sm">
              {language === 'ar' ? 'طريقة العرض:' : 'View Mode:'}
            </span>
            <div className="flex bg-background/50 rounded-lg p-1 border border-gray-700">
              <button
                onClick={() => setViewMode('grid')}
                className={`flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 rounded-md transition-all duration-300 ${
                  viewMode === 'grid'
                    ? 'bg-secondary text-primary shadow-lg'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                }`}
              >
                <Grid3X3 className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {language === 'ar' ? 'شبكة' : 'Grid'}
                </span>
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 rounded-md transition-all duration-300 ${
                  viewMode === 'list'
                    ? 'bg-secondary text-primary shadow-lg'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                }`}
              >
                <List className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {language === 'ar' ? 'قائمة' : 'List'}
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Services Display */}
        <div className={viewMode === 'grid'
          ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          : "space-y-6"
        }>
          {services.map((service, index) => {
            const Icon = getCategoryIcon(service.category);
            const colors = getCategoryColor(service.category);
            const isHovered = hoveredService === service.id;

            return (
              <div
                key={index}
                className={`group relative ${
                  viewMode === 'grid'
                    ? 'p-6 bg-gradient-to-br from-primary/80 to-background/80 backdrop-blur-sm rounded-2xl flex flex-col h-full'
                    : 'p-6 bg-gradient-to-r from-primary/80 to-background/80 backdrop-blur-sm rounded-xl flex items-center space-x-6 rtl:space-x-reverse'
                } border ${colors.borderColor} hover:border-secondary/50 transition-all duration-300 hover:shadow-xl hover:shadow-secondary/10 ${
                  viewMode === 'grid' ? 'hover:-translate-y-1' : ''
                }`}
                onMouseEnter={() => setHoveredService(service.id)}
                onMouseLeave={() => setHoveredService(null)}
                title={`${language === 'ar' ? service.name_ar : service.name_en} - $${service.price} - ${service.status === 'active' ? (language === 'ar' ? 'متاح' : 'Available') : (language === 'ar' ? 'غير متاح' : 'Unavailable')}`}
              >


                {/* Header */}
                <div className={`flex items-start ${viewMode === 'grid' ? 'space-x-4 rtl:space-x-reverse mb-4' : 'space-x-6 rtl:space-x-reverse flex-1'}`}>
                  <div className={`${viewMode === 'grid' ? 'w-12 h-12' : 'w-16 h-16'} bg-gradient-to-br ${colors.color} rounded-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110 shadow-lg flex-shrink-0`}>
                    <Icon className={`${viewMode === 'grid' ? 'w-6 h-6' : 'w-8 h-8'} text-white`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className={`${viewMode === 'grid' ? 'text-lg' : 'text-xl'} font-bold text-white group-hover:text-secondary transition-colors duration-300 truncate`}>
                        {language === 'ar' ? (service.name?.ar || service.name_ar) : (service.name?.en || service.name_en)}
                      </h3>
                      {service.isPremiumAddon && (
                        <span className="ml-2 px-2 py-1 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-400 border border-yellow-500/30 rounded-full text-xs flex items-center space-x-1 rtl:space-x-reverse flex-shrink-0">
                          <Crown className="w-3 h-3" />
                          <span>{language === 'ar' ? 'مميز' : 'Premium'}</span>
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 rtl:space-x-reverse mb-2">
                      <div className="text-sm font-semibold text-accent">
                        ${service.price}
                        {service.subscriptionType !== 'none' && (
                          <span className="text-xs text-gray-400 ml-1">
                            /{service.subscriptionType === 'monthly' ? (language === 'ar' ? 'شهر' : 'mo') : (language === 'ar' ? 'سنة' : 'yr')}
                          </span>
                        )}
                      </div>
                      {service.subscriptionType !== 'none' && (
                        <span className="px-2 py-1 bg-green-500/20 text-green-400 border border-green-500/30 rounded-full text-xs flex items-center space-x-1 rtl:space-x-reverse">
                          <RefreshCw className="w-3 h-3" />
                          <span>
                            {service.subscriptionType === 'monthly'
                              ? (language === 'ar' ? 'شهري' : 'Monthly')
                              : (language === 'ar' ? 'سنوي' : 'Yearly')
                            }
                          </span>
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Image Section - Only in Grid View */}
                {viewMode === 'grid' && service.image_url && (
                  <div className="relative h-32 rounded-lg overflow-hidden mb-4">
                    <img
                      src={service.image_url}
                      alt={language === 'ar' ? service.name_ar : service.name_en}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-primary/60 to-transparent"></div>

                    {/* Media Indicators */}
                    <div className="absolute bottom-2 right-2 flex space-x-1 rtl:space-x-reverse">
                      {service.video_url && (
                        <div className="bg-red-500/90 backdrop-blur-sm rounded-full p-1">
                          <Play className="w-3 h-3 text-white" />
                        </div>
                      )}
                      <div className="bg-blue-500/90 backdrop-blur-sm rounded-full p-1">
                        <ImageIcon className="w-3 h-3 text-white" />
                      </div>
                      {service.gallery_images && service.gallery_images.length > 0 && (
                        <div className="bg-green-500/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
                          <ImageIcon className="w-3 h-3 text-white" />
                          <span className="text-white text-xs font-medium">{service.gallery_images.length}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Image Section - List View (smaller) */}
                {viewMode === 'list' && service.image_url && (
                  <div className="relative w-24 h-24 rounded-lg overflow-hidden flex-shrink-0">
                    <img
                      src={service.image_url}
                      alt={language === 'ar' ? (service.name?.ar || service.name_ar) : (service.name?.en || service.name_en)}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-primary/60 to-transparent"></div>

                    {/* Media Indicators */}
                    <div className="absolute bottom-1 right-1 flex space-x-1 rtl:space-x-reverse">
                      {service.video_url && (
                        <div className="bg-red-500/90 backdrop-blur-sm rounded-full p-0.5">
                          <Play className="w-2 h-2 text-white" />
                        </div>
                      )}
                      <div className="bg-blue-500/90 backdrop-blur-sm rounded-full p-0.5">
                        <ImageIcon className="w-2 h-2 text-white" />
                      </div>
                    </div>
                  </div>
                )}

                {/* Content Section */}
                <div className={`${viewMode === 'list' ? 'flex-1' : ''}`}>
                  {/* Description */}
                  <p className={`text-gray-300 leading-relaxed mb-4 ${
                    viewMode === 'grid' ? 'text-sm line-clamp-3' : 'text-base line-clamp-2'
                  }`}>
                    {language === 'ar' ? (service.description?.ar || service.description_ar) : (service.description?.en || service.description_en)}
                  </p>

                  {/* Features - Only show in Grid view or first 2 in List view */}
                  <div className={`space-y-2 mb-6 ${viewMode === 'list' ? 'mb-4' : ''} ${viewMode === 'grid' ? 'flex-1' : ''}`}>
                    {(() => {
                      const features = language === 'ar'
                        ? (service.features?.ar || service.features_ar || [])
                        : (service.features?.en || service.features_en || []);
                      return features.slice(0, viewMode === 'grid' ? 3 : 2).map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-2 rtl:space-x-reverse">
                          <CheckCircle className={`w-4 h-4 ${colors.accentColor} flex-shrink-0`} />
                          <span className={`${viewMode === 'grid' ? 'text-xs' : 'text-sm'} text-gray-400 line-clamp-1`}>{feature}</span>
                        </div>
                      ));
                    })()}
                    {(language === 'ar' ? service.features_ar : service.features_en)?.length > (viewMode === 'grid' ? 3 : 2) && (
                      <div className="text-xs text-gray-500">
                        +{((language === 'ar' ? service.features_ar : service.features_en)?.length || 0) - (viewMode === 'grid' ? 3 : 2)} {language === 'ar' ? 'مميزة أخرى' : 'more features'}
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className={`${viewMode === 'list' ? 'flex flex-col space-y-2 ml-4 rtl:ml-0 rtl:mr-4' : 'space-y-2 mt-auto'}`}>
                  {/* Details Button */}
                  <button
                    onClick={() => handleViewDetails(service)}
                    className={`${viewMode === 'list' ? 'px-4 py-2' : 'w-full py-3 px-4'} bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white border border-gray-600 hover:border-gray-500 rounded-lg transition-all duration-300 flex items-center justify-center space-x-2 rtl:space-x-reverse text-sm font-medium group/btn hover:shadow-lg`}
                  >
                    <Eye className="w-4 h-4" />
                    <span>
                      {language === 'ar' ? 'التفاصيل' : 'Details'}
                    </span>
                  </button>

                  {/* Request Service Button */}
                  <button
                    onClick={() => handleRequestService(service)}
                    className={`${viewMode === 'list' ? 'px-4 py-2' : 'w-full py-3 px-4'} ${colors.bgColor} hover:bg-secondary/20 ${colors.accentColor} hover:text-secondary border ${colors.borderColor} hover:border-secondary/50 rounded-lg transition-all duration-300 flex items-center justify-center space-x-2 rtl:space-x-reverse text-sm font-medium group/btn hover:shadow-lg`}
                  >
                    <span>
                      {language === 'ar' ? 'اطلب الخدمة' : 'Request Service'}
                    </span>
                    {language === 'ar' ? (
                      <ArrowLeft className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform duration-300" />
                    ) : (
                      <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform duration-300" />
                    )}
                  </button>
                </div>
              </div>
            );
          })}
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-secondary/10 to-accent/10 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-secondary/20 hover:border-secondary/40 transition-all duration-300 group">
            <Target className="w-16 h-16 text-secondary mx-auto mb-6 group-hover:scale-110 transition-transform duration-300" />
            <h3 className="text-3xl font-bold mb-4 text-white">
              {t('common.readyToStart')}
            </h3>
            <p className="text-xl mb-8 text-gray-300">
              {t('common.joinUs')}
            </p>
            <button 
              onClick={handleContactTeam}
              className="bg-gradient-to-r from-secondary to-accent text-primary hover:text-white font-bold px-8 py-4 rounded-full transition-all duration-300 hover:shadow-xl hover:shadow-secondary/30 flex items-center space-x-2 rtl:space-x-reverse mx-auto group/btn border-2 border-transparent hover:border-white/20"
            >
              <span className="group-hover/btn:scale-105 transition-transform duration-300">{t('contact.title')}</span>
              {language === 'ar' ? (
                <ArrowLeft className="w-5 h-5 group-hover/btn:translate-x-1 transition-transform duration-300" />
              ) : (
                <ArrowRight className="w-5 h-5 group-hover/btn:translate-x-1 transition-transform duration-300" />
              )}
            </button>
          </div>
        </div>

        {/* Service Details Modal */}
        {showDetailsModal && selectedService && (
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-primary rounded-2xl shadow-2xl border border-gray-700 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              {/* Modal Header */}
              <div className="sticky top-0 bg-gradient-to-r from-secondary/20 to-accent/20 p-6 border-b border-gray-700 flex items-center justify-between">
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <div className={`w-12 h-12 bg-gradient-to-br ${getCategoryColor(selectedService.category).color} rounded-xl flex items-center justify-center shadow-lg`}>
                    {React.createElement(getCategoryIcon(selectedService.category), { className: "w-6 h-6 text-white" })}
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">{language === 'ar' ? selectedService.name_ar : selectedService.name_en}</h2>
                    <p className="text-gray-400">{language === 'ar' ? 'تفاصيل الخدمة التقنية' : 'Technical Service Details'}</p>
                  </div>
                </div>
                <button
                  onClick={closeDetailsModal}
                  className="w-10 h-10 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center transition-colors"
                >
                  <X className="w-6 h-6 text-white" />
                </button>
              </div>

              {/* Modal Content */}
              <div className="p-6 space-y-6">
                {/* Service Info */}
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">{language === 'ar' ? 'معلومات الخدمة' : 'Service Information'}</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-400">{language === 'ar' ? 'السعر:' : 'Price:'}</span>
                        <span className="text-secondary font-bold">${selectedService.price}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{language === 'ar' ? 'الفئة:' : 'Category:'}</span>
                        <span className="text-white capitalize">{selectedService.category}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{language === 'ar' ? 'نوع الاشتراك:' : 'Subscription:'}</span>
                        <span className="text-white">
                          {selectedService.subscriptionType === 'monthly'
                            ? (language === 'ar' ? 'شهري' : 'Monthly')
                            : selectedService.subscriptionType === 'yearly'
                            ? (language === 'ar' ? 'سنوي' : 'Yearly')
                            : (language === 'ar' ? 'لمرة واحدة' : 'One-time')
                          }
                        </span>
                      </div>
                      {selectedService.isPremiumAddon && (
                        <div className="flex justify-between">
                          <span className="text-gray-400">{language === 'ar' ? 'النسخة المميزة:' : 'Premium Edition:'}</span>
                          <span className="text-yellow-400 flex items-center space-x-1 rtl:space-x-reverse">
                            <Crown className="w-4 h-4" />
                            <span>{language === 'ar' ? 'متاح' : 'Available'}</span>
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Service Image/Video */}
                  {(selectedService.image_url || selectedService.video_url) && (
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">{language === 'ar' ? 'الوسائط' : 'Media'}</h3>
                      <div className="relative rounded-lg overflow-hidden">
                        {selectedService.video_url ? (
                          <iframe
                            src={selectedService.video_url}
                            className="w-full h-48 rounded-lg"
                            frameBorder="0"
                            allowFullScreen
                          ></iframe>
                        ) : selectedService.image_url ? (
                          <img
                            src={selectedService.image_url}
                            alt={language === 'ar' ? selectedService.name_ar : selectedService.name_en}
                            className="w-full h-48 object-cover rounded-lg"
                          />
                        ) : null}
                      </div>
                    </div>
                  )}
                </div>

                {/* Description */}
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">{language === 'ar' ? 'الوصف' : 'Description'}</h3>
                  <p className="text-gray-300 leading-relaxed">{language === 'ar' ? selectedService.description_ar : selectedService.description_en}</p>
                </div>

                {/* Features */}
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">{language === 'ar' ? 'المميزات' : 'Features'}</h3>
                  <div className="grid md:grid-cols-2 gap-3">
                    {(language === 'ar' ? selectedService.features_ar : selectedService.features_en)?.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-3 rtl:space-x-reverse">
                        <CheckCircle className={`w-5 h-5 ${getCategoryColor(selectedService.category).accentColor} flex-shrink-0`} />
                        <span className="text-gray-300">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4 rtl:space-x-reverse pt-4 border-t border-gray-700">
                  <button
                    onClick={() => {
                      handleRequestService(selectedService);
                      closeDetailsModal();
                    }}
                    className="flex-1 bg-gradient-to-r from-secondary to-accent text-primary hover:text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 hover:shadow-xl flex items-center justify-center space-x-2 rtl:space-x-reverse"
                  >
                    <span>{language === 'ar' ? 'اطلب الخدمة' : 'Request Service'}</span>
                    {language === 'ar' ? (
                      <ArrowLeft className="w-5 h-5" />
                    ) : (
                      <ArrowRight className="w-5 h-5" />
                    )}
                  </button>
                  <button
                    onClick={closeDetailsModal}
                    className="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                  >
                    {language === 'ar' ? 'إغلاق' : 'Close'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default Services;