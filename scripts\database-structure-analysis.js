/**
 * Database Structure Analysis
 * 
 * This script analyzes the actual database structure and compares it with code expectations
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'khanfashariya_db'
};

async function analyzeDatabase() {
  console.log('🔍 ANALYZING DATABASE STRUCTURE');
  console.log('=' .repeat(60));
  
  let connection;
  
  try {
    connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ Connected to database\n');
    
    // Check which tables exist
    console.log('📋 EXISTING TABLES:');
    console.log('-'.repeat(30));
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME, TABLE_ROWS, DATA_LENGTH 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ?
      ORDER BY TABLE_NAME
    `, [DB_CONFIG.database]);
    
    tables.forEach(table => {
      console.log(`   ${table.TABLE_NAME} (${table.TABLE_ROWS} rows)`);
    });
    
    // Analyze system_services table
    console.log('\n🔧 SYSTEM_SERVICES TABLE ANALYSIS:');
    console.log('-'.repeat(40));
    
    try {
      const [systemColumns] = await connection.execute(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_KEY
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'system_services'
        ORDER BY ORDINAL_POSITION
      `, [DB_CONFIG.database]);
      
      if (systemColumns.length === 0) {
        console.log('❌ system_services table does not exist!');
      } else {
        console.log(`✅ system_services table exists with ${systemColumns.length} columns:`);
        systemColumns.forEach(col => {
          const nullable = col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL';
          const key = col.COLUMN_KEY ? ` (${col.COLUMN_KEY})` : '';
          console.log(`   ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${nullable}${key}`);
        });
        
        // Check sample data
        const [systemData] = await connection.execute('SELECT COUNT(*) as count FROM system_services');
        console.log(`   📊 Total records: ${systemData[0].count}`);
        
        if (systemData[0].count > 0) {
          const [sampleData] = await connection.execute('SELECT id, name_ar, name_en, status FROM system_services LIMIT 3');
          console.log('   📝 Sample data:');
          sampleData.forEach(row => {
            console.log(`      ${row.id}: ${row.name_en} (${row.status})`);
          });
        }
      }
    } catch (error) {
      console.log('❌ Error analyzing system_services:', error.message);
    }
    
    // Analyze technical_services table
    console.log('\n🔧 TECHNICAL_SERVICES TABLE ANALYSIS:');
    console.log('-'.repeat(40));
    
    try {
      const [serviceColumns] = await connection.execute(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_KEY
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'technical_services'
        ORDER BY ORDINAL_POSITION
      `, [DB_CONFIG.database]);
      
      if (serviceColumns.length === 0) {
        console.log('❌ technical_services table does not exist!');
      } else {
        console.log(`✅ technical_services table exists with ${serviceColumns.length} columns:`);
        serviceColumns.forEach(col => {
          const nullable = col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL';
          const key = col.COLUMN_KEY ? ` (${col.COLUMN_KEY})` : '';
          console.log(`   ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${nullable}${key}`);
        });
        
        // Check sample data
        const [serviceData] = await connection.execute('SELECT COUNT(*) as count FROM technical_services');
        console.log(`   📊 Total records: ${serviceData[0].count}`);
        
        if (serviceData[0].count > 0) {
          const [sampleData] = await connection.execute('SELECT id, name_ar, name_en, status FROM technical_services LIMIT 3');
          console.log('   📝 Sample data:');
          sampleData.forEach(row => {
            console.log(`      ${row.id}: ${row.name_en} (${row.status})`);
          });
        }
      }
    } catch (error) {
      console.log('❌ Error analyzing technical_services:', error.message);
    }
    
    // Analyze premium_content table
    console.log('\n🔧 PREMIUM_CONTENT TABLE ANALYSIS:');
    console.log('-'.repeat(40));
    
    try {
      const [premiumColumns] = await connection.execute(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_KEY
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'premium_content'
        ORDER BY ORDINAL_POSITION
      `, [DB_CONFIG.database]);
      
      if (premiumColumns.length === 0) {
        console.log('❌ premium_content table does not exist!');
      } else {
        console.log(`✅ premium_content table exists with ${premiumColumns.length} columns:`);
        premiumColumns.forEach(col => {
          const nullable = col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL';
          const key = col.COLUMN_KEY ? ` (${col.COLUMN_KEY})` : '';
          console.log(`   ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${nullable}${key}`);
        });
        
        // Check sample data
        const [premiumData] = await connection.execute('SELECT COUNT(*) as count FROM premium_content');
        console.log(`   📊 Total records: ${premiumData[0].count}`);
        
        if (premiumData[0].count > 0) {
          const [sampleData] = await connection.execute('SELECT id, title_ar, title_en, status FROM premium_content LIMIT 3');
          console.log('   📝 Sample data:');
          sampleData.forEach(row => {
            console.log(`      ${row.id}: ${row.title_en} (${row.status})`);
          });
        }
      }
    } catch (error) {
      console.log('❌ Error analyzing premium_content:', error.message);
    }
    
    console.log('\n✅ Database structure analysis completed successfully!');
    
  } catch (error) {
    console.error('💥 Database analysis failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

analyzeDatabase();