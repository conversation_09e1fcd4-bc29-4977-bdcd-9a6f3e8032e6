/**
 * Test User Dashboard Fix
 * Tests the fixed user dashboard functionality
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';
const TEST_USER_CREDS = { email: '<EMAIL>', password: '123456' };

async function testUserDashboardFix() {
  console.log('🔧 Testing User Dashboard Fix...');
  
  try {
    // Login first
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, TEST_USER_CREDS);
    
    if (!loginResponse.data.success) {
      console.log('❌ Login failed');
      return;
    }
    
    const token = loginResponse.data.data.tokens.accessToken;
    const user = loginResponse.data.data.user;
    const headers = { Authorization: `Bearer ${token}` };
    
    console.log(`✅ Login successful for user: ${user.email}`);
    
    // Test user profile endpoint
    console.log('\n📋 Testing User Profile...');
    const profileResponse = await axios.get(`${API_BASE}/users/profile`, { headers });
    
    if (profileResponse.data.success) {
      const userData = profileResponse.data.data.user;
      console.log(`✅ Profile loaded: ${userData.email}`);
      console.log(`📊 Stats: Orders: ${userData.stats.total_orders}, Messages: ${userData.stats.unread_messages}`);
    } else {
      console.log('❌ Profile loading failed');
    }
    
    // Test user orders endpoint
    console.log('\n📦 Testing User Orders...');
    const ordersResponse = await axios.get(`${API_BASE}/orders`, { headers });
    
    if (ordersResponse.data.success) {
      const orders = ordersResponse.data.data.orders || [];
      console.log(`✅ Orders loaded: ${orders.length} orders found`);
      
      if (orders.length > 0) {
        const latestOrder = orders[0];
        console.log(`📋 Latest order: ${latestOrder.order_number} - $${latestOrder.final_price} - ${latestOrder.status}`);
      }
    } else {
      console.log('❌ Orders loading failed');
    }
    
    // Test user subscriptions endpoint
    console.log('\n💎 Testing User Subscriptions...');
    const subscriptionsResponse = await axios.get(`${API_BASE}/users/${user.id}/subscriptions`, { headers });
    
    if (subscriptionsResponse.data.success) {
      const subscriptions = subscriptionsResponse.data.data.subscriptions || [];
      console.log(`✅ Subscriptions loaded: ${subscriptions.length} subscriptions found`);
    } else {
      console.log('❌ Subscriptions loading failed');
    }
    
    // Test user messages endpoint
    console.log('\n💬 Testing User Messages...');
    const messagesResponse = await axios.get(`${API_BASE}/users/${user.id}/messages`, { headers });
    
    if (messagesResponse.data.success) {
      const messages = messagesResponse.data.data.messages || [];
      console.log(`✅ Messages loaded: ${messages.length} messages found`);
      
      if (messages.length > 0) {
        const latestMessage = messages[0];
        console.log(`📧 Latest message: ${latestMessage.subject_ar || latestMessage.subject_en} - ${latestMessage.is_read ? 'Read' : 'Unread'}`);
      }
    } else {
      console.log('❌ Messages loading failed');
    }
    
    // Test systems browsing
    console.log('\n🖥️ Testing Systems Browsing...');
    const systemsResponse = await axios.get(`${API_BASE}/systems`);
    
    if (systemsResponse.data.success) {
      const systems = systemsResponse.data.data.systems || [];
      console.log(`✅ Systems loaded: ${systems.length} systems available`);
      
      if (systems.length > 0) {
        const firstSystem = systems[0];
        console.log(`🖥️ First system: ${firstSystem.name_en} - $${firstSystem.price}`);
      }
    } else {
      console.log('❌ Systems loading failed');
    }
    
    // Test services browsing
    console.log('\n🔧 Testing Services Browsing...');
    const servicesResponse = await axios.get(`${API_BASE}/services/technical`);
    
    if (servicesResponse.data.success) {
      const services = servicesResponse.data.data || [];
      console.log(`✅ Services loaded: ${services.length} services available`);
      
      if (services.length > 0) {
        const firstService = services[0];
        console.log(`🔧 First service: ${firstService.name_en} - $${firstService.price}`);
      }
    } else {
      console.log('❌ Services loading failed');
    }
    
    console.log('\n🎉 User dashboard testing completed!');
    console.log('✅ All endpoints are working correctly for user dashboard');
    
  } catch (error) {
    console.log('❌ Error during testing:', error.response?.data || error.message);
  }
}

testUserDashboardFix();
