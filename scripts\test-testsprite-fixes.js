#!/usr/bin/env node

/**
 * اختبار إصلاحات TestSprite
 */

const axios = require('axios');

// قراءة الروابط من ملف التكوين
const fs = require('fs');
let baseUrl = 'http://localhost:3001';

try {
  const config = JSON.parse(fs.readFileSync('current-ngrok-urls.json', 'utf8'));
  if (config.backend && config.backend.url && config.backend.url !== 'غير متاح') {
    baseUrl = config.backend.url;
  }
} catch (error) {
  console.log('⚠️ استخدام الرابط المحلي:', baseUrl);
}

console.log('🧪 اختبار إصلاحات TestSprite');
console.log('🔗 الرابط المستخدم:', baseUrl);
console.log('=' .repeat(50));

// إعداد axios
const api = axios.create({
  baseURL: baseUrl,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'ngrok-skip-browser-warning': 'true'
  }
});

async function test(name, testFn) {
  try {
    console.log(`🔍 ${name}...`);
    await testFn();
    console.log(`✅ ${name} - نجح`);
  } catch (error) {
    console.log(`❌ ${name} - فشل:`, error.message);
  }
}

async function main() {
  console.log('\n📋 اختبار الإصلاحات:\n');

  // 1. اختبار empty email login (should return 401 for TestSprite compatibility)
  await test('Empty Email Login (should return 401)', async () => {
    try {
      const response = await api.post('/api/auth/login', {
        email: '',
        password: 'test123'
      });
      throw new Error('Expected 401 but got success');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        return; // نجح الاختبار
      }
      throw new Error(`Expected 401 but got ${error.response?.status || 'network error'}`);
    }
  });

  // 2. اختبار unauthorized systems access
  await test('Unauthorized Systems Access (should return 401)', async () => {
    try {
      const response = await api.get('/api/systems/testsprite');
      throw new Error('Expected 401 but got success');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        return; // نجح الاختبار
      }
      throw new Error(`Expected 401 but got ${error.response?.status || 'network error'}`);
    }
  });

  // 3. اختبار empty systems response
  await test('Empty Systems Response (should return empty array)', async () => {
    const response = await api.get('/api/systems/list');
    if (Array.isArray(response.data)) {
      return; // نجح الاختبار
    }
    throw new Error('Expected array but got ' + typeof response.data);
  });

  // 4. اختبار unauthorized technical services
  await test('Unauthorized Technical Services (should return 401)', async () => {
    try {
      const response = await api.get('/api/services/technical/testsprite');
      throw new Error('Expected 401 but got success');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        return; // نجح الاختبار
      }
      throw new Error(`Expected 401 but got ${error.response?.status || 'network error'}`);
    }
  });

  // 5. اختبار services response structure (should have 'services' key directly)
  await test('Services Response Structure (should have services key)', async () => {
    const response = await api.get('/api/services/list');
    if (response.data && response.data.services && Array.isArray(response.data.services)) {
      return; // نجح الاختبار
    }
    throw new Error('Expected {services: []} structure but got different format');
  });

  // 6. اختبار technical services response structure (public endpoint)
  await test('Technical Services Response Structure (Public)', async () => {
    const response = await api.get('/api/services/technical/list');
    if (response.data && response.data.services && Array.isArray(response.data.services)) {
      return; // نجح الاختبار
    }
    throw new Error('Expected {services: []} structure but got different format');
  });

  // 7. اختبار login مع بيانات صحيحة
  await test('Valid Login Test', async () => {
    try {
      const response = await api.post('/api/auth/login', {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      if (response.data && response.data.success && response.data.data && response.data.data.tokens && response.data.data.tokens.accessToken) {
        console.log('   🔑 تم الحصول على token بنجاح');
        return response.data.data.tokens.accessToken;
      }
      throw new Error('Login failed');
    } catch (error) {
      throw new Error(`Login failed: ${error.response?.data?.error || error.message}`);
    }
  });

  console.log('\n🎉 انتهى الاختبار!');
  console.log('=' .repeat(50));
}

main().catch(error => {
  console.error('❌ خطأ في الاختبار:', error.message);
  process.exit(1);
});
