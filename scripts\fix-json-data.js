/**
 * Fix JSON Data in Database
 * 
 * This script fixes corrupted JSON data in the database
 */

const { executeQuery } = require('../server/config/database');
require('dotenv').config();

// Safe JSON parsing utility
function safeJsonParse(jsonString, defaultValue = null) {
  if (!jsonString || typeof jsonString !== 'string') {
    return defaultValue;
  }
  
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    return defaultValue;
  }
}

// Safe JSON stringify
function safeJsonStringify(data, defaultValue = '[]') {
  try {
    if (data === null || data === undefined) {
      return defaultValue;
    }
    if (typeof data === 'string') {
      // Try to parse first to validate
      JSON.parse(data);
      return data;
    }
    return JSON.stringify(data);
  } catch (error) {
    return defaultValue;
  }
}

async function fixJsonData() {
  console.log('🔧 Fixing JSON data in database...\n');
  
  try {
    // Fix system_services table
    console.log('1️⃣ Fixing system_services JSON fields...');
    const { rows: systems } = await executeQuery(`
      SELECT id, features_ar, features_en, tech_specs_ar, tech_specs_en, gallery_images
      FROM system_services
    `);
    
    let systemsFixed = 0;
    for (const system of systems) {
      let needsUpdate = false;
      const updates = {};
      
      // Check and fix each JSON field
      ['features_ar', 'features_en', 'tech_specs_ar', 'tech_specs_en', 'gallery_images'].forEach(field => {
        const originalValue = system[field];
        const parsedValue = safeJsonParse(originalValue, []);
        const fixedValue = safeJsonStringify(parsedValue, '[]');
        
        if (originalValue !== fixedValue) {
          updates[field] = fixedValue;
          needsUpdate = true;
        }
      });
      
      if (needsUpdate) {
        const updateFields = Object.keys(updates).map(field => `${field} = ?`).join(', ');
        const updateValues = Object.values(updates);
        
        await executeQuery(`
          UPDATE system_services 
          SET ${updateFields}, updated_at = NOW()
          WHERE id = ?
        `, [...updateValues, system.id]);
        
        systemsFixed++;
        console.log(`   ✅ Fixed system ${system.id}`);
      }
    });
    
    console.log(`   📊 Fixed ${systemsFixed} systems out of ${systems.length}`);
    
    // Fix technical_services table
    console.log('\n2️⃣ Fixing technical_services JSON fields...');
    const { rows: services } = await executeQuery(`
      SELECT id, features_ar, features_en, tech_specs_ar, tech_specs_en, gallery_images
      FROM technical_services
    `);
    
    let servicesFixed = 0;
    for (const service of services) {
      let needsUpdate = false;
      const updates = {};
      
      // Check and fix each JSON field
      ['features_ar', 'features_en', 'tech_specs_ar', 'tech_specs_en', 'gallery_images'].forEach(field => {
        const originalValue = service[field];
        const parsedValue = safeJsonParse(originalValue, []);
        const fixedValue = safeJsonStringify(parsedValue, '[]');
        
        if (originalValue !== fixedValue) {
          updates[field] = fixedValue;
          needsUpdate = true;
        }
      });
      
      if (needsUpdate) {
        const updateFields = Object.keys(updates).map(field => `${field} = ?`).join(', ');
        const updateValues = Object.values(updates);
        
        await executeQuery(`
          UPDATE technical_services 
          SET ${updateFields}, updated_at = NOW()
          WHERE id = ?
        `, [...updateValues, service.id]);
        
        servicesFixed++;
        console.log(`   ✅ Fixed service ${service.id}`);
      }
    });
    
    console.log(`   📊 Fixed ${servicesFixed} services out of ${services.length}`);
    
    // Check for any remaining issues
    console.log('\n3️⃣ Checking for remaining JSON issues...');
    
    // Test parsing all JSON fields
    const { rows: testSystems } = await executeQuery(`
      SELECT id, features_ar, features_en, tech_specs_ar, tech_specs_en, gallery_images
      FROM system_services
      LIMIT 5
    `);
    
    let hasIssues = false;
    testSystems.forEach(system => {
      ['features_ar', 'features_en', 'tech_specs_ar', 'tech_specs_en', 'gallery_images'].forEach(field => {
        if (system[field] && safeJsonParse(system[field]) === null) {
          console.log(`   ⚠️  Still has issues: system ${system.id}, field ${field}`);
          hasIssues = true;
        }
      });
    });
    
    if (!hasIssues) {
      console.log('   ✅ No remaining JSON issues found');
    }
    
    console.log('\n✅ JSON data fix completed!');
    
  } catch (error) {
    console.error('❌ Error fixing JSON data:', error.message);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  fixJsonData()
    .then(() => {
      console.log('\n🎉 JSON data fix completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 JSON data fix failed:', error.message);
      process.exit(1);
    });
}

module.exports = { fixJsonData };