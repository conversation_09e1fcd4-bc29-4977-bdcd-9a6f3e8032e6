{"timestamp": "2025-07-22T01:28:04.455Z", "checks": [{"port": 3001, "name": "Backend Server", "status": "in_use", "message": "Port 3001 is in use (Backend Server likely running)"}, {"port": 5173, "name": "Frontend Server", "status": "available", "message": "Port 5173 is available"}, {"port": 4040, "name": "Ngrok Web Interface", "status": "available", "message": "Port 4040 is available"}, {"url": "http://localhost:3001/health", "name": "Backend Health", "status": "success", "statusCode": 200, "message": "Backend Health responded with 200", "responseTime": 31}, {"url": "http://localhost:5173", "name": "Frontend", "status": "success", "statusCode": 200, "message": "Frontend responded with 200", "responseTime": 19}, {"url": "http://127.0.0.1:4040", "name": "<PERSON>rok Interface", "status": "error", "message": "Ngrok Interface error: connect ECONNREFUSED 127.0.0.1:4040", "responseTime": 6}, {"status": "error", "message": "Ngrok API not accessible: connect ECONNREFUSED 127.0.0.1:4040"}, {"status": "success", "memory": {"total": 24515, "used": 11410, "free": 13105, "usagePercent": 47}, "message": "Memory usage: 47% (11410MB/24515MB)"}], "overall": "critical", "recommendations": ["Start the frontend server: npm run dev", "Some endpoints are not responding. Check server logs and restart services if needed."]}