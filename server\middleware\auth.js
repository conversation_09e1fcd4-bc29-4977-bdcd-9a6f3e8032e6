/**
 * Authentication Middleware
 * 
 * Provides JWT-based authentication with:
 * - Access token verification
 * - Refresh token management
 * - Role-based access control
 * - Session management
 * - Security logging
 */

const jwt = require('jsonwebtoken');
const { executeQuery } = require('../config/database');

/**
 * Verify JWT access token
 */
async function verifyToken(req, res, next) {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Access token required',
        code: 'TOKEN_MISSING'
      });
    }
    
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Verify the token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Check if token is blacklisted
    try {
      const { rows: blacklistedTokens } = await executeQuery(
        'SELECT id FROM token_blacklist WHERE token_jti = ? AND expires_at > NOW()',
        [decoded.jti || token.substring(0, 50)]
      );

      if (blacklistedTokens.length > 0) {
        return res.status(401).json({
          success: false,
          error: 'Token has been revoked',
          code: 'TOKEN_REVOKED'
        });
      }
    } catch (error) {
      // If blacklist table doesn't exist, continue (backward compatibility)
      console.warn('Token blacklist check failed:', error.message);
    }

    // Check if user still exists and is active
    const { rows } = await executeQuery(
      'SELECT id, email, username, full_name, role, status FROM users WHERE id = ? AND status = "active"',
      [decoded.userId]
    );
    
    if (rows.length === 0) {
      return res.status(401).json({
        success: false,
        error: 'User not found or inactive',
        code: 'USER_NOT_FOUND'
      });
    }
    
    const user = rows[0];
    
    // Add user info to request
    req.user = {
      id: user.id,
      email: user.email,
      username: user.username,
      full_name: user.full_name,
      role: user.role,
      status: user.status
    };
    
    // Add token info
    req.token = {
      iat: decoded.iat,
      exp: decoded.exp,
      userId: decoded.userId
    };
    
    next();
    
  } catch (error) {
    console.error('❌ Token verification failed:', error.message);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'Access token expired',
        code: 'TOKEN_EXPIRED'
      });
    }
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: 'Invalid access token',
        code: 'TOKEN_INVALID'
      });
    }
    
    return res.status(500).json({
      success: false,
      error: 'Authentication error',
      code: 'AUTH_ERROR'
    });
  }
}

/**
 * Verify refresh token
 */
async function verifyRefreshToken(req, res, next) {
  try {
    const { refreshToken } = req.body;
    
    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        error: 'Refresh token required',
        code: 'REFRESH_TOKEN_MISSING'
      });
    }
    
    // Verify the refresh token
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
    
    // Check if refresh token exists in database and is active
    const { rows } = await executeQuery(
      `SELECT us.*, u.email, u.username, u.full_name, u.role, u.status 
       FROM user_sessions us
       JOIN users u ON us.user_id = u.id
       WHERE us.refresh_token = ? AND us.is_active = true AND us.expires_at > NOW() AND u.status = "active"`,
      [refreshToken]
    );
    
    if (rows.length === 0) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired refresh token',
        code: 'REFRESH_TOKEN_INVALID'
      });
    }
    
    const session = rows[0];
    
    // Add user and session info to request
    req.user = {
      id: session.user_id,
      email: session.email,
      username: session.username,
      full_name: session.full_name,
      role: session.role,
      status: session.status
    };
    
    req.session = {
      id: session.id,
      refreshToken: session.refresh_token,
      expiresAt: session.expires_at
    };
    
    next();
    
  } catch (error) {
    console.error('❌ Refresh token verification failed:', error.message);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'Refresh token expired',
        code: 'REFRESH_TOKEN_EXPIRED'
      });
    }
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: 'Invalid refresh token',
        code: 'REFRESH_TOKEN_INVALID'
      });
    }
    
    return res.status(500).json({
      success: false,
      error: 'Refresh token verification error',
      code: 'REFRESH_AUTH_ERROR'
    });
  }
}

/**
 * Require admin role
 */
function requireAdmin(req, res, next) {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }
  
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required',
      code: 'ADMIN_REQUIRED'
    });
  }
  
  next();
}

/**
 * Require user to be the owner of the resource or admin
 */
function requireOwnerOrAdmin(userIdField = 'userId') {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }
    
    const resourceUserId = req.params[userIdField] || req.body[userIdField];
    
    if (req.user.role === 'admin' || req.user.id === resourceUserId) {
      next();
    } else {
      return res.status(403).json({
        success: false,
        error: 'Access denied: You can only access your own resources',
        code: 'ACCESS_DENIED'
      });
    }
  };
}

/**
 * Optional authentication (doesn't fail if no token)
 */
async function optionalAuth(req, res, next) {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // No token provided, continue without user info
      req.user = null;
      return next();
    }
    
    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get user info
    const { rows } = await executeQuery(
      'SELECT id, email, username, full_name, role, status FROM users WHERE id = ? AND status = "active"',
      [decoded.userId]
    );
    
    if (rows.length > 0) {
      req.user = rows[0];
    } else {
      req.user = null;
    }
    
    next();
    
  } catch (error) {
    // Token verification failed, but continue without user info
    req.user = null;
    next();
  }
}

/**
 * Generate JWT access token
 */
function generateAccessToken(userId) {
  const jti = require('crypto').randomBytes(16).toString('hex');
  return jwt.sign(
    {
      userId,
      jti,
      sub: userId
    },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '15m' } // Shorter expiry for better security
  );
}

/**
 * Generate JWT refresh token
 */
function generateRefreshToken(userId) {
  return jwt.sign(
    { userId },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' }
  );
}

/**
 * Create user session
 */
async function createUserSession(userId, refreshToken, deviceInfo = {}) {
  try {
    const sessionId = require('../config/database').generateUUID();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days from now
    
    await executeQuery(
      `INSERT INTO user_sessions (id, user_id, refresh_token, device_info, ip_address, user_agent, expires_at)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        sessionId,
        userId,
        refreshToken,
        JSON.stringify(deviceInfo),
        deviceInfo.ip_address || null,
        deviceInfo.user_agent || null,
        expiresAt
      ]
    );
    
    return sessionId;
    
  } catch (error) {
    console.error('❌ Failed to create user session:', error.message);
    throw error;
  }
}

/**
 * Invalidate user session and blacklist access token
 */
async function invalidateUserSession(refreshToken, accessToken = null) {
  try {
    // Invalidate the session
    await executeQuery(
      'UPDATE user_sessions SET is_active = false WHERE refresh_token = ?',
      [refreshToken]
    );

    // If access token provided, add it to blacklist
    if (accessToken) {
      try {
        const decoded = jwt.verify(accessToken, process.env.JWT_SECRET);
        await executeQuery(
          'INSERT INTO token_blacklist (token_jti, user_id, expires_at, created_at) VALUES (?, ?, FROM_UNIXTIME(?), NOW())',
          [decoded.jti || accessToken.substring(0, 50), decoded.sub, decoded.exp]
        );
      } catch (error) {
        // Token might be invalid, but continue with session invalidation
        console.warn('Could not blacklist token:', error.message);
      }
    }

    return true;

  } catch (error) {
    console.error('❌ Failed to invalidate user session:', error.message);
    throw error;
  }
}

/**
 * Invalidate all user sessions
 */
async function invalidateAllUserSessions(userId) {
  try {
    await executeQuery(
      'UPDATE user_sessions SET is_active = false WHERE user_id = ?',
      [userId]
    );
    
    return true;
    
  } catch (error) {
    console.error('❌ Failed to invalidate all user sessions:', error.message);
    throw error;
  }
}

/**
 * Clean up expired sessions
 */
async function cleanupExpiredSessions() {
  try {
    const { rows } = await executeQuery(
      'DELETE FROM user_sessions WHERE expires_at < NOW() OR is_active = false'
    );
    
    console.log(`🧹 Cleaned up expired sessions: ${rows.affectedRows || 0} sessions removed`);
    return rows.affectedRows || 0;
    
  } catch (error) {
    console.error('❌ Failed to cleanup expired sessions:', error.message);
    throw error;
  }
}

module.exports = {
  verifyToken,
  verifyRefreshToken,
  requireAdmin,
  requireOwnerOrAdmin,
  optionalAuth,
  generateAccessToken,
  generateRefreshToken,
  createUserSession,
  invalidateUserSession,
  invalidateAllUserSessions,
  cleanupExpiredSessions
};
