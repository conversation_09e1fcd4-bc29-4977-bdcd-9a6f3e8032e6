/**
 * Error Monitoring Script
 * 
 * This script helps monitor and log errors during testing
 */

const fs = require('fs');
const path = require('path');

class ErrorMonitor {
  constructor() {
    this.errorLog = [];
    this.logFile = path.join(__dirname, '..', 'error-log.txt');
    this.startTime = new Date();
    
    console.log('🔍 Error Monitor Started');
    console.log('📝 Logging to:', this.logFile);
    console.log('⏰ Start Time:', this.startTime.toLocaleString());
    
    this.initializeLog();
  }

  initializeLog() {
    const header = `
=== خانفشارية - سجل الأخطاء ===
تاريخ البدء: ${this.startTime.toLocaleString()}
الخادم الخلفي: http://localhost:3001
الخادم الأمامي: http://localhost:5173

=== تسجيل الأحداث ===
`;
    
    fs.writeFileSync(this.logFile, header);
  }

  logError(component, location, description, errorMessage, steps = []) {
    const timestamp = new Date().toLocaleString();
    const error = {
      timestamp,
      component,
      location,
      description,
      errorMessage,
      steps
    };
    
    this.errorLog.push(error);
    
    const logEntry = `
[${timestamp}] 🐛 خطأ في ${component}
📍 المكان: ${location}
🔍 الوصف: ${description}
💻 رسالة الخطأ: ${errorMessage}
🔄 خطوات إعادة الإنتاج: ${steps.join(' -> ')}
---
`;
    
    fs.appendFileSync(this.logFile, logEntry);
    
    console.log(`🚨 خطأ جديد مسجل في ${component}: ${description}`);
  }

  logSuccess(component, action) {
    const timestamp = new Date().toLocaleString();
    const logEntry = `[${timestamp}] ✅ نجح في ${component}: ${action}\n`;
    
    fs.appendFileSync(this.logFile, logEntry);
    console.log(`✅ ${component}: ${action}`);
  }

  logInfo(message) {
    const timestamp = new Date().toLocaleString();
    const logEntry = `[${timestamp}] ℹ️  ${message}\n`;
    
    fs.appendFileSync(this.logFile, logEntry);
    console.log(`ℹ️  ${message}`);
  }

  generateReport() {
    const endTime = new Date();
    const duration = Math.round((endTime - this.startTime) / 1000);
    
    const report = `
=== تقرير نهائي ===
وقت الانتهاء: ${endTime.toLocaleString()}
مدة التجربة: ${duration} ثانية
عدد الأخطاء: ${this.errorLog.length}

=== ملخص الأخطاء ===
${this.errorLog.map((error, index) => `
${index + 1}. ${error.component} - ${error.description}
   الوقت: ${error.timestamp}
   المكان: ${error.location}
   الخطأ: ${error.errorMessage}
`).join('')}

=== التوصيات ===
${this.errorLog.length === 0 ? 
  '🎉 لم يتم العثور على أخطاء! النظام يعمل بشكل ممتاز.' :
  `⚠️  تم العثور على ${this.errorLog.length} خطأ. يرجى مراجعة التفاصيل أعلاه.`
}
`;
    
    fs.appendFileSync(this.logFile, report);
    console.log('\n📋 تم إنشاء التقرير النهائي');
    console.log('📁 ملف السجل:', this.logFile);
    
    return {
      totalErrors: this.errorLog.length,
      duration,
      errors: this.errorLog
    };
  }
}

// إنشاء مثيل من مراقب الأخطاء
const monitor = new ErrorMonitor();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
  module.exports = monitor;
}

// إذا تم تشغيل الملف مباشرة
if (require.main === module) {
  console.log('🔍 مراقب الأخطاء جاهز للاستخدام');
  console.log('📝 استخدم الدوال التالية:');
  console.log('   monitor.logError(component, location, description, errorMessage, steps)');
  console.log('   monitor.logSuccess(component, action)');
  console.log('   monitor.logInfo(message)');
  console.log('   monitor.generateReport()');
  
  // مثال على الاستخدام
  monitor.logInfo('مراقب الأخطاء بدأ العمل');
  
  // الاستماع لإشارة الإنهاء
  process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف مراقب الأخطاء...');
    monitor.generateReport();
    process.exit(0);
  });
  
  // إبقاء البرنامج يعمل
  setInterval(() => {
    // فحص دوري كل 30 ثانية
  }, 30000);
}