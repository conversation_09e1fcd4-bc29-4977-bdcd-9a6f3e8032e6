const axios = require('axios');

async function testSystemUpdateFix() {
  console.log('🔧 اختبار إصلاح تحديث الأنظمة التقنية\n');
  
  let token;
  let testSystemId;
  
  try {
    // 1. تسجيل الدخول كإداري
    console.log('1️⃣ تسجيل الدخول كإداري...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    token = loginResponse.data.data.tokens.accessToken;
    const headers = { Authorization: `Bearer ${token}` };
    console.log('   ✅ تم تسجيل الدخول بنجاح');
    
    // 2. إنشاء نظام تجريبي للاختبار
    console.log('\n2️⃣ إنشاء نظام تجريبي...');
    const createData = {
      name_ar: 'نظام اختبار التحديث',
      name_en: 'Update Test System',
      description_ar: 'نظام لاختبار وظيفة التحديث',
      description_en: 'System for testing update functionality',
      price: 50.00,
      category: 'test',
      type: 'regular',
      is_premium_addon: false,
      features_ar: ['ميزة أولية'],
      features_en: ['Initial feature'],
      tech_specs_ar: ['مواصفة أولية'],
      tech_specs_en: ['Initial spec'],
      video_url: '',
      image_url: '',
      gallery_images: [],
      status: 'active'
    };
    
    const createResponse = await axios.post('http://localhost:3001/api/admin/systems', createData, { headers });
    testSystemId = createResponse.data.data.id;
    console.log('   ✅ تم إنشاء النظام التجريبي - ID:', testSystemId);
    console.log('   📋 البيانات الأولية:', {
      name_ar: createResponse.data.data.name_ar,
      price: createResponse.data.data.price,
      features_ar: createResponse.data.data.features_ar
    });
    
    // 3. اختبار تحديث النظام
    console.log('\n3️⃣ اختبار تحديث النظام...');
    const updateData = {
      name_ar: 'نظام اختبار التحديث - محدث',
      name_en: 'Update Test System - Updated',
      description_ar: 'نظام محدث لاختبار وظيفة التحديث',
      description_en: 'Updated system for testing update functionality',
      price: 75.00,
      category: 'test',
      type: 'regular',
      is_premium_addon: true,
      features_ar: ['ميزة أولية', 'ميزة جديدة'],
      features_en: ['Initial feature', 'New feature'],
      tech_specs_ar: ['مواصفة أولية', 'مواصفة جديدة'],
      tech_specs_en: ['Initial spec', 'New spec'],
      video_url: 'https://example.com/video.mp4',
      image_url: 'https://example.com/image.jpg',
      gallery_images: ['https://example.com/gallery1.jpg'],
      status: 'active'
    };
    
    const updateResponse = await axios.put(`http://localhost:3001/api/admin/systems/${testSystemId}`, updateData, { headers });
    
    if (updateResponse.status === 200) {
      console.log('   ✅ تم تحديث النظام بنجاح');
      console.log('   📋 استجابة التحديث:', {
        success: updateResponse.data.success,
        message: updateResponse.data.message,
        hasData: !!updateResponse.data.data
      });
      
      if (updateResponse.data.data) {
        console.log('   📄 البيانات المحدثة:', {
          name_ar: updateResponse.data.data.name_ar,
          price: updateResponse.data.data.price,
          features_ar: updateResponse.data.data.features_ar,
          is_premium_addon: updateResponse.data.data.is_premium_addon
        });
      }
    } else {
      console.log('   ❌ فشل في تحديث النظام - Status:', updateResponse.status);
    }
    
    // 4. التحقق من التحديث في قاعدة البيانات
    console.log('\n4️⃣ التحقق من التحديث في قاعدة البيانات...');
    const verifyResponse = await axios.get(`http://localhost:3001/api/systems/${testSystemId}`);

    if (verifyResponse.status === 200) {
      const systemData = verifyResponse.data.data.system; // البيانات داخل data.system
      console.log('   ✅ تم التحقق من البيانات في قاعدة البيانات');
      console.log('   📊 البيانات المحفوظة:', {
        name_ar: systemData.name_ar,
        price: systemData.price,
        features_ar: systemData.features_ar,
        is_premium_addon: systemData.is_premium_addon,
        video_url: systemData.video_url
      });

      // التحقق من صحة التحديث
      const isUpdated =
        systemData.name_ar === updateData.name_ar &&
        parseFloat(systemData.price) === updateData.price &&
        systemData.video_url === updateData.video_url &&
        Boolean(systemData.is_premium_addon) === updateData.is_premium_addon;

      console.log('   🔍 التحديث صحيح:', isUpdated ? '✅ نعم' : '❌ لا');
    }
    
    // 5. اختبار تحديث جزئي
    console.log('\n5️⃣ اختبار تحديث جزئي...');
    const partialUpdateData = {
      price: 100.00,
      status: 'inactive'
    };
    
    const partialUpdateResponse = await axios.put(`http://localhost:3001/api/admin/systems/${testSystemId}`, partialUpdateData, { headers });
    
    if (partialUpdateResponse.status === 200) {
      console.log('   ✅ تم التحديث الجزئي بنجاح');
      
      if (partialUpdateResponse.data.data) {
        console.log('   📄 البيانات بعد التحديث الجزئي:', {
          price: partialUpdateResponse.data.data.price,
          status: partialUpdateResponse.data.data.status,
          name_ar: partialUpdateResponse.data.data.name_ar // يجب أن يبقى كما هو
        });
      }
    }
    
    // 6. تنظيف البيانات التجريبية
    console.log('\n6️⃣ تنظيف البيانات التجريبية...');
    await axios.delete(`http://localhost:3001/api/admin/systems/${testSystemId}`, { headers });
    console.log('   ✅ تم حذف النظام التجريبي');
    
    console.log('\n🎉 اكتمل اختبار إصلاح تحديث الأنظمة بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.response?.data || error.message);
    
    // تنظيف في حالة الخطأ
    if (testSystemId && token) {
      try {
        const headers = { Authorization: `Bearer ${token}` };
        await axios.delete(`http://localhost:3001/api/admin/systems/${testSystemId}`, { headers });
        console.log('🧹 تم تنظيف البيانات التجريبية بعد الخطأ');
      } catch (cleanupError) {
        console.log('⚠️ فشل في تنظيف البيانات التجريبية');
      }
    }
  }
}

testSystemUpdateFix();
