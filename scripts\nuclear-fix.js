/**
 * Nuclear Fix - Complete System Reset
 * 
 * This script completely resets everything and fixes the infinite loop
 */

const { exec, spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('💥 NUCLEAR FIX - COMPLETE SYSTEM RESET');
console.log('=' .repeat(60));
console.log('This will completely stop all processes and fix everything\n');

async function nuclearFix() {
  try {
    // Step 1: Kill ALL Node processes aggressively
    console.log('1️⃣ KILLING ALL NODE PROCESSES...');
    const killCommands = [
      'taskkill /f /im node.exe',
      'taskkill /f /im nodemon.exe',
      'wmic process where "name=\'node.exe\'" delete',
      'wmic process where "name=\'nodemon.exe\'" delete'
    ];
    
    for (const cmd of killCommands) {
      try {
        await new Promise((resolve) => {
          exec(cmd, () => resolve());
        });
      } catch (e) {
        // Ignore errors
      }
    }
    console.log('✅ All Node processes terminated');
    
    // Step 2: Wait for complete cleanup
    console.log('\n2️⃣ WAITING FOR COMPLETE CLEANUP...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Step 3: Create emergency rate limit override
    console.log('\n3️⃣ CREATING EMERGENCY RATE LIMIT OVERRIDE...');
    const serverPath = path.join(__dirname, '..', 'server', 'server.js');
    let serverContent = fs.readFileSync(serverPath, 'utf8');
    
    // Force rate limit to be very high
    serverContent = serverContent.replace(
      /max: parseInt\(process\.env\.RATE_LIMIT_MAX_REQUESTS\) \|\| \d+/g,
      'max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 10000'
    );
    
    fs.writeFileSync(serverPath, serverContent);
    console.log('✅ Emergency rate limit override applied (10,000 requests)');
    
    // Step 4: Create emergency environment override
    console.log('\n4️⃣ SETTING EMERGENCY ENVIRONMENT...');
    const envPath = path.join(__dirname, '..', '.env');
    let envContent = '';
    
    try {
      envContent = fs.readFileSync(envPath, 'utf8');
    } catch (e) {
      // .env doesn't exist, create it
    }
    
    // Add or update rate limit settings
    const envLines = envContent.split('\n');
    const newEnvLines = envLines.filter(line => 
      !line.startsWith('RATE_LIMIT_MAX_REQUESTS=') && 
      !line.startsWith('RATE_LIMIT_WINDOW_MS=')
    );
    
    newEnvLines.push('RATE_LIMIT_MAX_REQUESTS=10000');
    newEnvLines.push('RATE_LIMIT_WINDOW_MS=900000');
    
    fs.writeFileSync(envPath, newEnvLines.join('\n'));
    console.log('✅ Emergency environment variables set');
    
    // Step 5: Clear all possible caches
    console.log('\n5️⃣ CLEARING ALL CACHES...');
    const cacheDirs = [
      path.join(__dirname, '..', 'node_modules', '.cache'),
      path.join(__dirname, '..', '.next'),
      path.join(__dirname, '..', 'dist'),
      path.join(__dirname, '..', 'build')
    ];
    
    for (const dir of cacheDirs) {
      try {
        if (fs.existsSync(dir)) {
          fs.rmSync(dir, { recursive: true, force: true });
          console.log(`   Cleared: ${dir}`);
        }
      } catch (e) {
        // Ignore errors
      }
    }
    console.log('✅ All caches cleared');
    
    // Step 6: Start server with maximum verbosity
    console.log('\n6️⃣ STARTING SERVER WITH EMERGENCY SETTINGS...');
    console.log('   Rate Limit: 10,000 requests per 15 minutes');
    console.log('   This should completely eliminate 429 errors\n');
    
    const serverProcess = spawn('npm', ['run', 'dev:server'], {
      cwd: path.resolve(__dirname, '..'),
      stdio: 'inherit',
      shell: true
    });
    
    // Monitor for successful start
    let startupTimer = setTimeout(() => {
      console.log('\n✅ SERVER SHOULD BE RUNNING NOW!');
      console.log('\n🚨 EMERGENCY INSTRUCTIONS:');
      console.log('1. CLOSE ALL BROWSER TABS of your website');
      console.log('2. CLEAR BROWSER DATA completely (Ctrl+Shift+Delete)');
      console.log('3. RESTART YOUR BROWSER completely');
      console.log('4. Open ONE TAB only to your website');
      console.log('5. The infinite refresh should be GONE');
      
      console.log('\n🔧 If still having issues:');
      console.log('• Try different browser (Chrome/Firefox/Edge)');
      console.log('• Try incognito/private mode');
      console.log('• Check if server is actually running');
      console.log('• Look for any error messages');
      
      console.log('\n💡 Rate limit is now 10,000 requests - this should fix everything!');
    }, 10000);
    
    serverProcess.on('close', (code) => {
      clearTimeout(startupTimer);
      console.log(`\n⚠️  Server process exited with code ${code}`);
    });
    
    // Keep process alive
    process.on('SIGINT', () => {
      clearTimeout(startupTimer);
      console.log('\n🛑 Nuclear fix interrupted');
      console.log('💡 Server should still be running');
      console.log('🌐 Try accessing website now');
      process.exit(0);
    });
    
  } catch (error) {
    console.error('\n💥 NUCLEAR FIX FAILED:', error.message);
    console.log('\n🆘 LAST RESORT MANUAL STEPS:');
    console.log('1. Close ALL terminals and applications');
    console.log('2. Restart your computer');
    console.log('3. Open fresh terminal');
    console.log('4. Run: npm run dev:server');
    console.log('5. Clear browser completely');
    console.log('6. Try accessing website');
  }
}

console.log('⚠️  WARNING: This will kill all Node.js processes!');
console.log('⏳ Starting nuclear fix in 3 seconds...\n');

setTimeout(() => {
  nuclearFix();
}, 3000);