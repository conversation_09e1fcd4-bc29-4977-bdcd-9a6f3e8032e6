#!/usr/bin/env node

/**
 * Fix table structure issues
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixTableIssues() {
  let connection;
  
  try {
    console.log('🔧 Fixing table structure issues...');
    
    // Connect to database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '123456',
      database: 'khanfashariya_db',
      charset: 'utf8mb4'
    });
    
    console.log('✅ Connected to database');
    
    // Fix users table - add password column if missing
    try {
      await connection.execute(`
        ALTER TABLE users 
        ADD COLUMN password VARCHAR(255) NOT NULL DEFAULT ''
      `);
      console.log('✅ Added password column to users table');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('⚠️  Password column already exists');
      } else {
        console.log('❌ Error adding password column:', error.message);
      }
    }
    
    // Fix orders table - add order_number column with auto-increment
    try {
      await connection.execute(`
        ALTER TABLE orders 
        ADD COLUMN order_number VARCHAR(50) UNIQUE NOT NULL DEFAULT ''
      `);
      console.log('✅ Added order_number column to orders table');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('⚠️  Order_number column already exists');
      } else {
        console.log('❌ Error adding order_number column:', error.message);
      }
    }
    
    // Update existing orders with order numbers
    try {
      const [orders] = await connection.execute('SELECT id FROM orders WHERE order_number = ""');
      for (let i = 0; i < orders.length; i++) {
        const orderNumber = `ORD-${Date.now()}-${i + 1}`;
        await connection.execute(
          'UPDATE orders SET order_number = ? WHERE id = ?',
          [orderNumber, orders[i].id]
        );
      }
      console.log(`✅ Updated ${orders.length} order numbers`);
    } catch (error) {
      console.log('❌ Error updating order numbers:', error.message);
    }
    
    // Fix users table - ensure all required columns exist
    const userColumns = [
      { name: 'password_hash', definition: 'VARCHAR(255)' },
      { name: 'email_verified_at', definition: 'TIMESTAMP NULL' },
      { name: 'remember_token', definition: 'VARCHAR(100)' },
      { name: 'two_factor_secret', definition: 'TEXT' },
      { name: 'two_factor_recovery_codes', definition: 'TEXT' },
      { name: 'profile_photo_path', definition: 'VARCHAR(2048)' }
    ];
    
    for (const column of userColumns) {
      try {
        await connection.execute(`ALTER TABLE users ADD COLUMN ${column.name} ${column.definition}`);
        console.log(`✅ Added ${column.name} column to users table`);
      } catch (error) {
        if (error.code === 'ER_DUP_FIELDNAME') {
          console.log(`⚠️  ${column.name} column already exists`);
        }
      }
    }
    
    // Create admin user if not exists
    try {
      const [adminExists] = await connection.execute(
        'SELECT id FROM users WHERE username = ? OR email = ?',
        ['admin', '<EMAIL>']
      );
      
      if (adminExists.length === 0) {
        await connection.execute(`
          INSERT INTO users (
            id, username, email, password, full_name, role, status,
            email_verified, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          'admin-user-1',
          'admin',
          '<EMAIL>',
          '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password: password
          'مدير النظام',
          'admin',
          'active',
          true
        ]);
        console.log('✅ Created admin user');
      } else {
        console.log('⚠️  Admin user already exists');
      }
    } catch (error) {
      console.log('❌ Error creating admin user:', error.message);
    }
    
    console.log('🎉 Table structure fixes completed!');
    
  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run fix
fixTableIssues().catch(error => {
  console.error('Fix script failed:', error);
  process.exit(1);
});
