/**
 * MySQL Integration Test Script
 * 
 * This script tests the MySQL integration by:
 * 1. Testing database connection
 * 2. Testing API endpoints
 * 3. Testing data operations
 * 4. Verifying data consistency
 */

const axios = require('axios');
const mysql = require('mysql2/promise');
require('dotenv').config();

const API_BASE_URL = process.env.VITE_API_BASE_URL || 'http://localhost:3001';
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'khanfashariya_db'
};

class MySQLIntegrationTester {
  constructor() {
    this.connection = null;
    this.testResults = {
      database: { passed: 0, failed: 0, tests: [] },
      api: { passed: 0, failed: 0, tests: [] },
      integration: { passed: 0, failed: 0, tests: [] }
    };
  }

  async run() {
    console.log('🚀 Starting MySQL Integration Tests...\n');
    
    try {
      await this.testDatabaseConnection();
      await this.testAPIEndpoints();
      await this.testDataIntegration();
      
      this.printResults();
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
    } finally {
      if (this.connection) {
        await this.connection.end();
      }
    }
  }

  async testDatabaseConnection() {
    console.log('📊 Testing Database Connection...');
    
    try {
      // Test connection
      this.connection = await mysql.createConnection(DB_CONFIG);
      this.addTestResult('database', 'Database Connection', true, 'Connected successfully');
      
      // Test basic query
      const [rows] = await this.connection.execute('SELECT 1 as test');
      this.addTestResult('database', 'Basic Query', rows[0].test === 1, 'SELECT query works');
      
      // Test tables exist
      const [tables] = await this.connection.execute(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = ?
      `, [DB_CONFIG.database]);
      
      const requiredTables = ['users', 'orders', 'system_services', 'technical_services'];
      const existingTables = tables.map(t => t.table_name);
      
      for (const table of requiredTables) {
        const exists = existingTables.includes(table);
        this.addTestResult('database', `Table: ${table}`, exists, exists ? 'Table exists' : 'Table missing');
      }
      
    } catch (error) {
      this.addTestResult('database', 'Database Connection', false, error.message);
    }
  }

  async testAPIEndpoints() {
    console.log('🌐 Testing API Endpoints...');
    
    const endpoints = [
      { method: 'GET', path: '/api/health', name: 'Health Check' },
      { method: 'GET', path: '/api/systems', name: 'System Services' },
      { method: 'GET', path: '/api/services/technical', name: 'Technical Services' }
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await axios({
          method: endpoint.method,
          url: `${API_BASE_URL}${endpoint.path}`,
          timeout: 5000,
          validateStatus: () => true // Don't throw on HTTP errors
        });
        
        const success = response.status < 500; // Accept 4xx but not 5xx
        this.addTestResult('api', endpoint.name, success, 
          `Status: ${response.status} ${response.statusText}`);
        
      } catch (error) {
        this.addTestResult('api', endpoint.name, false, error.message);
      }
    }
  }

  async testDataIntegration() {
    console.log('🔄 Testing Data Integration...');
    
    try {
      // Test user count consistency
      const [userRows] = await this.connection.execute('SELECT COUNT(*) as count FROM users');
      const dbUserCount = userRows[0].count;
      
      try {
        const apiResponse = await axios.get(`${API_BASE_URL}/api/admin/users`, {
          headers: { 'Authorization': 'Bearer test-token' },
          timeout: 5000,
          validateStatus: () => true
        });
        
        if (apiResponse.status === 200 && apiResponse.data.data) {
          const apiUserCount = apiResponse.data.data.users.length;
          const consistent = dbUserCount === apiUserCount;
          this.addTestResult('integration', 'User Count Consistency', consistent,
            `DB: ${dbUserCount}, API: ${apiUserCount}`);
        } else {
          this.addTestResult('integration', 'User Count Consistency', false,
            'API endpoint not accessible (auth required)');
        }
      } catch (error) {
        this.addTestResult('integration', 'User Count Consistency', false,
          'API endpoint not accessible');
      }
      
      // Test order count consistency
      const [orderRows] = await this.connection.execute('SELECT COUNT(*) as count FROM orders');
      const dbOrderCount = orderRows[0].count;
      
      this.addTestResult('integration', 'Order Data Available', dbOrderCount > 0,
        `Found ${dbOrderCount} orders in database`);
      
      // Test service count consistency
      const [systemRows] = await this.connection.execute('SELECT COUNT(*) as count FROM system_services');
      const [techRows] = await this.connection.execute('SELECT COUNT(*) as count FROM technical_services');
      
      this.addTestResult('integration', 'Service Data Available', 
        (systemRows[0].count + techRows[0].count) > 0,
        `Found ${systemRows[0].count} system services, ${techRows[0].count} technical services`);
      
    } catch (error) {
      this.addTestResult('integration', 'Data Integration', false, error.message);
    }
  }

  addTestResult(category, testName, passed, message) {
    this.testResults[category].tests.push({
      name: testName,
      passed,
      message
    });
    
    if (passed) {
      this.testResults[category].passed++;
      console.log(`  ✅ ${testName}: ${message}`);
    } else {
      this.testResults[category].failed++;
      console.log(`  ❌ ${testName}: ${message}`);
    }
  }

  printResults() {
    console.log('\n📋 Test Results Summary:');
    console.log('========================');
    
    let totalPassed = 0;
    let totalFailed = 0;
    
    for (const [category, results] of Object.entries(this.testResults)) {
      const total = results.passed + results.failed;
      const percentage = total > 0 ? Math.round((results.passed / total) * 100) : 0;
      
      console.log(`\n${category.toUpperCase()}:`);
      console.log(`  Passed: ${results.passed}`);
      console.log(`  Failed: ${results.failed}`);
      console.log(`  Success Rate: ${percentage}%`);
      
      totalPassed += results.passed;
      totalFailed += results.failed;
    }
    
    const overallTotal = totalPassed + totalFailed;
    const overallPercentage = overallTotal > 0 ? Math.round((totalPassed / overallTotal) * 100) : 0;
    
    console.log('\nOVERALL:');
    console.log(`  Total Tests: ${overallTotal}`);
    console.log(`  Passed: ${totalPassed}`);
    console.log(`  Failed: ${totalFailed}`);
    console.log(`  Success Rate: ${overallPercentage}%`);
    
    if (overallPercentage >= 80) {
      console.log('\n🎉 MySQL Integration is working well!');
    } else if (overallPercentage >= 60) {
      console.log('\n⚠️  MySQL Integration has some issues that need attention.');
    } else {
      console.log('\n🚨 MySQL Integration has significant problems that need immediate attention.');
    }
    
    console.log('\n💡 Recommendations:');
    if (this.testResults.database.failed > 0) {
      console.log('  - Check database connection and table structure');
    }
    if (this.testResults.api.failed > 0) {
      console.log('  - Ensure API server is running and accessible');
    }
    if (this.testResults.integration.failed > 0) {
      console.log('  - Verify data consistency between database and API');
    }
  }
}

// Run the tests
if (require.main === module) {
  const tester = new MySQLIntegrationTester();
  tester.run().catch(console.error);
}

module.exports = MySQLIntegrationTester;