/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: '#1e1a2b',
        secondary: '#D4AF37',
        background: '#0f0e17',
        accent: '#F5DEB3',
      },
      fontFamily: {
        arabic: ['Noto Sans Arabic', 'sans-serif'],
        english: ['Inter', 'sans-serif'],
      },
      animation: {
        'pulse-glow': 'pulse-glow 2s ease-in-out infinite',
        'float': 'float 6s ease-in-out infinite',
      },
      backdropBlur: {
        xs: '2px',
      },
      boxShadow: {
        'glow-secondary': '0 0 20px rgba(255, 178, 0, 0.3)',
        'glow-accent': '0 0 20px rgba(33, 192, 255, 0.3)',
      },
    },
  },
  plugins: [],
};