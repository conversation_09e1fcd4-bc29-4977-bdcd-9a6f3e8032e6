/**
 * Data Adapter for Khanfashariya.com
 * 
 * This adapter provides a unified interface for data operations
 * that can work with either localStorage (legacy) or API (new).
 * 
 * This allows for gradual migration and fallback capabilities.
 */

import * as apiServices from './apiServices';
import * as localDatabase from './database';
import { USE_API, DEBUG_CONFIG } from '../config/dataSource';

/**
 * Data Adapter Class
 * Provides unified interface for data operations
 */
class DataAdapter {
  private useApi: boolean;

  constructor(useApi: boolean = USE_API) {
    this.useApi = useApi;
    if (DEBUG_CONFIG.logDataSource) {
      console.log(`🔧 DataAdapter initialized with ${useApi ? 'API' : 'localStorage'} backend`);
    }
  }

  /**
   * Switch between API and localStorage
   */
  public setDataSource(useApi: boolean): void {
    this.useApi = useApi;
    console.log(`DataAdapter switched to ${useApi ? 'API' : 'localStorage'} backend`);
  }

  /**
   * Get current data source
   */
  public getDataSource(): 'api' | 'localStorage' {
    return this.useApi ? 'api' : 'localStorage';
  }

  // =====================================================
  // AUTHENTICATION METHODS
  // =====================================================

  public async signIn(email: string, password: string) {
    try {
      if (this.useApi) {
        return await apiServices.signIn(email, password);
      } else {
        return await localDatabase.signIn(email, password);
      }
    } catch (error) {
      console.error('DataAdapter signIn error:', error);
      // Fallback to localStorage if API fails
      if (this.useApi) {
        console.warn('API signIn failed, falling back to localStorage');
        return await localDatabase.signIn(email, password);
      }
      throw error;
    }
  }

  public async signUp(email: string, password: string, userData: { username: string; full_name: string }) {
    try {
      if (this.useApi) {
        return await apiServices.signUp(email, password, userData);
      } else {
        return await localDatabase.signUp(email, password, userData);
      }
    } catch (error) {
      console.error('DataAdapter signUp error:', error);
      // Fallback to localStorage if API fails
      if (this.useApi) {
        console.warn('API signUp failed, falling back to localStorage');
        return await localDatabase.signUp(email, password, userData);
      }
      throw error;
    }
  }

  public async signOut() {
    try {
      if (this.useApi) {
        return await apiServices.signOut();
      } else {
        return localDatabase.signOut();
      }
    } catch (error) {
      console.error('DataAdapter signOut error:', error);
      // Always try to clear localStorage as fallback
      return localDatabase.signOut();
    }
  }

  public getCurrentUser() {
    // For getCurrentUser, we can use localStorage as cache
    // since it's synchronous and always available
    return apiServices.getCurrentUser();
  }

  // =====================================================
  // SYSTEM SERVICES METHODS
  // =====================================================

  public async getSystemServices() {
    try {
      if (this.useApi) {
        return await apiServices.getSystemServices();
      } else {
        return localDatabase.getSystemServices();
      }
    } catch (error) {
      console.error('DataAdapter getSystemServices error:', error);
      // Fallback to localStorage if API fails
      if (this.useApi) {
        console.warn('API getSystemServices failed, falling back to localStorage');
        return localDatabase.getSystemServices();
      }
      throw error;
    }
  }

  public async createSystemService(serviceData: any) {
    try {
      if (this.useApi) {
        return await apiServices.createSystemService(serviceData);
      } else {
        return localDatabase.createSystemService(serviceData);
      }
    } catch (error) {
      console.error('DataAdapter createSystemService error:', error);
      // No fallback for create operations to avoid data inconsistency
      throw error;
    }
  }

  public async updateSystemService(id: string, serviceData: any) {
    try {
      if (this.useApi) {
        return await apiServices.updateSystemService(id, serviceData);
      } else {
        return localDatabase.updateSystemService(id, serviceData);
      }
    } catch (error) {
      console.error('DataAdapter updateSystemService error:', error);
      // No fallback for update operations to avoid data inconsistency
      throw error;
    }
  }

  public async deleteSystemService(id: string) {
    try {
      if (this.useApi) {
        return await apiServices.deleteSystemService(id);
      } else {
        return localDatabase.deleteSystemService(id);
      }
    } catch (error) {
      console.error('DataAdapter deleteSystemService error:', error);
      // No fallback for delete operations to avoid data inconsistency
      throw error;
    }
  }

  // =====================================================
  // TECHNICAL SERVICES METHODS
  // =====================================================

  public async getTechnicalServices() {
    try {
      if (this.useApi) {
        return await apiServices.getTechnicalServices();
      } else {
        return localDatabase.getTechnicalServices();
      }
    } catch (error) {
      console.error('DataAdapter getTechnicalServices error:', error);
      // Fallback to localStorage if API fails
      if (this.useApi) {
        console.warn('API getTechnicalServices failed, falling back to localStorage');
        return localDatabase.getTechnicalServices();
      }
      throw error;
    }
  }

  public async createTechnicalService(serviceData: any) {
    try {
      if (this.useApi) {
        return await apiServices.createTechnicalService(serviceData);
      } else {
        return localDatabase.createTechnicalService(serviceData);
      }
    } catch (error) {
      console.error('DataAdapter createTechnicalService error:', error);
      throw error;
    }
  }

  public async updateTechnicalService(id: string, serviceData: any) {
    try {
      if (this.useApi) {
        return await apiServices.updateTechnicalService(id, serviceData);
      } else {
        return localDatabase.updateTechnicalService(id, serviceData);
      }
    } catch (error) {
      console.error('DataAdapter updateTechnicalService error:', error);
      throw error;
    }
  }

  public async deleteTechnicalService(id: string) {
    try {
      if (this.useApi) {
        return await apiServices.deleteTechnicalService(id);
      } else {
        return localDatabase.deleteTechnicalService(id);
      }
    } catch (error) {
      console.error('DataAdapter deleteTechnicalService error:', error);
      throw error;
    }
  }

  // =====================================================
  // USER MANAGEMENT METHODS
  // =====================================================

  public async getAllUsers() {
    try {
      if (this.useApi) {
        return await apiServices.getAllUsers();
      } else {
        return localDatabase.getAllUsers();
      }
    } catch (error) {
      console.error('DataAdapter getAllUsers error:', error);
      // Fallback to localStorage if API fails
      if (this.useApi) {
        console.warn('API getAllUsers failed, falling back to localStorage');
        return localDatabase.getAllUsers();
      }
      throw error;
    }
  }

  public async getUserProfile(userId?: string) {
    try {
      if (this.useApi) {
        return await apiServices.getUserProfile(userId);
      } else {
        const user = userId ? localDatabase.getUserProfile(userId) : localDatabase.getCurrentUser();
        return { data: user, error: null };
      }
    } catch (error) {
      console.error('DataAdapter getUserProfile error:', error);
      // Fallback to localStorage if API fails
      if (this.useApi) {
        console.warn('API getUserProfile failed, falling back to localStorage');
        const user = userId ? localDatabase.getUserProfile(userId) : localDatabase.getCurrentUser();
        return { data: user, error: null };
      }
      throw error;
    }
  }

  public async updateUserRole(userId: string, role: 'user' | 'admin') {
    try {
      if (this.useApi) {
        return await apiServices.updateUserRole(userId, role);
      } else {
        return localDatabase.updateUserRole(userId, role);
      }
    } catch (error) {
      console.error('DataAdapter updateUserRole error:', error);
      throw error;
    }
  }

  // =====================================================
  // ORDER MANAGEMENT METHODS
  // =====================================================

  public async createOrder(orderData: any) {
    try {
      if (this.useApi) {
        return await apiServices.createOrder(orderData);
      } else {
        // For localStorage, we need to extract user_id and pass it separately
        const { user_id, ...restData } = orderData;
        return localDatabase.createOrder(user_id, restData);
      }
    } catch (error) {
      console.error('DataAdapter createOrder error:', error);
      throw error;
    }
  }

  public async getUserOrders(userId?: string) {
    try {
      if (this.useApi) {
        return await apiServices.getUserOrders(userId);
      } else {
        return userId ? localDatabase.getOrdersByUser(userId) : localDatabase.getAllOrders();
      }
    } catch (error) {
      console.error('DataAdapter getUserOrders error:', error);
      // Fallback to localStorage if API fails
      if (this.useApi) {
        console.warn('API getUserOrders failed, falling back to localStorage');
        return userId ? localDatabase.getOrdersByUser(userId) : localDatabase.getAllOrders();
      }
      throw error;
    }
  }

  public async getAllOrders() {
    try {
      if (this.useApi) {
        return await apiServices.getAllOrders();
      } else {
        return localDatabase.getAllOrders();
      }
    } catch (error) {
      console.error('DataAdapter getAllOrders error:', error);
      // Fallback to localStorage if API fails
      if (this.useApi) {
        console.warn('API getAllOrders failed, falling back to localStorage');
        return localDatabase.getAllOrders();
      }
      throw error;
    }
  }
}

// Create and export singleton instance
const dataAdapter = new DataAdapter();
export default dataAdapter;

// Export individual methods for convenience
export const {
  signIn,
  signUp,
  signOut,
  getCurrentUser,
  getSystemServices,
  createSystemService,
  updateSystemService,
  deleteSystemService,
  getTechnicalServices,
  createTechnicalService,
  updateTechnicalService,
  deleteTechnicalService,
  getAllUsers,
  getUserProfile,
  updateUserRole,
  createOrder,
  getUserOrders,
  getAllOrders
} = dataAdapter;
