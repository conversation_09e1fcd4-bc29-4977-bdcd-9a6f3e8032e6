const axios = require('axios');

async function testSimpleAdmin() {
  try {
    console.log('Testing simple admin functionality...');
    
    // 1. Admin login
    const adminLogin = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (!adminLogin.data.success) {
      console.log('❌ Admin login failed');
      return;
    }
    
    console.log('✅ Admin login successful');
    
    const headers = { Authorization: `Bearer ${adminLogin.data.data.tokens.accessToken}` };
    
    // 2. Get users
    const usersResponse = await axios.get('http://localhost:3001/api/admin/users', { headers });
    
    if (usersResponse.data.success) {
      console.log(`✅ Users retrieved: ${usersResponse.data.data.users.length} users`);
      
      const testUser = usersResponse.data.data.users.find(u => u.email === '<EMAIL>');
      
      if (testUser) {
        console.log(`✅ Test user found: ${testUser.id}`);
        
        // 3. Try to send a simple message
        const messageData = {
          subject_ar: 'اختبار',
          subject_en: 'Test',
          message_ar: 'رسالة اختبار',
          message_en: 'Test message'
        };
        
        console.log('Sending message with data:', messageData);
        
        const messageResponse = await axios.post(
          `http://localhost:3001/api/admin/users/${testUser.id}/messages`,
          messageData,
          { headers }
        );
        
        console.log('Message response:', messageResponse.data);
        
      } else {
        console.log('❌ Test user not found');
      }
    } else {
      console.log('❌ Failed to get users');
    }
    
  } catch (error) {
    console.log('❌ Error:', error.response?.data || error.message);
  }
}

testSimpleAdmin();
