{"backend": {"apiName": "Khanfashariya Backend API", "apiEndpoint": "https://720f35b15ec2.ngrok-free.app", "authenticationType": "None - No authentication required", "extraInfo": "Khanfashariya Metin2 Services Platform - Backend API\n\n🔧 API Endpoints:\n- GET /health - Health check\n- GET /api/status - API status\n- POST /api/auth/login - User login\n- POST /api/auth/register - User registration\n- GET /api/users/profile - User profile (requires auth)\n- GET /api/systems - System services\n- GET /api/services - Technical services\n- GET /api/orders - Orders (requires auth)\n- GET /api/admin/dashboard - Admin panel (requires auth)\n\n🔑 Test Credentials:\nEmail: <EMAIL>\nPassword: admin123\n\n📝 Sample Requests:\n\nLogin:\nPOST /api/auth/login\n{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"admin123\"\n}\n\nRegistration:\nPOST /api/auth/register\n{\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"confirmPassword\": \"password123\"\n}\n\n🗄️ Database: MySQL (localhost:3306)\n📊 Features: JWT Auth, Rate Limiting, File Upload, Admin Panel"}, "frontend": {"websiteName": "Khanfashariya Frontend", "websiteUrl": "https://abac08467e3d.ngrok-free.app", "description": "Khanfashariya Metin2 Services Platform - Frontend Application\n\n🌐 Full Stack React Application with:\n- User Authentication & Registration\n- Service Catalog & Ordering\n- Admin Dashboard\n- File Upload & Management\n- Responsive Design\n- Real-time Updates\n\n🔗 Connected to Backend API: https://720f35b15ec2.ngrok-free.app\n\n📱 Pages to Test:\n- / - Homepage\n- /login - Login page\n- /register - Registration page\n- /services - Services catalog\n- /profile - User profile (requires login)\n- /admin - Admin dashboard (requires admin login)\n\n🧪 Test Scenarios:\n1. User Registration Flow\n2. Login/Logout Process\n3. Service Browsing & Ordering\n4. Profile Management\n5. Admin Operations\n6. Responsive Design Testing\n7. Error Handling\n8. Performance Testing\n\n🔑 Test Credentials:\nEmail: <EMAIL>\nPassword: admin123"}}