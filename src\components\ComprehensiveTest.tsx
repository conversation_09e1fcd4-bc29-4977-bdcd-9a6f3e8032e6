import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuth } from '../hooks/useAuth';
import {
  signIn,
  getSystemServices,
  createSystemService,
  updateSystemService,
  deleteSystemService
} from '../lib/apiServices';
import { SystemService } from '../lib/database';
import { 
  X, 
  Play, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Eye,
  Edit,
  Save,
  Trash2,
  Database,
  User,
  Shield
} from 'lucide-react';

interface ComprehensiveTestProps {
  onClose: () => void;
}

/**
 * Comprehensive test component for all admin features
 */
const ComprehensiveTest: React.FC<ComprehensiveTestProps> = ({ onClose }) => {
  const { language } = useTranslation();
  const { isAuthenticated, isAdmin, userProfile } = useAuth();
  const [testResults, setTestResults] = useState<Record<string, 'pending' | 'success' | 'error'>>({});
  const [testLogs, setTestLogs] = useState<string[]>([]);
  const [systems, setSystems] = useState<SystemService[]>([]);
  const [testSystem, setTestSystem] = useState<SystemService | null>(null);
  const [showEditTest, setShowEditTest] = useState(false);
  const [showPreviewTest, setShowPreviewTest] = useState(false);
  const [running, setRunning] = useState(false);

  const addLog = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
    setTestLogs(prev => [...prev, `${timestamp} ${icon} ${message}`]);
  };

  const updateResult = (testName: string, result: 'success' | 'error' | 'pending') => {
    setTestResults(prev => ({ ...prev, [testName]: result }));
  };

  // Test 1: Authentication
  const testAuth = async () => {
    addLog('Testing authentication system...');
    updateResult('auth', 'pending');
    
    try {
      if (!isAuthenticated) {
        const result = await signIn('<EMAIL>', 'admin123');
        if (result.error) {
          throw new Error(result.error.message);
        }
      }
      
      if (isAdmin) {
        addLog('Authentication successful - Admin access confirmed', 'success');
        updateResult('auth', 'success');
        return true;
      } else {
        throw new Error('User is not admin');
      }
    } catch (error: any) {
      addLog(`Authentication failed: ${error.message}`, 'error');
      updateResult('auth', 'error');
      return false;
    }
  };

  // Test 2: Data Loading
  const testDataLoading = () => {
    addLog('Testing data loading...');
    updateResult('dataLoading', 'pending');
    
    try {
      const result = getSystemServices();
      if (result.data && result.data.length > 0) {
        setSystems(result.data);
        addLog(`Data loading successful - Found ${result.data.length} systems`, 'success');
        updateResult('dataLoading', 'success');
        return true;
      } else {
        addLog('No systems data found', 'error');
        updateResult('dataLoading', 'error');
        return false;
      }
    } catch (error: any) {
      addLog(`Data loading failed: ${error.message}`, 'error');
      updateResult('dataLoading', 'error');
      return false;
    }
  };

  // Test 3: Create Operation
  const testCreate = () => {
    addLog('Testing create operation...');
    updateResult('create', 'pending');

    try {
      const testData = {
        name: { ar: 'نظام اختبار شامل', en: 'Comprehensive Test System' },
        description: { ar: 'نظام تجريبي للاختبار الشامل', en: 'Test system for comprehensive testing' },
        price: 199,
        category: 'test',
        features: { ar: ['اختبار شامل', 'فحص الوظائف'], en: ['Comprehensive test', 'Function check'] },
        tech_specs: { ar: ['مواصفات تجريبية'], en: ['Test specifications'] },
        video_url: '',
        image_url: '',
        status: 'active' as const
      };

      const result = createSystemService(testData);
      if (result.data) {
        // Set the created system directly from the result
        setTestSystem(result.data);
        addLog(`Create operation successful - Created: ${result.data.name.en}`, 'success');
        updateResult('create', 'success');

        // Also refresh the systems list
        const updatedSystems = getSystemServices();
        if (updatedSystems.data) {
          setSystems(updatedSystems.data);
        }

        return true;
      } else {
        throw new Error(result.error?.message || 'Create failed');
      }
    } catch (error: any) {
      addLog(`Create operation failed: ${error.message}`, 'error');
      updateResult('create', 'error');
      return false;
    }
  };

  // Test 4: Edit Modal Test
  const testEditModal = () => {
    addLog('Testing edit modal functionality...');
    updateResult('editModal', 'pending');
    
    try {
      if (testSystem) {
        setShowEditTest(true);
        addLog('Edit modal opened successfully', 'success');
        updateResult('editModal', 'success');
        return true;
      } else {
        throw new Error('No test system available');
      }
    } catch (error: any) {
      addLog(`Edit modal test failed: ${error.message}`, 'error');
      updateResult('editModal', 'error');
      return false;
    }
  };

  // Test 5: Preview Modal Test
  const testPreviewModal = () => {
    addLog('Testing preview modal functionality...');
    updateResult('previewModal', 'pending');
    
    try {
      if (testSystem) {
        setShowPreviewTest(true);
        addLog('Preview modal opened successfully', 'success');
        updateResult('previewModal', 'success');
        return true;
      } else {
        throw new Error('No test system available');
      }
    } catch (error: any) {
      addLog(`Preview modal test failed: ${error.message}`, 'error');
      updateResult('previewModal', 'error');
      return false;
    }
  };

  // Test 6: Update Operation
  const testUpdate = () => {
    addLog('Testing update operation...');
    updateResult('update', 'pending');
    
    try {
      if (testSystem) {
        const updateData = {
          name: { ar: 'نظام محدث شامل', en: 'Updated Comprehensive System' },
          price: 299
        };
        
        const result = updateSystemService(testSystem.id, updateData);
        if (result.data) {
          // Refresh systems list to get the updated system
          const updatedSystems = getSystemServices();
          if (updatedSystems.data) {
            setSystems(updatedSystems.data);
            // Find the updated system
            const updatedSystem = updatedSystems.data.find(s => s.id === testSystem.id);
            if (updatedSystem) {
              setTestSystem(updatedSystem);
              addLog(`Update operation successful - Updated: ${updatedSystem.name.en}`, 'success');
              updateResult('update', 'success');
              return true;
            }
          }
          addLog(`Update operation successful but system not found in list`, 'error');
          updateResult('update', 'error');
          return false;
        } else {
          throw new Error(result.error?.message || 'Update failed');
        }
      } else {
        throw new Error('No test system available');
      }
    } catch (error: any) {
      addLog(`Update operation failed: ${error.message}`, 'error');
      updateResult('update', 'error');
      return false;
    }
  };

  // Test 7: Delete Operation
  const testDelete = () => {
    addLog('Testing delete operation...');
    updateResult('delete', 'pending');
    
    try {
      if (testSystem) {
        const systemName = testSystem.name.en;
        const result = deleteSystemService(testSystem.id);
        if (result.data) {
          // Refresh systems list
          const updatedSystems = getSystemServices();
          if (updatedSystems.data) {
            setSystems(updatedSystems.data);
          }
          addLog(`Delete operation successful - Deleted: ${systemName}`, 'success');
          setTestSystem(null);
          updateResult('delete', 'success');
          return true;
        } else {
          throw new Error(result.error?.message || 'Delete failed');
        }
      } else {
        throw new Error('No test system available');
      }
    } catch (error: any) {
      addLog(`Delete operation failed: ${error.message}`, 'error');
      updateResult('delete', 'error');
      return false;
    }
  };

  // Run all tests
  const runAllTests = async () => {
    setRunning(true);
    setTestResults({});
    setTestLogs([]);
    addLog('Starting comprehensive test suite...');
    
    // Test 1: Authentication
    const authSuccess = await testAuth();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (authSuccess) {
      // Test 2: Data Loading
      const dataSuccess = testDataLoading();
      await new Promise(resolve => setTimeout(resolve, 500));
      
      if (dataSuccess) {
        // Test 3: Create
        const createSuccess = testCreate();
        await new Promise(resolve => setTimeout(resolve, 500));
        
        if (createSuccess) {
          // Test 4: Edit Modal
          testEditModal();
          await new Promise(resolve => setTimeout(resolve, 1000));
          setShowEditTest(false);
          
          // Test 5: Preview Modal
          testPreviewModal();
          await new Promise(resolve => setTimeout(resolve, 1000));
          setShowPreviewTest(false);
          
          // Test 6: Update
          testUpdate();
          await new Promise(resolve => setTimeout(resolve, 500));
          
          // Test 7: Delete
          testDelete();
        }
      }
    }
    
    addLog('Comprehensive test suite completed!', 'success');
    setRunning(false);
  };

  const getStatusIcon = (status: 'pending' | 'success' | 'error' | undefined) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'error': return <XCircle className="w-5 h-5 text-red-400" />;
      case 'pending': return <AlertTriangle className="w-5 h-5 text-yellow-400 animate-pulse" />;
      default: return <div className="w-5 h-5 border-2 border-gray-400 rounded-full" />;
    }
  };

  const tests = [
    { key: 'auth', name: 'Authentication Test', icon: Shield },
    { key: 'dataLoading', name: 'Data Loading Test', icon: Database },
    { key: 'create', name: 'Create Operation Test', icon: Save },
    { key: 'editModal', name: 'Edit Modal Test', icon: Edit },
    { key: 'previewModal', name: 'Preview Modal Test', icon: Eye },
    { key: 'update', name: 'Update Operation Test', icon: Edit },
    { key: 'delete', name: 'Delete Operation Test', icon: Trash2 }
  ];

  return (
    <>
      <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 overflow-y-auto">
        <div className="min-h-screen py-8">
          <div className="max-w-6xl mx-auto bg-primary rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
            {/* Header */}
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-6">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-white">
                  {language === 'ar' ? 'اختبار شامل للنظام' : 'Comprehensive System Test'}
                </h2>
                <button
                  onClick={onClose}
                  className="w-10 h-10 bg-white/20 hover:bg-white/40 rounded-full flex items-center justify-center transition-colors"
                >
                  <X className="w-6 h-6 text-white" />
                </button>
              </div>
            </div>

            <div className="p-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Controls and Status */}
              <div className="space-y-6">
                {/* Run Button */}
                <div className="bg-background/50 rounded-lg p-4">
                  <button
                    onClick={runAllTests}
                    disabled={running}
                    className="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-3 rounded-lg hover:from-purple-600 hover:to-blue-600 transition-colors disabled:opacity-50 flex items-center justify-center space-x-2"
                  >
                    <Play className="w-5 h-5" />
                    <span>
                      {running 
                        ? (language === 'ar' ? 'جاري التشغيل...' : 'Running...') 
                        : (language === 'ar' ? 'تشغيل الاختبار الشامل' : 'Run Comprehensive Test')
                      }
                    </span>
                  </button>
                </div>

                {/* Test Results */}
                <div className="bg-background/50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-4">
                    {language === 'ar' ? 'نتائج الاختبارات' : 'Test Results'}
                  </h3>
                  <div className="space-y-3">
                    {tests.map(test => {
                      const IconComponent = test.icon;
                      return (
                        <div key={test.key} className="flex items-center justify-between p-3 bg-primary/30 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <IconComponent className="w-5 h-5 text-gray-400" />
                            <span className="text-white text-sm">{test.name}</span>
                          </div>
                          {getStatusIcon(testResults[test.key])}
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* System Info */}
                <div className="bg-background/50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-4">
                    {language === 'ar' ? 'معلومات النظام' : 'System Info'}
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">{language === 'ar' ? 'المستخدم:' : 'User:'}</span>
                      <span className="text-white">{userProfile?.username || 'Not logged in'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">{language === 'ar' ? 'الدور:' : 'Role:'}</span>
                      <span className="text-white">{userProfile?.role || 'None'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">{language === 'ar' ? 'الأنظمة:' : 'Systems:'}</span>
                      <span className="text-white">{systems.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">{language === 'ar' ? 'نظام الاختبار:' : 'Test System:'}</span>
                      <span className="text-white">{testSystem ? testSystem.name.en : 'None'}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Test Logs */}
              <div className="bg-background/50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'سجل الاختبارات' : 'Test Logs'}
                </h3>
                <div className="bg-black/50 rounded p-3 h-96 overflow-y-auto">
                  {testLogs.map((log, index) => (
                    <div key={index} className="text-sm text-gray-300 mb-1 font-mono">
                      {log}
                    </div>
                  ))}
                  {testLogs.length === 0 && (
                    <div className="text-gray-500 text-sm">
                      {language === 'ar' ? 'لا توجد سجلات بعد...' : 'No logs yet...'}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Edit Test Modal */}
      {showEditTest && testSystem && (
        <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-60 flex items-center justify-center p-4">
          <div className="bg-primary rounded-xl max-w-lg w-full border border-accent/30">
            <div className="p-6">
              <h3 className="text-lg font-bold text-white mb-4">
                {language === 'ar' ? 'اختبار نافذة التعديل' : 'Edit Modal Test'}
              </h3>
              <p className="text-gray-300 mb-4">
                {language === 'ar' ? 'هذه نافذة اختبار التعديل. النافذة تعمل بشكل صحيح!' : 'This is the edit modal test. The modal is working correctly!'}
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowEditTest(false);
                    addLog('Edit modal test completed successfully', 'success');
                  }}
                  className="px-4 py-2 bg-green-500/20 text-green-400 border border-green-500/30 rounded-lg hover:bg-green-500/30 transition-colors"
                >
                  {language === 'ar' ? 'تم الاختبار' : 'Test Complete'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Preview Test Modal */}
      {showPreviewTest && testSystem && (
        <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-60 flex items-center justify-center p-4">
          <div className="bg-primary rounded-xl max-w-2xl w-full border border-accent/30">
            <div className="p-6">
              <h3 className="text-lg font-bold text-white mb-4">
                {language === 'ar' ? 'اختبار نافذة المعاينة' : 'Preview Modal Test'}
              </h3>
              <div className="bg-gradient-to-br from-secondary/10 to-accent/10 rounded-lg p-6 border border-secondary/20 mb-4">
                <h4 className="text-xl font-bold text-white mb-2">{testSystem.name[language]}</h4>
                <p className="text-gray-300 mb-4">{testSystem.description[language]}</p>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">{language === 'ar' ? 'السعر:' : 'Price:'}</span>
                  <span className="text-2xl font-bold text-secondary">${testSystem.price}</span>
                </div>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowPreviewTest(false);
                    addLog('Preview modal test completed successfully', 'success');
                  }}
                  className="px-4 py-2 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded-lg hover:bg-blue-500/30 transition-colors"
                >
                  {language === 'ar' ? 'تم الاختبار' : 'Test Complete'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ComprehensiveTest;
