/**
 * Database Health Check Middleware
 * 
 * This middleware ensures database connectivity before processing requests
 */

const { checkDatabaseHealth, connectDatabase } = require('../config/database');

/**
 * Middleware to check database health before processing requests
 */
const dbHealthCheck = async (req, res, next) => {
  try {
    // Skip health check for non-database routes
    if (req.path === '/health' || req.path === '/ping') {
      return next();
    }
    
    // Check database health
    const health = await checkDatabaseHealth();
    
    if (health.status !== 'healthy') {
      console.warn('⚠️  Database health check failed, attempting reconnection...');
      
      try {
        await connectDatabase();
        console.log('✅ Database reconnection successful');
      } catch (reconnectError) {
        console.error('❌ Database reconnection failed:', reconnectError.message);
        
        return res.status(503).json({
          success: false,
          error: 'Database temporarily unavailable',
          message: 'Please try again in a few moments',
          code: 'DB_UNAVAILABLE'
        });
      }
    }
    
    next();
  } catch (error) {
    console.error('❌ Database health check middleware error:', error.message);
    
    // Don't block the request, but log the issue
    next();
  }
};

/**
 * Enhanced health check for critical database operations
 */
const criticalDbHealthCheck = async (req, res, next) => {
  try {
    const health = await checkDatabaseHealth();
    
    if (health.status !== 'healthy') {
      return res.status(503).json({
        success: false,
        error: 'Database service unavailable',
        message: 'Critical database operations are temporarily disabled',
        code: 'DB_CRITICAL_ERROR',
        details: health.error || 'Unknown database error'
      });
    }
    
    next();
  } catch (error) {
    console.error('❌ Critical database health check failed:', error.message);
    
    return res.status(503).json({
      success: false,
      error: 'Database health check failed',
      message: 'Unable to verify database connectivity',
      code: 'DB_HEALTH_CHECK_FAILED'
    });
  }
};

module.exports = {
  dbHealthCheck,
  criticalDbHealthCheck
};