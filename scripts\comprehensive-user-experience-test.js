/**
 * Comprehensive User Experience Test
 * Tests the complete user journey from registration to order completion
 */

const axios = require('axios');
const mysql = require('mysql2/promise');

const API_BASE = 'http://localhost:3001/api';
const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khan<PERSON><PERSON>riya_db'
};

// Generate unique test user
const generateTestUser = () => {
  const timestamp = Date.now().toString().slice(-6);
  return {
    email: `user${timestamp}@test.com`,
    password: 'TestPass123!',
    username: `user${timestamp}`,
    full_name: `Test User ${timestamp}`
  };
};

let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

function logTest(name, status, details = '') {
  testResults.total++;
  const icon = status === 'PASS' ? '✅' : '❌';
  const message = `${icon} ${name}`;
  
  if (status === 'PASS') {
    testResults.passed++;
    console.log(`${message} ${details}`);
  } else {
    testResults.failed++;
    console.log(`${message} - ${details}`);
    testResults.errors.push({ test: name, error: details });
  }
}

async function testCompleteUserJourney() {
  console.log('🚀 Starting Comprehensive User Experience Test');
  console.log('=' * 60);
  
  const testUser = generateTestUser();
  let authData = null;
  
  try {
    // 1. User Registration
    console.log('\n👤 Testing User Registration...');
    const registerResponse = await axios.post(`${API_BASE}/auth/register`, testUser);
    
    if (registerResponse.data.success) {
      logTest('User Registration', 'PASS', `User ${testUser.email} registered successfully`);
    } else {
      logTest('User Registration', 'FAIL', 'Registration failed');
      return;
    }
    
    // 2. User Login
    console.log('\n🔐 Testing User Login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    
    if (loginResponse.data.success && loginResponse.data.data.tokens) {
      authData = {
        token: loginResponse.data.data.tokens.accessToken,
        user: loginResponse.data.data.user,
        headers: { Authorization: `Bearer ${loginResponse.data.data.tokens.accessToken}` }
      };
      logTest('User Login', 'PASS', 'Login successful');
    } else {
      logTest('User Login', 'FAIL', 'Login failed');
      return;
    }
    
    // 3. Browse Products
    console.log('\n🛍️ Testing Product Browsing...');
    const systemsResponse = await axios.get(`${API_BASE}/systems`);
    
    if (systemsResponse.data.success && systemsResponse.data.data.systems.length > 0) {
      const systems = systemsResponse.data.data.systems;
      logTest('Systems Browsing', 'PASS', `Found ${systems.length} systems available`);
      
      // Test bilingual content
      const firstSystem = systems[0];
      if (firstSystem.name_ar && firstSystem.name_en) {
        logTest('Bilingual Content', 'PASS', `AR: "${firstSystem.name_ar}", EN: "${firstSystem.name_en}"`);
      } else {
        logTest('Bilingual Content', 'FAIL', 'Missing bilingual content');
      }
    } else {
      logTest('Systems Browsing', 'FAIL', 'No systems available');
      return;
    }
    
    // 4. Test Services Browsing
    const servicesResponse = await axios.get(`${API_BASE}/services/technical`);
    if (servicesResponse.data.success) {
      const services = servicesResponse.data.data || [];
      logTest('Services Browsing', 'PASS', `Found ${services.length} services available`);
    } else {
      logTest('Services Browsing', 'FAIL', 'Services browsing failed');
    }
    
    // 5. Create System Order
    console.log('\n📦 Testing System Order Creation...');
    const testSystem = systemsResponse.data.data.systems[0];
    
    const systemOrderData = {
      order_type: 'system_service',
      item_id: testSystem.id,
      quantity: 1,
      notes_ar: 'طلب تجريبي لنظام',
      notes_en: 'Test system order'
    };
    
    const systemOrderResponse = await axios.post(`${API_BASE}/orders`, systemOrderData, { headers: authData.headers });
    
    if (systemOrderResponse.data.success) {
      const order = systemOrderResponse.data.data.order;
      logTest('System Order Creation', 'PASS', `Order ${order.order_number} created - $${order.final_price}`);
    } else {
      logTest('System Order Creation', 'FAIL', systemOrderResponse.data.error || 'Order creation failed');
    }
    
    // 6. Create Service Order (if services available)
    if (servicesResponse.data.success && servicesResponse.data.data.length > 0) {
      console.log('\n🔧 Testing Service Order Creation...');
      const testService = servicesResponse.data.data[0];
      
      const serviceOrderData = {
        order_type: 'technical_service',
        item_id: testService.id,
        quantity: 1,
        notes_ar: 'طلب تجريبي لخدمة',
        notes_en: 'Test service order'
      };
      
      const serviceOrderResponse = await axios.post(`${API_BASE}/orders`, serviceOrderData, { headers: authData.headers });
      
      if (serviceOrderResponse.data.success) {
        const order = serviceOrderResponse.data.data.order;
        logTest('Service Order Creation', 'PASS', `Order ${order.order_number} created - $${order.final_price}`);
      } else {
        logTest('Service Order Creation', 'FAIL', serviceOrderResponse.data.error || 'Service order creation failed');
      }
    }
    
    // 7. Create Custom Order
    console.log('\n🎨 Testing Custom Order Creation...');
    const customOrderData = {
      order_type: 'system_service',
      item_id: 'custom-order-' + Date.now(),
      quantity: 1,
      notes_ar: 'طلب نظام مخصص للاختبار الشامل',
      notes_en: 'Custom system request for comprehensive testing'
    };
    
    const customOrderResponse = await axios.post(`${API_BASE}/orders`, customOrderData, { headers: authData.headers });
    
    if (customOrderResponse.data.success) {
      const order = customOrderResponse.data.data.order;
      logTest('Custom Order Creation', 'PASS', `Custom order ${order.order_number} created - $${order.final_price}`);
    } else {
      logTest('Custom Order Creation', 'FAIL', customOrderResponse.data.error || 'Custom order creation failed');
    }
    
    // 8. View User Orders
    console.log('\n📋 Testing Order History...');
    const ordersResponse = await axios.get(`${API_BASE}/orders`, { headers: authData.headers });
    
    if (ordersResponse.data.success) {
      const orders = ordersResponse.data.data.orders || [];
      logTest('Order History Access', 'PASS', `Found ${orders.length} orders in user history`);
      
      if (orders.length > 0) {
        const latestOrder = orders[0];
        logTest('Order Details', 'PASS', `Latest order: ${latestOrder.order_number} - Status: ${latestOrder.status}`);
      }
    } else {
      logTest('Order History Access', 'FAIL', 'Failed to retrieve order history');
    }
    
    // 9. Test User Profile Access
    console.log('\n👤 Testing User Profile...');
    const profileResponse = await axios.get(`${API_BASE}/users/profile`, { headers: authData.headers });
    
    if (profileResponse.data.success) {
      const user = profileResponse.data.data.user;
      logTest('User Profile Access', 'PASS', `Profile loaded for ${user.email}`);
      logTest('User Statistics', 'PASS', `Orders: ${user.stats.total_orders}, Messages: ${user.stats.unread_messages}`);
    } else {
      logTest('User Profile Access', 'FAIL', 'Failed to access user profile');
    }
    
    // 10. Test User Messages
    console.log('\n💬 Testing User Messages...');
    const messagesResponse = await axios.get(`${API_BASE}/users/${authData.user.id}/messages`, { headers: authData.headers });
    
    if (messagesResponse.data.success) {
      const messages = messagesResponse.data.data.messages || [];
      logTest('User Messages Access', 'PASS', `Found ${messages.length} messages`);
    } else {
      logTest('User Messages Access', 'FAIL', 'Failed to access user messages');
    }
    
    // 11. Test Database Consistency
    console.log('\n🗄️ Testing Database Consistency...');
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // Check if user exists in database
    const [users] = await connection.execute('SELECT * FROM users WHERE email = ?', [testUser.email]);
    if (users.length > 0) {
      logTest('User Database Record', 'PASS', 'User properly stored in database');
    } else {
      logTest('User Database Record', 'FAIL', 'User not found in database');
    }
    
    // Check if orders exist in database
    const [orders] = await connection.execute('SELECT * FROM orders WHERE user_id = ?', [authData.user.id]);
    logTest('Order Database Records', 'PASS', `${orders.length} orders stored in database`);
    
    await connection.end();
    
  } catch (error) {
    logTest('User Journey Test', 'FAIL', error.response?.data?.error || error.message);
  }
  
  // Print final summary
  const endTime = Date.now();
  
  console.log('\n' + '=' * 60);
  console.log('📊 COMPREHENSIVE USER EXPERIENCE TEST SUMMARY');
  console.log('=' * 60);
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.test}: ${error.error}`);
    });
  }
  
  // Final assessment
  const successRate = (testResults.passed / testResults.total) * 100;
  console.log('\n🎯 USER EXPERIENCE ASSESSMENT:');
  
  if (successRate >= 95) {
    console.log('🟢 EXCELLENT - Complete user experience working perfectly');
  } else if (successRate >= 85) {
    console.log('🟡 GOOD - User experience mostly working, minor issues to address');
  } else {
    console.log('🔴 NEEDS WORK - Critical user experience issues need resolution');
  }
  
  console.log('\n🎉 Comprehensive user experience testing completed!');
}

// Run the test
testCompleteUserJourney().catch(console.error);
