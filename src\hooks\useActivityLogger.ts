import { useState, useCallback } from 'react';
import { useTranslation } from './useTranslation';

export interface ActivityLog {
  id: string;
  type: 'create' | 'update' | 'delete' | 'status_change' | 'custom_request';
  entity: 'system' | 'service' | 'user' | 'order' | 'custom_request';
  entityId: string;
  entityName: string;
  action: string;
  details?: string;
  timestamp: string;
  user: string;
  icon: string;
  color: string;
}

const ACTIVITY_STORAGE_KEY = 'khanfashariya_activity_logs';
const MAX_LOGS = 100; // Keep only last 100 activities

/**
 * Hook for comprehensive activity logging system
 * Tracks all CRUD operations and user interactions
 */
export const useActivityLogger = () => {
  const { language } = useTranslation();
  const [activities, setActivities] = useState<ActivityLog[]>([]);

  // Save activities to localStorage
  const saveActivities = useCallback((newActivities: ActivityLog[]) => {
    try {
      // Keep only the most recent activities
      const trimmedActivities = newActivities.slice(0, MAX_LOGS);
      localStorage.setItem(ACTIVITY_STORAGE_KEY, JSON.stringify(trimmedActivities));
      setActivities(trimmedActivities);
    } catch (error) {
      console.error('Failed to save activities:', error);
    }
  }, []);

  // Log a new activity
  const logActivity = useCallback((activity: Omit<ActivityLog, 'id' | 'timestamp'>) => {
    const newActivity: ActivityLog = {
      ...activity,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString()
    };

    setActivities(prev => {
      const updated = [newActivity, ...prev];
      saveActivities(updated);
      return updated.slice(0, MAX_LOGS);
    });
  }, [saveActivities]);

  // Predefined activity types for consistency
  const logSystemCreate = useCallback((systemId: string, systemName: string, user: string = 'Admin') => {
    logActivity({
      type: 'create',
      entity: 'system',
      entityId: systemId,
      entityName: systemName,
      action: language === 'ar' ? 'تم إنشاء نظام جديد' : 'Created new system',
      user,
      icon: 'Plus',
      color: 'green'
    });
  }, [logActivity, language]);

  const logSystemUpdate = useCallback((systemId: string, systemName: string, user: string = 'Admin') => {
    logActivity({
      type: 'update',
      entity: 'system',
      entityId: systemId,
      entityName: systemName,
      action: language === 'ar' ? 'تم تحديث النظام' : 'Updated system',
      user,
      icon: 'Edit',
      color: 'blue'
    });
  }, [logActivity, language]);

  const logSystemDelete = useCallback((systemId: string, systemName: string, user: string = 'Admin') => {
    logActivity({
      type: 'delete',
      entity: 'system',
      entityId: systemId,
      entityName: systemName,
      action: language === 'ar' ? 'تم حذف النظام' : 'Deleted system',
      user,
      icon: 'Trash2',
      color: 'red'
    });
  }, [logActivity, language]);

  const logCustomRequestCreate = useCallback((requestId: string, projectName: string, user: string = 'User') => {
    logActivity({
      type: 'custom_request',
      entity: 'custom_request',
      entityId: requestId,
      entityName: projectName,
      action: language === 'ar' ? 'تم إرسال طلب نظام مخصص' : 'Submitted custom system request',
      user,
      icon: 'FileText',
      color: 'purple'
    });
  }, [logActivity, language]);

  const logCustomRequestDelete = useCallback((requestId: string, projectName: string, user: string = 'Admin') => {
    logActivity({
      type: 'delete',
      entity: 'custom_request',
      entityId: requestId,
      entityName: projectName,
      action: language === 'ar' ? 'تم حذف طلب النظام المخصص' : 'Deleted custom system request',
      user,
      icon: 'Trash2',
      color: 'red'
    });
  }, [logActivity, language]);

  const logStatusChange = useCallback((
    entity: 'system' | 'service' | 'order',
    entityId: string,
    entityName: string,
    oldStatus: string,
    newStatus: string,
    user: string = 'Admin'
  ) => {
    logActivity({
      type: 'status_change',
      entity,
      entityId,
      entityName,
      action: language === 'ar' 
        ? `تم تغيير الحالة من ${oldStatus} إلى ${newStatus}`
        : `Status changed from ${oldStatus} to ${newStatus}`,
      user,
      icon: 'CheckCircle',
      color: 'yellow'
    });
  }, [logActivity, language]);

  // Get recent activities (for dashboard)
  const getRecentActivities = useCallback((limit: number = 10) => {
    return activities.slice(0, limit);
  }, [activities]);

  // Get activities by type
  const getActivitiesByType = useCallback((type: ActivityLog['type']) => {
    return activities.filter(activity => activity.type === type);
  }, [activities]);

  // Get activities by entity
  const getActivitiesByEntity = useCallback((entity: ActivityLog['entity']) => {
    return activities.filter(activity => activity.entity === entity);
  }, [activities]);

  // Clear all activities
  const clearActivities = useCallback(() => {
    localStorage.removeItem(ACTIVITY_STORAGE_KEY);
    setActivities([]);
  }, []);

  return {
    activities,
    logActivity,
    logSystemCreate,
    logSystemUpdate,
    logSystemDelete,
    logCustomRequestCreate,
    logCustomRequestDelete,
    logStatusChange,
    getRecentActivities,
    getActivitiesByType,
    getActivitiesByEntity,
    clearActivities
  };
};
