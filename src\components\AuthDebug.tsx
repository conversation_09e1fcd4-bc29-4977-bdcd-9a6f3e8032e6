import React, { useState, useEffect } from 'react';
import { hashPassword, comparePassword } from '../utils/crypto';
import { getAllUsers, signIn } from '../lib/apiServices';
// resetAdminPassword function removed - use API instead
import { Bug, Key, User, CheckCircle, XCircle } from 'lucide-react';

/**
 * Authentication Debug Component
 * للتحقق من مشاكل تسجيل الدخول
 */
const AuthDebug: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [testResults, setTestResults] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    runDebugTests();
  }, []);

  const runDebugTests = async () => {
    setLoading(true);
    const results: any = {};
    
    try {
      // 1. التحقق من المستخدمين في قاعدة البيانات
      const usersResult = await getAllUsers();
      results.usersInDB = usersResult.data?.length || 0;
      results.users = usersResult.data || [];
      
      // 2. البحث عن المستخدم الإداري
      const adminUser = usersResult.data?.find(u => u.email === '<EMAIL>');
      results.adminExists = !!adminUser;
      results.adminUser = adminUser;
      
      // 3. اختبار تشفير كلمة المرور
      const testPassword = 'admin123';
      const hashedPassword = await hashPassword(testPassword);
      results.testPasswordHash = hashedPassword;
      
      // 4. مقارنة كلمة المرور مع المحفوظة
      if (adminUser) {
        const passwordMatch = await comparePassword(testPassword, adminUser.password_hash);
        results.passwordMatch = passwordMatch;
        results.storedHash = adminUser.password_hash;
      }
      
      // 5. اختبار تسجيل الدخول
      try {
        const loginResult = await signIn('<EMAIL>', 'admin123');
        results.loginTest = {
          success: !loginResult.error,
          error: loginResult.error?.message,
          user: loginResult.data?.data?.user
        };
      } catch (error: any) {
        results.loginTest = {
          success: false,
          error: error.message,
          user: null
        };
      }
      
      // 6. التحقق من API بدلاً من localStorage
      try {
        const apiUsersResult = await getAllUsers();
        results.apiUsersExists = !apiUsersResult.error;
        results.apiUsersCount = apiUsersResult.data?.length || 0;
      } catch (e) {
        results.apiUsersError = true;
      }
      
    } catch (error: any) {
      results.error = error.message;
    }
    
    setTestResults(results);
    setLoading(false);
  };

  const resetDatabase = () => {
    // لا نحتاج لحذف localStorage بعد الآن - البيانات في MySQL
    alert('البيانات محفوظة في قاعدة البيانات MySQL ولا يمكن حذفها من هنا');
  };

  const resetAdminPasswordHandler = async () => {
    try {
      const result = await resetAdminPassword();
      if (result.success) {
        alert('تم إعادة تعيين كلمة مرور المدير بنجاح!');
        runDebugTests(); // إعادة تشغيل الاختبارات
      } else {
        alert('فشل في إعادة تعيين كلمة المرور: ' + result.error);
      }
    } catch (error) {
      alert('خطأ: ' + error);
    }
  };

  const createAdminManually = async () => {
    try {
      const hashedPassword = await hashPassword('admin123');
      const adminUser = {
        id: 'user_' + Date.now(),
        email: '<EMAIL>',
        username: 'admin',
        full_name: 'مدير النظام',
        role: 'admin',
        password_hash: hashedPassword,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // استخدام API لإنشاء المستخدم الإداري
      try {
        // يجب استخدام API endpoint لإنشاء المستخدم الإداري
        // هذا مجرد مكون تشخيص - في الواقع يجب استخدام API
        console.log('Admin user would be created via API:', adminUser);
      } catch (error) {
        console.error('Error creating admin user via API:', error);
      }
      
      alert('تم إنشاء المستخدم الإداري بنجاح! سيتم إعادة تحميل الصفحة.');
      window.location.reload();
    } catch (error) {
      alert('خطأ في إنشاء المستخدم: ' + error);
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center">
        <div className="bg-primary p-6 rounded-lg">
          <div className="text-white">جاري فحص نظام المصادقة...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-primary to-background border border-accent/30 rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white flex items-center">
              <Bug className="w-6 h-6 mr-2 text-red-400" />
              فحص نظام المصادقة
            </h2>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              إغلاق
            </button>
          </div>

          {/* Test Results */}
          <div className="space-y-4">
            {/* Database Users */}
            <div className="p-4 bg-primary/30 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                <User className="w-5 h-5 mr-2" />
                المستخدمين في قاعدة البيانات
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">عدد المستخدمين:</span>
                  <span className="text-white">{testResults.usersInDB}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">المستخدم الإداري موجود:</span>
                  <span className={testResults.adminExists ? 'text-green-400' : 'text-red-400'}>
                    {testResults.adminExists ? (
                      <CheckCircle className="w-4 h-4 inline" />
                    ) : (
                      <XCircle className="w-4 h-4 inline" />
                    )}
                    {testResults.adminExists ? ' نعم' : ' لا'}
                  </span>
                </div>
                {testResults.adminUser && (
                  <div className="mt-2 p-2 bg-black/20 rounded text-xs">
                    <div>البريد: {testResults.adminUser.email}</div>
                    <div>الاسم: {testResults.adminUser.username}</div>
                    <div>الدور: {testResults.adminUser.role}</div>
                  </div>
                )}
              </div>
            </div>

            {/* Password Testing */}
            <div className="p-4 bg-primary/30 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                <Key className="w-5 h-5 mr-2" />
                اختبار كلمة المرور
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">تشفير كلمة المرور الاختبارية:</span>
                  <span className="text-white font-mono text-xs">{testResults.testPasswordHash?.substring(0, 16)}...</span>
                </div>
                {testResults.storedHash && (
                  <div className="flex justify-between">
                    <span className="text-gray-400">كلمة المرور المحفوظة:</span>
                    <span className="text-white font-mono text-xs">{testResults.storedHash?.substring(0, 16)}...</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-400">تطابق كلمة المرور:</span>
                  <span className={testResults.passwordMatch ? 'text-green-400' : 'text-red-400'}>
                    {testResults.passwordMatch ? (
                      <CheckCircle className="w-4 h-4 inline" />
                    ) : (
                      <XCircle className="w-4 h-4 inline" />
                    )}
                    {testResults.passwordMatch ? ' نعم' : ' لا'}
                  </span>
                </div>
              </div>
            </div>

            {/* Login Test */}
            <div className="p-4 bg-primary/30 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-3">اختبار تسجيل الدخول</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">نتيجة تسجيل الدخول:</span>
                  <span className={testResults.loginTest?.success ? 'text-green-400' : 'text-red-400'}>
                    {testResults.loginTest?.success ? (
                      <CheckCircle className="w-4 h-4 inline" />
                    ) : (
                      <XCircle className="w-4 h-4 inline" />
                    )}
                    {testResults.loginTest?.success ? ' نجح' : ' فشل'}
                  </span>
                </div>
                {testResults.loginTest?.error && (
                  <div className="text-red-400 text-xs">
                    خطأ: {testResults.loginTest.error}
                  </div>
                )}
              </div>
            </div>

            {/* API Info */}
            <div className="p-4 bg-primary/30 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-3">معلومات API</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">API متاح:</span>
                  <span className={testResults.apiUsersExists ? 'text-green-400' : 'text-red-400'}>
                    {testResults.apiUsersExists ? 'نعم' : 'لا'}
                  </span>
                </div>
                {testResults.apiUsersCount && (
                  <div className="flex justify-between">
                    <span className="text-gray-400">المستخدمين في قاعدة البيانات:</span>
                    <span className="text-white">{testResults.apiUsersCount}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-6 grid grid-cols-2 gap-3">
            <button
              onClick={runDebugTests}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              إعادة الفحص
            </button>
            <button
              onClick={resetAdminPasswordHandler}
              className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
            >
              إعادة تعيين كلمة مرور المدير
            </button>
            <button
              onClick={createAdminManually}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              إنشاء مستخدم إداري جديد
            </button>
            <button
              onClick={resetDatabase}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
            >
              إعادة تعيين قاعدة البيانات
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthDebug;
