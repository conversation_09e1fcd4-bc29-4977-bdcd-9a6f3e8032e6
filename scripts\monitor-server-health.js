#!/usr/bin/env node

const http = require('http');
const https = require('https');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class ServerHealthMonitor {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      checks: [],
      overall: 'unknown',
      recommendations: []
    };
  }

  log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  async checkPort(port, name) {
    return new Promise((resolve) => {
      const server = http.createServer();
      
      server.listen(port, () => {
        server.close();
        resolve({ port, name, status: 'available', message: `Port ${port} is available` });
      });

      server.on('error', (err) => {
        if (err.code === 'EADDRINUSE') {
          resolve({ port, name, status: 'in_use', message: `Port ${port} is in use (${name} likely running)` });
        } else {
          resolve({ port, name, status: 'error', message: `Port ${port} error: ${err.message}` });
        }
      });
    });
  }

  async checkHTTPEndpoint(url, name, timeout = 5000) {
    return new Promise((resolve) => {
      const isHttps = url.startsWith('https');
      const client = isHttps ? https : http;
      
      const req = client.get(url, { timeout }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            url,
            name,
            status: 'success',
            statusCode: res.statusCode,
            message: `${name} responded with ${res.statusCode}`,
            responseTime: Date.now() - startTime
          });
        });
      });

      const startTime = Date.now();
      
      req.on('error', (err) => {
        resolve({
          url,
          name,
          status: 'error',
          message: `${name} error: ${err.message}`,
          responseTime: Date.now() - startTime
        });
      });

      req.on('timeout', () => {
        req.destroy();
        resolve({
          url,
          name,
          status: 'timeout',
          message: `${name} timed out after ${timeout}ms`,
          responseTime: timeout
        });
      });
    });
  }

  async checkNgrokTunnels() {
    return new Promise((resolve) => {
      http.get('http://127.0.0.1:4040/api/tunnels', (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            const tunnels = JSON.parse(data);
            resolve({
              status: 'success',
              tunnels: tunnels.tunnels || [],
              message: `Found ${tunnels.tunnels?.length || 0} active tunnels`
            });
          } catch (err) {
            resolve({
              status: 'error',
              message: `Failed to parse ngrok response: ${err.message}`
            });
          }
        });
      }).on('error', (err) => {
        resolve({
          status: 'error',
          message: `Ngrok API not accessible: ${err.message}`
        });
      });
    });
  }

  async checkSystemResources() {
    return new Promise((resolve) => {
      exec('wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /value', (error, stdout) => {
        if (error) {
          resolve({
            status: 'error',
            message: `Failed to get system resources: ${error.message}`
          });
          return;
        }

        try {
          const lines = stdout.split('\n').filter(line => line.includes('='));
          const memory = {};
          lines.forEach(line => {
            const [key, value] = line.split('=');
            if (key && value) {
              memory[key.trim()] = parseInt(value.trim());
            }
          });

          const totalMB = Math.round(memory.TotalVisibleMemorySize / 1024);
          const freeMB = Math.round(memory.FreePhysicalMemory / 1024);
          const usedMB = totalMB - freeMB;
          const usagePercent = Math.round((usedMB / totalMB) * 100);

          resolve({
            status: 'success',
            memory: {
              total: totalMB,
              used: usedMB,
              free: freeMB,
              usagePercent
            },
            message: `Memory usage: ${usagePercent}% (${usedMB}MB/${totalMB}MB)`
          });
        } catch (err) {
          resolve({
            status: 'error',
            message: `Failed to parse memory info: ${err.message}`
          });
        }
      });
    });
  }

  async runAllChecks() {
    this.log('\n🔍 Starting Server Health Check...', 'bright');
    this.log('=' * 50, 'blue');

    // Check ports
    this.log('\n📡 Checking Ports...', 'cyan');
    const portChecks = await Promise.all([
      this.checkPort(3001, 'Backend Server'),
      this.checkPort(5173, 'Frontend Server'),
      this.checkPort(4040, 'Ngrok Web Interface')
    ]);

    portChecks.forEach(check => {
      this.results.checks.push(check);
      const color = check.status === 'in_use' ? 'green' : 
                   check.status === 'available' ? 'yellow' : 'red';
      this.log(`  ${check.status === 'in_use' ? '✅' : check.status === 'available' ? '⚠️' : '❌'} ${check.message}`, color);
    });

    // Check HTTP endpoints
    this.log('\n🌐 Checking HTTP Endpoints...', 'cyan');
    const httpChecks = await Promise.all([
      this.checkHTTPEndpoint('http://localhost:3001/health', 'Backend Health'),
      this.checkHTTPEndpoint('http://localhost:5173', 'Frontend'),
      this.checkHTTPEndpoint('http://127.0.0.1:4040', 'Ngrok Interface')
    ]);

    httpChecks.forEach(check => {
      this.results.checks.push(check);
      const color = check.status === 'success' ? 'green' : 'red';
      this.log(`  ${check.status === 'success' ? '✅' : '❌'} ${check.message} (${check.responseTime}ms)`, color);
    });

    // Check ngrok tunnels
    this.log('\n🚇 Checking Ngrok Tunnels...', 'cyan');
    const ngrokCheck = await this.checkNgrokTunnels();
    this.results.checks.push(ngrokCheck);
    
    if (ngrokCheck.status === 'success') {
      this.log(`  ✅ ${ngrokCheck.message}`, 'green');
      if (ngrokCheck.tunnels && ngrokCheck.tunnels.length > 0) {
        ngrokCheck.tunnels.forEach(tunnel => {
          this.log(`    🔗 ${tunnel.name}: ${tunnel.public_url}`, 'blue');
        });
      }
    } else {
      this.log(`  ❌ ${ngrokCheck.message}`, 'red');
    }

    // Check system resources
    this.log('\n💻 Checking System Resources...', 'cyan');
    const resourceCheck = await this.checkSystemResources();
    this.results.checks.push(resourceCheck);
    
    if (resourceCheck.status === 'success') {
      const color = resourceCheck.memory.usagePercent > 80 ? 'red' : 
                   resourceCheck.memory.usagePercent > 60 ? 'yellow' : 'green';
      this.log(`  ${resourceCheck.memory.usagePercent > 80 ? '⚠️' : '✅'} ${resourceCheck.message}`, color);
    } else {
      this.log(`  ❌ ${resourceCheck.message}`, 'red');
    }

    this.generateRecommendations();
    this.printSummary();
    this.saveResults();
  }

  generateRecommendations() {
    const checks = this.results.checks;
    
    // Check if backend is not running
    const backendPort = checks.find(c => c.port === 3001);
    if (backendPort && backendPort.status !== 'in_use') {
      this.results.recommendations.push('Start the backend server: npm run start');
    }

    // Check if frontend is not running
    const frontendPort = checks.find(c => c.port === 5173);
    if (frontendPort && frontendPort.status !== 'in_use') {
      this.results.recommendations.push('Start the frontend server: npm run dev');
    }

    // Check if ngrok is not running
    const ngrokCheck = checks.find(c => c.tunnels !== undefined);
    if (ngrokCheck && ngrokCheck.status !== 'success') {
      this.results.recommendations.push('Start ngrok tunnels: npx ngrok start --config ngrok-config.yml --all');
    }

    // Check memory usage
    const memoryCheck = checks.find(c => c.memory !== undefined);
    if (memoryCheck && memoryCheck.memory && memoryCheck.memory.usagePercent > 80) {
      this.results.recommendations.push('High memory usage detected. Consider restarting applications or closing unnecessary programs.');
    }

    // Check for failed HTTP endpoints
    const failedEndpoints = checks.filter(c => c.url && c.status !== 'success');
    if (failedEndpoints.length > 0) {
      this.results.recommendations.push('Some endpoints are not responding. Check server logs and restart services if needed.');
    }
  }

  printSummary() {
    this.log('\n' + '=' * 50, 'bright');
    this.log('📋 HEALTH CHECK SUMMARY', 'bright');
    this.log('=' * 50, 'bright');

    const totalChecks = this.results.checks.length;
    const successfulChecks = this.results.checks.filter(c => 
      c.status === 'success' || c.status === 'in_use'
    ).length;
    
    const healthPercent = Math.round((successfulChecks / totalChecks) * 100);
    this.results.overall = healthPercent >= 80 ? 'healthy' : healthPercent >= 60 ? 'warning' : 'critical';
    
    const color = this.results.overall === 'healthy' ? 'green' : 
                 this.results.overall === 'warning' ? 'yellow' : 'red';
    
    this.log(`Overall Health: ${healthPercent}% (${this.results.overall.toUpperCase()})`, color);
    this.log(`Successful Checks: ${successfulChecks}/${totalChecks}`, 'blue');

    if (this.results.recommendations.length > 0) {
      this.log('\n💡 Recommendations:', 'yellow');
      this.results.recommendations.forEach((rec, index) => {
        this.log(`  ${index + 1}. ${rec}`, 'yellow');
      });
    }
  }

  saveResults() {
    const resultsFile = path.join(__dirname, '..', 'server-health-report.json');
    fs.writeFileSync(resultsFile, JSON.stringify(this.results, null, 2));
    this.log(`\n💾 Health report saved to: ${resultsFile}`, 'blue');
  }
}

// Main execution
async function main() {
  const monitor = new ServerHealthMonitor();
  await monitor.runAllChecks();
  
  // Exit with appropriate code
  const exitCode = monitor.results.overall === 'critical' ? 1 : 0;
  process.exit(exitCode);
}

if (require.main === module) {
  main().catch(error => {
    console.error(`❌ Fatal error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = ServerHealthMonitor;
