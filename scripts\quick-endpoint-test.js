/**
 * Quick Endpoint Test
 * 
 * This script quickly tests the admin endpoints without authentication
 */

const axios = require('axios');
require('dotenv').config();

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';

async function quickEndpointTest() {
  console.log('⚡ Quick Endpoint Test...\n');
  
  try {
    // Test public systems endpoint
    console.log('1️⃣ Testing public systems endpoint...');
    try {
      const systemsResponse = await axios.get(`${BASE_URL}/api/systems`);
      console.log('✅ Public systems endpoint working');
      console.log(`📊 Response format: ${typeof systemsResponse.data}`);
      console.log(`📊 Has data: ${systemsResponse.data.data ? 'Yes' : 'No'}`);
      if (systemsResponse.data.data && systemsResponse.data.data.systems) {
        console.log(`📊 Systems count: ${systemsResponse.data.data.systems.length}`);
      }
    } catch (error) {
      console.error('❌ Public systems endpoint failed:', error.response?.status, error.response?.statusText);
    }
    
    // Test public services endpoint
    console.log('\n2️⃣ Testing public services endpoint...');
    try {
      const servicesResponse = await axios.get(`${BASE_URL}/api/services/technical`);
      console.log('✅ Public services endpoint working');
      console.log(`📊 Response format: ${typeof servicesResponse.data}`);
      console.log(`📊 Has data: ${servicesResponse.data.data ? 'Yes' : 'No'}`);
      if (servicesResponse.data.data && servicesResponse.data.data.services) {
        console.log(`📊 Services count: ${servicesResponse.data.data.services.length}`);
      }
    } catch (error) {
      console.error('❌ Public services endpoint failed:', error.response?.status, error.response?.statusText);
    }
    
    // Test server health
    console.log('\n3️⃣ Testing server health...');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/api/health`);
      console.log('✅ Server health endpoint working');
    } catch (error) {
      // Health endpoint might not exist, that's ok
      console.log('ℹ️  Health endpoint not available (normal)');
    }
    
    console.log('\n📋 Summary:');
    console.log('- Server is responding');
    console.log('- Public endpoints are accessible');
    console.log('- Ready for admin endpoint testing');
    
  } catch (error) {
    console.error('❌ Quick test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure server is running: npm run dev:server');
    console.log('2. Check if port 3001 is correct');
    console.log('3. Verify database connection');
  }
}

// Run if called directly
if (require.main === module) {
  quickEndpointTest();
}

module.exports = { quickEndpointTest };