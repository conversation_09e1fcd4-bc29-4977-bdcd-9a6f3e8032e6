/**
 * Data Source Indicator Component
 * 
 * Shows which data source is currently being used (API vs localStorage)
 * Only visible in development mode.
 */

import React from 'react';
import { Database, Server, Wifi, WifiOff } from 'lucide-react';
import { getDataSourceStatus } from '../config/dataSource';
import dataAdapter from '../lib/dataAdapter';

const DataSourceIndicator: React.FC = () => {
  const status = getDataSourceStatus();
  const currentSource = dataAdapter.getDataSource();
  
  // Only show in development
  if (import.meta.env.NODE_ENV !== 'development') {
    return null;
  }

  const isApi = currentSource === 'api';
  
  return (
    <div className={`fixed bottom-4 left-4 z-50 px-3 py-2 rounded-lg shadow-lg text-xs font-mono ${
      isApi 
        ? 'bg-green-100 text-green-800 border border-green-300' 
        : 'bg-yellow-100 text-yellow-800 border border-yellow-300'
    }`}>
      <div className="flex items-center space-x-2">
        {isApi ? (
          <>
            <Server className="w-3 h-3" />
            <Wifi className="w-3 h-3" />
            <span>API</span>
          </>
        ) : (
          <>
            <Database className="w-3 h-3" />
            <WifiOff className="w-3 h-3" />
            <span>localStorage</span>
          </>
        )}
      </div>
      <div className="text-xs opacity-75 mt-1">
        {status.apiBaseURL}
      </div>
    </div>
  );
};

export default DataSourceIndicator;
