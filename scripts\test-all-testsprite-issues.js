#!/usr/bin/env node

/**
 * اختبار جميع مشاكل TestSprite المحددة
 */

const axios = require('axios');
const fs = require('fs');

// قراءة الروابط من ملف التكوين
let baseUrl = 'http://localhost:3001';
let frontendUrl = 'http://localhost:5173';

try {
  const config = JSON.parse(fs.readFileSync('current-ngrok-urls.json', 'utf8'));
  if (config.backend && config.backend.url && config.backend.url !== 'غير متاح') {
    baseUrl = config.backend.url;
  }
  if (config.frontend && config.frontend.url && config.frontend.url !== 'غير متاح') {
    frontendUrl = config.frontend.url;
  }
} catch (error) {
  console.log('⚠️ استخدام الروابط المحلية');
}

console.log('🧪 اختبار جميع مشاكل TestSprite');
console.log('🔗 Backend:', baseUrl);
console.log('🌐 Frontend:', frontendUrl);
console.log('=' .repeat(60));

// إعداد axios
const api = axios.create({
  baseURL: baseUrl,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'ngrok-skip-browser-warning': 'true'
  }
});

async function test(name, testFn) {
  try {
    console.log(`🔍 ${name}...`);
    await testFn();
    console.log(`✅ ${name} - نجح`);
    return true;
  } catch (error) {
    console.log(`❌ ${name} - فشل:`, error.message);
    return false;
  }
}

async function main() {
  console.log('\n📋 اختبار الإصلاحات:\n');
  
  let passedTests = 0;
  let totalTests = 0;

  // 1. اختبار empty email login (should return 401)
  totalTests++;
  if (await test('Empty Email Login (should return 401)', async () => {
    try {
      const response = await api.post('/api/auth/login', {
        email: '',
        password: 'test123'
      });
      throw new Error('Expected 401 but got success');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        return; // نجح الاختبار
      }
      throw new Error(`Expected 401 but got ${error.response?.status || 'network error'}`);
    }
  })) passedTests++;

  // 2. اختبار password length validation
  totalTests++;
  if (await test('Password Length Validation (should return 400 for long password)', async () => {
    const longPassword = 'a'.repeat(200); // 200 characters
    try {
      const response = await api.post('/api/auth/register', {
        email: '<EMAIL>',
        username: 'testuser',
        full_name: 'Test User',
        password: longPassword
      });
      throw new Error('Expected 400 but got success');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        return; // نجح الاختبار
      }
      throw new Error(`Expected 400 but got ${error.response?.status || 'network error'}`);
    }
  })) passedTests++;

  // 3. اختبار registration endpoint
  totalTests++;
  if (await test('Registration Endpoint Available', async () => {
    try {
      const response = await api.post('/api/auth/register', {
        email: 'testsprite_' + Date.now() + '@test.com',
        username: 'testsprite_' + Date.now(),
        full_name: 'TestSprite User',
        password: 'testpass123'
      });
      
      if (response.data.success) {
        return; // نجح التسجيل
      }
      throw new Error('Registration failed');
    } catch (error) {
      // قد يفشل بسبب وجود المستخدم، لكن المهم أن endpoint متاح
      if (error.response && (error.response.status === 400 || error.response.status === 409)) {
        return; // endpoint متاح
      }
      throw error;
    }
  })) passedTests++;

  // 4. اختبار login/logout session handling
  totalTests++;
  if (await test('Login/Logout Session Handling', async () => {
    // تسجيل دخول
    const loginResponse = await api.post('/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (!loginResponse.data.success) {
      throw new Error('Login failed');
    }
    
    const { accessToken, refreshToken } = loginResponse.data.data.tokens;
    
    // تسجيل خروج
    const logoutResponse = await api.post('/api/auth/logout', {
      refreshToken
    });
    
    if (!logoutResponse.data.success) {
      throw new Error('Logout failed');
    }
    
    // محاولة استخدام token بعد logout (يجب أن تفشل)
    try {
      await api.get('/api/users/profile', {
        headers: { Authorization: `Bearer ${accessToken}` }
      });
      throw new Error('Token should be invalid after logout');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        return; // نجح الاختبار
      }
      throw error;
    }
  })) passedTests++;

  // 5. اختبار frontend pages availability
  totalTests++;
  if (await test('Frontend Pages Availability', async () => {
    const frontendApi = axios.create({
      timeout: 10000,
      headers: {
        'ngrok-skip-browser-warning': 'true'
      }
    });
    
    // اختبار الصفحة الرئيسية
    const homeResponse = await frontendApi.get(frontendUrl + '/');
    if (homeResponse.status !== 200) {
      throw new Error('Home page not accessible');
    }
    
    // اختبار صفحة التسجيل
    const registerResponse = await frontendApi.get(frontendUrl + '/register');
    if (registerResponse.status !== 200) {
      throw new Error('Register page not accessible');
    }
    
    // اختبار صفحة تسجيل الدخول
    const loginResponse = await frontendApi.get(frontendUrl + '/login');
    if (loginResponse.status !== 200) {
      throw new Error('Login page not accessible');
    }
  })) passedTests++;

  // 6. اختبار systems and services endpoints (public access)
  totalTests++;
  if (await test('Public Systems and Services Access', async () => {
    // اختبار systems
    const systemsResponse = await api.get('/api/systems');
    if (!systemsResponse.data.success) {
      throw new Error('Systems endpoint failed');
    }
    
    // اختبار technical services
    const servicesResponse = await api.get('/api/services/technical');
    if (!servicesResponse.data.success) {
      throw new Error('Technical services endpoint failed');
    }
  })) passedTests++;

  // 7. اختبار TestSprite compatible endpoints
  totalTests++;
  if (await test('TestSprite Compatible Endpoints', async () => {
    // اختبار systems list
    const systemsListResponse = await api.get('/api/systems/list');
    if (!Array.isArray(systemsListResponse.data)) {
      throw new Error('Systems list should return array');
    }
    
    // اختبار services list
    const servicesListResponse = await api.get('/api/services/list');
    if (!servicesListResponse.data.services || !Array.isArray(servicesListResponse.data.services)) {
      throw new Error('Services list should return {services: []}');
    }
  })) passedTests++;

  console.log('\n🎉 انتهى الاختبار!');
  console.log('=' .repeat(60));
  console.log(`📊 النتائج: ${passedTests}/${totalTests} اختبار نجح`);
  
  if (passedTests === totalTests) {
    console.log('🎯 جميع الاختبارات نجحت! جاهز لـ TestSprite');
  } else {
    console.log('⚠️ بعض الاختبارات فشلت، يحتاج مراجعة');
  }
}

main().catch(error => {
  console.error('❌ خطأ في الاختبار:', error.message);
  process.exit(1);
});
