#!/usr/bin/env node

/**
 * Simple endpoint testing
 */

const http = require('http');

function testEndpoint(path, description) {
  return new Promise((resolve, reject) => {
    console.log(`🧪 Testing: ${description}`);
    
    http.get(`http://localhost:3001${path}`, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (result.success) {
            console.log(`✅ PASSED: ${description}`);
            resolve(result);
          } else {
            console.log(`❌ FAILED: ${description} - ${result.error || 'Unknown error'}`);
            resolve(null);
          }
        } catch (error) {
          console.log(`❌ FAILED: ${description} - Invalid JSON response`);
          console.log('Response:', data.substring(0, 200));
          resolve(null);
        }
      });
    }).on('error', err => {
      console.log(`❌ FAILED: ${description} - ${err.message}`);
      resolve(null);
    });
  });
}

async function runTests() {
  console.log('🚀 Testing Khanfashariya API Endpoints...\n');
  
  // Test basic endpoints
  await testEndpoint('/health', 'Health Check');
  await testEndpoint('/api/status', 'API Status');
  await testEndpoint('/', 'Root Route');
  
  console.log('\n📦 Testing System Services...');
  await testEndpoint('/api/systems', 'Get System Services');
  await testEndpoint('/api/systems/categories', 'Get System Categories');
  
  console.log('\n🛠️ Testing Technical Services...');
  await testEndpoint('/api/services/technical', 'Get Technical Services');
  await testEndpoint('/api/services/premium', 'Get Premium Content');
  await testEndpoint('/api/services/packages', 'Get Premium Packages');
  
  console.log('\n🎉 Testing completed!');
}

runTests().catch(console.error);
