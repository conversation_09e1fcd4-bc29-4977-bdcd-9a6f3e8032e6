import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './hooks/useAuth';
import { useTranslation } from './hooks/useTranslation';
import Layout from './components/Layout';
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import DashboardPage from './pages/DashboardPage';
import { NotificationProvider } from './hooks/useNotification';
import ErrorBoundary from './components/ErrorBoundary';
import { FullPageLoading } from './components/ui/LoadingStates';
import ScrollToTop from './components/ui/ScrollToTop';
import DataSourceIndicator from './components/DataSourceIndicator';


import { applyTheme, defaultTheme } from './styles/theme';
import { initializePerformanceMonitoring} from './utils/performance';
import { setupSmartPreloading, preloadCriticalComponents  } from './utils/lazyComponents';

function App() {
  const { loading } = useAuth();
  const { t } = useTranslation();

  // Apply theme on mount
  React.useEffect(() => {
    applyTheme(defaultTheme);
  }, []);

  // Initialize performance monitoring
  React.useEffect(() => {
    initializePerformanceMonitoring();
    setupSmartPreloading();
    preloadCriticalComponents();
  }, []);



  // Show enhanced loading screen
  if (loading) {
    return (
      <FullPageLoading
        text="جاري تحميل خان فشارية..."
        textEn="Loading Khanfashariya..."
        showLogo={true}
      />
    );
  }

  return (
    <ErrorBoundary
      showDetails={process.env.NODE_ENV === 'development'}
      onError={(error, errorInfo) => {
        // Log error to console in development
        if (process.env.NODE_ENV === 'development') {
          console.error('App Error:', error, errorInfo);
        }
        // In production, you would send this to an error reporting service
      }}
    >
      <NotificationProvider>
        <Router>
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={
              <Layout>
                <HomePage />
                <ScrollToTop />
                <DataSourceIndicator />
              </Layout>
            } />

            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />

            {/* Protected Routes */}
            <Route path="/dashboard" element={<DashboardPage />} />

            {/* Development/Testing Routes */}
            {process.env.NODE_ENV === 'development' && (
              <Route path="/test-systems" element={
                <React.Suspense fallback={<div>Loading...</div>}>
                  {React.createElement(React.lazy(() => import('./components/admin/SystemsTest')))}
                </React.Suspense>
              } />
            )}

            {/* Redirect unknown routes to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Router>
      </NotificationProvider>
    </ErrorBoundary>
  );
}

export default App;