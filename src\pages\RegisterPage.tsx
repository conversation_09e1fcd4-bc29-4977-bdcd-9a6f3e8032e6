import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useNotification } from '../hooks/useNotification';
import { signUp } from '../lib/apiServices';
import { useNavigate } from 'react-router-dom';
import {
  Lock,
  User,
  Eye,
  EyeOff,
  Shield,
  Mail,
  UserPlus,
  Sword,
  Target,
  Star
} from 'lucide-react';

const RegisterPage: React.FC = () => {
  const { t, language } = useTranslation();
  const { showNotification } = useNotification();
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    username: '',
    full_name: '',
    password: '',
    confirmPassword: ''
  });

  const isRTL = language === 'ar';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Validation
    if (formData.password !== formData.confirmPassword) {
      showNotification({
        type: 'error',
        title: t('common.validationError'),
        message: t('auth.passwordMismatch', 'كلمات المرور غير متطابقة')
      });
      setIsLoading(false);
      return;
    }

    if (formData.password.length < 6) {
      showNotification({
        type: 'error',
        title: t('common.validationError'),
        message: t('auth.passwordTooShort', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل')
      });
      setIsLoading(false);
      return;
    }

    if (formData.password.length > 128) {
      showNotification({
        type: 'error',
        title: t('common.validationError'),
        message: t('auth.passwordTooLong', 'كلمة المرور طويلة جداً (الحد الأقصى 128 حرف)')
      });
      setIsLoading(false);
      return;
    }

    try {
      const { error } = await signUp(formData.email, formData.password, {
        username: formData.username,
        full_name: formData.full_name
      });
      
      if (error) {
        showNotification({
          type: 'error',
          title: t('common.registrationError'),
          message: error.message
        });
      } else {
        showNotification({
          type: 'success',
          title: t('common.success'),
          message: t('notifications.accountCreateSuccess', 'تم إنشاء الحساب بنجاح')
        });
        // Redirect to login
        navigate('/login');
      }
    } catch (error) {
      showNotification({
        type: 'error',
        title: t('common.registrationError'),
        message: t('common.unexpectedError')
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-purple-500 to-cyan-500 p-3 rounded-full">
              <UserPlus className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">
            {t('auth.signUp', 'إنشاء حساب جديد')}
          </h1>
          <p className="text-slate-400">
            {t('auth.signUpSubtitle', 'انضم إلينا واحصل على أفضل الخدمات التقنية')}
          </p>
        </div>

        {/* Register Form */}
        <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8 shadow-2xl">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Full Name Field */}
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                {t('auth.fullName', 'الاسم الكامل')}
              </label>
              <div className="relative">
                <User className={`absolute top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 ${isRTL ? 'right-3' : 'left-3'}`} />
                <input
                  type="text"
                  name="full_name"
                  value={formData.full_name}
                  onChange={handleInputChange}
                  className={`w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ${isRTL ? 'pr-12 text-right' : 'pl-12'}`}
                  placeholder={t('auth.fullNamePlaceholder', 'أدخل اسمك الكامل')}
                  required
                />
              </div>
            </div>

            {/* Username Field */}
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                {t('auth.username', 'اسم المستخدم')}
              </label>
              <div className="relative">
                <User className={`absolute top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 ${isRTL ? 'right-3' : 'left-3'}`} />
                <input
                  type="text"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  className={`w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ${isRTL ? 'pr-12 text-right' : 'pl-12'}`}
                  placeholder={t('auth.usernamePlaceholder', 'أدخل اسم المستخدم')}
                  required
                />
              </div>
            </div>

            {/* Email Field */}
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                {t('auth.email', 'البريد الإلكتروني')}
              </label>
              <div className="relative">
                <Mail className={`absolute top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 ${isRTL ? 'right-3' : 'left-3'}`} />
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ${isRTL ? 'pr-12 text-right' : 'pl-12'}`}
                  placeholder={t('auth.emailPlaceholder', 'أدخل بريدك الإلكتروني')}
                  required
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                {t('auth.password', 'كلمة المرور')}
              </label>
              <div className="relative">
                <Lock className={`absolute top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 ${isRTL ? 'right-3' : 'left-3'}`} />
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className={`w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ${isRTL ? 'pr-12 pl-12 text-right' : 'pl-12 pr-12'}`}
                  placeholder={t('auth.passwordPlaceholder', 'أدخل كلمة المرور')}
                  required
                  minLength={6}
                  maxLength={128}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className={`absolute top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors ${isRTL ? 'left-3' : 'right-3'}`}
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* Confirm Password Field */}
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                {t('auth.confirmPassword', 'تأكيد كلمة المرور')}
              </label>
              <div className="relative">
                <Lock className={`absolute top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 ${isRTL ? 'right-3' : 'left-3'}`} />
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className={`w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ${isRTL ? 'pr-12 pl-12 text-right' : 'pl-12 pr-12'}`}
                  placeholder={t('auth.confirmPasswordPlaceholder', 'أعد إدخال كلمة المرور')}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className={`absolute top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors ${isRTL ? 'left-3' : 'right-3'}`}
                >
                  {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-2"
            >
              {isLoading ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <>
                  <UserPlus className="w-5 h-5" />
                  {t('auth.signUp', 'إنشاء حساب')}
                </>
              )}
            </button>
          </form>

          {/* Login Link */}
          <div className="mt-6 text-center">
            <p className="text-slate-400">
              {t('auth.hasAccount', 'لديك حساب بالفعل؟')}{' '}
              <button
                onClick={() => navigate('/login')}
                className="text-purple-400 hover:text-purple-300 font-medium transition-colors"
              >
                {t('auth.signIn', 'تسجيل الدخول')}
              </button>
            </p>
          </div>
        </div>

        {/* Features */}
        <div className="mt-8 grid grid-cols-3 gap-4 text-center">
          <div className="text-slate-400">
            <Sword className="w-6 h-6 mx-auto mb-2 text-purple-400" />
            <p className="text-xs">{t('features.gaming', 'ألعاب')}</p>
          </div>
          <div className="text-slate-400">
            <Target className="w-6 h-6 mx-auto mb-2 text-cyan-400" />
            <p className="text-xs">{t('features.precision', 'دقة')}</p>
          </div>
          <div className="text-slate-400">
            <Star className="w-6 h-6 mx-auto mb-2 text-yellow-400" />
            <p className="text-xs">{t('features.premium', 'مميز')}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
