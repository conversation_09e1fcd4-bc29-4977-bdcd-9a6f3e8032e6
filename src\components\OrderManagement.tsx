import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuth } from '../hooks/useAuth';
import { 
  getAllOrders,
  updateOrderStatus,
  getAllUsers,
} from '../lib/apiServices';
import {
  UserService,
  User
} from '../lib/database';
import { 
  ShoppingCart, 
  Search, 
  Filter,
  Calendar,
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Eye,
  Edit3,
  Download,
  RefreshCw,
  TrendingUp,
  Users,
  Package
} from 'lucide-react';

interface OrderManagementProps {
  onClose: () => void;
}

interface OrderStats {
  total: number;
  pending: number;
  active: number;
  completed: number;
  cancelled: number;
  totalRevenue: number;
}

const OrderManagement: React.FC<OrderManagementProps> = ({ onClose }) => {
  const { t, language } = useTranslation();
  const { userProfile } = useAuth();
  const [orders, setOrders] = useState<UserService[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | UserService['status']>('all');
  const [dateFilter, setDateFilter] = useState<'all' | 'today' | 'week' | 'month'>('all');
  const [selectedOrder, setSelectedOrder] = useState<UserService | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [stats, setStats] = useState<OrderStats>({
    total: 0,
    pending: 0,
    active: 0,
    completed: 0,
    cancelled: 0,
    totalRevenue: 0
  });

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    calculateStats();
  }, [orders]);

  const loadData = async () => {
    setLoading(true);
    try {
      const [ordersResult, usersResult] = await Promise.all([
        getAllOrders(),
        getAllUsers()
      ]);
      
      if (ordersResult.data) {
        // Convert orders to services format for compatibility
        const servicesFromOrders = ordersResult.data.map(order => ({
          id: order.id,
          user_id: order.user_id,
          service_name: order.service_name || order.item_name_en,
          service_type: order.order_type,
          status: order.status,
          purchase_date: order.created_at,
          completion_date: order.updated_at,
          price: order.final_price,
          notes: order.notes
        }));
        setOrders(servicesFromOrders);
      }
      if (usersResult.data) setUsers(usersResult.data);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = () => {
    const newStats: OrderStats = {
      total: orders.length,
      pending: orders.filter(o => o.status === 'pending').length,
      active: orders.filter(o => o.status === 'active').length,
      completed: orders.filter(o => o.status === 'completed').length,
      cancelled: orders.filter(o => o.status === 'cancelled').length,
      totalRevenue: orders.filter(o => o.status === 'completed').reduce((sum, o) => sum + o.price, 0)
    };
    setStats(newStats);
  };

  const handleStatusChange = async (orderId: string, status: UserService['status']) => {
    try {
      const result = await updateOrderStatus(orderId, status);
      if (result.data) {
        setOrders(orders.map(order => 
          order.id === orderId ? { ...order, status } : order
        ));
      }
    } catch (error) {
      console.error('Error updating order status:', error);
    }
  };

  const getUserById = (userId: string) => {
    return users.find(user => user.id === userId);
  };

  const filterOrdersByDate = (order: UserService) => {
    if (dateFilter === 'all') return true;
    
    const orderDate = new Date(order.purchase_date);
    const now = new Date();
    
    switch (dateFilter) {
      case 'today':
        return orderDate.toDateString() === now.toDateString();
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        return orderDate >= weekAgo;
      case 'month':
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        return orderDate >= monthAgo;
      default:
        return true;
    }
  };

  const filteredOrders = orders.filter(order => {
    const user = getUserById(order.user_id);
    const matchesSearch = order.service_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.service_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (user && (user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                  user.email.toLowerCase().includes(searchTerm.toLowerCase())));
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    const matchesDate = filterOrdersByDate(order);
    
    return matchesSearch && matchesStatus && matchesDate;
  });

  const getStatusIcon = (status: UserService['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'active':
        return <Clock className="w-5 h-5 text-blue-500" />;
      case 'pending':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: UserService['status']) => {
    const statusMap = {
      'completed': language === 'ar' ? 'مكتمل' : 'Completed',
      'active': language === 'ar' ? 'نشط' : 'Active',
      'pending': language === 'ar' ? 'في الانتظار' : 'Pending',
      'cancelled': language === 'ar' ? 'ملغي' : 'Cancelled'
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status: UserService['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'active':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'cancelled':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  if (!userProfile || userProfile.role !== 'admin') {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-primary rounded-lg p-6 max-w-md w-full mx-4">
          <div className="text-center">
            <ShoppingCart className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-white mb-2">
              {language === 'ar' ? 'غير مصرح' : 'Unauthorized'}
            </h3>
            <p className="text-gray-300 mb-4">
              {language === 'ar' ? 'ليس لديك صلاحية للوصول إلى هذه الصفحة' : 'You do not have permission to access this page'}
            </p>
            <button
              onClick={onClose}
              className="bg-secondary text-primary px-4 py-2 rounded-lg hover:bg-secondary/80 transition-colors"
            >
              {language === 'ar' ? 'إغلاق' : 'Close'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  React.useEffect(() => {
    document.body.classList.add('modal-open');
    return () => {
      document.body.classList.remove('modal-open');
    };
  }, []);

  return (
    <div className="modal-backdrop flex items-center justify-center p-4"
         onClick={(e) => e.target === e.currentTarget && onClose()}>
      <div className="bg-primary rounded-lg max-w-7xl w-full max-h-[95vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-secondary to-accent p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <ShoppingCart className="w-8 h-8 text-primary" />
              <div>
                <h2 className="text-2xl font-bold text-primary">
                  {language === 'ar' ? 'إدارة الطلبات' : 'Order Management'}
                </h2>
                <p className="text-primary/70">
                  {language === 'ar' ? 'إدارة ومتابعة طلبات العملاء' : 'Manage and track customer orders'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <button
                onClick={loadData}
                className="p-2 bg-primary/20 text-primary rounded-lg hover:bg-primary/30 transition-colors"
                title={language === 'ar' ? 'تحديث' : 'Refresh'}
              >
                <RefreshCw className="w-5 h-5" />
              </button>
              <button
                onClick={onClose}
                className="text-primary hover:text-primary/70 transition-colors"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="p-6 border-b border-gray-700">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="bg-background rounded-lg p-4 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">{language === 'ar' ? 'إجمالي الطلبات' : 'Total Orders'}</p>
                  <p className="text-2xl font-bold text-white">{stats.total}</p>
                </div>
                <Package className="w-8 h-8 text-secondary" />
              </div>
            </div>
            
            <div className="bg-background rounded-lg p-4 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">{language === 'ar' ? 'في الانتظار' : 'Pending'}</p>
                  <p className="text-2xl font-bold text-yellow-400">{stats.pending}</p>
                </div>
                <AlertCircle className="w-8 h-8 text-yellow-400" />
              </div>
            </div>
            
            <div className="bg-background rounded-lg p-4 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">{language === 'ar' ? 'نشط' : 'Active'}</p>
                  <p className="text-2xl font-bold text-blue-400">{stats.active}</p>
                </div>
                <Clock className="w-8 h-8 text-blue-400" />
              </div>
            </div>
            
            <div className="bg-background rounded-lg p-4 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">{language === 'ar' ? 'مكتمل' : 'Completed'}</p>
                  <p className="text-2xl font-bold text-green-400">{stats.completed}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-400" />
              </div>
            </div>
            
            <div className="bg-background rounded-lg p-4 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">{language === 'ar' ? 'ملغي' : 'Cancelled'}</p>
                  <p className="text-2xl font-bold text-red-400">{stats.cancelled}</p>
                </div>
                <XCircle className="w-8 h-8 text-red-400" />
              </div>
            </div>
            
            <div className="bg-background rounded-lg p-4 border border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">{language === 'ar' ? 'إجمالي الإيرادات' : 'Total Revenue'}</p>
                  <p className="text-2xl font-bold text-secondary">${stats.totalRevenue}</p>
                </div>
                <TrendingUp className="w-8 h-8 text-secondary" />
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder={language === 'ar' ? 'البحث في الطلبات...' : 'Search orders...'}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-background border border-gray-600 rounded-lg pl-10 rtl:pr-10 rtl:pl-4 pr-4 py-2 text-white placeholder-gray-400 focus:border-secondary focus:outline-none"
              />
            </div>
            
            <div className="flex gap-4">
              <div className="relative">
                <Filter className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as 'all' | UserService['status'])}
                  className="bg-background border border-gray-600 rounded-lg pl-10 rtl:pr-10 rtl:pl-4 pr-8 py-2 text-white focus:border-secondary focus:outline-none appearance-none min-w-[140px]"
                >
                  <option value="all">{language === 'ar' ? 'جميع الحالات' : 'All Status'}</option>
                  <option value="pending">{language === 'ar' ? 'في الانتظار' : 'Pending'}</option>
                  <option value="active">{language === 'ar' ? 'نشط' : 'Active'}</option>
                  <option value="completed">{language === 'ar' ? 'مكتمل' : 'Completed'}</option>
                  <option value="cancelled">{language === 'ar' ? 'ملغي' : 'Cancelled'}</option>
                </select>
              </div>
              
              <div className="relative">
                <Calendar className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value as 'all' | 'today' | 'week' | 'month')}
                  className="bg-background border border-gray-600 rounded-lg pl-10 rtl:pr-10 rtl:pl-4 pr-8 py-2 text-white focus:border-secondary focus:outline-none appearance-none min-w-[140px]"
                >
                  <option value="all">{language === 'ar' ? 'جميع التواريخ' : 'All Time'}</option>
                  <option value="today">{language === 'ar' ? 'اليوم' : 'Today'}</option>
                  <option value="week">{language === 'ar' ? 'هذا الأسبوع' : 'This Week'}</option>
                  <option value="month">{language === 'ar' ? 'هذا الشهر' : 'This Month'}</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="w-8 h-8 border-4 border-secondary border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredOrders.map((order) => {
                const user = getUserById(order.user_id);
                return (
                  <div key={order.id} className="bg-background rounded-lg border border-gray-700 p-4 hover:border-secondary/50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 rtl:space-x-reverse flex-1">
                        <div className="w-12 h-12 bg-gradient-to-br from-secondary to-accent rounded-lg flex items-center justify-center">
                          <Package className="w-6 h-6 text-blue-400" />
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
                            <h3 className="text-lg font-semibold text-white">{order.service_name}</h3>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(order.status)}`}>
                              {getStatusIcon(order.status)}
                              <span className="ml-1 rtl:mr-1 rtl:ml-0">{getStatusText(order.status)}</span>
                            </span>
                          </div>
                          
                          <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-400">
                            <span className="flex items-center space-x-1 rtl:space-x-reverse">
                              <Users className="w-4 h-4" />
                              <span>{user ? user.full_name : 'Unknown User'}</span>
                            </span>
                            <span className="flex items-center space-x-1 rtl:space-x-reverse">
                              <Calendar className="w-4 h-4" />
                              <span>{new Date(order.purchase_date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}</span>
                            </span>
                            <span className="flex items-center space-x-1 rtl:space-x-reverse">
                              <DollarSign className="w-4 h-4" />
                              <span className="text-secondary font-semibold">${order.price}</span>
                            </span>
                          </div>
                          
                          <p className="text-sm text-gray-500 mt-1">{order.service_type}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <select
                          value={order.status}
                          onChange={(e) => handleStatusChange(order.id, e.target.value as UserService['status'])}
                          className="bg-primary border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:border-secondary focus:outline-none"
                        >
                          <option value="pending">{language === 'ar' ? 'في الانتظار' : 'Pending'}</option>
                          <option value="active">{language === 'ar' ? 'نشط' : 'Active'}</option>
                          <option value="completed">{language === 'ar' ? 'مكتمل' : 'Completed'}</option>
                          <option value="cancelled">{language === 'ar' ? 'ملغي' : 'Cancelled'}</option>
                        </select>
                        
                        <button
                          onClick={() => {
                            setSelectedOrder(order);
                            setShowOrderDetails(true);
                          }}
                          className="p-2 bg-accent/20 text-accent rounded-lg hover:bg-accent/30 transition-colors"
                          title={language === 'ar' ? 'عرض التفاصيل' : 'View Details'}
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
              
              {filteredOrders.length === 0 && (
                <div className="text-center py-12">
                  <ShoppingCart className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-400 mb-2">
                    {language === 'ar' ? 'لا توجد طلبات' : 'No Orders Found'}
                  </h3>
                  <p className="text-gray-500">
                    {language === 'ar' ? 'لا توجد طلبات تطابق معايير البحث' : 'No orders match your search criteria'}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Order Details Modal */}
        {showOrderDetails && selectedOrder && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-60 p-4">
            <div className="bg-primary rounded-lg max-w-2xl w-full max-h-[80vh] overflow-hidden">
              <div className="bg-gradient-to-r from-secondary to-accent p-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-bold text-primary">
                    {language === 'ar' ? 'تفاصيل الطلب' : 'Order Details'}
                  </h3>
                  <button
                    onClick={() => setShowOrderDetails(false)}
                    className="text-primary hover:text-primary/70"
                  >
                    <XCircle className="w-5 h-5" />
                  </button>
                </div>
              </div>
              
              <div className="p-6 overflow-y-auto max-h-[60vh]">
                <div className="space-y-6">
                  {/* Order Info */}
                  <div className="bg-background rounded-lg p-4">
                    <h4 className="text-lg font-semibold text-white mb-3">
                      {language === 'ar' ? 'معلومات الطلب' : 'Order Information'}
                    </h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm text-gray-400">{language === 'ar' ? 'اسم الخدمة' : 'Service Name'}</label>
                        <p className="text-white">{selectedOrder.service_name}</p>
                      </div>
                      <div>
                        <label className="text-sm text-gray-400">{language === 'ar' ? 'نوع الخدمة' : 'Service Type'}</label>
                        <p className="text-white">{selectedOrder.service_type}</p>
                      </div>
                      <div>
                        <label className="text-sm text-gray-400">{language === 'ar' ? 'السعر' : 'Price'}</label>
                        <p className="text-secondary font-bold text-lg">${selectedOrder.price}</p>
                      </div>
                      <div>
                        <label className="text-sm text-gray-400">{language === 'ar' ? 'الحالة' : 'Status'}</label>
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          {getStatusIcon(selectedOrder.status)}
                          <span className="text-white">{getStatusText(selectedOrder.status)}</span>
                        </div>
                      </div>
                      <div>
                        <label className="text-sm text-gray-400">{language === 'ar' ? 'تاريخ الطلب' : 'Order Date'}</label>
                        <p className="text-white">{new Date(selectedOrder.purchase_date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}</p>
                      </div>
                      {selectedOrder.completion_date && (
                        <div>
                          <label className="text-sm text-gray-400">{language === 'ar' ? 'تاريخ الإنجاز' : 'Completion Date'}</label>
                          <p className="text-white">{new Date(selectedOrder.completion_date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Customer Info */}
                  <div className="bg-background rounded-lg p-4">
                    <h4 className="text-lg font-semibold text-white mb-3">
                      {language === 'ar' ? 'معلومات العميل' : 'Customer Information'}
                    </h4>
                    {(() => {
                      const user = getUserById(selectedOrder.user_id);
                      return user ? (
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm text-gray-400">{language === 'ar' ? 'الاسم الكامل' : 'Full Name'}</label>
                            <p className="text-white">{user.full_name}</p>
                          </div>
                          <div>
                            <label className="text-sm text-gray-400">{language === 'ar' ? 'اسم المستخدم' : 'Username'}</label>
                            <p className="text-white">@{user.username}</p>
                          </div>
                          <div className="col-span-2">
                            <label className="text-sm text-gray-400">{language === 'ar' ? 'البريد الإلكتروني' : 'Email'}</label>
                            <p className="text-white">{user.email}</p>
                          </div>
                        </div>
                      ) : (
                        <p className="text-gray-400">{language === 'ar' ? 'معلومات العميل غير متوفرة' : 'Customer information not available'}</p>
                      );
                    })()}
                  </div>

                  {/* Notes */}
                  {selectedOrder.notes && (
                    <div className="bg-background rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-white mb-3">
                        {language === 'ar' ? 'ملاحظات' : 'Notes'}
                      </h4>
                      <p className="text-gray-300">{selectedOrder.notes}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderManagement;