{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "dev:server": "nodemon server/server.js", "dev:full": "concurrently \"npm run dev\" \"npm run dev:server\"", "build": "vite build", "setup:db": "node scripts/setup-database.js", "migrate": "node scripts/migrate-data.js", "migrate:dry": "node scripts/migrate-data.js --dry-run", "test:api": "node scripts/test-api-endpoints.js", "test:mysql": "node scripts/test-mysql-integration.js", "test:admin": "node scripts/test-admin-routes.js", "test:endpoints": "node scripts/test-admin-endpoints.js", "restart:clean": "node scripts/restart-server-clean.js", "create:admin": "node scripts/create-admin-user.js", "fix:admin-access": "node scripts/fix-admin-access.js", "check:users": "node scripts/check-users-table.js", "fix:complete": "node scripts/complete-admin-fix.js", "fix:json": "node scripts/fix-json-data.js", "check:columns": "node scripts/check-table-columns.js", "test:quick": "node scripts/quick-endpoint-test.js", "fix:ultimate": "node scripts/ultimate-admin-fix.js", "fix:emergency": "node scripts/emergency-fix-rate-limit.js", "clear:cache": "node scripts/clear-rate-limit-cache.js", "fix:infinite": "node scripts/fix-infinite-requests.js", "fix:nuclear": "node scripts/nuclear-fix.js", "disable:rate-limit": "node scripts/disable-rate-limit.js", "test:local": "node scripts/local-api-test.js", "health:check": "node scripts/monitor-server-health.js", "restart:all": "node scripts/restart-all-services.js", "fix:ngrok": "node scripts/fix-ngrok-tunnels.js", "testsprite:setup": "node scripts/setup-testsprite.js", "ngrok:stable": "node scripts/restart-ngrok-stable.js", "testsprite:verify": "node scripts/verify-setup.js", "analyze:db": "node scripts/database-structure-analysis.js", "fix:admin": "node scripts/fix-all-admin-components.js", "monitor:db": "node scripts/monitor-database-health.js", "setup:complete": "node scripts/setup-complete-integration.js", "setup:full": "npm run setup:db && npm run migrate && npm run setup:complete && npm run test:mysql", "start": "node server/server.js", "start:prod": "NODE_ENV=production node server/server.js", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "testsprite:auto": "node scripts/auto-setup-testsprite.js", "start:all": "node scripts/get-ngrok-urls.js", "quick:start": "start quick-start.bat", "services:start": "powershell -ExecutionPolicy Bypass -File start-services.ps1", "test:fixes": "node scripts/test-testsprite-fixes.js", "test:all-issues": "node scripts/test-all-testsprite-issues.js", "setup:token-blacklist": "node scripts/setup-token-blacklist.js"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "morgan": "^1.10.0", "multer": "^1.4.4-lts.1", "mysql2": "^3.6.5", "puppeteer": "^24.14.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.7.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.18", "concurrently": "^9.2.0", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^26.1.0", "nodemon": "^3.1.10", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^7.0.4", "vitest": "^3.2.4"}}