#!/usr/bin/env node

/**
 * Complete Data Migration Script
 * Migrates all data from local database to MySQL
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

// Local data files
const DATA_DIR = path.join(__dirname, '..', 'src', 'data');

async function migrateAllData() {
  let connection;
  
  try {
    console.log('🚀 Starting complete data migration...');
    
    // Connect to MySQL database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '123456',
      database: 'khanfashariya_db',
      charset: 'utf8mb4'
    });
    
    console.log('✅ Connected to MySQL database');
    
    // 1. Migrate System Services
    await migrateSystemServices(connection);
    
    // 2. Migrate Technical Services  
    await migrateTechnicalServices(connection);
    
    // 3. Migrate Premium Content
    await migratePremiumContent(connection);
    
    // 4. Migrate Users (sample data)
    await migrateUsers(connection);
    
    // 5. Migrate Orders (sample data)
    await migrateOrders(connection);
    
    console.log('🎉 Complete data migration finished successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

async function migrateSystemServices(connection) {
  console.log('📦 Migrating System Services...');
  
  try {
    // Read local data
    const systemsData = await fs.readFile(path.join(DATA_DIR, 'systems.json'), 'utf8');
    const systems = JSON.parse(systemsData);
    
    // Clear existing data
    await connection.execute('DELETE FROM system_services');
    console.log('🗑️  Cleared existing system services');
    
    // Insert new data
    for (const system of systems) {
      await connection.execute(`
        INSERT INTO system_services (
          id, name_ar, name_en, description_ar, description_en, 
          price, category, type, features_ar, features_en,
          video_url, image_url, gallery_images, version, file_size,
          requirements_ar, requirements_en, installation_guide_ar, installation_guide_en,
          status, featured, sort_order, download_count, rating, rating_count,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        system.id,
        system.name_ar || system.name,
        system.name_en || system.name,
        system.description_ar || system.description,
        system.description_en || system.description,
        system.price || 0,
        system.category || 'General',
        system.type || 'regular',
        JSON.stringify(system.features_ar || system.features || []),
        JSON.stringify(system.features_en || system.features || []),
        system.video_url || system.videoUrl,
        system.image_url || system.imageUrl || system.image,
        JSON.stringify(system.gallery_images || system.images || []),
        system.version || '1.0.0',
        system.file_size || system.fileSize || 'N/A',
        system.requirements_ar || system.requirements,
        system.requirements_en || system.requirements,
        system.installation_guide_ar || system.installationGuide,
        system.installation_guide_en || system.installationGuide,
        system.status || 'active',
        system.featured || false,
        system.sort_order || 0,
        system.download_count || system.downloadCount || 0,
        system.rating || 0,
        system.rating_count || system.ratingCount || 0
      ]);
    }
    
    console.log(`✅ Migrated ${systems.length} system services`);
    
  } catch (error) {
    console.error('❌ System services migration failed:', error.message);
    // Create sample data if file doesn't exist
    await createSampleSystemServices(connection);
  }
}

async function migrateTechnicalServices(connection) {
  console.log('🛠️  Migrating Technical Services...');
  
  try {
    // Read local data
    const servicesData = await fs.readFile(path.join(DATA_DIR, 'services.json'), 'utf8');
    const services = JSON.parse(servicesData);
    
    // Clear existing data
    await connection.execute('DELETE FROM technical_services');
    console.log('🗑️  Cleared existing technical services');
    
    // Insert new data
    for (const service of services) {
      await connection.execute(`
        INSERT INTO technical_services (
          id, name_ar, name_en, description_ar, description_en,
          price, category, service_type, features_ar, features_en,
          delivery_time_ar, delivery_time_en, video_url, image_url, gallery_images,
          requirements_ar, requirements_en, process_steps_ar, process_steps_en,
          status, featured, sort_order, order_count, rating, rating_count,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        service.id,
        service.name_ar || service.name,
        service.name_en || service.name,
        service.description_ar || service.description,
        service.description_en || service.description,
        service.price || 0,
        service.category || 'Development',
        service.service_type || service.type || 'development',
        JSON.stringify(service.features_ar || service.features || []),
        JSON.stringify(service.features_en || service.features || []),
        service.delivery_time_ar || service.deliveryTime,
        service.delivery_time_en || service.deliveryTime,
        service.video_url || service.videoUrl,
        service.image_url || service.imageUrl || service.image,
        JSON.stringify(service.gallery_images || service.images || []),
        service.requirements_ar || service.requirements,
        service.requirements_en || service.requirements,
        JSON.stringify(service.process_steps_ar || service.processSteps || []),
        JSON.stringify(service.process_steps_en || service.processSteps || []),
        service.status || 'active',
        service.featured || false,
        service.sort_order || 0,
        service.order_count || service.orderCount || 0,
        service.rating || 0,
        service.rating_count || service.ratingCount || 0
      ]);
    }
    
    console.log(`✅ Migrated ${services.length} technical services`);
    
  } catch (error) {
    console.error('❌ Technical services migration failed:', error.message);
    // Create sample data if file doesn't exist
    await createSampleTechnicalServices(connection);
  }
}

async function migratePremiumContent(connection) {
  console.log('💎 Migrating Premium Content...');
  
  try {
    // Read local data
    const premiumData = await fs.readFile(path.join(DATA_DIR, 'premium.json'), 'utf8');
    const premiumItems = JSON.parse(premiumData);
    
    // Clear existing data
    await connection.execute('DELETE FROM premium_content');
    console.log('🗑️  Cleared existing premium content');
    
    // Insert new data
    for (const item of premiumItems) {
      await connection.execute(`
        INSERT INTO premium_content (
          id, title_ar, title_en, description_ar, description_en,
          price, category, features_ar, features_en,
          video_url, image_url, gallery_images,
          included_systems, included_services,
          status, featured, sort_order, purchase_count, rating, rating_count,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        item.id,
        item.title_ar || item.title,
        item.title_en || item.title,
        item.description_ar || item.description,
        item.description_en || item.description,
        item.price || 0,
        item.category || 'Premium',
        JSON.stringify(item.features_ar || item.features || []),
        JSON.stringify(item.features_en || item.features || []),
        item.video_url || item.videoUrl,
        item.image_url || item.imageUrl || item.image,
        JSON.stringify(item.gallery_images || item.images || []),
        JSON.stringify(item.included_systems || item.includedSystems || []),
        JSON.stringify(item.included_services || item.includedServices || []),
        item.status || 'active',
        item.featured !== false,
        item.sort_order || 0,
        item.purchase_count || item.purchaseCount || 0,
        item.rating || 0,
        item.rating_count || item.ratingCount || 0
      ]);
    }
    
    console.log(`✅ Migrated ${premiumItems.length} premium content items`);
    
  } catch (error) {
    console.error('❌ Premium content migration failed:', error.message);
    // Create sample data if file doesn't exist
    await createSamplePremiumContent(connection);
  }
}

async function migrateUsers(connection) {
  console.log('👥 Creating sample users...');

  try {
    // Clear existing users (except admin)
    await connection.execute('DELETE FROM users WHERE role != "admin"');

    // Create sample users
    const sampleUsers = [
      {
        id: 'user-sample-1',
        username: 'testuser1',
        email: '<EMAIL>',
        password: '$2b$10$example.hash.here', // This should be properly hashed
        full_name: 'مستخدم تجريبي 1',
        role: 'user',
        status: 'active'
      },
      {
        id: 'user-sample-2',
        username: 'testuser2',
        email: '<EMAIL>',
        password: '$2b$10$example.hash.here',
        full_name: 'مستخدم تجريبي 2',
        role: 'user',
        status: 'active'
      }
    ];

    for (const user of sampleUsers) {
      await connection.execute(`
        INSERT IGNORE INTO users (
          id, username, email, password, full_name, role, status,
          email_verified, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        user.id, user.username, user.email, user.password,
        user.full_name, user.role, user.status, true
      ]);
    }

    console.log(`✅ Created ${sampleUsers.length} sample users`);

  } catch (error) {
    console.error('❌ Users migration failed:', error.message);
  }
}

async function migrateOrders(connection) {
  console.log('📋 Creating sample orders...');

  try {
    // Clear existing orders
    await connection.execute('DELETE FROM orders');

    // Create sample orders
    const sampleOrders = [
      {
        id: 'order-sample-1',
        user_id: 'user-sample-1',
        order_type: 'system_service',
        item_id: 'system-1',
        quantity: 1,
        unit_price: 299.99,
        total_price: 299.99,
        status: 'completed',
        payment_status: 'paid'
      },
      {
        id: 'order-sample-2',
        user_id: 'user-sample-2',
        order_type: 'technical_service',
        item_id: 'service-1',
        quantity: 1,
        unit_price: 499.99,
        total_price: 499.99,
        status: 'in_progress',
        payment_status: 'paid'
      }
    ];

    for (const order of sampleOrders) {
      const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      await connection.execute(`
        INSERT IGNORE INTO orders (
          id, user_id, order_type, item_id, quantity, unit_price, total_price,
          status, payment_status, order_number, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        order.id, order.user_id, order.order_type, order.item_id,
        order.quantity, order.unit_price, order.total_price,
        order.status, order.payment_status, orderNumber
      ]);
    }

    console.log(`✅ Created ${sampleOrders.length} sample orders`);

  } catch (error) {
    console.error('❌ Orders migration failed:', error.message);
  }
}

// Sample data creation functions
async function createSampleSystemServices(connection) {
  console.log('📦 Creating sample system services...');

  const sampleSystems = [
    {
      id: 'system-1',
      name_ar: 'نظام المتجر المتقدم',
      name_en: 'Advanced Shop System',
      description_ar: 'نظام متجر متقدم مع واجهة حديثة وميزات متطورة',
      description_en: 'Advanced shop system with modern interface and advanced features',
      price: 299.99,
      category: 'Shop Systems',
      type: 'regular',
      features_ar: ['واجهة حديثة', 'نظام دفع متقدم', 'إدارة المخزون'],
      features_en: ['Modern Interface', 'Advanced Payment', 'Inventory Management'],
      status: 'active',
      featured: true
    },
    {
      id: 'system-2',
      name_ar: 'نظام الجيلد المطور',
      name_en: 'Enhanced Guild System',
      description_ar: 'نظام جيلد محسن مع ميزات إضافية ونظام رانكات',
      description_en: 'Enhanced guild system with additional features and ranking system',
      price: 199.99,
      category: 'Guild Systems',
      type: 'regular',
      features_ar: ['نظام رانكات', 'مهام الجيلد', 'إحصائيات متقدمة'],
      features_en: ['Ranking System', 'Guild Missions', 'Advanced Statistics'],
      status: 'active',
      featured: false
    }
  ];

  for (const system of sampleSystems) {
    await connection.execute(`
      INSERT IGNORE INTO system_services (
        id, name_ar, name_en, description_ar, description_en,
        price, category, type, features_ar, features_en,
        status, featured, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      system.id, system.name_ar, system.name_en,
      system.description_ar, system.description_en,
      system.price, system.category, system.type,
      JSON.stringify(system.features_ar), JSON.stringify(system.features_en),
      system.status, system.featured
    ]);
  }

  console.log(`✅ Created ${sampleSystems.length} sample system services`);
}

async function createSampleTechnicalServices(connection) {
  console.log('🛠️  Creating sample technical services...');

  const sampleServices = [
    {
      id: 'service-1',
      name_ar: 'تطوير نظام مخصص',
      name_en: 'Custom System Development',
      description_ar: 'تطوير أنظمة مخصصة حسب متطلباتك الخاصة',
      description_en: 'Custom system development according to your specific requirements',
      price: 499.99,
      category: 'Development',
      service_type: 'development',
      features_ar: ['تصميم مخصص', 'برمجة احترافية', 'دعم فني'],
      features_en: ['Custom Design', 'Professional Programming', 'Technical Support'],
      delivery_time_ar: '7-14 يوم',
      delivery_time_en: '7-14 days',
      status: 'active',
      featured: true
    },
    {
      id: 'service-2',
      name_ar: 'استشارة تقنية',
      name_en: 'Technical Consultation',
      description_ar: 'استشارة تقنية متخصصة لحل مشاكل الخادم',
      description_en: 'Specialized technical consultation for server problem solving',
      price: 99.99,
      category: 'Consultation',
      service_type: 'consultation',
      features_ar: ['تحليل المشاكل', 'حلول مبتكرة', 'متابعة مستمرة'],
      features_en: ['Problem Analysis', 'Innovative Solutions', 'Continuous Follow-up'],
      delivery_time_ar: '1-3 أيام',
      delivery_time_en: '1-3 days',
      status: 'active',
      featured: false
    }
  ];

  for (const service of sampleServices) {
    await connection.execute(`
      INSERT IGNORE INTO technical_services (
        id, name_ar, name_en, description_ar, description_en,
        price, category, service_type, features_ar, features_en,
        delivery_time_ar, delivery_time_en, status, featured,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      service.id, service.name_ar, service.name_en,
      service.description_ar, service.description_en,
      service.price, service.category, service.service_type,
      JSON.stringify(service.features_ar), JSON.stringify(service.features_en),
      service.delivery_time_ar, service.delivery_time_en,
      service.status, service.featured
    ]);
  }

  console.log(`✅ Created ${sampleServices.length} sample technical services`);
}

async function createSamplePremiumContent(connection) {
  console.log('💎 Creating sample premium content...');

  const samplePremium = [
    {
      id: 'premium-1',
      title_ar: 'الحزمة الذهبية الشاملة',
      title_en: 'Complete Gold Package',
      description_ar: 'حزمة شاملة تتضمن جميع الأنظمة والخدمات المتاحة',
      description_en: 'Complete package including all available systems and services',
      price: 1999.99,
      category: 'Premium',
      features_ar: ['جميع الأنظمة', 'دعم مدى الحياة', 'تحديثات مجانية'],
      features_en: ['All Systems', 'Lifetime Support', 'Free Updates'],
      included_systems: ['system-1', 'system-2'],
      included_services: ['service-1', 'service-2'],
      status: 'active',
      featured: true
    },
    {
      id: 'premium-2',
      title_ar: 'حزمة المطور المحترف',
      title_en: 'Professional Developer Package',
      description_ar: 'حزمة خاصة للمطورين مع أدوات متقدمة',
      description_en: 'Special package for developers with advanced tools',
      price: 999.99,
      category: 'Developer',
      features_ar: ['أدوات التطوير', 'مكتبات البرمجة', 'دعم تقني متقدم'],
      features_en: ['Development Tools', 'Programming Libraries', 'Advanced Technical Support'],
      included_systems: ['system-1'],
      included_services: ['service-1'],
      status: 'active',
      featured: false
    }
  ];

  for (const premium of samplePremium) {
    await connection.execute(`
      INSERT IGNORE INTO premium_content (
        id, title_ar, title_en, description_ar, description_en,
        price, category, features_ar, features_en,
        included_systems, included_services, status, featured,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      premium.id, premium.title_ar, premium.title_en,
      premium.description_ar, premium.description_en,
      premium.price, premium.category,
      JSON.stringify(premium.features_ar), JSON.stringify(premium.features_en),
      JSON.stringify(premium.included_systems), JSON.stringify(premium.included_services),
      premium.status, premium.featured
    ]);
  }

  console.log(`✅ Created ${samplePremium.length} sample premium content items`);
}

// Run the migration
if (require.main === module) {
  migrateAllData().catch(error => {
    console.error('Migration script failed:', error);
    process.exit(1);
  });
}
