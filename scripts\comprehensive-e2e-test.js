/**
 * Comprehensive End-to-End Testing Script
 * Khanfashariya.com Production Readiness Validation
 * 
 * Tests complete user journey from registration to order completion
 * Based on current codebase and MySQL database integration
 */

const axios = require('axios');
const mysql = require('mysql2/promise');

// Configuration
const API_BASE = 'http://localhost:3001/api';
const FRONTEND_BASE = 'http://localhost:5173';
const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khanfashariya_db'
};

// Test credentials
const ADMIN_CREDS = { email: '<EMAIL>', password: 'admin123' };
const TEST_USER_CREDS = { email: '<EMAIL>', password: '123456' };

// Test results tracking
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

// Utility functions
function logTest(name, status, details = '') {
  testResults.total++;
  const icon = status === 'PASS' ? '✅' : '❌';
  const message = `${icon} ${name}`;
  
  if (status === 'PASS') {
    testResults.passed++;
    console.log(`${message} ${details}`);
  } else {
    testResults.failed++;
    console.log(`${message} - ${details}`);
    testResults.errors.push({ test: name, error: details });
  }
}

async function testDatabaseConnection() {
  console.log('\n🔍 Testing Database Connection...');
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    await connection.execute('SELECT 1');
    await connection.end();
    logTest('Database Connection', 'PASS', 'MySQL connection successful');
    return true;
  } catch (error) {
    logTest('Database Connection', 'FAIL', error.message);
    return false;
  }
}

async function testAPIEndpoints() {
  console.log('\n🔍 Testing Core API Endpoints...');
  
  const endpoints = [
    { name: 'Health Check', url: '/health', method: 'GET' },
    { name: 'Systems List', url: '/systems', method: 'GET' },
    { name: 'Technical Services', url: '/services/technical', method: 'GET' },
    { name: 'Premium Content', url: '/premium', method: 'GET' },
    { name: 'Premium Packages', url: '/services/packages', method: 'GET' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await axios({
        method: endpoint.method,
        url: `${API_BASE}${endpoint.url}`,
        timeout: 5000
      });
      
      if (response.status === 200 && response.data.success) {
        logTest(`API: ${endpoint.name}`, 'PASS', `Status: ${response.status}`);
      } else {
        logTest(`API: ${endpoint.name}`, 'FAIL', `Unexpected response: ${response.status}`);
      }
    } catch (error) {
      logTest(`API: ${endpoint.name}`, 'FAIL', error.message);
    }
  }
}

async function testAuthentication() {
  console.log('\n🔍 Testing Authentication System...');
  
  // Test admin login
  try {
    const adminLogin = await axios.post(`${API_BASE}/auth/login`, ADMIN_CREDS);
    if (adminLogin.data.success && adminLogin.data.data.tokens) {
      logTest('Admin Login', 'PASS', 'Admin authentication successful');
      
      // Test admin dashboard access
      const token = adminLogin.data.data.tokens.accessToken;
      const dashboardResponse = await axios.get(`${API_BASE}/admin/dashboard`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (dashboardResponse.data.success) {
        logTest('Admin Dashboard Access', 'PASS', 'Dashboard data retrieved');
      } else {
        logTest('Admin Dashboard Access', 'FAIL', 'Dashboard access failed');
      }
    } else {
      logTest('Admin Login', 'FAIL', 'Invalid login response');
    }
  } catch (error) {
    logTest('Admin Login', 'FAIL', error.message);
  }
  
  // Test user login
  try {
    const userLogin = await axios.post(`${API_BASE}/auth/login`, TEST_USER_CREDS);
    if (userLogin.data.success && userLogin.data.data.tokens) {
      logTest('User Login', 'PASS', 'User authentication successful');
    } else {
      logTest('User Login', 'FAIL', 'Invalid login response');
    }
  } catch (error) {
    logTest('User Login', 'FAIL', error.message);
  }
}

async function testOrderSystem() {
  console.log('\n🔍 Testing Order System...');
  
  try {
    // Login as test user
    const userLogin = await axios.post(`${API_BASE}/auth/login`, TEST_USER_CREDS);
    const token = userLogin.data.data.tokens.accessToken;
    
    // Get available systems
    const systemsResponse = await axios.get(`${API_BASE}/systems`);
    const systems = systemsResponse.data.data.systems;
    
    if (systems && systems.length > 0) {
      logTest('Systems Data Available', 'PASS', `Found ${systems.length} systems`);
      
      // Test order creation
      const testSystem = systems[0];
      const orderData = {
        order_type: 'system_service',
        item_id: testSystem.id,
        quantity: 1,
        notes_ar: 'طلب تجريبي',
        notes_en: 'Test order'
      };
      
      const orderResponse = await axios.post(`${API_BASE}/orders`, orderData, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (orderResponse.data.success) {
        logTest('Order Creation', 'PASS', `Order ID: ${orderResponse.data.data.order.id}`);
        
        // Test order retrieval
        const ordersResponse = await axios.get(`${API_BASE}/orders`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        if (ordersResponse.data.success && ordersResponse.data.data.orders.length > 0) {
          logTest('Order Retrieval', 'PASS', `Found ${ordersResponse.data.data.orders.length} orders`);
        } else {
          logTest('Order Retrieval', 'FAIL', 'No orders found');
        }
      } else {
        logTest('Order Creation', 'FAIL', 'Order creation failed');
      }
    } else {
      logTest('Systems Data Available', 'FAIL', 'No systems found');
    }
  } catch (error) {
    logTest('Order System', 'FAIL', error.message);
  }
}

async function testDataIntegrity() {
  console.log('\n🔍 Testing Data Integrity...');
  
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // Check table counts
    const tables = ['users', 'system_services', 'technical_services', 'premium_content', 'orders'];
    
    for (const table of tables) {
      const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
      const count = rows[0].count;
      logTest(`Table: ${table}`, 'PASS', `${count} records`);
    }
    
    // Check data consistency
    const [systemsCount] = await connection.execute('SELECT COUNT(*) as count FROM system_services WHERE status = "active"');
    const [ordersCount] = await connection.execute('SELECT COUNT(*) as count FROM orders');
    
    logTest('Active Systems', 'PASS', `${systemsCount[0].count} active systems`);
    logTest('Total Orders', 'PASS', `${ordersCount[0].count} orders in database`);
    
    await connection.end();
  } catch (error) {
    logTest('Data Integrity', 'FAIL', error.message);
  }
}

async function testFrontendIntegration() {
  console.log('\n🔍 Testing Frontend Integration...');
  
  try {
    // Test frontend server
    const frontendResponse = await axios.get(FRONTEND_BASE, { timeout: 5000 });
    if (frontendResponse.status === 200) {
      logTest('Frontend Server', 'PASS', 'Frontend accessible');
    } else {
      logTest('Frontend Server', 'FAIL', `Status: ${frontendResponse.status}`);
    }
    
    // Test API proxy
    const proxyResponse = await axios.get(`${FRONTEND_BASE}/api/systems`, { timeout: 5000 });
    if (proxyResponse.status === 200 && proxyResponse.data.success) {
      logTest('Frontend API Proxy', 'PASS', 'Proxy working correctly');
    } else {
      logTest('Frontend API Proxy', 'FAIL', 'Proxy not working');
    }
  } catch (error) {
    logTest('Frontend Integration', 'FAIL', error.message);
  }
}

async function runComprehensiveTest() {
  console.log('🚀 Starting Comprehensive End-to-End Testing');
  console.log('=' * 60);
  
  const startTime = Date.now();
  
  // Run all test suites
  await testDatabaseConnection();
  await testAPIEndpoints();
  await testAuthentication();
  await testOrderSystem();
  await testDataIntegrity();
  await testFrontendIntegration();
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  // Print summary
  console.log('\n' + '=' * 60);
  console.log('📊 TEST SUMMARY');
  console.log('=' * 60);
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  console.log(`Duration: ${duration} seconds`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.test}: ${error.error}`);
    });
  }
  
  // Production readiness assessment
  const successRate = (testResults.passed / testResults.total) * 100;
  console.log('\n🎯 PRODUCTION READINESS ASSESSMENT:');
  
  if (successRate >= 95) {
    console.log('🟢 READY FOR PRODUCTION - All critical systems operational');
  } else if (successRate >= 85) {
    console.log('🟡 NEEDS MINOR FIXES - Most systems operational, minor issues to resolve');
  } else {
    console.log('🔴 NOT READY - Critical issues need to be resolved before production');
  }
  
  console.log('\n🎉 Comprehensive testing completed!');
}

// Run the test
runComprehensiveTest().catch(console.error);
