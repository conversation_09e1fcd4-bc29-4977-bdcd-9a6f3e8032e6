import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useNotification } from '../hooks/useNotification';
import { signIn } from '../lib/apiServices';
import { useNavigate } from 'react-router-dom';
import {
  Lock,
  User,
  Eye,
  EyeOff,
  Shield,
  Zap,
  Mail,
  LogIn,
  Sword,
  Target,
  Star
} from 'lucide-react';

const LoginPage: React.FC = () => {
  const { t, language } = useTranslation();
  const { showNotification } = useNotification();
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });

  const isRTL = language === 'ar';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const { error } = await signIn(formData.email, formData.password);
      
      if (error) {
        showNotification({
          type: 'error',
          title: t('common.loginError'),
          message: error.message
        });
      } else {
        showNotification({
          type: 'success',
          title: t('common.success'),
          message: t('notifications.loginSuccess')
        });
        // Redirect to dashboard or home
        navigate('/dashboard');
      }
    } catch (error) {
      showNotification({
        type: 'error',
        title: t('common.loginError'),
        message: t('common.unexpectedError')
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-purple-500 to-cyan-500 p-3 rounded-full">
              <Shield className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">
            {t('auth.signIn', 'تسجيل الدخول')}
          </h1>
          <p className="text-slate-400">
            {t('auth.signInSubtitle', 'ادخل إلى حسابك للوصول إلى جميع الخدمات')}
          </p>
        </div>

        {/* Login Form */}
        <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8 shadow-2xl">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                {t('auth.email', 'البريد الإلكتروني')}
              </label>
              <div className="relative">
                <Mail className={`absolute top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 ${isRTL ? 'right-3' : 'left-3'}`} />
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ${isRTL ? 'pr-12 text-right' : 'pl-12'}`}
                  placeholder={t('auth.emailPlaceholder', 'أدخل بريدك الإلكتروني')}
                  required
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                {t('auth.password', 'كلمة المرور')}
              </label>
              <div className="relative">
                <Lock className={`absolute top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 ${isRTL ? 'right-3' : 'left-3'}`} />
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className={`w-full bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ${isRTL ? 'pr-12 pl-12 text-right' : 'pl-12 pr-12'}`}
                  placeholder={t('auth.passwordPlaceholder', 'أدخل كلمة المرور')}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className={`absolute top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors ${isRTL ? 'left-3' : 'right-3'}`}
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-2"
            >
              {isLoading ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <>
                  <LogIn className="w-5 h-5" />
                  {t('auth.signIn', 'تسجيل الدخول')}
                </>
              )}
            </button>
          </form>

          {/* Register Link */}
          <div className="mt-6 text-center">
            <p className="text-slate-400">
              {t('auth.noAccount', 'ليس لديك حساب؟')}{' '}
              <button
                onClick={() => navigate('/register')}
                className="text-purple-400 hover:text-purple-300 font-medium transition-colors"
              >
                {t('auth.signUp', 'إنشاء حساب جديد')}
              </button>
            </p>
          </div>
        </div>

        {/* Features */}
        <div className="mt-8 grid grid-cols-3 gap-4 text-center">
          <div className="text-slate-400">
            <Sword className="w-6 h-6 mx-auto mb-2 text-purple-400" />
            <p className="text-xs">{t('features.gaming', 'ألعاب')}</p>
          </div>
          <div className="text-slate-400">
            <Target className="w-6 h-6 mx-auto mb-2 text-cyan-400" />
            <p className="text-xs">{t('features.precision', 'دقة')}</p>
          </div>
          <div className="text-slate-400">
            <Star className="w-6 h-6 mx-auto mb-2 text-yellow-400" />
            <p className="text-xs">{t('features.premium', 'مميز')}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
