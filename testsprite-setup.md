# إعداد TestSprite لمشروع Khanfashariya

## خطوات الإعداد:

### 1. تشغيل المشروع محلياً
```bash
# تشغيل قاعدة البيانات والخادم
npm run dev:full
```

### 2. معلومات API لـ TestSprite:
- **API Name:** Khanfashariya API
- **API Endpoint:** http://localhost:3001
- **Authentication:** None (للبداية)

### 3. نقاط النهاية المتاحة للاختبار:

#### نقاط عامة:
- `GET /health` - فحص صحة النظام
- `GET /api/status` - حالة API
- `GET /` - معلومات API الأساسية

#### المصادقة:
- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/register` - التسجيل
- `POST /api/auth/refresh` - ت<PERSON><PERSON><PERSON><PERSON> الرمز المميز

#### إدارة المستخدمين:
- `GET /api/users/profile` - ملف المستخدم
- `PUT /api/users/profile` - تحديث الملف الشخصي

#### الخدمات:
- `GET /api/systems` - خدمات النظام
- `GET /api/services` - الخدمات التقنية
- `GET /api/orders` - إدارة الطلبات

#### الإدارة:
- `GET /api/admin/dashboard` - لوحة الإدارة
- `GET /api/admin/users` - إدارة المستخدمين

### 4. بيانات اختبار نموذجية:

#### تسجيل الدخول:
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

#### تسجيل مستخدم جديد:
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123"
}
```

### 5. إعداد TestSprite - خيارات الوصول:

#### الخيار الأول: استخدام ngrok (الأسهل):
1. تثبيت ngrok:
   ```bash
   # تحميل من https://ngrok.com/download
   # أو استخدام npm
   npm install -g ngrok
   ```

2. تشغيل المشروع:
   ```bash
   npm run dev:full
   ```

3. في terminal جديد، تشغيل ngrok:
   ```bash
   ngrok http 3001
   ```

4. استخدام الرابط الذي يظهر في TestSprite:
   - API Endpoint: `https://xxxxx.ngrok.io` (الرابط الذي يظهر)

#### الخيار الثاني: استخدام localtunnel:
1. تثبيت localtunnel:
   ```bash
   npm install -g localtunnel
   ```

2. تشغيل المشروع ثم:
   ```bash
   lt --port 3001 --subdomain khanfashariya-api
   ```

#### الخيار الثالث: رفع على خادم مؤقت:
- استخدام Heroku أو Vercel أو Railway لرفع نسخة تجريبية

#### إعداد TestSprite:
1. افتح TestSprite
2. اختر "Add API"
3. أدخل المعلومات التالية:
   - API Name: `Khanfashariya API`
   - API Endpoint: `الرابط من ngrok أو localtunnel`
   - Authentication Type: `None` (للبداية)
4. احفظ الإعدادات

### 6. اختبارات مقترحة:
- اختبار نقاط النهاية العامة أولاً
- اختبار المصادقة
- اختبار CRUD operations
- اختبار معالجة الأخطاء
- اختبار الأمان والتحقق

### 7. ملاحظات مهمة:
- تأكد من تشغيل المشروع قبل بدء الاختبارات
- استخدم بيانات اختبار وليس بيانات حقيقية
- راقب logs الخادم أثناء الاختبار
## حل مشك
لة الوصول المحلي:

### لماذا قد لا يعمل localhost مباشرة؟
- TestSprite يعمل كخدمة سحابية
- لا يمكنه الوصول إلى `localhost` على جهازك مباشرة
- يحتاج رابط عام للوصول إلى API

### الحل الموصى به - استخدام ngrok:

#### 1. تثبيت ngrok:
```bash
# الطريقة الأولى: تحميل من الموقع
# اذهب إلى https://ngrok.com/download
# حمل الملف وضعه في مجلد PATH

# الطريقة الثانية: استخدام npm
npm install -g ngrok
```

#### 2. خطوات التشغيل:
```bash
# Terminal 1: تشغيل المشروع
npm run dev:full

# Terminal 2: تشغيل ngrok
ngrok http 3001
```

#### 3. ستحصل على رابط مثل:
```
https://abc123.ngrok.io -> http://localhost:3001
```

#### 4. استخدم هذا الرابط في TestSprite:
- API Endpoint: `https://abc123.ngrok.io`

### مثال عملي:
```bash
# تشغيل المشروع
npm run dev:full

# في terminal آخر
ngrok http 3001

# النتيجة:
# Forwarding https://1234-abcd.ngrok.io -> http://localhost:3001
```

### ملاحظات مهمة:
- ngrok مجاني للاستخدام الأساسي
- الرابط يتغير كل مرة تعيد تشغيل ngrok
- للحصول على رابط ثابت، تحتاج حساب مدفوع
- تأكد من أن قاعدة البيانات تعمل قبل بدء الاختبار

### التحقق من عمل الإعداد:
1. افتح الرابط من ngrok في المتصفح
2. يجب أن ترى رسالة API
3. جرب `/health` للتأكد من عمل قاعدة البيانات
4. إذا كان كل شيء يعمل، يمكن لـ TestSprite الوصول إليه