import React, { forwardRef, useState } from 'react';
import { Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';

interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  error?: string;
  success?: string;
  hint?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loading?: boolean;
  showPasswordToggle?: boolean;
}

/**
 * Enhanced Input component with comprehensive features
 * 
 * Features:
 * - Multiple sizes and variants
 * - Bilingual label and error support
 * - Password visibility toggle
 * - Icon support (left and right)
 * - Loading state
 * - Validation states (error, success)
 * - RTL layout support
 * - Accessibility features
 * - Floating label animation
 */
const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  success,
  hint,
  size = 'md',
  variant = 'default',
  leftIcon,
  rightIcon,
  loading = false,
  showPasswordToggle = false,
  type = 'text',
  className = '',
  disabled,
  required,
  ...props
}, ref) => {
  const { language } = useTranslation();
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(!!props.value || !!props.defaultValue);

  const isRTL = language === 'ar';
  const inputType = showPasswordToggle && type === 'password' 
    ? (showPassword ? 'text' : 'password') 
    : type;

  // Base classes for the input container
  const containerClasses = [
    'relative',
    'w-full'
  ].join(' ');

  // Input wrapper classes
  const wrapperClasses = [
    'relative',
    'flex',
    'items-center',
    'transition-all',
    'duration-200'
  ].join(' ');

  // Size-specific classes
  const sizeClasses = {
    sm: {
      input: 'h-10 px-3 text-sm',
      icon: 'w-4 h-4',
      label: 'text-sm'
    },
    md: {
      input: 'h-11 px-4 text-base',
      icon: 'w-5 h-5',
      label: 'text-base'
    },
    lg: {
      input: 'h-12 px-5 text-lg',
      icon: 'w-6 h-6',
      label: 'text-lg'
    }
  };

  // Variant-specific classes
  const variantClasses = {
    default: [
      'bg-primary-500/30',
      'border',
      'border-accent-500/30',
      'focus:border-accent-500',
      'focus:bg-primary-500/40'
    ].join(' '),

    filled: [
      'bg-gray-100',
      'border',
      'border-transparent',
      'focus:border-accent-500',
      'focus:bg-white'
    ].join(' '),

    outlined: [
      'bg-transparent',
      'border-2',
      'border-accent-500/40',
      'focus:border-accent-500'
    ].join(' ')
  };

  // State-specific classes
  const stateClasses = error 
    ? 'border-error-500 focus:border-error-500 focus:ring-error-500/20'
    : success 
    ? 'border-success-500 focus:border-success-500 focus:ring-success-500/20'
    : 'focus:ring-accent-500/20';

  // Input classes
  const inputClasses = [
    'w-full',
    'bg-transparent',
    'text-white',
    'placeholder-gray-400',
    'border-none',
    'outline-none',
    'focus:ring-0',
    sizeClasses[size].input,
    leftIcon ? (isRTL ? 'pr-10' : 'pl-10') : '',
    (rightIcon || showPasswordToggle || loading) ? (isRTL ? 'pl-10' : 'pr-10') : '',
    disabled ? 'cursor-not-allowed opacity-50' : '',
    isRTL ? 'text-right' : 'text-left'
  ].join(' ');

  // Wrapper classes with state
  const finalWrapperClasses = [
    wrapperClasses,
    'rounded-lg',
    variantClasses[variant],
    stateClasses,
    disabled ? 'opacity-50 cursor-not-allowed' : '',
    isFocused ? 'ring-2' : ''
  ].join(' ');

  // Label classes for floating effect
  const labelClasses = [
    'absolute',
    'transition-all',
    'duration-200',
    'pointer-events-none',
    sizeClasses[size].label,
    isRTL ? 'right-4' : 'left-4',
    (isFocused || hasValue)
      ? `${isRTL ? '-top-2 right-2' : '-top-2 left-2'} text-xs bg-primary px-2 text-secondary font-medium`
      : `top-1/2 -translate-y-1/2 text-gray-400`,
    error ? 'text-red-400' : success ? 'text-green-400' : ''
  ].join(' ');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setHasValue(!!e.target.value);
    props.onChange?.(e);
  };

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    props.onFocus?.(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    props.onBlur?.(e);
  };

  return (
    <div className={`${containerClasses} ${className} floating-label-fix`}>
      {/* Input wrapper */}
      <div className={finalWrapperClasses}>
        {/* Left icon */}
        {leftIcon && (
          <div className={`absolute ${isRTL ? 'right-3' : 'left-3'} text-gray-400 ${sizeClasses[size].icon}`}>
            {leftIcon}
          </div>
        )}

        {/* Input field */}
        <input
          ref={ref}
          type={inputType}
          className={inputClasses}
          disabled={disabled}
          required={required}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onChange={handleInputChange}
          {...props}
        />

        {/* Floating label */}
        {label && (
          <label className={labelClasses}>
            {label}
            {required && <span className="text-error-500 ml-1">*</span>}
          </label>
        )}

        {/* Right side icons */}
        <div className={`absolute ${isRTL ? 'left-3' : 'right-3'} flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
          {/* Loading spinner */}
          {loading && (
            <div className={`animate-spin text-accent-500 ${sizeClasses[size].icon}`}>
              <div className="w-full h-full border-2 border-current border-t-transparent rounded-full" />
            </div>
          )}

          {/* Success icon */}
          {success && !loading && (
            <CheckCircle className={`text-success-500 ${sizeClasses[size].icon}`} />
          )}

          {/* Error icon */}
          {error && !loading && (
            <AlertCircle className={`text-error-500 ${sizeClasses[size].icon}`} />
          )}

          {/* Password toggle */}
          {showPasswordToggle && type === 'password' && !loading && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className={`text-gray-400 hover:text-gray-300 transition-colors ${sizeClasses[size].icon}`}
              aria-label={showPassword ? 'Hide password' : 'Show password'}
            >
              {showPassword ? <EyeOff /> : <Eye />}
            </button>
          )}

          {/* Custom right icon */}
          {rightIcon && !loading && !error && !success && (
            <div className={`text-gray-400 ${sizeClasses[size].icon}`}>
              {rightIcon}
            </div>
          )}
        </div>
      </div>

      {/* Helper text */}
      {(error || success || hint) && (
        <div className="mt-2 space-y-1">
          {error && (
            <p className="text-sm text-error-500 flex items-center space-x-1 rtl:space-x-reverse">
              <AlertCircle className="w-4 h-4 flex-shrink-0" />
              <span>{error}</span>
            </p>
          )}
          
          {success && !error && (
            <p className="text-sm text-success-500 flex items-center space-x-1 rtl:space-x-reverse">
              <CheckCircle className="w-4 h-4 flex-shrink-0" />
              <span>{success}</span>
            </p>
          )}
          
          {hint && !error && !success && (
            <p className="text-sm text-gray-400">
              {hint}
            </p>
          )}
        </div>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
