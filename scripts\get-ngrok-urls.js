#!/usr/bin/env node

/**
 * سكريبت للحصول على روابط ngrok وحفظها
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🔗 الحصول على روابط ngrok...');

async function getTunnelInfo(port, name) {
    return new Promise((resolve) => {
        const req = http.get(`http://127.0.0.1:${port}/api/tunnels`, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const tunnelData = JSON.parse(data);
                    if (tunnelData.tunnels && tunnelData.tunnels.length > 0) {
                        const tunnel = tunnelData.tunnels[0];
                        resolve({
                            name,
                            url: tunnel.public_url,
                            local: tunnel.config.addr,
                            status: 'active'
                        });
                    } else {
                        resolve({
                            name,
                            url: null,
                            local: null,
                            status: 'no_tunnels'
                        });
                    }
                } catch (error) {
                    resolve({
                        name,
                        url: null,
                        local: null,
                        status: 'parse_error'
                    });
                }
            });
        });
        
        req.on('error', () => {
            resolve({
                name,
                url: null,
                local: null,
                status: 'connection_error'
            });
        });
        
        req.setTimeout(5000, () => {
            resolve({
                name,
                url: null,
                local: null,
                status: 'timeout'
            });
        });
    });
}

async function main() {
    try {
        console.log('📡 فحص Backend ngrok (port 4040)...');
        const backendInfo = await getTunnelInfo(4040, 'Backend');
        
        console.log('🎨 فحص Frontend ngrok (port 4041)...');
        const frontendInfo = await getTunnelInfo(4041, 'Frontend');
        
        // إنشاء التكوين النهائي
        const config = {
            timestamp: new Date().toISOString(),
            status: 'ready',
            backend: {
                name: 'Khanfashariya Backend API',
                url: backendInfo.url || 'غير متاح',
                local: 'http://localhost:3001',
                port: 3001,
                ngrok_status: backendInfo.status
            },
            frontend: {
                name: 'Khanfashariya Frontend',
                url: frontendInfo.url || 'غير متاح',
                local: 'http://localhost:5173',
                port: 5173,
                ngrok_status: frontendInfo.status
            },
            testsprite_endpoints: {},
            notes: []
        };
        
        // إضافة endpoints إذا كان Backend متاح
        if (backendInfo.url) {
            config.testsprite_endpoints = {
                health: `${backendInfo.url}/health`,
                admin_login: `${backendInfo.url}/api/auth/login`,
                systems: `${backendInfo.url}/api/systems`,
                technical_services: `${backendInfo.url}/api/services/technical`,
                premium_services: `${backendInfo.url}/api/services/premium`
            };
        }
        
        // إضافة الملاحظات
        if (backendInfo.url) {
            config.notes.push(`✅ Backend متاح: ${backendInfo.url}`);
        } else {
            config.notes.push(`❌ Backend غير متاح: ${backendInfo.status}`);
        }
        
        if (frontendInfo.url) {
            config.notes.push(`✅ Frontend متاح: ${frontendInfo.url}`);
        } else {
            config.notes.push(`❌ Frontend غير متاح: ${frontendInfo.status}`);
        }
        
        // حفظ التكوين
        const configPath = path.join(process.cwd(), 'current-ngrok-urls.json');
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8');
        
        // تحديث ملف testsprite-urls.json إذا كان موجوداً
        const testspritePath = path.join(process.cwd(), 'testsprite-urls.json');
        if (fs.existsSync(testspritePath)) {
            try {
                const testspriteConfig = JSON.parse(fs.readFileSync(testspritePath, 'utf8'));
                
                if (backendInfo.url) {
                    testspriteConfig.backend_api_url = backendInfo.url;
                    testspriteConfig.testsprite_settings.base_url = backendInfo.url;
                    
                    // تحديث endpoints
                    testspriteConfig.endpoints.forEach(endpoint => {
                        if (endpoint.url.includes('null/')) {
                            endpoint.url = endpoint.url.replace('null/', `${backendInfo.url}/`);
                        } else {
                            // استبدال URL القديم بالجديد
                            const urlParts = endpoint.url.split('/');
                            if (urlParts.length > 3) {
                                const path = '/' + urlParts.slice(3).join('/');
                                endpoint.url = backendInfo.url + path;
                            }
                        }
                    });
                }
                
                if (frontendInfo.url) {
                    testspriteConfig.frontend_url = frontendInfo.url;
                    
                    // تحديث frontend pages
                    if (testspriteConfig.frontend_pages) {
                        testspriteConfig.frontend_pages = testspriteConfig.frontend_pages.map(page => {
                            const urlParts = page.split('/');
                            if (urlParts.length > 3) {
                                const path = '/' + urlParts.slice(3).join('/');
                                return frontendInfo.url + path;
                            }
                            return frontendInfo.url + '/';
                        });
                    }
                }
                
                fs.writeFileSync(testspritePath, JSON.stringify(testspriteConfig, null, 2), 'utf8');
                console.log('✅ تم تحديث testsprite-urls.json');
            } catch (error) {
                console.log('⚠️ خطأ في تحديث testsprite-urls.json:', error.message);
            }
        }
        
        // طباعة النتائج
        console.log('\n🎉 تم الحصول على الروابط بنجاح!');
        console.log('=' .repeat(50));
        console.log(`🔧 Backend: ${backendInfo.url || 'غير متاح'}`);
        console.log(`🎨 Frontend: ${frontendInfo.url || 'غير متاح'}`);
        console.log('=' .repeat(50));
        console.log(`📁 تم حفظ التكوين في: ${configPath}`);
        
        if (backendInfo.url && frontendInfo.url) {
            console.log('\n🚀 جاهز للاستخدام مع TestSprite!');
        } else {
            console.log('\n⚠️ بعض الخدمات غير متاحة. تحقق من تشغيل ngrok.');
        }
        
    } catch (error) {
        console.error('❌ خطأ:', error.message);
        process.exit(1);
    }
}

// تشغيل السكريبت
main();
