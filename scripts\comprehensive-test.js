#!/usr/bin/env node

/**
 * Comprehensive Test Script
 * 
 * Tests all aspects of the website to ensure everything works correctly
 */

const axios = require('axios');
const puppeteer = require('puppeteer');

const API_BASE_URL = 'http://localhost:3001';
const FRONTEND_URL = 'http://localhost:5173';

async function testAPI() {
  console.log('🔌 Testing API endpoints...\n');
  
  const tests = [
    { name: 'Health Check', url: '/health' },
    { name: 'Systems API', url: '/api/systems' },
    { name: 'Technical Services API', url: '/api/services/technical' },
    { name: 'Login API', url: '/api/auth/login', method: 'POST', data: { email: '<EMAIL>', password: 'admin123' } }
  ];
  
  const results = {};
  
  for (const test of tests) {
    try {
      let response;
      if (test.method === 'POST') {
        response = await axios.post(`${API_BASE_URL}${test.url}`, test.data);
      } else {
        response = await axios.get(`${API_BASE_URL}${test.url}`);
      }
      
      if (response.data.success !== false) {
        console.log(`✅ ${test.name}: Working`);
        results[test.name] = 'success';
      } else {
        console.log(`❌ ${test.name}: Failed - ${response.data.message}`);
        results[test.name] = 'failed';
      }
    } catch (error) {
      console.log(`❌ ${test.name}: Error - ${error.message}`);
      results[test.name] = 'error';
    }
  }
  
  console.log('');
  return results;
}

async function testFrontend() {
  console.log('🌐 Testing frontend functionality...\n');
  
  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Monitor console errors
    const consoleErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Monitor network failures
    const networkErrors = [];
    page.on('requestfailed', request => {
      networkErrors.push(`${request.method()} ${request.url()} - ${request.failure().errorText}`);
    });
    
    console.log('📱 Loading homepage...');
    await page.goto(FRONTEND_URL, { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });
    
    // Wait for content to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check for React errors
    const hasReactErrors = await page.evaluate(() => {
      const errorBoundary = document.querySelector('[data-testid="error-boundary"]');
      return errorBoundary && errorBoundary.textContent.includes('خطأ');
    });
    
    // Check if systems are visible
    const systemsVisible = await page.evaluate(() => {
      const systemElements = document.querySelectorAll('[data-testid="system-card"], .system-card, .grid > div');
      return systemElements.length > 0;
    });
    
    // Check if services are visible
    const servicesVisible = await page.evaluate(() => {
      const serviceElements = document.querySelectorAll('[data-testid="service-card"], .service-card');
      return serviceElements.length > 0;
    });
    
    // Test navigation
    console.log('🧭 Testing navigation...');
    const navLinks = await page.$$eval('nav a', links => 
      links.map(link => ({ text: link.textContent, href: link.href }))
    );
    
    // Test language switching
    console.log('🌍 Testing language switching...');
    const languageButton = await page.$('[data-testid="language-toggle"], button[aria-label*="language"], button[aria-label*="لغة"]');
    if (languageButton) {
      await languageButton.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('📊 Frontend Test Results:');
    console.log('─'.repeat(25));
    console.log(`   React Errors: ${hasReactErrors ? '❌ Found' : '✅ None'}`);
    console.log(`   Systems Visible: ${systemsVisible ? '✅ Yes' : '❌ No'}`);
    console.log(`   Services Visible: ${servicesVisible ? '✅ Yes' : '❌ No'}`);
    console.log(`   Console Errors: ${consoleErrors.length}`);
    console.log(`   Network Errors: ${networkErrors.length}`);
    console.log(`   Navigation Links: ${navLinks.length}`);
    
    if (consoleErrors.length > 0) {
      console.log('\n🚨 Console Errors:');
      consoleErrors.slice(0, 5).forEach(error => {
        console.log(`   • ${error.substring(0, 100)}...`);
      });
    }
    
    if (networkErrors.length > 0) {
      console.log('\n🌐 Network Errors:');
      networkErrors.slice(0, 3).forEach(error => {
        console.log(`   • ${error}`);
      });
    }
    
    console.log('');
    
    return {
      hasReactErrors: !hasReactErrors,
      systemsVisible,
      servicesVisible,
      consoleErrorCount: consoleErrors.length,
      networkErrorCount: networkErrors.length,
      navigationWorking: navLinks.length > 0
    };
    
  } catch (error) {
    console.log(`❌ Frontend test failed: ${error.message}`);
    return {
      hasReactErrors: false,
      systemsVisible: false,
      servicesVisible: false,
      consoleErrorCount: 999,
      networkErrorCount: 999,
      navigationWorking: false
    };
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

async function testSpecificPages() {
  console.log('📄 Testing specific pages...\n');
  
  const pages = [
    { name: 'Homepage', url: FRONTEND_URL },
    { name: 'Systems', url: `${FRONTEND_URL}#systems` },
    { name: 'Services', url: `${FRONTEND_URL}#services` },
    { name: 'Contact', url: `${FRONTEND_URL}#contact` }
  ];
  
  let browser;
  const results = {};
  
  try {
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    for (const pageTest of pages) {
      const page = await browser.newPage();
      
      try {
        console.log(`🔍 Testing ${pageTest.name}...`);
        
        await page.goto(pageTest.url, { 
          waitUntil: 'networkidle0',
          timeout: 15000 
        });
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Check for errors
        const hasErrors = await page.evaluate(() => {
          const errorElements = document.querySelectorAll('.error, [class*="error"], .notification.error');
          const errorBoundary = document.querySelector('[data-testid="error-boundary"]');
          return errorElements.length > 0 || (errorBoundary && errorBoundary.textContent.includes('خطأ'));
        });
        
        // Check if content loaded
        const hasContent = await page.evaluate(() => {
          const contentElements = document.querySelectorAll('main, .container, .content, section');
          return contentElements.length > 0;
        });
        
        results[pageTest.name] = {
          loaded: true,
          hasErrors: !hasErrors,
          hasContent
        };
        
        console.log(`   ${hasErrors ? '❌' : '✅'} ${pageTest.name}: ${hasErrors ? 'Has errors' : 'Working'}`);
        
      } catch (error) {
        results[pageTest.name] = {
          loaded: false,
          hasErrors: false,
          hasContent: false
        };
        console.log(`   ❌ ${pageTest.name}: Failed to load - ${error.message}`);
      }
      
      await page.close();
    }
    
  } catch (error) {
    console.log(`❌ Page testing failed: ${error.message}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
  
  console.log('');
  return results;
}

async function generateFinalReport(apiResults, frontendResults, pageResults) {
  console.log('📋 Comprehensive Test Report');
  console.log('============================\n');
  
  // Calculate overall score
  let totalTests = 0;
  let passedTests = 0;
  
  // API tests
  Object.values(apiResults).forEach(result => {
    totalTests++;
    if (result === 'success') passedTests++;
  });
  
  // Frontend tests
  if (frontendResults.hasReactErrors) passedTests++;
  if (frontendResults.systemsVisible) passedTests++;
  if (frontendResults.servicesVisible) passedTests++;
  if (frontendResults.consoleErrorCount < 5) passedTests++;
  if (frontendResults.networkErrorCount === 0) passedTests++;
  if (frontendResults.navigationWorking) passedTests++;
  totalTests += 6;
  
  // Page tests
  Object.values(pageResults).forEach(result => {
    totalTests += 3;
    if (result.loaded) passedTests++;
    if (result.hasErrors) passedTests++;
    if (result.hasContent) passedTests++;
  });
  
  const score = Math.round((passedTests / totalTests) * 100);
  
  console.log(`🎯 Overall Score: ${score}% (${passedTests}/${totalTests} tests passed)\n`);
  
  if (score >= 90) {
    console.log('🎉 Excellent! Website is working perfectly');
    console.log('✅ All major functionality is operational');
    console.log('✅ No critical errors detected');
    console.log('✅ Ready for production use');
  } else if (score >= 75) {
    console.log('✅ Good! Website is mostly working');
    console.log('⚠️ Some minor issues detected');
    console.log('🔧 Consider fixing remaining issues');
  } else if (score >= 50) {
    console.log('⚠️ Fair! Website has some issues');
    console.log('❌ Several problems need attention');
    console.log('🔧 Fix critical issues before production');
  } else {
    console.log('❌ Poor! Website has major issues');
    console.log('🚨 Critical problems detected');
    console.log('🔧 Immediate fixes required');
  }
  
  console.log('\n🌐 Access the website at: http://localhost:5173');
  console.log('🔧 Admin panel at: http://localhost:5173/admin');
  
  return score >= 75;
}

async function main() {
  console.log('🧪 Comprehensive Website Test');
  console.log('=============================\n');
  
  try {
    // Test API
    const apiResults = await testAPI();
    
    // Test Frontend
    const frontendResults = await testFrontend();
    
    // Test Specific Pages
    const pageResults = await testSpecificPages();
    
    // Generate Final Report
    const success = await generateFinalReport(apiResults, frontendResults, pageResults);
    
    process.exit(success ? 0 : 1);
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
