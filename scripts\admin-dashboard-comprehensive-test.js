/**
 * Admin Dashboard Comprehensive Test
 * Tests all admin functionality including user management, order management, and system administration
 */

const axios = require('axios');
const mysql = require('mysql2/promise');

const API_BASE = 'http://localhost:3001/api';
const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khanfashariya_db'
};

const ADMIN_CREDS = { email: '<EMAIL>', password: 'admin123' };

let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

function logTest(name, status, details = '') {
  testResults.total++;
  const icon = status === 'PASS' ? '✅' : '❌';
  const message = `${icon} ${name}`;
  
  if (status === 'PASS') {
    testResults.passed++;
    console.log(`${message} ${details}`);
  } else {
    testResults.failed++;
    console.log(`${message} - ${details}`);
    testResults.errors.push({ test: name, error: details });
  }
}

async function testAdminDashboardComprehensive() {
  console.log('🛠️ Starting Admin Dashboard Comprehensive Test');
  console.log('=' * 60);
  
  let adminAuth = null;
  
  try {
    // 1. Admin Login
    console.log('\n🔐 Testing Admin Authentication...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, ADMIN_CREDS);
    
    if (loginResponse.data.success && loginResponse.data.data.tokens) {
      adminAuth = {
        token: loginResponse.data.data.tokens.accessToken,
        user: loginResponse.data.data.user,
        headers: { Authorization: `Bearer ${loginResponse.data.data.tokens.accessToken}` }
      };
      logTest('Admin Login', 'PASS', 'Admin authentication successful');
      
      if (adminAuth.user.role === 'admin') {
        logTest('Admin Role Verification', 'PASS', 'User has admin privileges');
      } else {
        logTest('Admin Role Verification', 'FAIL', `User role is ${adminAuth.user.role}, not admin`);
      }
    } else {
      logTest('Admin Login', 'FAIL', 'Admin authentication failed');
      return;
    }
    
    // 2. Admin Dashboard Access
    console.log('\n📊 Testing Admin Dashboard...');
    const dashboardResponse = await axios.get(`${API_BASE}/admin/dashboard`, { headers: adminAuth.headers });
    
    if (dashboardResponse.data.success) {
      const stats = dashboardResponse.data.data.stats;
      logTest('Dashboard Access', 'PASS', 'Admin dashboard accessible');
      logTest('Dashboard Statistics', 'PASS', `Users: ${stats.users.total_users}, Orders: ${stats.orders.total_orders}, Revenue: $${stats.revenue.total_revenue}`);
      logTest('Database Health', 'PASS', `Active sessions: ${stats.system.active_sessions}, Recent activities: ${stats.system.recent_activities}`);
    } else {
      logTest('Dashboard Access', 'FAIL', 'Admin dashboard inaccessible');
    }
    
    // 3. User Management
    console.log('\n👥 Testing User Management...');
    const usersResponse = await axios.get(`${API_BASE}/admin/users`, { headers: adminAuth.headers });
    
    if (usersResponse.data.success) {
      const users = usersResponse.data.data.users || [];
      logTest('User Management Access', 'PASS', `Found ${users.length} users`);
      
      // Test user filtering
      const activeUsersResponse = await axios.get(`${API_BASE}/admin/users?status=active`, { headers: adminAuth.headers });
      if (activeUsersResponse.data.success) {
        const activeUsers = activeUsersResponse.data.data.users || [];
        logTest('User Filtering', 'PASS', `${activeUsers.length} active users found`);
      }
      
      // Test user search
      const searchResponse = await axios.get(`${API_BASE}/admin/users?search=test`, { headers: adminAuth.headers });
      if (searchResponse.data.success) {
        const searchResults = searchResponse.data.data.users || [];
        logTest('User Search', 'PASS', `${searchResults.length} users found with 'test' in their data`);
      }
    } else {
      logTest('User Management Access', 'FAIL', 'User management inaccessible');
    }
    
    // 4. Order Management
    console.log('\n📦 Testing Order Management...');
    const ordersResponse = await axios.get(`${API_BASE}/admin/orders`, { headers: adminAuth.headers });
    
    if (ordersResponse.data.success) {
      const orders = ordersResponse.data.data.orders || [];
      logTest('Order Management Access', 'PASS', `Found ${orders.length} orders`);
      
      if (orders.length > 0) {
        const testOrder = orders[0];
        logTest('Order Details', 'PASS', `Order ${testOrder.order_number}: $${testOrder.final_price} - ${testOrder.status}`);
        
        // Test order filtering
        const pendingOrdersResponse = await axios.get(`${API_BASE}/admin/orders?status=pending`, { headers: adminAuth.headers });
        if (pendingOrdersResponse.data.success) {
          const pendingOrders = pendingOrdersResponse.data.data.orders || [];
          logTest('Order Filtering', 'PASS', `${pendingOrders.length} pending orders found`);
        }
      }
    } else {
      logTest('Order Management Access', 'FAIL', 'Order management inaccessible');
    }
    
    // 5. Systems Management
    console.log('\n🖥️ Testing Systems Management...');
    const systemsResponse = await axios.get(`${API_BASE}/admin/systems`, { headers: adminAuth.headers });
    
    if (systemsResponse.data.success) {
      const systems = systemsResponse.data.data.systems || [];
      logTest('Systems Management Access', 'PASS', `Found ${systems.length} systems`);
      
      // Test system creation
      const newSystemData = {
        name_ar: 'نظام اختبار إداري',
        name_en: 'Admin Test System',
        description_ar: 'نظام للاختبار الإداري',
        description_en: 'System for admin testing',
        price: 199.99,
        category: 'testing',
        status: 'active'
      };
      
      const createSystemResponse = await axios.post(`${API_BASE}/admin/systems`, newSystemData, { headers: adminAuth.headers });
      
      if (createSystemResponse.data.success) {
        const newSystem = createSystemResponse.data.data || createSystemResponse.data.data.system;
        const systemId = newSystem.id || newSystem.system?.id;

        if (systemId) {
          logTest('System Creation', 'PASS', `System created with ID: ${systemId}`);

          // Test system deletion
          const deleteResponse = await axios.delete(`${API_BASE}/admin/systems/${systemId}`, { headers: adminAuth.headers });
          if (deleteResponse.data.success) {
            logTest('System Deletion', 'PASS', 'Test system deleted successfully');
          } else {
            logTest('System Deletion', 'FAIL', 'Failed to delete test system');
          }
        } else {
          logTest('System Creation', 'FAIL', 'System created but no ID returned');
        }
      } else {
        logTest('System Creation', 'FAIL', 'Failed to create test system');
      }
    } else {
      logTest('Systems Management Access', 'FAIL', 'Systems management inaccessible');
    }
    
    // 6. Technical Services Management
    console.log('\n🔧 Testing Technical Services Management...');
    const servicesResponse = await axios.get(`${API_BASE}/admin/technical-services`, { headers: adminAuth.headers });
    
    if (servicesResponse.data.success) {
      const services = servicesResponse.data.data || [];
      logTest('Services Management Access', 'PASS', `Found ${services.length} services`);
      
      // Test service creation
      const newServiceData = {
        name_ar: 'خدمة اختبار إدارية',
        name_en: 'Admin Test Service',
        description_ar: 'خدمة للاختبار الإداري',
        description_en: 'Service for admin testing',
        price: 99.99,
        category: 'testing',
        status: 'active'
      };
      
      const createServiceResponse = await axios.post(`${API_BASE}/admin/technical-services`, newServiceData, { headers: adminAuth.headers });
      
      if (createServiceResponse.data.success) {
        const newService = createServiceResponse.data.data || createServiceResponse.data.data.service;
        const serviceId = newService.id || newService.service?.id;

        if (serviceId) {
          logTest('Service Creation', 'PASS', `Service created with ID: ${serviceId}`);

          // Test service deletion
          const deleteResponse = await axios.delete(`${API_BASE}/admin/technical-services/${serviceId}`, { headers: adminAuth.headers });
          if (deleteResponse.data.success) {
            logTest('Service Deletion', 'PASS', 'Test service deleted successfully');
          } else {
            logTest('Service Deletion', 'FAIL', 'Failed to delete test service');
          }
        } else {
          logTest('Service Creation', 'FAIL', 'Service created but no ID returned');
        }
      } else {
        logTest('Service Creation', 'FAIL', 'Failed to create test service');
      }
    } else {
      logTest('Services Management Access', 'FAIL', 'Services management inaccessible');
    }
    
    // 7. Premium Content Management
    console.log('\n💎 Testing Premium Content Management...');
    const premiumResponse = await axios.get(`${API_BASE}/premium/admin`, { headers: adminAuth.headers });
    
    if (premiumResponse.data.success) {
      const premium = premiumResponse.data.data || [];
      logTest('Premium Content Access', 'PASS', `Found ${premium.length} premium items`);
    } else {
      logTest('Premium Content Access', 'FAIL', 'Premium content management inaccessible');
    }
    
    // 8. Analytics and Reporting
    console.log('\n📈 Testing Analytics and Reporting...');
    const analyticsResponse = await axios.get(`${API_BASE}/admin/analytics?period=30`, { headers: adminAuth.headers });
    
    if (analyticsResponse.data.success) {
      const analytics = analyticsResponse.data.data;
      logTest('Analytics Access', 'PASS', 'Analytics data accessible');
      logTest('Analytics Data', 'PASS', `Order trends: ${analytics.orderTrends?.length || 0} data points`);
    } else {
      logTest('Analytics Access', 'FAIL', 'Analytics inaccessible');
    }
    
    // 9. Database Integrity Check
    console.log('\n🗄️ Testing Database Integrity...');
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // Check admin user exists
    const [adminUsers] = await connection.execute('SELECT * FROM users WHERE role = "admin"');
    logTest('Admin User Database', 'PASS', `${adminUsers.length} admin users in database`);
    
    // Check data consistency
    const [orderUserCheck] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM orders o 
      LEFT JOIN users u ON o.user_id = u.id 
      WHERE u.id IS NULL
    `);
    
    if (orderUserCheck[0].count === 0) {
      logTest('Data Integrity', 'PASS', 'All orders have valid user references');
    } else {
      logTest('Data Integrity', 'FAIL', `${orderUserCheck[0].count} orphaned orders found`);
    }
    
    await connection.end();
    
  } catch (error) {
    logTest('Admin Dashboard Test', 'FAIL', error.response?.data?.error || error.message);
  }
  
  // Print final summary
  console.log('\n' + '=' * 60);
  console.log('📊 ADMIN DASHBOARD COMPREHENSIVE TEST SUMMARY');
  console.log('=' * 60);
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.test}: ${error.error}`);
    });
  }
  
  // Final assessment
  const successRate = (testResults.passed / testResults.total) * 100;
  console.log('\n🎯 ADMIN DASHBOARD ASSESSMENT:');
  
  if (successRate >= 95) {
    console.log('🟢 EXCELLENT - Admin dashboard fully functional for business management');
  } else if (successRate >= 85) {
    console.log('🟡 GOOD - Admin dashboard mostly working, minor issues to address');
  } else {
    console.log('🔴 NEEDS WORK - Critical admin dashboard issues need resolution');
  }
  
  console.log('\n🎉 Admin dashboard comprehensive testing completed!');
}

// Run the test
testAdminDashboardComprehensive().catch(console.error);
