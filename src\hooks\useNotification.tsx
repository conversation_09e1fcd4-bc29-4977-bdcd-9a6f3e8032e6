/**
 * Enhanced Notification System
 *
 * Comprehensive notification hook with support for different types,
 * actions, persistence, and accessibility
 */

import React, { createContext, useContext, useState, useCallback } from 'react'
import { X, CheckCircle, AlertCircle, AlertTriangle, Info, Bell } from 'lucide-react'

// Notification types
export interface NotificationAction {
  label: string
  labelEn?: string
  onClick: () => void
  variant?: 'primary' | 'secondary' | 'ghost'
}

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  titleEn?: string
  message?: string
  messageEn?: string
  duration?: number // in milliseconds, 0 for persistent
  action?: NotificationAction
  persistent?: boolean
  timestamp: Date
  read?: boolean
}

// Notification context
interface NotificationContextType {
  notifications: Notification[]
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => string
  removeNotification: (id: string) => void
  clearAll: () => void
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  unreadCount: number
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

// Notification provider component
export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([])

  // Add notification
  const addNotification = useCallback((notificationData: Omit<Notification, 'id' | 'timestamp'>) => {
    const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const notification: Notification = {
      ...notificationData,
      id,
      timestamp: new Date(),
      read: false
    }

    setNotifications(prev => [notification, ...prev])

    // Auto remove if not persistent
    if (!notification.persistent && notification.duration !== 0) {
      const duration = notification.duration || 5000
      setTimeout(() => {
        removeNotification(id)
      }, duration)
    }

    return id
  }, [])

  // Remove notification
  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }, [])

  // Clear all notifications
  const clearAll = useCallback(() => {
    setNotifications([])
  }, [])

  // Mark as read
  const markAsRead = useCallback((id: string) => {
    setNotifications(prev =>
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    )
  }, [])

  // Mark all as read
  const markAllAsRead = useCallback(() => {
    setNotifications(prev =>
      prev.map(n => ({ ...n, read: true }))
    )
  }, [])

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.read).length

  const value = {
    notifications,
    addNotification,
    removeNotification,
    clearAll,
    markAsRead,
    markAllAsRead,
    unreadCount
  }

  return (
    <NotificationContext.Provider value={value}>
      {children}
      <NotificationContainer />
    </NotificationContext.Provider>
  )
}

// Notification container component
const NotificationContainer: React.FC = () => {
  const context = useContext(NotificationContext)
  if (!context) return null

  const { notifications, removeNotification, markAsRead } = context

  // Show only recent notifications in toast format (last 3)
  const toastNotifications = notifications.slice(0, 3)

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
      {toastNotifications.map((notification) => (
        <NotificationToast
          key={notification.id}
          notification={notification}
          onClose={() => removeNotification(notification.id)}
          onRead={() => markAsRead(notification.id)}
        />
      ))}
    </div>
  )
}

// Individual notification toast
interface NotificationToastProps {
  notification: Notification
  onClose: () => void
  onRead: () => void
}

const NotificationToast: React.FC<NotificationToastProps> = ({
  notification,
  onClose,
  onRead
}) => {
  const { type, title, titleEn, message, messageEn, action, read } = notification

  // Icon mapping
  const icons = {
    success: CheckCircle,
    error: AlertCircle,
    warning: AlertTriangle,
    info: Info
  }

  // Color mapping
  const colors = {
    success: {
      bg: 'bg-green-900/20',
      border: 'border-green-500',
      icon: 'text-green-400',
      text: 'text-green-400'
    },
    error: {
      bg: 'bg-red-900/20',
      border: 'border-red-500',
      icon: 'text-red-400',
      text: 'text-red-400'
    },
    warning: {
      bg: 'bg-yellow-900/20',
      border: 'border-yellow-500',
      icon: 'text-yellow-400',
      text: 'text-yellow-400'
    },
    info: {
      bg: 'bg-blue-900/20',
      border: 'border-blue-500',
      icon: 'text-blue-400',
      text: 'text-blue-400'
    }
  }

  const Icon = icons[type as keyof typeof icons] || Info
  const colorScheme = colors[type as keyof typeof colors] || colors.info

  // Mark as read when clicked
  const handleClick = () => {
    if (!read) {
      onRead()
    }
  }

  return (
    <div
      className={`
        ${colorScheme.bg} ${colorScheme.border} border rounded-lg p-4 shadow-lg
        transform transition-all duration-300 ease-in-out
        hover:scale-105 cursor-pointer bg-slate-800
        ${read ? 'opacity-75' : 'opacity-100'}
      `}
      onClick={handleClick}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-start gap-3">
        {/* Icon */}
        <Icon className={`w-5 h-5 ${colorScheme.icon} flex-shrink-0 mt-0.5`} />

        {/* Content */}
        <div className="flex-1 min-w-0">
          {/* Title */}
          <div className="font-medium text-white">
            <p className="text-sm">{title}</p>
            {titleEn && (
              <p className="text-xs text-gray-300">{titleEn}</p>
            )}
          </div>

          {/* Message */}
          {(message || messageEn) && (
            <div className="mt-1 text-gray-300">
              {message && <p className="text-sm">{message}</p>}
              {messageEn && <p className="text-xs">{messageEn}</p>}
            </div>
          )}

          {/* Action */}
          {action && (
            <div className="mt-3">
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  action.onClick()
                }}
                className={`
                  text-sm font-medium px-3 py-1 rounded-md transition-colors duration-200
                  ${action.variant === 'primary'
                    ? `${colorScheme.text} hover:bg-gray-700`
                    : 'text-gray-300 hover:text-white hover:bg-gray-700'
                  }
                `}
              >
                {action.label}
                {action.labelEn && (
                  <span className="block text-xs opacity-75">{action.labelEn}</span>
                )}
              </button>
            </div>
          )}
        </div>

        {/* Close button */}
        <button
          onClick={(e) => {
            e.stopPropagation()
            onClose()
          }}
          className="text-gray-400 hover:text-white transition-colors duration-200 p-1"
          aria-label="Close notification"
        >
          <X className="w-4 h-4" />
        </button>
      </div>

      {/* Unread indicator */}
      {!read && (
        <div className="absolute top-2 left-2 w-2 h-2 bg-blue-500 rounded-full" />
      )}
    </div>
  )
}

// Main hook
export const useNotification = () => {
  const context = useContext(NotificationContext)

  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider')
  }

  const { addNotification, removeNotification, clearAll, markAsRead, markAllAsRead, notifications, unreadCount } = context

  // Get language from document or default to 'ar'
  const language = document.documentElement.lang || 'ar'

  // Convenience methods
  const showSuccess = useCallback((title: string, options?: Partial<Notification>) => {
    return addNotification({
      type: 'success',
      title,
      ...options
    })
  }, [addNotification])

  const showError = useCallback((title: string, options?: Partial<Notification>) => {
    return addNotification({
      type: 'error',
      title,
      persistent: true, // Errors are persistent by default
      ...options
    })
  }, [addNotification])

  const showWarning = useCallback((title: string, options?: Partial<Notification>) => {
    return addNotification({
      type: 'warning',
      title,
      ...options
    })
  }, [addNotification])

  const showInfo = useCallback((title: string, options?: Partial<Notification>) => {
    return addNotification({
      type: 'info',
      title,
      ...options
    })
  }, [addNotification])

  // Generic showNotification function for backward compatibility
  const showNotification = useCallback((notification: any) => {
    if (notification.type === 'confirm') {
      // Handle confirm type notifications with action
      const { type, onConfirm, ...rest } = notification
      const notificationId = addNotification({
        ...rest,
        type: 'warning',
        title: notification.message || notification.title || 'تأكيد',
        action: onConfirm ? {
          label: language === 'ar' ? 'تأكيد' : 'Confirm',
          onClick: () => {
            // Execute the confirm action
            if (onConfirm) {
              onConfirm();
            }
            // Remove the notification after confirmation
            removeNotification(notificationId);
          },
          variant: 'primary' as const
        } : undefined,
        persistent: true
      })
      return notificationId;
    } else {
      // Handle regular notifications
      const { type, ...rest } = notification
      return addNotification({
        ...rest,
        title: notification.message || notification.title || '',
        type: type || 'info'
      })
    }
  }, [addNotification, removeNotification, language])

  return {
    // Basic methods
    addNotification,
    removeNotification,
    clearAll,
    markAsRead,
    markAllAsRead,

    // Convenience methods
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showNotification, // Added for backward compatibility

    // State
    notifications,
    unreadCount
  }
}