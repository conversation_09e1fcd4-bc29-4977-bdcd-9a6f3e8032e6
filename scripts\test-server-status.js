const axios = require('axios');

async function testServerStatus() {
  try {
    console.log('Testing server status...');
    const response = await axios.get('http://localhost:3001/api/systems');
    console.log('✅ Server is running');
    console.log('Response:', response.data.success ? 'Success' : 'Failed');
  } catch (error) {
    console.log('❌ Server is not responding:', error.message);
  }
}

testServerStatus();
