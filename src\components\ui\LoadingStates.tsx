/**
 * Enhanced Loading States Components
 * 
 * Comprehensive loading indicators for different use cases
 * with accessibility and internationalization support
 */

import React from 'react'
import { Loader2, RefreshCw, Download, Upload, Search } from 'lucide-react'

// Basic loading spinner
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'secondary' | 'accent' | 'white'
  className?: string
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }
  
  const colorClasses = {
    primary: 'text-interactive-primary',
    secondary: 'text-interactive-secondary',
    accent: 'text-interactive-accent',
    white: 'text-white'
  }
  
  return (
    <Loader2 
      className={`animate-spin ${sizeClasses[size]} ${colorClasses[color]} ${className}`}
      aria-label="Loading"
    />
  )
}

// Loading with text
interface LoadingWithTextProps {
  text?: string
  textEn?: string
  size?: 'sm' | 'md' | 'lg'
  direction?: 'horizontal' | 'vertical'
  className?: string
}

export const LoadingWithText: React.FC<LoadingWithTextProps> = ({
  text = 'جاري التحميل...',
  textEn = 'Loading...',
  size = 'md',
  direction = 'horizontal',
  className = ''
}) => {
  const isHorizontal = direction === 'horizontal'
  
  return (
    <div className={`flex items-center justify-center ${isHorizontal ? 'flex-row gap-3' : 'flex-col gap-2'} ${className}`}>
      <LoadingSpinner size={size} />
      <div className={`text-center ${isHorizontal ? '' : 'mt-2'}`}>
        <p className="text-sm text-text-secondary font-medium">{text}</p>
        <p className="text-xs text-text-tertiary">{textEn}</p>
      </div>
    </div>
  )
}

// Progress bar loading
interface ProgressLoadingProps {
  progress: number // 0-100
  text?: string
  textEn?: string
  showPercentage?: boolean
  className?: string
}

export const ProgressLoading: React.FC<ProgressLoadingProps> = ({
  progress,
  text = 'جاري التحميل...',
  textEn = 'Loading...',
  showPercentage = true,
  className = ''
}) => {
  const clampedProgress = Math.max(0, Math.min(100, progress))
  
  return (
    <div className={`w-full max-w-md ${className}`}>
      {/* Text */}
      <div className="flex justify-between items-center mb-2">
        <div>
          <p className="text-sm text-text-primary font-medium">{text}</p>
          <p className="text-xs text-text-tertiary">{textEn}</p>
        </div>
        {showPercentage && (
          <span className="text-sm text-text-secondary font-mono">
            {Math.round(clampedProgress)}%
          </span>
        )}
      </div>
      
      {/* Progress bar */}
      <div className="w-full bg-background-tertiary rounded-full h-2 overflow-hidden">
        <div 
          className="h-full bg-interactive-primary transition-all duration-300 ease-out rounded-full"
          style={{ width: `${clampedProgress}%` }}
        />
      </div>
    </div>
  )
}

// Skeleton loading for content
interface SkeletonProps {
  width?: string | number
  height?: string | number
  className?: string
  variant?: 'text' | 'rectangular' | 'circular'
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = '1rem',
  className = '',
  variant = 'rectangular'
}) => {
  const variantClasses = {
    text: 'rounded',
    rectangular: 'rounded-md',
    circular: 'rounded-full'
  }
  
  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height
  }
  
  return (
    <div 
      className={`bg-background-tertiary animate-pulse ${variantClasses[variant]} ${className}`}
      style={style}
      aria-label="Loading content"
    />
  )
}

// Card skeleton
export const CardSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`bg-background-card border border-background-border rounded-lg p-4 ${className}`}>
      <div className="space-y-3">
        {/* Header */}
        <div className="flex items-center space-x-3 space-x-reverse">
          <Skeleton variant="circular" width={40} height={40} />
          <div className="flex-1 space-y-2">
            <Skeleton width="60%" height="1rem" />
            <Skeleton width="40%" height="0.75rem" />
          </div>
        </div>
        
        {/* Content */}
        <div className="space-y-2">
          <Skeleton width="100%" height="0.75rem" />
          <Skeleton width="80%" height="0.75rem" />
          <Skeleton width="90%" height="0.75rem" />
        </div>
        
        {/* Footer */}
        <div className="flex justify-between items-center pt-2">
          <Skeleton width="30%" height="2rem" />
          <Skeleton width="20%" height="1.5rem" />
        </div>
      </div>
    </div>
  )
}

// List skeleton
interface ListSkeletonProps {
  items?: number
  className?: string
}

export const ListSkeleton: React.FC<ListSkeletonProps> = ({ 
  items = 5, 
  className = '' 
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      {Array.from({ length: items }).map((_, index) => (
        <div key={index} className="flex items-center space-x-3 space-x-reverse p-3 bg-background-card rounded-lg">
          <Skeleton variant="circular" width={48} height={48} />
          <div className="flex-1 space-y-2">
            <Skeleton width="70%" height="1rem" />
            <Skeleton width="50%" height="0.75rem" />
          </div>
          <Skeleton width="20%" height="2rem" />
        </div>
      ))}
    </div>
  )
}

// Contextual loading states
interface ContextualLoadingProps {
  type: 'search' | 'download' | 'upload' | 'refresh' | 'processing'
  text?: string
  textEn?: string
  className?: string
}

export const ContextualLoading: React.FC<ContextualLoadingProps> = ({
  type,
  text,
  textEn,
  className = ''
}) => {
  const configs = {
    search: {
      icon: Search,
      defaultText: 'جاري البحث...',
      defaultTextEn: 'Searching...',
      color: 'text-interactive-accent'
    },
    download: {
      icon: Download,
      defaultText: 'جاري التحميل...',
      defaultTextEn: 'Downloading...',
      color: 'text-interactive-primary'
    },
    upload: {
      icon: Upload,
      defaultText: 'جاري الرفع...',
      defaultTextEn: 'Uploading...',
      color: 'text-interactive-primary'
    },
    refresh: {
      icon: RefreshCw,
      defaultText: 'جاري التحديث...',
      defaultTextEn: 'Refreshing...',
      color: 'text-interactive-secondary'
    },
    processing: {
      icon: Loader2,
      defaultText: 'جاري المعالجة...',
      defaultTextEn: 'Processing...',
      color: 'text-interactive-primary'
    }
  }
  
  const config = configs[type]
  const Icon = config.icon
  
  return (
    <div className={`flex items-center justify-center gap-3 ${className}`}>
      <Icon className={`w-5 h-5 animate-spin ${config.color}`} />
      <div>
        <p className="text-sm text-text-secondary font-medium">
          {text || config.defaultText}
        </p>
        <p className="text-xs text-text-tertiary">
          {textEn || config.defaultTextEn}
        </p>
      </div>
    </div>
  )
}

// Full page loading
interface FullPageLoadingProps {
  text?: string
  textEn?: string
  showLogo?: boolean
}

export const FullPageLoading: React.FC<FullPageLoadingProps> = ({
  text = 'جاري تحميل خان فشارية...',
  textEn = 'Loading Khanfashariya...',
  showLogo = true
}) => {
  return (
    <div className="fixed inset-0 bg-background-primary flex items-center justify-center z-50">
      <div className="text-center space-y-6">
        {/* Logo */}
        {showLogo && (
          <div className="w-16 h-16 mx-auto bg-interactive-primary rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xl">خ</span>
          </div>
        )}
        
        {/* Loading animation */}
        <LoadingWithText 
          text={text}
          textEn={textEn}
          size="lg"
          direction="vertical"
        />
        
        {/* Additional info */}
        <p className="text-xs text-text-tertiary max-w-xs">
          يرجى الانتظار بينما نقوم بتحميل المحتوى...
        </p>
      </div>
    </div>
  )
}

// Loading overlay for components
interface LoadingOverlayProps {
  isLoading: boolean
  children: React.ReactNode
  text?: string
  textEn?: string
  blur?: boolean
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  children,
  text = 'جاري التحميل...',
  textEn = 'Loading...',
  blur = true
}) => {
  return (
    <div className="relative">
      {children}
      
      {isLoading && (
        <div className={`
          absolute inset-0 bg-background-primary bg-opacity-80 
          flex items-center justify-center z-10 rounded-lg
          ${blur ? 'backdrop-blur-sm' : ''}
        `}>
          <LoadingWithText 
            text={text}
            textEn={textEn}
            direction="vertical"
          />
        </div>
      )}
    </div>
  )
}

// Hook for managing loading states
export const useLoadingState = (initialState = false) => {
  const [isLoading, setIsLoading] = React.useState(initialState)
  const [error, setError] = React.useState<string | null>(null)
  
  const startLoading = () => {
    setIsLoading(true)
    setError(null)
  }
  
  const stopLoading = () => {
    setIsLoading(false)
  }
  
  const setLoadingError = (errorMessage: string) => {
    setIsLoading(false)
    setError(errorMessage)
  }
  
  const reset = () => {
    setIsLoading(false)
    setError(null)
  }
  
  return {
    isLoading,
    error,
    startLoading,
    stopLoading,
    setLoadingError,
    reset
  }
}
