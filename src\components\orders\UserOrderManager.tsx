import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotification } from '../../hooks/useNotification';
import { getUserOrders } from '../../lib/apiServices';
import {
  Package,
  MessageSquare,
  Eye,
  Download,
  Calendar,
  DollarSign,
  CheckCircle,
  Clock,
  AlertCircle,
  X,
  Send,
  Paperclip,
  Star,
  Filter,
  Search,
  RefreshCw,
  FileText,
  User,
  CreditCard,
  Truck,
  Settings,
  Bell,
  ArrowRight,
  ArrowLeft,
  ChevronDown,
  List,
  Grid,
  ChevronUp
} from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Modal from '../ui/Modal';
import Card from '../ui/Card';
import Pagination from '../ui/Pagination';

// Enhanced Database Order interface (matches new API response)
interface DatabaseOrder {
  id: string;
  user_id: string;
  order_number: string;
  order_type: 'system_service' | 'technical_service' | 'premium_content' | 'premium_package' | 'custom_request';
  order_category?: 'standard' | 'premium_base' | 'premium_custom' | 'subscription' | 'maintenance';
  item_id: string;
  item_name_ar: string;
  item_name_en: string;
  quantity: number;

  // Enhanced pricing structure
  unit_price: number;
  base_price?: number;
  addons_price?: number;
  subscription_price?: number;
  maintenance_price?: number;
  total_price: number;
  discount_amount: number;
  tax_amount?: number;
  final_price: number;

  // Enhanced status management
  status: 'pending' | 'confirmed' | 'in_progress' | 'testing' | 'completed' | 'cancelled' | 'refunded' | 'on_hold' | 'expired';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  progress_percentage?: number;

  // Payment and subscription
  payment_status: 'pending' | 'paid' | 'partial' | 'failed' | 'refunded' | 'disputed';
  payment_method?: string;
  payment_reference?: string;
  subscription_type?: 'none' | 'monthly' | 'quarterly' | 'yearly' | 'lifetime';

  // Service details
  maintenance_included?: boolean;
  installation_included?: boolean;
  support_level?: 'basic' | 'standard' | 'premium' | 'enterprise';

  // Enhanced order details
  order_details?: string; // JSON string
  selected_addons?: string; // JSON string
  customer_requirements?: string;

  // Timeline
  created_at: string;
  updated_at: string;
  confirmed_at?: string;
  started_at?: string;
  estimated_completion?: string;
  completed_at?: string;
  delivery_date?: string;
  completion_date?: string; // Legacy field

  // Notes
  notes_ar?: string;
  notes_en?: string;
  admin_notes?: string;
}

// UI Order interface (for display purposes)
interface Order {
  id: string;
  items: OrderItem[];
  totalAmount: number;
  discountAmount: number;
  finalAmount: number;
  status: 'pending' | 'processing' | 'completed' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: string;
  notes: string;
  adminNotes: string;
  messages: OrderMessage[];
  createdAt: string;
  updatedAt: string;
  estimatedDelivery?: string;
  trackingInfo?: {
    stage: string;
    description: string;
    timestamp: string;
  }[];
}

interface OrderItem {
  id: string;
  type: 'system' | 'service' | 'premium';
  name: { ar: string; en: string };
  description: { ar: string; en: string };
  price: number;
  quantity: number;
  category: string;
}

interface OrderMessage {
  id: string;
  senderId: string;
  senderType: 'user' | 'admin';
  senderName: string;
  message: string;
  timestamp: string;
  attachments?: string[];
  isRead: boolean;
}

interface UserOrderManagerProps {
  userId: string;
}

/**
 * User Order Management and Communication System
 */
const UserOrderManager: React.FC<UserOrderManagerProps> = ({ userId }) => {
  const { t, language } = useTranslation();
  const { showNotification } = useNotification();
  
  const [orders, setOrders] = useState<Order[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'status'>('date');
  const [expandedOrder, setExpandedOrder] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Message form state
  const [messageData, setMessageData] = useState({
    message: '',
    attachments: [] as string[]
  });

  useEffect(() => {
    loadOrders();
  }, [userId]);

  const loadOrders = async () => {
    setLoading(true);
    try {
      const ordersResult = await getUserOrders(userId);

      if (ordersResult.data && ordersResult.data.length > 0) {
        // Convert database orders to the expected format
        const convertedOrders: Order[] = ordersResult.data.map((dbOrder: DatabaseOrder) => {
          return {
          id: dbOrder.id || dbOrder.order_number,
          items: [
            {
              id: dbOrder.item_id || dbOrder.id,
              type: dbOrder.order_type === 'system_service' ? 'system' :
                    dbOrder.order_type === 'technical_service' ? 'service' :
                    dbOrder.order_type === 'premium_content' ? 'premium' : 'system',
              name: {
                ar: dbOrder.item_name_ar || 'خدمة غير محددة',
                en: dbOrder.item_name_en || 'Unspecified Service'
              },
              description: {
                ar: dbOrder.notes_ar || 'لا توجد تفاصيل',
                en: dbOrder.notes_en || 'No details available'
              },
              price: parseFloat(dbOrder.unit_price?.toString() || dbOrder.final_price?.toString() || '0') || 0,
              quantity: parseInt(dbOrder.quantity?.toString() || '1') || 1,
              category: dbOrder.order_type || 'general'
            }
          ],
          totalAmount: parseFloat(dbOrder.total_price?.toString() || dbOrder.final_price?.toString() || '0') || 0,
          discountAmount: parseFloat(dbOrder.discount_amount?.toString() || '0') || 0,
          finalAmount: parseFloat(dbOrder.final_price?.toString() || '0') || 0,
          status: dbOrder.status === 'pending' ? 'pending' :
                  dbOrder.status === 'completed' ? 'completed' :
                  dbOrder.status === 'cancelled' ? 'cancelled' :
                  dbOrder.status === 'refunded' ? 'cancelled' :
                  dbOrder.status === 'expired' ? 'cancelled' :
                  ['confirmed', 'in_progress', 'testing', 'on_hold'].includes(dbOrder.status) ? 'processing' : 'processing',
          paymentStatus: dbOrder.payment_status || 'pending',
          paymentMethod: dbOrder.payment_method || 'Unknown',
          notes: dbOrder.notes_en || '',
          adminNotes: dbOrder.admin_notes || '',
          messages: [],
          createdAt: dbOrder.created_at ? new Date(dbOrder.created_at).toISOString() : new Date().toISOString(),
          updatedAt: dbOrder.updated_at ? new Date(dbOrder.updated_at).toISOString() : new Date().toISOString(),
          estimatedDelivery: dbOrder.delivery_date ? new Date(dbOrder.delivery_date).toISOString() : undefined,
          trackingInfo: [
            {
              stage: 'order_placed',
              description: language === 'ar' ? 'تم إنشاء الطلب' : 'Order placed',
              timestamp: dbOrder.created_at ? new Date(dbOrder.created_at).toISOString() : new Date().toISOString()
            },
            ...(dbOrder.confirmed_at ? [{
              stage: 'confirmed',
              description: language === 'ar' ? 'تم تأكيد الطلب' : 'Order confirmed',
              timestamp: new Date(dbOrder.confirmed_at).toISOString()
            }] : []),
            ...(dbOrder.started_at ? [{
              stage: 'in_progress',
              description: language === 'ar' ? 'بدء العمل' : 'Work started',
              timestamp: new Date(dbOrder.started_at).toISOString()
            }] : []),
            ...(dbOrder.status === 'testing' ? [{
              stage: 'testing',
              description: language === 'ar' ? 'قيد الاختبار' : 'Testing phase',
              timestamp: dbOrder.updated_at ? new Date(dbOrder.updated_at).toISOString() : new Date().toISOString()
            }] : []),
            ...(dbOrder.status === 'completed' ? [{
              stage: 'completed',
              description: language === 'ar' ? 'تم الإنجاز' : 'Completed',
              timestamp: dbOrder.completed_at ? new Date(dbOrder.completed_at).toISOString() :
                        (dbOrder.completion_date ? new Date(dbOrder.completion_date).toISOString() :
                        (dbOrder.updated_at ? new Date(dbOrder.updated_at).toISOString() : new Date().toISOString()))
            }] : [])
          ]
        };
        });

        setOrders(convertedOrders);
      } else {
        setOrders([]);
      }
    } catch (error) {
      console.error('Error loading orders:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في تحميل الطلبات' : 'Failed to load orders'
      });
    }
    setLoading(false);
  };

  const sendMessage = async (orderId: string) => {
    if (!messageData.message.trim()) return;

    try {
      const newMessage: OrderMessage = {
        id: Date.now().toString(),
        senderId: userId,
        senderType: 'user',
        senderName: 'أحمد محمد', // This would come from user data
        message: messageData.message,
        timestamp: new Date().toISOString(),
        attachments: messageData.attachments,
        isRead: false
      };

      setOrders(prev => prev.map(order => 
        order.id === orderId 
          ? { ...order, messages: [...order.messages, newMessage] }
          : order
      ));

      setMessageData({ message: '', attachments: [] });
      
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم إرسال الرسالة' : 'Message sent'
      });
    } catch (error) {
      console.error('Error sending message:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في إرسال الرسالة' : 'Failed to send message'
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4 text-yellow-400" />;
      case 'processing': return <Package className="w-4 h-4 text-blue-400" />;
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'cancelled': return <X className="w-4 h-4 text-red-400" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'processing': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'completed': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'cancelled': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US', {
        calendar: 'gregory',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  const formatDateTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString(language === 'ar' ? 'ar-EG' : 'en-US', {
        calendar: 'gregory',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.items.some(item => 
                           item.name[language].toLowerCase().includes(searchTerm.toLowerCase())
                         );
    const matchesStatus = filterStatus === 'all' || order.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const sortedOrders = [...filteredOrders].sort((a, b) => {
    switch (sortBy) {
      case 'date':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      case 'amount':
        return b.finalAmount - a.finalAmount;
      case 'status':
        return a.status.localeCompare(b.status);
      default:
        return 0;
    }
  });

  // Pagination logic
  const totalPages = Math.ceil(sortedOrders.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedOrders = sortedOrders.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterStatus]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-secondary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-6 space-y-4 lg:space-y-0">
        <div>
          <h1 className="text-2xl font-bold text-white flex items-center">
            <Package className="w-8 h-8 mr-3 text-accent" />
            {language === 'ar' ? 'طلباتي' : 'My Orders'}
          </h1>
          <p className="text-gray-400 text-sm mt-1">
            {language === 'ar' ? 'تتبع ومراجعة جميع طلباتك' : 'Track and review all your orders'}
          </p>
        </div>

        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Button variant="outline" onClick={loadOrders} size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            {language === 'ar' ? 'تصدير' : 'Export'}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-gradient-to-br from-blue-500/10 to-blue-600/5 border border-blue-500/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-300 text-sm font-medium">{language === 'ar' ? 'إجمالي الطلبات' : 'Total Orders'}</p>
              <p className="text-2xl font-bold text-white">{orders.length}</p>
            </div>
            <Package className="w-8 h-8 text-blue-400" />
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-500/10 to-green-600/5 border border-green-500/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-300 text-sm font-medium">{language === 'ar' ? 'مكتملة' : 'Completed'}</p>
              <p className="text-2xl font-bold text-white">{orders.filter(o => o.status === 'completed').length}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className="bg-gradient-to-br from-yellow-500/10 to-yellow-600/5 border border-yellow-500/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-yellow-300 text-sm font-medium">{language === 'ar' ? 'معلقة' : 'Pending'}</p>
              <p className="text-2xl font-bold text-white">{orders.filter(o => o.status === 'pending').length}</p>
            </div>
            <Clock className="w-8 h-8 text-yellow-400" />
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-500/10 to-purple-600/5 border border-purple-500/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-300 text-sm font-medium">{language === 'ar' ? 'إجمالي المبلغ' : 'Total Amount'}</p>
              <p className="text-2xl font-bold text-white">
                ${orders.reduce((sum, o) => sum + (parseFloat(o.finalAmount?.toString() || '0') || 0), 0).toFixed(2)}
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-purple-400" />
          </div>
        </div>
      </div>



      {/* Enhanced Filters */}
      <Card className="mb-6">
        <Card.Body>
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder={language === 'ar' ? 'البحث في الطلبات...' : 'Search orders...'}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                leftIcon={<Search />}
              />
            </div>
            <div className="flex space-x-4 rtl:space-x-reverse">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-4 py-2 bg-primary/50 border border-accent/30 rounded-lg text-white"
              >
                <option value="all">{language === 'ar' ? 'جميع الحالات' : 'All Status'}</option>
                <option value="pending">{language === 'ar' ? 'معلق' : 'Pending'}</option>
                <option value="processing">{language === 'ar' ? 'قيد التنفيذ' : 'Processing'}</option>
                <option value="completed">{language === 'ar' ? 'مكتمل' : 'Completed'}</option>
                <option value="cancelled">{language === 'ar' ? 'ملغي' : 'Cancelled'}</option>
              </select>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-4 py-2 bg-primary/50 border border-accent/30 rounded-lg text-white"
              >
                <option value="date">{language === 'ar' ? 'التاريخ' : 'Date'}</option>
                <option value="amount">{language === 'ar' ? 'المبلغ' : 'Amount'}</option>
                <option value="status">{language === 'ar' ? 'الحالة' : 'Status'}</option>
              </select>

              {/* View Mode Toggle */}
              <div className="flex items-center bg-primary/50 border border-accent/30 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'list'
                      ? 'bg-secondary text-white'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <List className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'grid'
                      ? 'bg-secondary text-white'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <Grid className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </Card.Body>
      </Card>

      {/* Orders List */}
      {sortedOrders.length === 0 ? (
        <Card>
          <Card.Body>
            <div className="text-center py-12">
              <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                {language === 'ar' ? 'لا توجد طلبات' : 'No Orders Found'}
              </h3>
              <p className="text-gray-400">
                {language === 'ar' ? 'لم تقم بأي طلبات بعد' : 'You haven\'t made any orders yet'}
              </p>
            </div>
          </Card.Body>
        </Card>
      ) : (
        <>
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4' : 'space-y-4'}>
            {paginatedOrders.map((order) => (
            <Card key={order.id} className="hover:border-secondary/50 transition-all duration-300">
              <Card.Body className={viewMode === 'grid' ? 'p-4' : 'p-6'}>
                <div className={viewMode === 'grid' ? 'space-y-3' : 'space-y-4'}>
                  {/* Order Header */}
                  <div className={`flex items-center ${viewMode === 'grid' ? 'flex-col space-y-2' : 'justify-between'}`}>
                    <div className={`flex items-center ${viewMode === 'grid' ? 'flex-col text-center space-y-2' : 'space-x-4 rtl:space-x-reverse'}`}>
                      <div className={`${viewMode === 'grid' ? 'w-10 h-10' : 'w-12 h-12'} bg-gradient-to-br from-secondary to-accent rounded-lg flex items-center justify-center`}>
                        <Package className={`${viewMode === 'grid' ? 'w-5 h-5' : 'w-6 h-6'} text-white`} />
                      </div>
                      <div>
                        <h3 className={`${viewMode === 'grid' ? 'text-base' : 'text-lg'} font-semibold text-white`}>
                          {order.items[0]?.name[language] || (language === 'ar' ? 'طلب رقم' : 'Order')} #{order.id.slice(0, 8)}
                        </h3>
                        <p className="text-gray-400 text-sm">
                          {formatDate(order.createdAt)} • ${order.finalAmount}
                        </p>
                      </div>
                    </div>

                    <div className={`flex items-center ${viewMode === 'grid' ? 'justify-center space-x-2' : 'space-x-3'} rtl:space-x-reverse`}>
                      <span className={`px-2 py-1 rounded-md text-xs border flex items-center ${getStatusColor(order.status)}`}>
                        {getStatusIcon(order.status)}
                        <span className="ml-1">
                          {language === 'ar' ?
                            (order.status === 'pending' ? 'معلق' :
                             order.status === 'processing' ? 'قيد التنفيذ' :
                             order.status === 'completed' ? 'مكتمل' : 'ملغي') :
                            order.status.charAt(0).toUpperCase() + order.status.slice(1)
                          }
                        </span>
                      </span>

                      {viewMode === 'list' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setExpandedOrder(expandedOrder === order.id ? null : order.id)}
                        >
                          {expandedOrder === order.id ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                        </Button>
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedOrder(order);
                          setShowOrderDetails(true);
                        }}
                        className="bg-blue-500/10 border-blue-500/30 text-blue-400 hover:bg-blue-500/20"
                      >
                        <Eye className="w-3 h-3 mr-1" />
                        {viewMode === 'grid' ? '' : (language === 'ar' ? 'عرض' : 'View')}
                      </Button>
                    </div>
                  </div>

                  {/* Order Items Preview */}
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <span className="text-gray-400 text-sm">
                      {language === 'ar' ? 'العناصر:' : 'Items:'}
                    </span>
                    <div className="flex space-x-2 rtl:space-x-reverse">
                      {order.items.slice(0, 2).map((item, index) => (
                        <span key={index} className="text-white text-sm bg-accent/20 px-2 py-1 rounded">
                          {item.name[language]}
                        </span>
                      ))}
                      {order.items.length > 2 && (
                        <span className="text-gray-400 text-sm">
                          +{order.items.length - 2} {language === 'ar' ? 'المزيد' : 'more'}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Expanded Content */}
                  {expandedOrder === order.id && (
                    <div className="border-t border-accent/20 pt-4 space-y-4">
                      {/* Order Items */}
                      <div>
                        <h4 className="text-white font-medium mb-3">
                          {language === 'ar' ? 'تفاصيل الطلب' : 'Order Details'}
                        </h4>
                        <div className="space-y-2">
                          {order.items.map((item) => (
                            <div key={item.id} className="flex justify-between items-center p-3 bg-primary/30 rounded-lg">
                              <div>
                                <p className="text-white font-medium">{item.name[language]}</p>
                                <p className="text-gray-400 text-sm">{item.description[language]}</p>
                                <p className="text-gray-400 text-sm">
                                  {language === 'ar' ? 'الكمية:' : 'Qty:'} {item.quantity}
                                </p>
                              </div>
                              <span className="text-secondary font-bold">${item.price * item.quantity}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Order Summary */}
                      <div className="bg-accent/5 p-4 rounded-lg">
                        <div className="space-y-2">
                          <div className="flex justify-between text-gray-300">
                            <span>{language === 'ar' ? 'المجموع الفرعي' : 'Subtotal'}</span>
                            <span>${order.totalAmount}</span>
                          </div>
                          {order.discountAmount > 0 && (
                            <div className="flex justify-between text-green-400">
                              <span>{language === 'ar' ? 'الخصم' : 'Discount'}</span>
                              <span>-${order.discountAmount}</span>
                            </div>
                          )}
                          <div className="flex justify-between text-white font-bold text-lg border-t border-accent/20 pt-2">
                            <span>{language === 'ar' ? 'المجموع النهائي' : 'Final Total'}</span>
                            <span>${order.finalAmount}</span>
                          </div>
                        </div>
                      </div>

                      {/* Tracking Information */}
                      {order.trackingInfo && (
                        <div>
                          <h4 className="text-white font-medium mb-3 flex items-center">
                            <Truck className="w-4 h-4 mr-2" />
                            {language === 'ar' ? 'تتبع الطلب' : 'Order Tracking'}
                          </h4>
                          <div className="space-y-3">
                            {order.trackingInfo.map((track, index) => (
                              <div key={index} className="flex items-center space-x-3 rtl:space-x-reverse">
                                <div className="w-3 h-3 bg-secondary rounded-full"></div>
                                <div className="flex-1">
                                  <p className="text-white font-medium">{track.description}</p>
                                  <p className="text-gray-400 text-sm">{formatDateTime(track.timestamp)}</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Messages */}
                      {order.messages.length > 0 && (
                        <div>
                          <h4 className="text-white font-medium mb-3 flex items-center">
                            <MessageSquare className="w-4 h-4 mr-2" />
                            {language === 'ar' ? 'الرسائل' : 'Messages'}
                            {order.messages.some(m => !m.isRead && m.senderType === 'admin') && (
                              <span className="ml-2 w-2 h-2 bg-red-500 rounded-full"></span>
                            )}
                          </h4>
                          <div className="space-y-3 max-h-64 overflow-y-auto">
                            {order.messages.map((message) => (
                              <div
                                key={message.id}
                                className={`p-3 rounded-lg ${
                                  message.senderType === 'user'
                                    ? 'bg-secondary/20 ml-8 rtl:ml-0 rtl:mr-8'
                                    : 'bg-accent/20 mr-8 rtl:mr-0 rtl:ml-8'
                                }`}
                              >
                                <div className="flex justify-between items-start mb-2">
                                  <span className="text-white font-medium text-sm">{message.senderName}</span>
                                  <span className="text-gray-400 text-xs">{formatDateTime(message.timestamp)}</span>
                                </div>
                                <p className="text-gray-300">{message.message}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex space-x-3 rtl:space-x-reverse pt-4 border-t border-accent/20">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedOrder(order);
                            setShowOrderDetails(true);
                          }}
                        >
                          <Eye className="w-4 h-4 mr-2" />
                          {language === 'ar' ? 'عرض التفاصيل' : 'View Details'}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedOrder(order);
                            setShowMessageModal(true);
                          }}
                        >
                          <MessageSquare className="w-4 h-4 mr-2" />
                          {language === 'ar' ? 'إرسال رسالة' : 'Send Message'}
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="w-4 h-4 mr-2" />
                          {language === 'ar' ? 'تحميل الفاتورة' : 'Download Invoice'}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </Card.Body>
            </Card>
          ))}
          </div>

          {/* Pagination */}
          {sortedOrders.length > 0 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={sortedOrders.length}
              itemsPerPage={itemsPerPage}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
              itemsPerPageOptions={[5, 10, 15, 20]}
              className="mt-6"
            />
          )}
        </>
      )}

      {/* Message Modal */}
      {showMessageModal && selectedOrder && (
        <Modal
          isOpen={showMessageModal}
          onClose={() => setShowMessageModal(false)}
          title={`${language === 'ar' ? 'إرسال رسالة - طلب رقم' : 'Send Message - Order'} #${selectedOrder.id}`}
          size="md"
        >
          <Modal.Body>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-secondary mb-2">
                  {language === 'ar' ? 'الرسالة' : 'Message'}
                </label>
                <textarea
                  value={messageData.message}
                  onChange={(e) => setMessageData(prev => ({ ...prev, message: e.target.value }))}
                  rows={4}
                  className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                  placeholder={language === 'ar' ? 'اكتب رسالتك هنا...' : 'Type your message here...'}
                />
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowMessageModal(false)}>
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button
              variant="primary"
              onClick={() => sendMessage(selectedOrder.id)}
              disabled={!messageData.message.trim()}
            >
              <Send className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'إرسال' : 'Send'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}
    </div>
  );
};

export default UserOrderManager;
