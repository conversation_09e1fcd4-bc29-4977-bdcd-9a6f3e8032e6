{"localStorage": [{"file": "scripts\\migrate-localstorage-to-mysql.js", "line": 38, "content": "const value = localStorage.getItem(key);", "context": [{"line": 36, "content": "if (key && key.startsWith('k<PERSON><PERSON><PERSON><PERSON>_')) {", "current": false}, {"line": 37, "content": "try {", "current": false}, {"line": 38, "content": "const value = localStorage.getItem(key);", "current": true}, {"line": 39, "content": "result[key] = JSON.parse(value);", "current": false}, {"line": 40, "content": "} catch {", "current": false}]}, {"file": "scripts\\test-homepage-display.js", "line": 89, "content": "return window.localStorage.getItem('services') || 'No services in localStorage';", "context": [{"line": 87, "content": "// Check if services data is loaded in console", "current": false}, {"line": 88, "content": "const servicesData = await page.evaluate(() => {", "current": false}, {"line": 89, "content": "return window.localStorage.getItem('services') || 'No services in localStorage';", "current": true}, {"line": 90, "content": "});", "current": false}, {"line": 91, "content": "console.log('   🔍 Services in localStorage:', servicesData.substring(0, 100) + '...');", "current": false}]}, {"file": "src\\components\\LazyComponents.tsx", "line": 70, "content": "if (!localStorage.getItem('k<PERSON><PERSON><PERSON><PERSON>_current_user')) {", "context": [{"line": 68, "content": "export const preloadCriticalComponents = () => {", "current": false}, {"line": 69, "content": "// Preload user login for unauthenticated users", "current": false}, {"line": 70, "content": "if (!localStorage.getItem('k<PERSON><PERSON><PERSON><PERSON>_current_user')) {", "current": true}, {"line": 71, "content": "import('./UserLogin');", "current": false}, {"line": 72, "content": "}", "current": false}]}, {"file": "src\\components\\LazyComponents.tsx", "line": 75, "content": "if (localStorage.getItem('k<PERSON><PERSON><PERSON><PERSON>_current_user')) {", "context": [{"line": 73, "content": "", "current": false}, {"line": 74, "content": "// Preload user dashboard for authenticated users", "current": false}, {"line": 75, "content": "if (localStorage.getItem('k<PERSON><PERSON><PERSON><PERSON>_current_user')) {", "current": true}, {"line": 76, "content": "import('./SimpleUserDashboard');", "current": false}, {"line": 77, "content": "}", "current": false}]}, {"file": "src\\components\\SimpleUserDashboard.tsx", "line": 261, "content": "localStorage.removeItem('khan<PERSON><PERSON><PERSON>_db');", "context": [{"line": 259, "content": "// This component does not have a notification provider, so we use the default confirm", "current": false}, {"line": 260, "content": "if (window.confirm(t('notifications.logoutConfirm', 'Are you sure you want to logout?'))) {", "current": false}, {"line": 261, "content": "localStorage.removeItem('khan<PERSON><PERSON><PERSON>_db');", "current": true}, {"line": 262, "content": "window.location.reload();", "current": false}, {"line": 263, "content": "}", "current": false}]}, {"file": "src\\hooks\\useActivityLogger.ts", "line": 29, "content": "const stored = localStorage.getItem(ACTIVITY_STORAGE_KEY);", "context": [{"line": 27, "content": "const [activities, setActivities] = useState<ActivityLog[]>(() => {", "current": false}, {"line": 28, "content": "try {", "current": false}, {"line": 29, "content": "const stored = localStorage.getItem(ACTIVITY_STORAGE_KEY);", "current": true}, {"line": 30, "content": "return stored ? JSON.parse(stored) : [];", "current": false}, {"line": 31, "content": "} catch {", "current": false}]}, {"file": "src\\lib\\apiServices.ts", "line": 388, "content": "localStorage.setItem('khan<PERSON><PERSON><PERSON>_access_token', tokens.accessToken);", "context": [{"line": 386, "content": "", "current": false}, {"line": 387, "content": "// Store tokens", "current": false}, {"line": 388, "content": "localStorage.setItem('khan<PERSON><PERSON><PERSON>_access_token', tokens.accessToken);", "current": true}, {"line": 389, "content": "localStorage.setItem('k<PERSON><PERSON><PERSON><PERSON>_refresh_token', tokens.refreshToken);", "current": false}, {"line": 390, "content": "", "current": false}]}, {"file": "src\\lib\\apiServices.ts", "line": 389, "content": "localStorage.setItem('k<PERSON><PERSON><PERSON><PERSON>_refresh_token', tokens.refreshToken);", "context": [{"line": 387, "content": "// Store tokens", "current": false}, {"line": 388, "content": "localStorage.setItem('khan<PERSON><PERSON><PERSON>_access_token', tokens.accessToken);", "current": false}, {"line": 389, "content": "localStorage.setItem('k<PERSON><PERSON><PERSON><PERSON>_refresh_token', tokens.refreshToken);", "current": true}, {"line": 390, "content": "", "current": false}, {"line": 391, "content": "// Store user data", "current": false}]}, {"file": "src\\lib\\apiServices.ts", "line": 392, "content": "localStorage.setItem('k<PERSON><PERSON><PERSON><PERSON>_current_user', JSON.stringify(user));", "context": [{"line": 390, "content": "", "current": false}, {"line": 391, "content": "// Store user data", "current": false}, {"line": 392, "content": "localStorage.setItem('k<PERSON><PERSON><PERSON><PERSON>_current_user', JSON.stringify(user));", "current": true}, {"line": 393, "content": "", "current": false}, {"line": 394, "content": "return { data: data.data, error: null };", "current": false}]}, {"file": "src\\lib\\database.ts", "line": 185, "content": "const storedDb = localStorage.getItem('khan<PERSON>shariya_db');", "context": [{"line": 183, "content": "private loadFromStorage() {", "current": false}, {"line": 184, "content": "try {", "current": false}, {"line": 185, "content": "const storedDb = localStorage.getItem('khan<PERSON>shariya_db');", "current": true}, {"line": 186, "content": "if (storedDb) {", "current": false}, {"line": 187, "content": "this.db = { ...this.db, ...JSON.parse(storedDb) };", "current": false}]}, {"file": "src\\lib\\database.ts", "line": 191, "content": "localStorage.removeItem('khan<PERSON><PERSON><PERSON>_db');", "context": [{"line": 189, "content": "} catch (error) {", "current": false}, {"line": 190, "content": "console.error('Error loading data from storage:', error);", "current": false}, {"line": 191, "content": "localStorage.removeItem('khan<PERSON><PERSON><PERSON>_db');", "current": true}, {"line": 192, "content": "}", "current": false}, {"line": 193, "content": "}", "current": false}]}, {"file": "src\\lib\\database.ts", "line": 197, "content": "localStorage.setItem('k<PERSON><PERSON><PERSON><PERSON>_db', JSON.stringify(this.db));", "context": [{"line": 195, "content": "private saveToStorage() {", "current": false}, {"line": 196, "content": "try {", "current": false}, {"line": 197, "content": "localStorage.setItem('k<PERSON><PERSON><PERSON><PERSON>_db', JSON.stringify(this.db));", "current": true}, {"line": 198, "content": "} catch (error) {", "current": false}, {"line": 199, "content": "console.error('Error saving data to storage:', error);", "current": false}]}], "sessionStorage": [], "hardcodedData": [{"file": "scripts\\recreate-clean-data.js", "line": 32, "content": "const technicalServices = [", "context": [{"line": 30, "content": "console.log('🗑️ Cleared technical services');", "current": false}, {"line": 31, "content": "", "current": false}, {"line": 32, "content": "const technicalServices = [", "current": true}, {"line": 33, "content": "{", "current": false}, {"line": 34, "content": "id: 'tech-service-1',", "current": false}]}, {"file": "scripts\\recreate-clean-data.js", "line": 127, "content": "const systemServices = [", "context": [{"line": 125, "content": "console.log('🗑️ Cleared system services');", "current": false}, {"line": 126, "content": "", "current": false}, {"line": 127, "content": "const systemServices = [", "current": true}, {"line": 128, "content": "{", "current": false}, {"line": 129, "content": "id: 'system-shop-1',", "current": false}]}, {"file": "src\\components\\admin\\EnhancedPremiumManager.tsx", "line": 340, "content": "const sortedSystems = [...filteredSystems].sort((a, b) => {", "context": [{"line": 338, "content": "", "current": false}, {"line": 339, "content": "// Sort filtered data", "current": false}, {"line": 340, "content": "const sortedSystems = [...filteredSystems].sort((a, b) => {", "current": true}, {"line": 341, "content": "switch (filterValues.sortBy) {", "current": false}, {"line": 342, "content": "case 'price':", "current": false}]}, {"file": "src\\components\\admin\\TechnicalServicesManager.tsx", "line": 476, "content": "const sortedServices = [...filteredServices].sort((a, b) => {", "context": [{"line": 474, "content": "", "current": false}, {"line": 475, "content": "// Sort services", "current": false}, {"line": 476, "content": "const sortedServices = [...filteredServices].sort((a, b) => {", "current": true}, {"line": 477, "content": "switch (filterValues.sortBy) {", "current": false}, {"line": 478, "content": "case 'price':", "current": false}]}, {"file": "src\\components\\admin\\TechnicalSystemsManager.tsx", "line": 471, "content": "const sortedSystems = [...filteredSystems].sort((a, b) => {", "context": [{"line": 469, "content": "", "current": false}, {"line": 470, "content": "// Sort filtered systems", "current": false}, {"line": 471, "content": "const sortedSystems = [...filteredSystems].sort((a, b) => {", "current": true}, {"line": 472, "content": "switch (filterValues.sortBy) {", "current": false}, {"line": 473, "content": "case 'price':", "current": false}]}], "nonApiCalls": [{"file": "scripts\\add-rich-system-data.js", "line": 159, "content": "const systemsWithImages = updatedSystems.filter(s => s.image_url);", "context": [{"line": 157, "content": "const updatedSystems = updatedSystemsResponse.data.data.systems;", "current": false}, {"line": 158, "content": "", "current": false}, {"line": 159, "content": "const systemsWithImages = updatedSystems.filter(s => s.image_url);", "current": true}, {"line": 160, "content": "const systemsWithGallery = updatedSystems.filter(s => s.gallery_images && s.gallery_images.length > 0);", "current": false}, {"line": 161, "content": "const systemsWithFeatures = updatedSystems.filter(s => s.features_ar && s.features_ar.length > 0);", "current": false}]}, {"file": "scripts\\add-rich-system-data.js", "line": 160, "content": "const systemsWithGallery = updatedSystems.filter(s => s.gallery_images && s.gallery_images.length > 0);", "context": [{"line": 158, "content": "", "current": false}, {"line": 159, "content": "const systemsWithImages = updatedSystems.filter(s => s.image_url);", "current": false}, {"line": 160, "content": "const systemsWithGallery = updatedSystems.filter(s => s.gallery_images && s.gallery_images.length > 0);", "current": true}, {"line": 161, "content": "const systemsWithFeatures = updatedSystems.filter(s => s.features_ar && s.features_ar.length > 0);", "current": false}, {"line": 162, "content": "const premiumSystems = updatedSystems.filter(s => s.is_premium_addon);", "current": false}]}, {"file": "scripts\\add-rich-system-data.js", "line": 161, "content": "const systemsWithFeatures = updatedSystems.filter(s => s.features_ar && s.features_ar.length > 0);", "context": [{"line": 159, "content": "const systemsWithImages = updatedSystems.filter(s => s.image_url);", "current": false}, {"line": 160, "content": "const systemsWithGallery = updatedSystems.filter(s => s.gallery_images && s.gallery_images.length > 0);", "current": false}, {"line": 161, "content": "const systemsWithFeatures = updatedSystems.filter(s => s.features_ar && s.features_ar.length > 0);", "current": true}, {"line": 162, "content": "const premiumSystems = updatedSystems.filter(s => s.is_premium_addon);", "current": false}, {"line": 163, "content": "const systemsWithVideo = updatedSystems.filter(s => s.video_url);", "current": false}]}, {"file": "scripts\\add-rich-system-data.js", "line": 162, "content": "const premiumSystems = updatedSystems.filter(s => s.is_premium_addon);", "context": [{"line": 160, "content": "const systemsWithGallery = updatedSystems.filter(s => s.gallery_images && s.gallery_images.length > 0);", "current": false}, {"line": 161, "content": "const systemsWithFeatures = updatedSystems.filter(s => s.features_ar && s.features_ar.length > 0);", "current": false}, {"line": 162, "content": "const premiumSystems = updatedSystems.filter(s => s.is_premium_addon);", "current": true}, {"line": 163, "content": "const systemsWithVideo = updatedSystems.filter(s => s.video_url);", "current": false}, {"line": 164, "content": "", "current": false}]}, {"file": "scripts\\add-rich-system-data.js", "line": 163, "content": "const systemsWithVideo = updatedSystems.filter(s => s.video_url);", "context": [{"line": 161, "content": "const systemsWithFeatures = updatedSystems.filter(s => s.features_ar && s.features_ar.length > 0);", "current": false}, {"line": 162, "content": "const premiumSystems = updatedSystems.filter(s => s.is_premium_addon);", "current": false}, {"line": 163, "content": "const systemsWithVideo = updatedSystems.filter(s => s.video_url);", "current": true}, {"line": 164, "content": "", "current": false}, {"line": 165, "content": "console.log(`   🖼️ أنظمة بصور رئيسية: ${systemsWithImages.length}/${updatedSystems.length}`);", "current": false}]}, {"file": "scripts\\check-table-columns.js", "line": 70, "content": "const missingSystemColumns = expectedSystemColumns.filter(col => !systemColumnNames.includes(col));", "context": [{"line": 68, "content": "// Check system_services missing columns", "current": false}, {"line": 69, "content": "const systemColumnNames = systemColumns.map(col => col.COLUMN_NAME);", "current": false}, {"line": 70, "content": "const missingSystemColumns = expectedSystemColumns.filter(col => !systemColumnNames.includes(col));", "current": true}, {"line": 71, "content": "", "current": false}, {"line": 72, "content": "if (missingSystemColumns.length > 0) {", "current": false}]}, {"file": "scripts\\check-table-columns.js", "line": 81, "content": "const missingServiceColumns = expectedServiceColumns.filter(col => !serviceColumnNames.includes(col));", "context": [{"line": 79, "content": "// Check technical_services missing columns", "current": false}, {"line": 80, "content": "const serviceColumnNames = serviceColumns.map(col => col.COLUMN_NAME);", "current": false}, {"line": 81, "content": "const missingServiceColumns = expectedServiceColumns.filter(col => !serviceColumnNames.includes(col));", "current": true}, {"line": 82, "content": "", "current": false}, {"line": 83, "content": "if (missingServiceColumns.length > 0) {", "current": false}]}, {"file": "scripts\\check-table-columns.js", "line": 93, "content": "const extraSystemColumns = systemColumnNames.filter(col =>", "context": [{"line": 91, "content": "console.log('\\n4️⃣ Checking for extra columns...');", "current": false}, {"line": 92, "content": "", "current": false}, {"line": 93, "content": "const extraSystemColumns = systemColumnNames.filter(col =>", "current": true}, {"line": 94, "content": "!expectedSystemColumns.includes(col) &&", "current": false}, {"line": 95, "content": "!['created_at', 'updated_at', 'download_count', 'rating', 'rating_count', 'version', 'file_size', 'sort_order'].includes(col)", "current": false}]}, {"file": "scripts\\check-table-columns.js", "line": 98, "content": "const extraServiceColumns = serviceColumnNames.filter(col =>", "context": [{"line": 96, "content": ");", "current": false}, {"line": 97, "content": "", "current": false}, {"line": 98, "content": "const extraServiceColumns = serviceColumnNames.filter(col =>", "current": true}, {"line": 99, "content": "!expectedServiceColumns.includes(col) &&", "current": false}, {"line": 100, "content": "!['created_at', 'updated_at', 'order_count', 'rating', 'rating_count', 'delivery_time_ar', 'delivery_time_en', 'process_steps_ar', 'process_steps_en', 'sort_order'].includes(col)", "current": false}]}, {"file": "scripts\\nuclear-fix.js", "line": 68, "content": "const newEnvLines = envLines.filter(line =>", "context": [{"line": 66, "content": "// Add or update rate limit settings", "current": false}, {"line": 67, "content": "const envLines = envContent.split('\\n');", "current": false}, {"line": 68, "content": "const newEnvLines = envLines.filter(line =>", "current": true}, {"line": 69, "content": "!line.startsWith('RATE_LIMIT_MAX_REQUESTS=') &&", "current": false}, {"line": 70, "content": "!line.startsWith('RATE_LIMIT_WINDOW_MS=')", "current": false}]}, {"file": "scripts\\quick-test.js", "line": 46, "content": "const activeSystems = systems.filter(s => s.status === 'active');", "context": [{"line": 44, "content": "console.log(`✅ Systems loaded: ${systems.length} systems`);", "current": false}, {"line": 45, "content": "", "current": false}, {"line": 46, "content": "const activeSystems = systems.filter(s => s.status === 'active');", "current": true}, {"line": 47, "content": "console.log(`   Active systems: ${activeSystems.length}`);", "current": false}, {"line": 48, "content": "", "current": false}]}, {"file": "scripts\\quick-test.js", "line": 74, "content": "const activeServices = services.filter(s => s.status === 'active');", "context": [{"line": 72, "content": "console.log(`✅ Services loaded: ${services.length} services`);", "current": false}, {"line": 73, "content": "", "current": false}, {"line": 74, "content": "const activeServices = services.filter(s => s.status === 'active');", "current": true}, {"line": 75, "content": "console.log(`   Active services: ${activeServices.length}`);", "current": false}, {"line": 76, "content": "", "current": false}]}, {"file": "scripts\\verify-migration.js", "line": 156, "content": "const activeSystems = systems.filter(s => s.status === 'active');", "context": [{"line": 154, "content": "", "current": false}, {"line": 155, "content": "// Check systems data", "current": false}, {"line": 156, "content": "const activeSystems = systems.filter(s => s.status === 'active');", "current": true}, {"line": 157, "content": "console.log(`   Active Systems: ${activeSystems.length}/${systems.length} ✅`);", "current": false}, {"line": 158, "content": "", "current": false}]}, {"file": "scripts\\verify-migration.js", "line": 160, "content": "const activeServices = services.filter(s => s.status === 'active');", "context": [{"line": 158, "content": "", "current": false}, {"line": 159, "content": "// Check services data", "current": false}, {"line": 160, "content": "const activeServices = services.filter(s => s.status === 'active');", "current": true}, {"line": 161, "content": "console.log(`   Active Services: ${activeServices.length}/${services.length} ✅`);", "current": false}, {"line": 162, "content": "", "current": false}]}, {"file": "src\\components\\admin\\EnhancedPremiumManager.tsx", "line": 292, "content": "const filteredSystems = premiumSystems.filter(system => {", "context": [{"line": 290, "content": "};", "current": false}, {"line": 291, "content": "", "current": false}, {"line": 292, "content": "const filteredSystems = premiumSystems.filter(system => {", "current": true}, {"line": 293, "content": "const matchesSearch = searchTerm === '' ||", "current": false}, {"line": 294, "content": "getSystemText(system, 'name', language).toLowerCase().includes(searchTerm.toLowerCase()) ||", "current": false}]}, {"file": "src\\components\\admin\\EnhancedPremiumManager.tsx", "line": 316, "content": "const filteredPackages = premiumPackages.filter(pkg => {", "context": [{"line": 314, "content": "});", "current": false}, {"line": 315, "content": "", "current": false}, {"line": 316, "content": "const filteredPackages = premiumPackages.filter(pkg => {", "current": true}, {"line": 317, "content": "const matchesSearch = searchTerm === '' ||", "current": false}, {"line": 318, "content": "pkg.name[language].toLowerCase().includes(searchTerm.toLowerCase()) ||", "current": false}]}, {"file": "src\\components\\admin\\EnhancedUserManager.tsx", "line": 195, "content": "const newUsersThisMonth = usersArray.filter(user => {", "context": [{"line": 193, "content": "const ordersArray = Array.isArray(userOrders) ? userOrders : [];", "current": false}, {"line": 194, "content": "", "current": false}, {"line": 195, "content": "const newUsersThisMonth = usersArray.filter(user => {", "current": true}, {"line": 196, "content": "try {", "current": false}, {"line": 197, "content": "const userDate = new Date(user.created_at || '');", "current": false}]}, {"file": "src\\components\\admin\\EnhancedUserManager.tsx", "line": 204, "content": "const totalRevenue = ordersArray.filter(order => order.status === 'completed').reduce((sum, order) => sum + (parseFloat(order.price) || 0), 0);", "context": [{"line": 202, "content": "}).length;", "current": false}, {"line": 203, "content": "", "current": false}, {"line": 204, "content": "const totalRevenue = ordersArray.filter(order => order.status === 'completed').reduce((sum, order) => sum + (parseFloat(order.price) || 0), 0);", "current": true}, {"line": 205, "content": "", "current": false}, {"line": 206, "content": "setUserStats({", "current": false}]}, {"file": "src\\components\\admin\\EnhancedUserManager.tsx", "line": 225, "content": "const adminCount = users.filter(u => u.role === 'admin').length;", "context": [{"line": 223, "content": "// Check if this is the last admin and prevent demotion", "current": false}, {"line": 224, "content": "if (isDemoting) {", "current": false}, {"line": 225, "content": "const adminCount = users.filter(u => u.role === 'admin').length;", "current": true}, {"line": 226, "content": "if (adminCount <= 1) {", "current": false}, {"line": 227, "content": "showNotification({", "current": false}]}, {"file": "src\\components\\admin\\EnhancedUserManager.tsx", "line": 374, "content": "const filteredUsers = users.filter(user => {", "context": [{"line": 372, "content": "};", "current": false}, {"line": 373, "content": "", "current": false}, {"line": 374, "content": "const filteredUsers = users.filter(user => {", "current": true}, {"line": 375, "content": "const matchesSearch = searchTerm === '' ||", "current": false}, {"line": 376, "content": "user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||", "current": false}]}, {"file": "src\\components\\admin\\TechnicalServicesManager.tsx", "line": 448, "content": "const filteredServices = services.filter(service => {", "context": [{"line": 446, "content": "};", "current": false}, {"line": 447, "content": "", "current": false}, {"line": 448, "content": "const filteredServices = services.filter(service => {", "current": true}, {"line": 449, "content": "// Safe access to service fields", "current": false}, {"line": 450, "content": "const name = getServiceText(service, 'name', language);", "current": false}]}, {"file": "src\\components\\AdvancedOrderManagement.tsx", "line": 108, "content": "const filteredOrders = orders.filter(order => {", "context": [{"line": 106, "content": "};", "current": false}, {"line": 107, "content": "", "current": false}, {"line": 108, "content": "const filteredOrders = orders.filter(order => {", "current": true}, {"line": 109, "content": "const matchesSearch = order.service_name.toLowerCase().includes(searchTerm.toLowerCase()) ||", "current": false}, {"line": 110, "content": "getUserById(order.user_id)?.full_name.toLowerCase().includes(searchTerm.toLowerCase());", "current": false}]}, {"file": "src\\components\\EmbeddedOrderManagement.tsx", "line": 92, "content": "const filteredOrders = orders.filter(order => {", "context": [{"line": 90, "content": "};", "current": false}, {"line": 91, "content": "", "current": false}, {"line": 92, "content": "const filteredOrders = orders.filter(order => {", "current": true}, {"line": 93, "content": "const user = getUserById(order.user_id);", "current": false}, {"line": 94, "content": "const matchesSearch = order.service_name.toLowerCase().includes(searchTerm.toLowerCase()) ||", "current": false}]}, {"file": "src\\components\\EmbeddedUserManagement.tsx", "line": 52, "content": "const filteredUsers = users.filter(user =>", "context": [{"line": 50, "content": "};", "current": false}, {"line": 51, "content": "", "current": false}, {"line": 52, "content": "const filteredUsers = users.filter(user =>", "current": true}, {"line": 53, "content": "user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||", "current": false}, {"line": 54, "content": "user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||", "current": false}]}, {"file": "src\\components\\OrderManagement.tsx", "line": 153, "content": "const filteredOrders = orders.filter(order => {", "context": [{"line": 151, "content": "};", "current": false}, {"line": 152, "content": "", "current": false}, {"line": 153, "content": "const filteredOrders = orders.filter(order => {", "current": true}, {"line": 154, "content": "const user = getUserById(order.user_id);", "current": false}, {"line": 155, "content": "const matchesSearch = order.service_name.toLowerCase().includes(searchTerm.toLowerCase()) ||", "current": false}]}, {"file": "src\\components\\PremiumEdition.tsx", "line": 871, "content": "const service = availableServices.find(s => s.id === id);", "context": [{"line": 869, "content": "</div>", "current": false}, {"line": 870, "content": "{(selectedServices.some(id => {", "current": false}, {"line": 871, "content": "const service = availableServices.find(s => s.id === id);", "current": true}, {"line": 872, "content": "return service && service.subscriptionType !== 'none';", "current": false}, {"line": 873, "content": "})) && (", "current": false}]}, {"file": "src\\components\\SystemsGrid.tsx", "line": 73, "content": "const activeSystems = systemsData.filter(s => s.status === 'active');", "context": [{"line": 71, "content": "const systemsData = result.data?.systems || result.data;", "current": false}, {"line": 72, "content": "if (systemsData && Array.isArray(systemsData)) {", "current": false}, {"line": 73, "content": "const activeSystems = systemsData.filter(s => s.status === 'active');", "current": true}, {"line": 74, "content": "setSystems(activeSystems);", "current": false}, {"line": 75, "content": "console.log('SystemsGrid - Active systems loaded:', activeSystems.length);", "current": false}]}, {"file": "src\\components\\ui\\Toast.tsx", "line": 193, "content": "const toastNotifications = notifications.filter(n => n.duration !== 0);", "context": [{"line": 191, "content": "", "current": false}, {"line": 192, "content": "// Only show toast notifications (not persistent ones)", "current": false}, {"line": 193, "content": "const toastNotifications = notifications.filter(n => n.duration !== 0);", "current": true}, {"line": 194, "content": "", "current": false}, {"line": 195, "content": "if (toastNotifications.length === 0) return null;", "current": false}]}, {"file": "src\\components\\UserManagement.tsx", "line": 161, "content": "const filteredUsers = users.filter(user => {", "context": [{"line": 159, "content": "};", "current": false}, {"line": 160, "content": "", "current": false}, {"line": 161, "content": "const filteredUsers = users.filter(user => {", "current": true}, {"line": 162, "content": "const searchTermLower = searchTerm.toLowerCase();", "current": false}, {"line": 163, "content": "return (", "current": false}]}, {"file": "src\\hooks\\useNotification.tsx", "line": 98, "content": "const unreadCount = notifications.filter(n => !n.read).length", "context": [{"line": 96, "content": "", "current": false}, {"line": 97, "content": "// Calculate unread count", "current": false}, {"line": 98, "content": "const unreadCount = notifications.filter(n => !n.read).length", "current": true}, {"line": 99, "content": "", "current": false}, {"line": 100, "content": "const value = {", "current": false}]}], "suspiciousPatterns": []}