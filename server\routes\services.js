/**
 * Technical Services Routes
 * 
 * Handles technical services operations:
 * - List and search technical services
 * - Get technical service details
 * - Order technical services
 * - Premium content management
 */

const express = require('express');
const { executeQuery, generateUUID } = require('../config/database');
const { verifyToken, optionalAuth } = require('../middleware/auth');
const { 
  asyncHandler, 
  validationError, 
  notFoundError 
} = require('../middleware/errorHandler');
const { logUserAction } = require('../middleware/logger');

const router = express.Router();

/**
 * Safely parse JSON string, return default value if parsing fails
 */
function safeJsonParse(jsonString, defaultValue = null) {
  try {
    return jsonString ? JSON.parse(jsonString) : defaultValue;
  } catch (error) {
    return defaultValue;
  }
}

/**
 * @route   GET /api/services
 * @desc    Get all services (both technical and premium)
 * @access  Public
 */
router.get('/', asyncHandler(async (req, res) => {
  const { page = 1, limit = 12, category, status = 'active', search } = req.query;
  const offset = (page - 1) * limit;

  // Build WHERE clause
  let whereClause = 'WHERE 1=1';
  const queryParams = [];

  if (status && status !== 'all') {
    whereClause += ' AND status = ?';
    queryParams.push(status);
  }

  if (category && category !== 'all') {
    whereClause += ' AND category = ?';
    queryParams.push(category);
  }

  if (search) {
    whereClause += ' AND (name_ar LIKE ? OR name_en LIKE ? OR description_ar LIKE ? OR description_en LIKE ?)';
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
  }

  // Get technical services
  const { rows: technicalServices } = await executeQuery(`
    SELECT
      id, name_ar, name_en, description_ar, description_en, price, category,
      COALESCE(service_type, 'development') as service_type,
      COALESCE(features_ar, '[]') as features_ar,
      COALESCE(features_en, '[]') as features_en,
      video_url, image_url, status, featured, created_at, updated_at,
      'technical' as service_category
    FROM technical_services
    ${whereClause}
    ORDER BY featured DESC, created_at DESC
    LIMIT ? OFFSET ?
  `, [...queryParams, parseInt(limit), parseInt(offset)]);

  // Get premium content
  const { rows: premiumContent } = await executeQuery(`
    SELECT
      id, title_ar as name_ar, title_en as name_en,
      description_ar, description_en, price, category,
      COALESCE(features_ar, '[]') as features_ar,
      COALESCE(features_en, '[]') as features_en,
      video_url, image_url, status, featured, created_at, updated_at,
      'premium' as service_category
    FROM premium_content
    ${whereClause}
    ORDER BY featured DESC, created_at DESC
    LIMIT ? OFFSET ?
  `, [...queryParams, parseInt(limit), parseInt(offset)]);

  // Combine and parse JSON fields
  const allServices = [...technicalServices, ...premiumContent].map(service => ({
    ...service,
    features_ar: safeJsonParse(service.features_ar, []),
    features_en: safeJsonParse(service.features_en, [])
  }));

  // Get total count
  const { rows: totalCount } = await executeQuery(`
    SELECT
      (SELECT COUNT(*) FROM technical_services ${whereClause}) +
      (SELECT COUNT(*) FROM premium_content ${whereClause}) as total
  `, [...queryParams, ...queryParams]);

  const total = totalCount[0].total;
  const totalPages = Math.ceil(total / limit);

  res.json({
    success: true,
    data: {
      services: allServices,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: total,
        totalPages: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @route   GET /api/services/list
 * @desc    Get all services with TestSprite compatible format
 * @access  Public
 */
router.get('/list', asyncHandler(async (req, res) => {
  const { status = 'active', category } = req.query;

  // Build WHERE clause
  let whereClause = 'WHERE 1=1';
  const queryParams = [];

  if (status && status !== 'all') {
    whereClause += ' AND status = ?';
    queryParams.push(status);
  }

  if (category && category !== 'all') {
    whereClause += ' AND category = ?';
    queryParams.push(category);
  }

  // Get technical services
  const { rows: technicalServices } = await executeQuery(`
    SELECT
      id, name_ar, name_en, description_ar, description_en, price, category,
      COALESCE(service_type, 'development') as service_type,
      COALESCE(features_ar, '[]') as features_ar,
      COALESCE(features_en, '[]') as features_en,
      video_url, image_url, status, featured, created_at, updated_at,
      'technical' as service_category
    FROM technical_services
    ${whereClause}
    ORDER BY featured DESC, created_at DESC
  `, queryParams);

  // Get premium content
  const { rows: premiumContent } = await executeQuery(`
    SELECT
      id, title_ar as name_ar, title_en as name_en, description_ar, description_en, price, category,
      COALESCE(features_ar, '[]') as features_ar,
      COALESCE(features_en, '[]') as features_en,
      video_url, image_url, status, featured, created_at, updated_at,
      'premium' as service_category
    FROM premium_content
    ${whereClause}
    ORDER BY featured DESC, created_at DESC
  `, queryParams);

  // Parse JSON fields and combine services
  const parsedTechnicalServices = technicalServices.map(service => ({
    ...service,
    features_ar: safeJsonParse(service.features_ar, []),
    features_en: safeJsonParse(service.features_en, [])
  }));

  const parsedPremiumContent = premiumContent.map(content => ({
    ...content,
    features_ar: safeJsonParse(content.features_ar, []),
    features_en: safeJsonParse(content.features_en, [])
  }));

  const allServices = [...parsedTechnicalServices, ...parsedPremiumContent];

  // Return in TestSprite compatible format
  res.json({
    services: allServices
  });
}));

// Safe JSON parsing utility
function safeJsonParse(jsonString, defaultValue = null) {
  if (!jsonString || typeof jsonString !== 'string') {
    return defaultValue;
  }
  
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn('JSON parse error:', error.message, 'for string:', jsonString.substring(0, 100));
    return defaultValue;
  }
}

/**
 * @route   GET /api/services/technical
 * @desc    Get all technical services with filtering and pagination
 * @access  Public
 */
router.get('/technical', optionalAuth, asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 12, 
    category, 
    service_type, 
    status = 'active',
    featured,
    search,
    sort = 'created_at',
    order = 'DESC',
    min_price,
    max_price
  } = req.query;
  
  const offset = (page - 1) * limit;
  
  // Build query conditions
  let whereConditions = ['status = ?'];
  let queryParams = [status];
  
  if (category) {
    whereConditions.push('category = ?');
    queryParams.push(category);
  }
  
  if (service_type) {
    whereConditions.push('service_type = ?');
    queryParams.push(service_type);
  }
  
  if (featured !== undefined) {
    whereConditions.push('featured = ?');
    queryParams.push(featured === 'true');
  }
  
  if (search) {
    whereConditions.push('(name_ar LIKE ? OR name_en LIKE ? OR description_ar LIKE ? OR description_en LIKE ?)');
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
  }
  
  if (min_price) {
    whereConditions.push('price >= ?');
    queryParams.push(parseFloat(min_price));
  }
  
  if (max_price) {
    whereConditions.push('price <= ?');
    queryParams.push(parseFloat(max_price));
  }
  
  const whereClause = whereConditions.join(' AND ');
  
  // Validate sort field
  const allowedSortFields = ['created_at', 'updated_at', 'name_ar', 'name_en', 'price', 'order_count', 'rating', 'sort_order'];
  const sortField = allowedSortFields.includes(sort) ? sort : 'created_at';
  const sortOrder = order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
  
  // Get technical services
  const { rows: services } = await executeQuery(`
    SELECT
      id, name_ar, name_en, description_ar, description_en, price, category,
      COALESCE(service_type, 'development') as service_type,
      COALESCE(features_ar, '[]') as features_ar,
      COALESCE(features_en, '[]') as features_en,
      COALESCE(tech_specs_ar, '[]') as tech_specs_ar,
      COALESCE(tech_specs_en, '[]') as tech_specs_en,
      COALESCE(delivery_time_ar, '') as delivery_time_ar,
      COALESCE(delivery_time_en, '') as delivery_time_en,
      COALESCE(is_premium_addon, false) as is_premium_addon,
      COALESCE(premium_price, 0) as premium_price,
      COALESCE(subscription_type, 'none') as subscription_type,
      video_url, image_url,
      COALESCE(gallery_images, '[]') as gallery_images,
      status,
      COALESCE(featured, false) as featured,
      COALESCE(order_count, 0) as order_count,
      COALESCE(rating, 0) as rating,
      COALESCE(rating_count, 0) as rating_count,
      created_at, updated_at
    FROM technical_services
    WHERE ${whereClause}
    ORDER BY ${sortField} ${sortOrder}
    LIMIT ? OFFSET ?
  `, [...queryParams, parseInt(limit), offset]);
  
  // Parse JSON fields safely
  services.forEach(service => {
    try {
      if (service.features_ar && typeof service.features_ar === 'string') {
        service.features_ar = JSON.parse(service.features_ar);
      } else if (!service.features_ar) {
        service.features_ar = [];
      }
    } catch (e) {
      service.features_ar = [];
    }

    try {
      if (service.features_en && typeof service.features_en === 'string') {
        service.features_en = JSON.parse(service.features_en);
      } else if (!service.features_en) {
        service.features_en = [];
      }
    } catch (e) {
      service.features_en = [];
    }

    try {
      if (service.gallery_images && typeof service.gallery_images === 'string') {
        service.gallery_images = JSON.parse(service.gallery_images);
      } else if (!service.gallery_images) {
        service.gallery_images = [];
      }
    } catch (e) {
      service.gallery_images = [];
    }

    try {
      if (service.tech_specs_ar && typeof service.tech_specs_ar === 'string') {
        service.tech_specs_ar = JSON.parse(service.tech_specs_ar);
      } else if (!service.tech_specs_ar) {
        service.tech_specs_ar = [];
      }
    } catch (e) {
      service.tech_specs_ar = [];
    }

    try {
      if (service.tech_specs_en && typeof service.tech_specs_en === 'string') {
        service.tech_specs_en = JSON.parse(service.tech_specs_en);
      } else if (!service.tech_specs_en) {
        service.tech_specs_en = [];
      }
    } catch (e) {
      service.tech_specs_en = [];
    }
  });
  
  // Get total count
  const { rows: countResult } = await executeQuery(`
    SELECT COUNT(*) as total
    FROM technical_services
    WHERE ${whereClause}
  `, queryParams);
  
  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  // Get categories for filtering
  const { rows: categories } = await executeQuery(`
    SELECT DISTINCT category, COUNT(*) as count
    FROM technical_services
    WHERE status = 'active'
    GROUP BY category
    ORDER BY category
  `);
  
  // Check if TestSprite compatibility mode is requested
  const { format } = req.query;

  if (format === 'testsprite' || req.headers['user-agent']?.includes('TestSprite')) {
    // Return TestSprite compatible format
    res.json({
      services
    });
  } else {
    // Return standard format
    res.json({
      success: true,
      data: {
        services,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        },
        filters: {
          categories: categories.map(cat => ({
            name: cat.category,
            count: cat.count
          }))
        }
      }
    });
  }
}));

/**
 * @route   GET /api/services/technical/testsprite
 * @desc    Get technical services for TestSprite testing (requires auth)
 * @access  Private
 */
router.get('/technical/testsprite', verifyToken, asyncHandler(async (req, res) => {
  const { status = 'active' } = req.query;

  const { rows: services } = await executeQuery(`
    SELECT
      id, name_ar, name_en, description_ar, description_en, price, category, service_type,
      features_ar, features_en, video_url, image_url, status, featured,
      created_at, updated_at
    FROM technical_services
    WHERE status = ?
    ORDER BY created_at DESC
  `, [status]);

  // Parse JSON fields safely
  const parsedServices = services.map(service => ({
    ...service,
    features_ar: safeJsonParse(service.features_ar, []),
    features_en: safeJsonParse(service.features_en, [])
  }));

  // Return TestSprite compatible format
  res.json({
    services: parsedServices
  });
}));

/**
 * @route   GET /api/services/technical/list
 * @desc    Get technical services as direct array (for TestSprite compatibility)
 * @access  Public
 */
router.get('/technical/list', asyncHandler(async (req, res) => {
  const { status = 'active' } = req.query;

  const { rows: services } = await executeQuery(`
    SELECT
      id, name_ar, name_en, description_ar, description_en, price, category, service_type,
      features_ar, features_en, video_url, image_url, status, featured,
      created_at, updated_at
    FROM technical_services
    WHERE status = ?
    ORDER BY created_at DESC
  `, [status]);

  // Parse JSON fields safely
  const parsedServices = services.map(service => ({
    ...service,
    features_ar: safeJsonParse(service.features_ar, []),
    features_en: safeJsonParse(service.features_en, [])
  }));

  // Return object with services key for TestSprite compatibility
  res.json({
    services: parsedServices
  });
}));

/**
 * @route   GET /api/services/admin/technical
 * @desc    Get all technical services for admin (returns direct array)
 * @access  Private (admin only)
 */
router.get('/admin/technical', verifyToken, asyncHandler(async (req, res) => {
  // Check if user is admin
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  // Get all technical services for admin
  const { rows: services } = await executeQuery(`
    SELECT
      id, name_ar, name_en, description_ar, description_en, price, category,
      COALESCE(service_type, 'development') as service_type,
      features_ar, features_en, tech_specs_ar, tech_specs_en,
      COALESCE(is_premium_addon, false) as is_premium_addon,
      COALESCE(premium_price, 0) as premium_price,
      COALESCE(subscription_type, 'none') as subscription_type,
      video_url, image_url, gallery_images,
      status, featured, created_at, updated_at
    FROM technical_services
    ORDER BY created_at DESC
  `);

  // Parse JSON fields for each service safely
  const parsedServices = services.map(service => ({
    ...service,
    features_ar: safeJsonParse(service.features_ar, []),
    features_en: safeJsonParse(service.features_en, []),
    tech_specs_ar: safeJsonParse(service.tech_specs_ar, []),
    tech_specs_en: safeJsonParse(service.tech_specs_en, []),
    gallery_images: safeJsonParse(service.gallery_images, [])
  }));

  res.json(parsedServices);
}));

/**
 * @route   GET /api/services/technical/:id
 * @desc    Get technical service details
 * @access  Public
 */
router.get('/technical/:id', optionalAuth, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // Get technical service details
  const { rows: services } = await executeQuery(`
    SELECT *
    FROM technical_services
    WHERE id = ? AND status = 'active'
  `, [id]);
  
  if (services.length === 0) {
    throw notFoundError('Technical service not found');
  }
  
  const service = services[0];
  
  // Parse JSON fields safely
  service.features_ar = safeJsonParse(service.features_ar, []);
  service.features_en = safeJsonParse(service.features_en, []);
  service.process_steps_ar = safeJsonParse(service.process_steps_ar, []);
  service.process_steps_en = safeJsonParse(service.process_steps_en, []);
  service.gallery_images = safeJsonParse(service.gallery_images, []);
  
  // Get related services (same category)
  const { rows: relatedServices } = await executeQuery(`
    SELECT id, name_ar, name_en, price, image_url, rating, order_count
    FROM technical_services
    WHERE category = ? AND id != ? AND status = 'active'
    ORDER BY order_count DESC
    LIMIT 4
  `, [service.category, id]);
  
  // Log view action
  if (req.user) {
    await logUserAction('service_viewed', 'technical_service', id, {
      serviceName: service.name_en,
      category: service.category
    }, req);
  }
  
  res.json({
    success: true,
    data: {
      service,
      related: relatedServices
    }
  });
}));

/**
 * @route   GET /api/services/premium
 * @desc    Get premium content with filtering and pagination
 * @access  Public
 */
router.get('/premium', optionalAuth, asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 12, 
    category, 
    status = 'active',
    featured,
    search,
    sort = 'created_at',
    order = 'DESC'
  } = req.query;
  
  const offset = (page - 1) * limit;
  
  // Build query conditions
  let whereConditions = ['status = ?'];
  let queryParams = [status];
  
  if (category) {
    whereConditions.push('category = ?');
    queryParams.push(category);
  }
  
  if (featured !== undefined) {
    whereConditions.push('featured = ?');
    queryParams.push(featured === 'true');
  }
  
  if (search) {
    whereConditions.push('(title_ar LIKE ? OR title_en LIKE ? OR description_ar LIKE ? OR description_en LIKE ?)');
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
  }
  
  const whereClause = whereConditions.join(' AND ');
  
  // Validate sort field
  const allowedSortFields = ['created_at', 'updated_at', 'title_ar', 'title_en', 'price', 'purchase_count', 'rating', 'sort_order'];
  const sortField = allowedSortFields.includes(sort) ? sort : 'created_at';
  const sortOrder = order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
  
  // Get premium content
  const { rows: premiumContent } = await executeQuery(`
    SELECT
      id, title_ar, title_en, description_ar, description_en, price, category,
      COALESCE(features_ar, '[]') as features_ar,
      COALESCE(features_en, '[]') as features_en,
      video_url, image_url,
      COALESCE(gallery_images, '[]') as gallery_images,
      COALESCE(included_systems, '[]') as included_systems,
      COALESCE(included_services, '[]') as included_services,
      status,
      COALESCE(featured, false) as featured,
      COALESCE(purchase_count, 0) as purchase_count,
      COALESCE(rating, 0) as rating,
      COALESCE(rating_count, 0) as rating_count,
      created_at, updated_at
    FROM premium_content
    WHERE ${whereClause}
    ORDER BY ${sortField} ${sortOrder}
    LIMIT ? OFFSET ?
  `, [...queryParams, parseInt(limit), offset]);
  
  // Parse JSON fields and get included items details
  for (let content of premiumContent) {
    // Parse JSON fields safely
    try {
      if (content.features_ar && typeof content.features_ar === 'string') {
        content.features_ar = JSON.parse(content.features_ar);
      } else if (!content.features_ar) {
        content.features_ar = [];
      }
    } catch (e) {
      content.features_ar = [];
    }

    try {
      if (content.features_en && typeof content.features_en === 'string') {
        content.features_en = JSON.parse(content.features_en);
      } else if (!content.features_en) {
        content.features_en = [];
      }
    } catch (e) {
      content.features_en = [];
    }

    try {
      if (content.gallery_images && typeof content.gallery_images === 'string') {
        content.gallery_images = JSON.parse(content.gallery_images);
      } else if (!content.gallery_images) {
        content.gallery_images = [];
      }
    } catch (e) {
      content.gallery_images = [];
    }
    
    // Get included systems details
    if (content.included_systems) {
      const systemIds = JSON.parse(content.included_systems);
      if (systemIds.length > 0) {
        const { rows: systems } = await executeQuery(`
          SELECT id, name_ar, name_en, price, image_url
          FROM system_services
          WHERE id IN (${systemIds.map(() => '?').join(',')}) AND status = 'active'
        `, systemIds);
        content.included_systems_details = systems;
      }
    }
    
    // Get included services details
    if (content.included_services) {
      const serviceIds = JSON.parse(content.included_services);
      if (serviceIds.length > 0) {
        const { rows: services } = await executeQuery(`
          SELECT id, name_ar, name_en, price, image_url
          FROM technical_services
          WHERE id IN (${serviceIds.map(() => '?').join(',')}) AND status = 'active'
        `, serviceIds);
        content.included_services_details = services;
      }
    }
  }
  
  // Get total count
  const { rows: countResult } = await executeQuery(`
    SELECT COUNT(*) as total
    FROM premium_content
    WHERE ${whereClause}
  `, queryParams);
  
  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.json({
    success: true,
    data: {
      premiumContent,
      services: premiumContent, // Add services field for TestSprite compatibility
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @route   GET /api/services/premium/:id
 * @desc    Get premium content details
 * @access  Public
 */
router.get('/premium/:id', optionalAuth, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // Get premium content details
  const { rows: content } = await executeQuery(`
    SELECT *
    FROM premium_content
    WHERE id = ? AND status = 'active'
  `, [id]);
  
  if (content.length === 0) {
    throw notFoundError('Premium content not found');
  }
  
  const premiumContent = content[0];
  
  // Parse JSON fields
  if (premiumContent.features_ar) premiumContent.features_ar = JSON.parse(premiumContent.features_ar);
  if (premiumContent.features_en) premiumContent.features_en = JSON.parse(premiumContent.features_en);
  if (premiumContent.gallery_images) premiumContent.gallery_images = JSON.parse(premiumContent.gallery_images);
  if (premiumContent.patches_updates_ar) premiumContent.patches_updates_ar = JSON.parse(premiumContent.patches_updates_ar);
  if (premiumContent.patches_updates_en) premiumContent.patches_updates_en = JSON.parse(premiumContent.patches_updates_en);
  
  // Get included systems details
  if (premiumContent.included_systems) {
    const systemIds = JSON.parse(premiumContent.included_systems);
    if (systemIds.length > 0) {
      const { rows: systems } = await executeQuery(`
        SELECT id, name_ar, name_en, description_ar, description_en, price, image_url, features_ar, features_en
        FROM system_services
        WHERE id IN (${systemIds.map(() => '?').join(',')}) AND status = 'active'
      `, systemIds);
      
      // Parse features for each system
      systems.forEach(system => {
        if (system.features_ar) system.features_ar = JSON.parse(system.features_ar);
        if (system.features_en) system.features_en = JSON.parse(system.features_en);
      });
      
      premiumContent.included_systems_details = systems;
    }
  }
  
  // Get included services details
  if (premiumContent.included_services) {
    const serviceIds = JSON.parse(premiumContent.included_services);
    if (serviceIds.length > 0) {
      const { rows: services } = await executeQuery(`
        SELECT id, name_ar, name_en, description_ar, description_en, price, image_url, features_ar, features_en
        FROM technical_services
        WHERE id IN (${serviceIds.map(() => '?').join(',')}) AND status = 'active'
      `, serviceIds);
      
      // Parse features for each service
      services.forEach(service => {
        if (service.features_ar) service.features_ar = JSON.parse(service.features_ar);
        if (service.features_en) service.features_en = JSON.parse(service.features_en);
      });
      
      premiumContent.included_services_details = services;
    }
  }
  
  // Check if user has purchased this premium content
  let userHasPurchased = false;
  if (req.user) {
    const { rows: userServices } = await executeQuery(`
      SELECT id FROM user_services
      WHERE user_id = ? AND service_id = ? AND service_type = 'premium_content' AND status = 'active'
    `, [req.user.id, id]);
    
    userHasPurchased = userServices.length > 0;
  }
  
  // Log view action
  if (req.user) {
    await logUserAction('premium_content_viewed', 'premium_content', id, {
      contentTitle: premiumContent.title_en,
      category: premiumContent.category
    }, req);
  }
  
  res.json({
    success: true,
    data: {
      premiumContent: {
        ...premiumContent,
        userHasPurchased
      }
    }
  });
}));

/**
 * @route   GET /api/services/packages
 * @desc    Get premium packages (using premium_content as packages)
 * @access  Public
 */
router.get('/packages', optionalAuth, asyncHandler(async (req, res) => {
  const { status = 'active', featured } = req.query;

  // Build query conditions
  let whereConditions = ['status = ?'];
  let queryParams = [status];

  if (featured !== undefined) {
    whereConditions.push('featured = ?');
    queryParams.push(featured === 'true');
  }

  const whereClause = whereConditions.join(' AND ');

  // Get premium packages from premium_content table with safe JSON handling
  const { rows: packages } = await executeQuery(`
    SELECT
      id, title_ar as name_ar, title_en as name_en,
      description_ar, description_en, detailed_description_ar, detailed_description_en,
      price, original_price, discount_percentage, category,
      COALESCE(features_ar, '[]') as features_ar,
      COALESCE(features_en, '[]') as features_en,
      COALESCE(tech_specs_ar, '[]') as tech_specs_ar,
      COALESCE(tech_specs_en, '[]') as tech_specs_en,
      video_url, image_url,
      COALESCE(gallery_images, '[]') as gallery_images,
      COALESCE(included_systems, '[]') as included_systems,
      COALESCE(included_services, '[]') as included_services,
      status, featured, sort_order,
      purchase_count, rating, rating_count,
      installation_guide_ar, installation_guide_en,
      support_info_ar, support_info_en, created_at, updated_at
    FROM premium_content
    WHERE ${whereClause}
    ORDER BY sort_order ASC, created_at DESC
  `, queryParams);
  
  // Parse JSON fields and get included content details
  for (let pkg of packages) {
    if (pkg.features_ar) pkg.features_ar = JSON.parse(pkg.features_ar);
    if (pkg.features_en) pkg.features_en = JSON.parse(pkg.features_en);
    
    // Get included content details
    if (pkg.included_content) {
      const contentIds = JSON.parse(pkg.included_content);
      if (contentIds.length > 0) {
        const { rows: content } = await executeQuery(`
          SELECT id, title_ar, title_en, price, image_url
          FROM premium_content
          WHERE id IN (${contentIds.map(() => '?').join(',')}) AND status = 'active'
        `, contentIds);
        pkg.included_content_details = content;
      }
    }
  }
  
  res.json({
    success: true,
    data: {
      packages
    }
  });
}));

module.exports = router;
