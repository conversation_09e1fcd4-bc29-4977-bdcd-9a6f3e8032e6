/**
 * Database Configuration and Connection Management
 * 
 * This module handles MySQL database connections with:
 * - Connection pooling for better performance
 * - Automatic reconnection on connection loss
 * - Comprehensive error handling
 * - Connection monitoring and logging
 * - Graceful shutdown handling
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'khanfashariya_db',
  charset: 'utf8mb4',
  timezone: '+00:00',
  
  // Connection pool settings (fixed)
  connectionLimit: 20,
  acquireTimeout: 30000,
  timeout: 30000,
  idleTimeout: 300000, // 5 minutes
  
  // SSL configuration (for production)
  ssl: process.env.NODE_ENV === 'production' ? {
    rejectUnauthorized: false
  } : false,
  
  // Additional MySQL settings
  supportBigNumbers: true,
  bigNumberStrings: true,
  dateStrings: false,
  multipleStatements: false,
  
  // Pool-specific settings for better connection management
  reconnect: true,
  keepAliveInitialDelay: 0,
  enableKeepAlive: true,
  
  // Handle disconnections
  handleDisconnects: true
};

// Global connection pool
let pool = null;
let isConnected = false;

/**
 * Create and configure the connection pool
 */
function createPool() {
  if (pool) {
    return pool;
  }
  
  console.log('🔧 Creating MySQL connection pool...');
  console.log(`📊 Database: ${dbConfig.database}@${dbConfig.host}:${dbConfig.port}`);
  
  pool = mysql.createPool(dbConfig);
  
  // Handle pool events with better error management
  pool.on('connection', (connection) => {
    console.log(`🔌 New database connection established (ID: ${connection.threadId})`);
    isConnected = true;
    
    // Set connection timeout and keep-alive
    connection.config.timeout = 30000;
    connection.config.acquireTimeout = 30000;
    
    // Handle individual connection errors
    connection.on('error', (err) => {
      console.error(`💥 Connection ${connection.threadId} error:`, err.message);
      if (err.code === 'PROTOCOL_CONNECTION_LOST') {
        console.log(`🔄 Connection ${connection.threadId} lost, will be recreated`);
      }
    });
  });
  
  pool.on('acquire', (connection) => {
    console.log(`📥 Connection ${connection.threadId} acquired`);
  });
  
  pool.on('release', (connection) => {
    console.log(`🔓 Connection ${connection.threadId} released`);
  });
  
  pool.on('error', (error) => {
    console.error('💥 Database pool error:', error.message);
    
    if (error.code === 'PROTOCOL_CONNECTION_LOST' || 
        error.code === 'ECONNRESET' || 
        error.code === 'ETIMEDOUT') {
      console.log('🔄 Connection lost, pool will handle reconnection...');
      isConnected = false;
    } else if (error.code === 'ER_SERVER_SHUTDOWN') {
      console.error('❌ MySQL server shutdown detected');
      isConnected = false;
      // Force pool recreation on next query
      setTimeout(() => {
        console.log('🔄 Attempting to recreate connection pool...');
        pool = null;
      }, 5000);
    } else {
      console.error('❌ Fatal database error:', error);
      isConnected = false;
    }
  });
  
  // Handle pool enqueue (when waiting for connection)
  pool.on('enqueue', () => {
    console.log('⏳ Waiting for available connection slot...');
  });
  
  return pool;
}

/**
 * Get a database connection from the pool
 */
async function connectDatabase() {
  try {
    if (!pool) {
      createPool();
    }
    
    // Test the connection
    const connection = await pool.getConnection();
    
    // Verify the connection is working
    await connection.execute('SELECT 1 as test');
    
    // Release the test connection back to the pool
    connection.release();
    
    isConnected = true;
    return pool;
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    isConnected = false;
    
    // Provide helpful error messages
    if (error.code === 'ECONNREFUSED') {
      console.error('🔧 Troubleshooting: MySQL server is not running or not accessible');
      console.error('   - Check if MySQL/WampServer is running');
      console.error('   - Verify host and port configuration');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('🔧 Troubleshooting: Database access denied');
      console.error('   - Check username and password in .env file');
      console.error('   - Verify user has proper privileges');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('🔧 Troubleshooting: Database does not exist');
      console.error('   - Run: npm run setup:db to create the database');
    }
    
    throw error;
  }
}

/**
 * Execute a query with automatic connection handling and retry logic
 */
async function executeQuery(sql, params = [], retries = 3) {
  let connection;
  
  try {
    if (!pool) {
      await connectDatabase();
    }
    
    connection = await pool.getConnection();
    
    // Test connection before executing query
    await connection.ping();
    
    const [rows, fields] = await connection.execute(sql, params);
    
    return { rows, fields };
    
  } catch (error) {
    console.error('❌ Query execution failed:', error.message);
    console.error('📝 SQL:', sql);
    console.error('📋 Params:', params);
    
    // Handle connection errors with retry logic
    if ((error.code === 'PROTOCOL_CONNECTION_LOST' || 
         error.code === 'ECONNRESET' || 
         error.code === 'ETIMEDOUT' ||
         error.code === 'ER_SERVER_SHUTDOWN') && retries > 0) {
      
      console.log(`🔄 Retrying query (${retries} attempts left)...`);
      
      // Release the failed connection
      if (connection) {
        try {
          connection.release();
        } catch (releaseError) {
          console.warn('Warning: Failed to release connection:', releaseError.message);
        }
        connection = null;
      }
      
      // Wait a bit before retrying
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Recreate pool if necessary
      if (error.code === 'ER_SERVER_SHUTDOWN') {
        pool = null;
        isConnected = false;
      }
      
      // Retry the query
      return executeQuery(sql, params, retries - 1);
    }
    
    throw error;
  } finally {
    if (connection) {
      try {
        connection.release();
      } catch (releaseError) {
        console.warn('Warning: Failed to release connection:', releaseError.message);
      }
    }
  }
}

/**
 * Execute multiple queries in a transaction
 */
async function executeTransaction(queries) {
  let connection;
  
  try {
    if (!pool) {
      await connectDatabase();
    }
    
    connection = await pool.getConnection();
    
    // Start transaction
    await connection.beginTransaction();
    
    const results = [];
    
    // Execute all queries
    for (const query of queries) {
      const { sql, params = [] } = query;
      const [rows, fields] = await connection.execute(sql, params);
      results.push({ rows, fields });
    }
    
    // Commit transaction
    await connection.commit();
    
    return results;
    
  } catch (error) {
    // Rollback transaction on error
    if (connection) {
      try {
        await connection.rollback();
        console.log('🔄 Transaction rolled back due to error');
      } catch (rollbackError) {
        console.error('❌ Rollback failed:', rollbackError.message);
      }
    }
    
    console.error('❌ Transaction failed:', error.message);
    throw error;
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

/**
 * Get database statistics
 */
async function getDatabaseStats() {
  try {
    const stats = {};
    
    // Get connection pool stats
    if (pool) {
      stats.pool = {
        totalConnections: pool.pool._allConnections.length,
        freeConnections: pool.pool._freeConnections.length,
        usedConnections: pool.pool._allConnections.length - pool.pool._freeConnections.length
      };
    }
    
    // Get database size
    const { rows: sizeRows } = await executeQuery(`
      SELECT 
        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
      FROM information_schema.tables 
      WHERE table_schema = ?
    `, [dbConfig.database]);
    
    stats.database = {
      name: dbConfig.database,
      size_mb: sizeRows[0]?.size_mb || 0
    };
    
    // Get table counts
    const { rows: tableRows } = await executeQuery(`
      SELECT 
        table_name,
        table_rows
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_type = 'BASE TABLE'
      ORDER BY table_rows DESC
    `, [dbConfig.database]);
    
    stats.tables = tableRows.reduce((acc, row) => {
      acc[row.table_name] = row.table_rows || 0;
      return acc;
    }, {});
    
    return stats;
    
  } catch (error) {
    console.error('❌ Failed to get database stats:', error.message);
    return { error: error.message };
  }
}

/**
 * Check database health
 */
async function checkDatabaseHealth() {
  try {
    const startTime = Date.now();
    
    // Test basic connectivity
    await executeQuery('SELECT 1 as test');
    
    const responseTime = Date.now() - startTime;
    
    // Get additional health metrics
    const { rows: processRows } = await executeQuery('SHOW PROCESSLIST');
    const { rows: statusRows } = await executeQuery("SHOW STATUS LIKE 'Threads_%'");
    
    const status = statusRows.reduce((acc, row) => {
      acc[row.Variable_name] = row.Value;
      return acc;
    }, {});
    
    return {
      status: 'healthy',
      responseTime: `${responseTime}ms`,
      isConnected,
      activeConnections: processRows.length,
      threadsConnected: status.Threads_connected || 0,
      threadsRunning: status.Threads_running || 0,
      timestamp: new Date().toISOString()
    };
    
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      isConnected: false,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Monitor connection health and recreate pool if needed
 */
async function monitorConnectionHealth() {
  if (!pool) return;
  
  try {
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    
    if (!isConnected) {
      console.log('✅ Database connection restored');
      isConnected = true;
    }
  } catch (error) {
    console.error('🔍 Connection health check failed:', error.message);
    isConnected = false;
    
    // If pool is completely broken, recreate it
    if (error.code === 'ER_SERVER_SHUTDOWN' || 
        error.code === 'ECONNREFUSED' ||
        error.code === 'ENOTFOUND') {
      console.log('🔄 Recreating connection pool due to server issues...');
      try {
        await pool.end();
      } catch (endError) {
        console.warn('Warning during pool cleanup:', endError.message);
      }
      pool = null;
      
      // Try to recreate pool after a delay
      setTimeout(async () => {
        try {
          await connectDatabase();
          console.log('✅ Connection pool recreated successfully');
        } catch (recreateError) {
          console.error('❌ Failed to recreate connection pool:', recreateError.message);
        }
      }, 5000);
    }
  }
}

// Start connection health monitoring
setInterval(monitorConnectionHealth, 30000); // Check every 30 seconds

/**
 * Close all database connections
 */
async function closeDatabase() {
  try {
    if (pool) {
      console.log('🔌 Closing database connection pool...');
      await pool.end();
      pool = null;
      isConnected = false;
      console.log('✅ Database connections closed successfully');
    }
  } catch (error) {
    console.error('❌ Error closing database connections:', error.message);
    throw error;
  }
}

/**
 * Utility function to generate UUID
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Utility function to format SQL for logging
 */
function formatSQL(sql, params = []) {
  let formattedSQL = sql;
  
  if (params.length > 0) {
    params.forEach((param, index) => {
      const placeholder = typeof param === 'string' ? `'${param}'` : param;
      formattedSQL = formattedSQL.replace('?', placeholder);
    });
  }
  
  return formattedSQL;
}

module.exports = {
  connectDatabase,
  closeDatabase,
  executeQuery,
  executeTransaction,
  getDatabaseStats,
  checkDatabaseHealth,
  generateUUID,
  formatSQL,
  
  // Getters
  get isConnected() { return isConnected; },
  get pool() { return pool; },
  get config() { return { ...dbConfig, password: '***' }; } // Hide password in config
};
