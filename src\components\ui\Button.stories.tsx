import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { Save, Download, Trash2, Plus } from 'lucide-react';
import But<PERSON> from './Button';

const meta = {
  title: 'UI Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# Button Component

A versatile button component with multiple variants, sizes, and states.

## Features
- Multiple variants (primary, secondary, outline, ghost, danger)
- Three sizes (sm, md, lg) with touch-friendly minimum heights
- Loading state with spinner
- Disabled state handling
- Icon support
- Accessibility features (ARIA attributes, keyboard navigation)
- RTL layout support

## Usage

\`\`\`tsx
import Button from './Button';

// Basic usage
<Button variant="primary" size="md" onClick={handleClick}>
  Save Changes
</Button>

// With loading state
<Button loading={isLoading} disabled={isLoading}>
  {isLoading ? 'Saving...' : 'Save'}
</Button>

// With icon
<Button variant="secondary">
  <Save className="w-4 h-4 mr-2" />
  Save Document
</Button>
\`\`\`
        `
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'outline', 'ghost', 'danger'],
      description: 'Visual style variant of the button'
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size of the button (affects padding and height)'
    },
    loading: {
      control: 'boolean',
      description: 'Shows loading spinner and disables the button'
    },
    disabled: {
      control: 'boolean',
      description: 'Disables the button and shows disabled state'
    },
    children: {
      control: 'text',
      description: 'Button content (text, icons, etc.)'
    },
    onClick: {
      action: 'clicked',
      description: 'Click event handler'
    }
  },
  args: {
    onClick: fn(),
    children: 'Button'
  }
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic variants
export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Primary Button'
  }
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Secondary Button'
  }
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    children: 'Outline Button'
  }
};

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    children: 'Ghost Button'
  }
};

export const Danger: Story = {
  args: {
    variant: 'danger',
    children: 'Danger Button'
  }
};

// Sizes
export const Small: Story = {
  args: {
    size: 'sm',
    children: 'Small Button'
  }
};

export const Medium: Story = {
  args: {
    size: 'md',
    children: 'Medium Button'
  }
};

export const Large: Story = {
  args: {
    size: 'lg',
    children: 'Large Button'
  }
};

// States
export const Loading: Story = {
  args: {
    loading: true,
    children: 'Loading...'
  }
};

export const Disabled: Story = {
  args: {
    disabled: true,
    children: 'Disabled Button'
  }
};

// With icons
export const WithIcon: Story = {
  args: {
    variant: 'primary',
    children: (
      <>
        <Save className="w-4 h-4 mr-2" />
        Save Changes
      </>
    )
  }
};

export const IconOnly: Story = {
  args: {
    variant: 'outline',
    size: 'md',
    'aria-label': 'Download file',
    children: <Download className="w-4 h-4" />
  }
};

// Button group example
export const ButtonGroup: Story = {
  render: () => (
    <div className="flex space-x-2 rtl:space-x-reverse">
      <Button variant="outline" size="sm">
        <Plus className="w-4 h-4 mr-1" />
        Add
      </Button>
      <Button variant="primary" size="sm">
        <Save className="w-4 h-4 mr-1" />
        Save
      </Button>
      <Button variant="danger" size="sm">
        <Trash2 className="w-4 h-4 mr-1" />
        Delete
      </Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Example of multiple buttons used together in a group.'
      }
    }
  }
};

// All sizes comparison
export const SizeComparison: Story = {
  render: () => (
    <div className="flex items-center space-x-4 rtl:space-x-reverse">
      <Button variant="primary" size="sm">
        Small
      </Button>
      <Button variant="primary" size="md">
        Medium
      </Button>
      <Button variant="primary" size="lg">
        Large
      </Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Comparison of all available button sizes.'
      }
    }
  }
};

// All variants comparison
export const VariantComparison: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        <Button variant="primary">Primary</Button>
        <Button variant="secondary">Secondary</Button>
        <Button variant="outline">Outline</Button>
        <Button variant="ghost">Ghost</Button>
        <Button variant="danger">Danger</Button>
      </div>
      <div className="flex flex-wrap gap-4">
        <Button variant="primary" disabled>Primary Disabled</Button>
        <Button variant="secondary" disabled>Secondary Disabled</Button>
        <Button variant="outline" disabled>Outline Disabled</Button>
        <Button variant="ghost" disabled>Ghost Disabled</Button>
        <Button variant="danger" disabled>Danger Disabled</Button>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Comparison of all available button variants in normal and disabled states.'
      }
    }
  }
};

// Arabic/RTL example
export const ArabicExample: Story = {
  render: () => (
    <div className="space-y-4" dir="rtl">
      <div className="flex space-x-4 rtl:space-x-reverse">
        <Button variant="primary">
          <Save className="w-4 h-4 ml-2" />
          حفظ التغييرات
        </Button>
        <Button variant="secondary">
          <Download className="w-4 h-4 ml-2" />
          تحميل الملف
        </Button>
      </div>
      <Button variant="outline" loading>
        جاري التحميل...
      </Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Example of buttons with Arabic text and RTL layout.'
      }
    }
  }
};
