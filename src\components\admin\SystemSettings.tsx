import React, { useState } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotifications } from '../../store/simpleStore';
import { useNotification } from '../../hooks/useNotification';
import {
  getAllUsers
} from '../../lib/apiServices';
import {
  createSampleData,
  generateBillingAlerts,
  createTestSubscription,
} from '../../lib/database';
import Button from '../ui/Button';
import Card from '../ui/Card';
import Input from '../ui/Input';
import Modal from '../ui/Modal';
import { Save, Download, Trash2, Plus, Settings, Globe, ArrowLeft, Home, TestTube, Database, Eye, Edit, Shield } from 'lucide-react';
import LanguageTest from '../LanguageTest';
import TestAdminFeatures from '../TestAdminFeatures';
import SimpleTestModal from '../SimpleTestModal';
import DatabaseTest from '../DatabaseTest';
import ComprehensiveTest from '../ComprehensiveTest';
import AdminPanelTest from '../AdminPanelTest';
import ModalTest from '../ModalTest';
import QuickTest from '../QuickTest';

/**
 * System Settings component for admin panel
 */
interface SystemSettingsProps {
  onBack?: () => void;
}

const SystemSettings: React.FC<SystemSettingsProps> = ({ onBack }) => {
  const { t, language, changeLanguage } = useTranslation();
  const { addNotification } = useNotifications();
  const { showNotification } = useNotification();
  const [showModal, setShowModal] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [showComponentTest, setShowComponentTest] = useState(false);
  const [showLanguageTest, setShowLanguageTest] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  // Test component states
  const [showTestFeatures, setShowTestFeatures] = useState(false);
  const [showSimpleTest, setShowSimpleTest] = useState(false);
  const [showDatabaseTest, setShowDatabaseTest] = useState(false);
  const [showComprehensiveTest, setShowComprehensiveTest] = useState(false);
  const [showAdminPanelTest, setShowAdminPanelTest] = useState(false);
  const [showModalTest, setShowModalTest] = useState(false);
  const [showQuickTest, setShowQuickTest] = useState(false);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const handleCreateSampleData = async () => {
    setLoading(true);
    try {
      const result = createSampleData();
      if (result.success) {
        addTestResult(language === 'ar' ? 'تم إنشاء البيانات التجريبية بنجاح' : 'Sample data created successfully');
        showNotification({
          type: 'success',
          message: language === 'ar' ? 'تم إنشاء البيانات التجريبية بنجاح' : 'Sample data created successfully'
        });
      } else {
        throw new Error(result.error || 'Failed to create sample data');
      }
    } catch (error) {
      addTestResult(language === 'ar' ? 'فشل في إنشاء البيانات التجريبية' : 'Failed to create sample data');
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في إنشاء البيانات التجريبية' : 'Failed to create sample data'
      });
    }
    setLoading(false);
  };

  const handleGenerateBillingAlerts = async () => {
    setLoading(true);
    try {
      const result = generateBillingAlerts();
      if (result.success) {
        addTestResult(`${language === 'ar' ? 'تم إنشاء' : 'Generated'} ${result.alertsCreated} ${language === 'ar' ? 'تنبيهات فوترة' : 'billing alerts'}`);
        showNotification({
          type: 'success',
          message: `${language === 'ar' ? 'تم إنشاء' : 'Generated'} ${result.alertsCreated} ${language === 'ar' ? 'تنبيهات فوترة' : 'billing alerts'}`
        });
      } else {
        throw new Error(result.error || 'Failed to generate billing alerts');
      }
    } catch (error) {
      addTestResult(language === 'ar' ? 'فشل في إنشاء تنبيهات الفوترة' : 'Failed to generate billing alerts');
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في إنشاء تنبيهات الفوترة' : 'Failed to generate billing alerts'
      });
    }
    setLoading(false);
  };

  const handleCreateTestSubscription = async () => {
    setLoading(true);
    try {
      const users = getAllUsers();
      if (users.data && users.data.length > 0) {
        const testUser = users.data[0];
        const result = createTestSubscription(testUser.id);
        if (result.data) {
          addTestResult(language === 'ar' ? 'تم إنشاء اشتراك تجريبي بنجاح' : 'Test subscription created successfully');
          showNotification({
            type: 'success',
            message: language === 'ar' ? 'تم إنشاء اشتراك تجريبي بنجاح' : 'Test subscription created successfully'
          });
        } else {
          throw new Error('Failed to create test subscription');
        }
      } else {
        throw new Error('No users found');
      }
    } catch (error) {
      addTestResult(language === 'ar' ? 'فشل في إنشاء الاشتراك التجريبي' : 'Failed to create test subscription');
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في إنشاء الاشتراك التجريبي' : 'Failed to create test subscription'
      });
    }
    setLoading(false);
  };

  const showSuccessNotification = () => {
    addNotification({
      type: 'success',
      title: t('common.success'),
      message: t('notifications.updateSuccess'),
      duration: 5000
    });
  };

  const showErrorNotification = () => {
    addNotification({
      type: 'error',
      title: language === 'ar' ? 'خطأ' : 'Error',
      message: language === 'ar' ? 'حدث خطأ أثناء الحفظ' : 'An error occurred while saving',
      duration: 0
    });
  };

  const showWarningNotification = () => {
    addNotification({
      type: 'warning',
      title: language === 'ar' ? 'تحذير' : 'Warning',
      message: language === 'ar' ? 'يرجى التحقق من الإعدادات' : 'Please check your settings',
      duration: 7000
    });
  };

  const showInfoNotification = () => {
    addNotification({
      type: 'info',
      title: language === 'ar' ? 'معلومات' : 'Information',
      message: language === 'ar' ? 'تم تحديث النظام' : 'System has been updated',
      duration: 4000
    });
  };

  const handleLoadingTest = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      showSuccessNotification();
    }, 3000);
  };

  if (showComponentTest) {
    return (
      <div className="min-h-screen bg-background p-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-3xl font-bold text-white">
                {language === 'ar' ? 'اختبار المكونات' : 'Component Testing'}
              </h1>
              
              {/* Back Button */}
              <Button
                variant="outline"
                onClick={() => setShowComponentTest(false)}
                className="flex items-center"
              >
                {language === 'ar' ? (
                  <>
                    <span className="mr-2">العودة للإعدادات</span>
                    <ArrowLeft className="w-4 h-4" />
                  </>
                ) : (
                  <>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    <span>Back to Settings</span>
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Database & System Tests */}
          <Card className="mb-8">
            <Card.Header>
              <h2 className="text-xl font-semibold text-white flex items-center space-x-2 rtl:space-x-reverse">
                <Database className="w-6 h-6 text-secondary" />
                <span>{language === 'ar' ? 'أدوات اختبار النظام' : 'System Testing Tools'}</span>
              </h2>
              <p className="text-gray-400 mt-2">
                {language === 'ar' ? 'أدوات لاختبار وإدارة بيانات النظام' : 'Tools for testing and managing system data'}
              </p>
            </Card.Header>
            <Card.Body>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                {/* Create Sample Data */}
                <div className="bg-primary/30 rounded-lg border border-blue-500/30 p-4">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse mb-3">
                    <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                      <Database className="w-5 h-5 text-blue-400" />
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">
                        {language === 'ar' ? 'إنشاء بيانات تجريبية' : 'Create Sample Data'}
                      </h3>
                    </div>
                  </div>
                  <p className="text-gray-300 text-sm mb-4">
                    {language === 'ar' ? 'إنشاء نسخة مميزة وأنظمة وخدمات تجريبية للاختبار' : 'Create sample premium edition, systems, and services for testing'}
                  </p>
                  <Button
                    onClick={handleCreateSampleData}
                    disabled={loading}
                    variant="outline"
                    className="w-full border-blue-500/30 text-blue-400 hover:bg-blue-500/20"
                  >
                    <TestTube className="w-4 h-4 mr-2" />
                    {language === 'ar' ? 'إنشاء البيانات' : 'Create Data'}
                  </Button>
                </div>

                {/* Generate Billing Alerts */}
                <div className="bg-primary/30 rounded-lg border border-orange-500/30 p-4">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse mb-3">
                    <div className="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center">
                      <TestTube className="w-5 h-5 text-orange-400" />
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">
                        {language === 'ar' ? 'تنبيهات الفوترة' : 'Billing Alerts'}
                      </h3>
                    </div>
                  </div>
                  <p className="text-gray-300 text-sm mb-4">
                    {language === 'ar' ? 'إنشاء تنبيهات للاشتراكات المنتهية الصلاحية' : 'Generate alerts for expiring subscriptions'}
                  </p>
                  <Button
                    onClick={handleGenerateBillingAlerts}
                    disabled={loading}
                    variant="outline"
                    className="w-full border-orange-500/30 text-orange-400 hover:bg-orange-500/20"
                  >
                    <TestTube className="w-4 h-4 mr-2" />
                    {language === 'ar' ? 'إنشاء التنبيهات' : 'Generate Alerts'}
                  </Button>
                </div>

                {/* Create Test Subscription */}
                <div className="bg-primary/30 rounded-lg border border-green-500/30 p-4">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse mb-3">
                    <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                      <TestTube className="w-5 h-5 text-green-400" />
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">
                        {language === 'ar' ? 'اشتراك تجريبي' : 'Test Subscription'}
                      </h3>
                    </div>
                  </div>
                  <p className="text-gray-300 text-sm mb-4">
                    {language === 'ar' ? 'إنشاء اشتراك تجريبي لاختبار نظام الفوترة' : 'Create a test subscription to test billing system'}
                  </p>
                  <Button
                    onClick={handleCreateTestSubscription}
                    disabled={loading}
                    variant="outline"
                    className="w-full border-green-500/30 text-green-400 hover:bg-green-500/20"
                  >
                    <TestTube className="w-4 h-4 mr-2" />
                    {language === 'ar' ? 'إنشاء اشتراك' : 'Create Subscription'}
                  </Button>
                </div>
              </div>

              {/* Test Results */}
              <div className="bg-background rounded-lg border border-gray-700 p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-white font-semibold flex items-center space-x-2 rtl:space-x-reverse">
                    <TestTube className="w-5 h-5 text-secondary" />
                    <span>{language === 'ar' ? 'نتائج الاختبار' : 'Test Results'}</span>
                  </h3>
                  <Button
                    onClick={() => setTestResults([])}
                    variant="outline"
                    size="sm"
                    className="border-red-500/30 text-red-400 hover:bg-red-500/20"
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                    {language === 'ar' ? 'مسح' : 'Clear'}
                  </Button>
                </div>

                <div className="bg-primary/30 rounded-lg p-3 max-h-48 overflow-y-auto">
                  {testResults.length > 0 ? (
                    <div className="space-y-1">
                      {testResults.map((result, index) => (
                        <div key={index} className="text-gray-300 text-sm font-mono">
                          {result}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center text-gray-500 py-4">
                      <TestTube className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">{language === 'ar' ? 'لا توجد نتائج اختبار بعد' : 'No test results yet'}</p>
                    </div>
                  )}
                </div>
              </div>
            </Card.Body>
          </Card>

          {/* Button Tests */}
          <Card className="mb-8">
            <Card.Header>
              <h2 className="text-xl font-semibold text-white">
                {language === 'ar' ? 'اختبار الأزرار' : 'Button Tests'}
              </h2>
            </Card.Header>
            <Card.Body>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Button Variants */}
                <div className="space-y-4">
                  <h3 className="text-lg text-secondary font-semibold">
                    {language === 'ar' ? 'الأنواع' : 'Variants'}
                  </h3>
                  <div className="space-y-3">
                    <Button variant="primary" className="w-full">
                      <Save className="w-4 h-4 mr-2" />
                      {language === 'ar' ? 'أساسي' : 'Primary'}
                    </Button>
                    <Button variant="secondary" className="w-full">
                      <Download className="w-4 h-4 mr-2" />
                      {language === 'ar' ? 'ثانوي' : 'Secondary'}
                    </Button>
                    <Button variant="outline" className="w-full">
                      <Plus className="w-4 h-4 mr-2" />
                      {language === 'ar' ? 'محدد' : 'Outline'}
                    </Button>
                    <Button variant="ghost" className="w-full">
                      <Settings className="w-4 h-4 mr-2" />
                      {language === 'ar' ? 'شفاف' : 'Ghost'}
                    </Button>
                    <Button variant="danger" className="w-full">
                      <Trash2 className="w-4 h-4 mr-2" />
                      {language === 'ar' ? 'خطر' : 'Danger'}
                    </Button>
                  </div>
                </div>

                {/* Button Sizes */}
                <div className="space-y-4">
                  <h3 className="text-lg text-secondary font-semibold">
                    {language === 'ar' ? 'الأحجام' : 'Sizes'}
                  </h3>
                  <div className="space-y-3">
                    <Button size="sm" className="w-full">
                      {language === 'ar' ? 'صغير' : 'Small'}
                    </Button>
                    <Button size="md" className="w-full">
                      {language === 'ar' ? 'متوسط' : 'Medium'}
                    </Button>
                    <Button size="lg" className="w-full">
                      {language === 'ar' ? 'كبير' : 'Large'}
                    </Button>
                  </div>
                </div>

                {/* Button States */}
                <div className="space-y-4">
                  <h3 className="text-lg text-secondary font-semibold">
                    {language === 'ar' ? 'الحالات' : 'States'}
                  </h3>
                  <div className="space-y-3">
                    <Button loading={loading} onClick={handleLoadingTest} className="w-full">
                      {loading 
                        ? (language === 'ar' ? 'جاري التحميل...' : 'Loading...') 
                        : (language === 'ar' ? 'اختبار التحميل' : 'Test Loading')
                      }
                    </Button>
                    <Button disabled className="w-full">
                      {language === 'ar' ? 'معطل' : 'Disabled'}
                    </Button>
                  </div>
                </div>
              </div>
            </Card.Body>
          </Card>

          {/* Input Tests */}
          <Card className="mb-8">
            <Card.Header>
              <h2 className="text-xl font-semibold text-white">
                {language === 'ar' ? 'اختبار حقول الإدخال' : 'Input Tests'}
              </h2>
            </Card.Header>
            <Card.Body>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <Input
                    label={language === 'ar' ? 'الاسم' : 'Name'}
                    placeholder={language === 'ar' ? 'أدخل اسمك' : 'Enter your name'}
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                  />
                  
                  <Input
                    label={language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                    type="email"
                    placeholder={language === 'ar' ? 'أدخل بريدك الإلكتروني' : 'Enter your email'}
                    error={inputValue.length > 0 && !inputValue.includes('@') ? 
                      (language === 'ar' ? 'البريد الإلكتروني غير صحيح' : 'Invalid email') : 
                      undefined
                    }
                  />
                  
                  <Input
                    label={language === 'ar' ? 'كلمة المرور' : 'Password'}
                    type="password"
                    showPasswordToggle
                    placeholder={language === 'ar' ? 'أدخل كلمة المرور' : 'Enter password'}
                  />
                </div>
                
                <div className="space-y-4">
                  <Input
                    label={language === 'ar' ? 'البحث' : 'Search'}
                    leftIcon={<Settings />}
                    placeholder={language === 'ar' ? 'ابحث...' : 'Search...'}
                  />
                  
                  <Input
                    label={language === 'ar' ? 'مع تحميل' : 'With Loading'}
                    loading={loading}
                    placeholder={language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
                  />
                  
                  <Input
                    label={language === 'ar' ? 'نجح' : 'Success'}
                    success={language === 'ar' ? 'البيانات صحيحة' : 'Data is valid'}
                    placeholder={language === 'ar' ? 'بيانات صحيحة' : 'Valid data'}
                  />
                </div>
              </div>
            </Card.Body>
          </Card>

          {/* Notification Tests */}
          <Card className="mb-8">
            <Card.Header>
              <h2 className="text-xl font-semibold text-white">
                {language === 'ar' ? 'اختبار الإشعارات' : 'Notification Tests'}
              </h2>
            </Card.Header>
            <Card.Body>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button variant="outline" onClick={showSuccessNotification}>
                  {language === 'ar' ? 'نجاح' : 'Success'}
                </Button>
                <Button variant="outline" onClick={showErrorNotification}>
                  {language === 'ar' ? 'خطأ' : 'Error'}
                </Button>
                <Button variant="outline" onClick={showWarningNotification}>
                  {language === 'ar' ? 'تحذير' : 'Warning'}
                </Button>
                <Button variant="outline" onClick={showInfoNotification}>
                  {language === 'ar' ? 'معلومات' : 'Info'}
                </Button>
              </div>
            </Card.Body>
          </Card>

          {/* Modal Test */}
          <Card>
            <Card.Header>
              <h2 className="text-xl font-semibold text-white">
                {language === 'ar' ? 'اختبار النوافذ المنبثقة' : 'Modal Tests'}
              </h2>
            </Card.Header>
            <Card.Body>
              <Button onClick={() => setShowModal(true)}>
                {language === 'ar' ? 'فتح نافذة منبثقة' : 'Open Modal'}
              </Button>
            </Card.Body>
          </Card>

          {/* Modal */}
          {showModal && (
            <Modal
              isOpen={showModal}
              onClose={() => setShowModal(false)}
              title={language === 'ar' ? 'نافذة اختبار' : 'Test Modal'}
            >
              <Modal.Body>
                <p className="text-white">
                  {language === 'ar' 
                    ? 'هذه نافذة منبثقة للاختبار. يمكنك إغلاقها بالنقر على زر الإغلاق أو الضغط على Escape.'
                    : 'This is a test modal. You can close it by clicking the close button or pressing Escape.'
                  }
                </p>
              </Modal.Body>
              <Modal.Footer>
                <Button variant="outline" onClick={() => setShowModal(false)}>
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button onClick={() => {
                  setShowModal(false);
                  showSuccessNotification();
                }}>
                  {language === 'ar' ? 'موافق' : 'OK'}
                </Button>
              </Modal.Footer>
            </Modal>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-white">
          {language === 'ar' ? 'إعدادات النظام' : 'System Settings'}
        </h1>
        
        {onBack && (
          <Button variant="outline" onClick={onBack}>
            {language === 'ar' ? 'العودة' : 'Back'}
          </Button>
        )}
      </div>

      {/* Settings Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Language Settings */}
        <Card>
          <Card.Header>
            <h2 className="text-xl font-semibold text-white flex items-center">
              <Globe className="w-5 h-5 mr-2" />
              {t('common.languageSettings')}
            </h2>
          </Card.Header>
          <Card.Body>
            <div className="space-y-4">
              <p className="text-gray-300">
                {t('common.chooseLanguage')}
              </p>
              <div className="space-y-3">
                <Button
                  variant="outline"
                  onClick={() => changeLanguage(language === 'ar' ? 'en' : 'ar')}
                  className="w-full"
                >
                  <Globe className="w-4 h-4 mr-2" />
                  {language === 'ar' ? t('common.switchToEnglish') : t('common.switchToArabic')}
                </Button>

                <Button
                  variant="secondary"
                  onClick={() => setShowLanguageTest(true)}
                  className="w-full"
                >
                  <TestTube className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'اختبار اللغة' : 'Language Test'}
                </Button>
              </div>
            </div>
          </Card.Body>
        </Card>

        {/* Component Testing */}
        <Card>
          <Card.Header>
            <h2 className="text-xl font-semibold text-white flex items-center">
              <TestTube className="w-5 h-5 mr-2" />
              {language === 'ar' ? 'اختبار المكونات' : 'Component Testing'}
            </h2>
          </Card.Header>
          <Card.Body>
            <div className="space-y-4">
              <p className="text-gray-300">
                {language === 'ar' 
                  ? 'اختبر جميع مكونات الواجهة والتأكد من عملها بشكل صحيح'
                  : 'Test all UI components and ensure they work correctly'
                }
              </p>
              <Button
                variant="primary"
                onClick={() => setShowComponentTest(true)}
                className="w-full"
              >
                <TestTube className="w-4 h-4 mr-2" />
                {language === 'ar' ? 'فتح صفحة الاختبار' : 'Open Test Page'}
              </Button>
            </div>
          </Card.Body>
        </Card>

        {/* Notification Testing */}
        <Card>
          <Card.Header>
            <h2 className="text-xl font-semibold text-white">
              {language === 'ar' ? 'اختبار الإشعارات' : 'Notification Testing'}
            </h2>
          </Card.Header>
          <Card.Body>
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" size="sm" onClick={showSuccessNotification}>
                {language === 'ar' ? 'نجاح' : 'Success'}
              </Button>
              <Button variant="outline" size="sm" onClick={showErrorNotification}>
                {language === 'ar' ? 'خطأ' : 'Error'}
              </Button>
              <Button variant="outline" size="sm" onClick={showWarningNotification}>
                {language === 'ar' ? 'تحذير' : 'Warning'}
              </Button>
              <Button variant="outline" size="sm" onClick={showInfoNotification}>
                {language === 'ar' ? 'معلومات' : 'Info'}
              </Button>
            </div>
          </Card.Body>
        </Card>

        {/* System Data Management */}
        <Card className="mb-6">
          <Card.Header>
            <h2 className="text-xl font-semibold text-white flex items-center">
              <Database className="w-5 h-5 mr-2 text-secondary" />
              {language === 'ar' ? 'إدارة بيانات النظام' : 'System Data Management'}
            </h2>
            <p className="text-gray-400 text-sm mt-2">
              {language === 'ar'
                ? 'أدوات لإنشاء وإدارة البيانات التجريبية والاختبارات'
                : 'Tools for creating and managing test data and system tests'
              }
            </p>
          </Card.Header>
          <Card.Body>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              {/* Create Sample Data */}
              <div className="bg-primary/30 rounded-lg border border-blue-500/30 p-4">
                <div className="flex items-center space-x-3 rtl:space-x-reverse mb-3">
                  <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <Database className="w-5 h-5 text-blue-400" />
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">
                      {language === 'ar' ? 'إنشاء بيانات تجريبية' : 'Create Sample Data'}
                    </h3>
                  </div>
                </div>
                <p className="text-gray-300 text-sm mb-4">
                  {language === 'ar' ? 'إنشاء نسخة مميزة وأنظمة وخدمات تجريبية للاختبار' : 'Create sample premium edition, systems, and services for testing'}
                </p>
                <Button
                  onClick={handleCreateSampleData}
                  disabled={loading}
                  variant="outline"
                  className="w-full border-blue-500/30 text-blue-400 hover:bg-blue-500/20"
                >
                  <TestTube className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'إنشاء البيانات' : 'Create Data'}
                </Button>
              </div>

              {/* Generate Billing Alerts */}
              <div className="bg-primary/30 rounded-lg border border-orange-500/30 p-4">
                <div className="flex items-center space-x-3 rtl:space-x-reverse mb-3">
                  <div className="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center">
                    <TestTube className="w-5 h-5 text-orange-400" />
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">
                      {language === 'ar' ? 'تنبيهات الفوترة' : 'Billing Alerts'}
                    </h3>
                  </div>
                </div>
                <p className="text-gray-300 text-sm mb-4">
                  {language === 'ar' ? 'إنشاء تنبيهات للاشتراكات المنتهية الصلاحية' : 'Generate alerts for expiring subscriptions'}
                </p>
                <Button
                  onClick={handleGenerateBillingAlerts}
                  disabled={loading}
                  variant="outline"
                  className="w-full border-orange-500/30 text-orange-400 hover:bg-orange-500/20"
                >
                  <TestTube className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'إنشاء التنبيهات' : 'Generate Alerts'}
                </Button>
              </div>

              {/* Create Test Subscription */}
              <div className="bg-primary/30 rounded-lg border border-green-500/30 p-4">
                <div className="flex items-center space-x-3 rtl:space-x-reverse mb-3">
                  <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <TestTube className="w-5 h-5 text-green-400" />
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">
                      {language === 'ar' ? 'اشتراك تجريبي' : 'Test Subscription'}
                    </h3>
                  </div>
                </div>
                <p className="text-gray-300 text-sm mb-4">
                  {language === 'ar' ? 'إنشاء اشتراك تجريبي لاختبار نظام الفوترة' : 'Create a test subscription to test billing system'}
                </p>
                <Button
                  onClick={handleCreateTestSubscription}
                  disabled={loading}
                  variant="outline"
                  className="w-full border-green-500/30 text-green-400 hover:bg-green-500/20"
                >
                  <TestTube className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'إنشاء اشتراك' : 'Create Subscription'}
                </Button>
              </div>
            </div>

            {/* Test Results */}
            <div className="bg-background rounded-lg border border-gray-700 p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-white font-semibold flex items-center space-x-2 rtl:space-x-reverse">
                  <TestTube className="w-5 h-5 text-secondary" />
                  <span>{language === 'ar' ? 'نتائج الاختبار' : 'Test Results'}</span>
                </h3>
                <Button
                  onClick={() => setTestResults([])}
                  variant="outline"
                  size="sm"
                  className="border-red-500/30 text-red-400 hover:bg-red-500/20"
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  {language === 'ar' ? 'مسح' : 'Clear'}
                </Button>
              </div>

              <div className="bg-primary/30 rounded-lg p-3 max-h-48 overflow-y-auto">
                {testResults.length > 0 ? (
                  <div className="space-y-1">
                    {testResults.map((result, index) => (
                      <div key={index} className="text-gray-300 text-sm font-mono">
                        {result}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-4">
                    <TestTube className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">{language === 'ar' ? 'لا توجد نتائج اختبار بعد' : 'No test results yet'}</p>
                  </div>
                )}
              </div>
            </div>
          </Card.Body>
        </Card>

        {/* Advanced Testing Suite */}
        <Card>
          <Card.Header>
            <h2 className="text-xl font-semibold text-white flex items-center">
              <Shield className="w-5 h-5 mr-2" />
              {language === 'ar' ? 'مجموعة الاختبارات المتقدمة' : 'Advanced Testing Suite'}
            </h2>
            <p className="text-gray-400 text-sm mt-2">
              {language === 'ar'
                ? 'اختبارات شاملة لجميع وظائف النظام والمكونات'
                : 'Comprehensive tests for all system functions and components'
              }
            </p>
          </Card.Header>
          <Card.Body>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Quick Test */}
              <Button
                variant="outline"
                onClick={() => setShowQuickTest(true)}
                className="flex items-center justify-start p-4 h-auto"
              >
                <TestTube className="w-5 h-5 mr-3 text-green-400" />
                <div className="text-left">
                  <div className="font-medium text-white">
                    {language === 'ar' ? 'اختبار سريع' : 'Quick Test'}
                  </div>
                  <div className="text-xs text-gray-400">
                    {language === 'ar' ? 'اختبار سريع للمشاكل' : 'Quick debug test'}
                  </div>
                </div>
              </Button>

              {/* Admin Panel Test */}
              <Button
                variant="outline"
                onClick={() => setShowAdminPanelTest(true)}
                className="flex items-center justify-start p-4 h-auto"
              >
                <Shield className="w-5 h-5 mr-3 text-indigo-400" />
                <div className="text-left">
                  <div className="font-medium text-white">
                    {language === 'ar' ? 'دليل لوحة التحكم' : 'Admin Panel Guide'}
                  </div>
                  <div className="text-xs text-gray-400">
                    {language === 'ar' ? 'دليل اختبار لوحة التحكم' : 'Admin panel test guide'}
                  </div>
                </div>
              </Button>

              {/* Modal Test */}
              <Button
                variant="outline"
                onClick={() => setShowModalTest(true)}
                className="flex items-center justify-start p-4 h-auto"
              >
                <Eye className="w-5 h-5 mr-3 text-orange-400" />
                <div className="text-left">
                  <div className="font-medium text-white">
                    {language === 'ar' ? 'اختبار النوافذ' : 'Modal Test'}
                  </div>
                  <div className="text-xs text-gray-400">
                    {language === 'ar' ? 'اختبار النوافذ المنبثقة' : 'Test modal windows'}
                  </div>
                </div>
              </Button>

              {/* Database Test */}
              <Button
                variant="outline"
                onClick={() => setShowDatabaseTest(true)}
                className="flex items-center justify-start p-4 h-auto"
              >
                <Database className="w-5 h-5 mr-3 text-blue-400" />
                <div className="text-left">
                  <div className="font-medium text-white">
                    {language === 'ar' ? 'اختبار قاعدة البيانات' : 'Database Test'}
                  </div>
                  <div className="text-xs text-gray-400">
                    {language === 'ar' ? 'اختبار عمليات قاعدة البيانات' : 'Test database operations'}
                  </div>
                </div>
              </Button>

              {/* Comprehensive Test */}
              <Button
                variant="outline"
                onClick={() => setShowComprehensiveTest(true)}
                className="flex items-center justify-start p-4 h-auto"
              >
                <Settings className="w-5 h-5 mr-3 text-purple-400" />
                <div className="text-left">
                  <div className="font-medium text-white">
                    {language === 'ar' ? 'اختبار شامل' : 'Comprehensive Test'}
                  </div>
                  <div className="text-xs text-gray-400">
                    {language === 'ar' ? 'اختبار شامل لجميع الوظائف' : 'Complete system test'}
                  </div>
                </div>
              </Button>

              {/* Test Admin Features */}
              <Button
                variant="outline"
                onClick={() => setShowTestFeatures(true)}
                className="flex items-center justify-start p-4 h-auto"
              >
                <Edit className="w-5 h-5 mr-3 text-red-400" />
                <div className="text-left">
                  <div className="font-medium text-white">
                    {language === 'ar' ? 'اختبار ميزات الإدارة' : 'Test Admin Features'}
                  </div>
                  <div className="text-xs text-gray-400">
                    {language === 'ar' ? 'اختبار وظائف الإدارة' : 'Test admin functions'}
                  </div>
                </div>
              </Button>
            </div>
          </Card.Body>
        </Card>

        {/* System Information */}
        <Card>
          <Card.Header>
            <h2 className="text-xl font-semibold text-white">
              {language === 'ar' ? 'معلومات النظام' : 'System Information'}
            </h2>
          </Card.Header>
          <Card.Body>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">
                  {language === 'ar' ? 'إصدار النظام:' : 'System Version:'}
                </span>
                <span className="text-white">v2.0.0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">
                  {language === 'ar' ? 'آخر تحديث:' : 'Last Update:'}
                </span>
                <span className="text-white">
                  {new Date().toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US', {
                    calendar: 'gregory',
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                  })}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">
                  {language === 'ar' ? 'حالة النظام:' : 'System Status:'}
                </span>
                <span className="text-green-400">
                  {language === 'ar' ? 'يعمل بشكل طبيعي' : 'Running Normally'}
                </span>
              </div>
            </div>
          </Card.Body>
        </Card>
      </div>

      {/* Language Test Modal */}
      {showLanguageTest && (
        <LanguageTest />
      )}

      {/* Quick Test Modal */}
      {showQuickTest && (
        <QuickTest onClose={() => setShowQuickTest(false)} />
      )}

      {/* Admin Panel Test Guide Modal */}
      {showAdminPanelTest && (
        <AdminPanelTest onClose={() => setShowAdminPanelTest(false)} />
      )}

      {/* Modal Test Modal */}
      {showModalTest && (
        <ModalTest onClose={() => setShowModalTest(false)} />
      )}

      {/* Database Test Modal */}
      {showDatabaseTest && (
        <DatabaseTest onClose={() => setShowDatabaseTest(false)} />
      )}

      {/* Comprehensive Test Modal */}
      {showComprehensiveTest && (
        <ComprehensiveTest onClose={() => setShowComprehensiveTest(false)} />
      )}

      {/* Simple Test Modal */}
      {showSimpleTest && (
        <SimpleTestModal onClose={() => setShowSimpleTest(false)} />
      )}

      {/* Test Admin Features Modal */}
      {showTestFeatures && (
        <TestAdminFeatures onClose={() => setShowTestFeatures(false)} />
      )}
    </div>
  );
};

export default SystemSettings;
