#!/usr/bin/env node

/**
 * API Testing Script for Khanfashariya.com
 * 
 * This script performs comprehensive testing of all API endpoints:
 * - Authentication endpoints
 * - User management endpoints
 * - System services endpoints
 * - Technical services endpoints
 * - Orders endpoints
 * - Admin endpoints
 * - File upload endpoints
 * 
 * Usage: node scripts/test-api.js [--host=localhost] [--port=3001]
 */

const axios = require('axios');
require('dotenv').config();

// Configuration
const API_HOST = process.env.API_HOST || 'localhost';
const API_PORT = process.env.API_PORT || 3001;
const BASE_URL = `http://${API_HOST}:${API_PORT}`;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  failures: []
};

/**
 * Test helper function
 */
async function test(name, testFn) {
  testResults.total++;
  
  try {
    log(`🧪 Testing: ${name}`, 'blue');
    await testFn();
    testResults.passed++;
    log(`   ✅ PASSED: ${name}`, 'green');
  } catch (error) {
    testResults.failed++;
    testResults.failures.push({ name, error: error.message });
    log(`   ❌ FAILED: ${name} - ${error.message}`, 'red');
  }
}

/**
 * API request helper
 */
async function apiRequest(method, endpoint, data = null, headers = {}) {
  const config = {
    method,
    url: `${BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  const response = await axios(config);
  return response.data;
}

/**
 * Test server health
 */
async function testServerHealth() {
  await test('Server Health Check', async () => {
    const response = await apiRequest('GET', '/health');
    
    if (response.status !== 'healthy') {
      throw new Error('Server is not healthy');
    }
    
    if (!response.database || response.database !== 'connected') {
      throw new Error('Database is not connected');
    }
  });
  
  await test('API Status Check', async () => {
    const response = await apiRequest('GET', '/api/status');
    
    if (!response.message || !response.endpoints) {
      throw new Error('API status response is invalid');
    }
  });
}

/**
 * Test authentication endpoints
 */
async function testAuthentication() {
  let accessToken = '';
  let refreshToken = '';
  
  await test('User Registration', async () => {
    const userData = {
      email: '<EMAIL>',
      username: 'testuser',
      full_name: 'Test User',
      password: 'test123'
    };
    
    const response = await apiRequest('POST', '/api/auth/register', userData);
    
    if (!response.success || !response.data.user) {
      throw new Error('Registration failed');
    }
  });
  
  await test('User Login', async () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'admin123'
    };
    
    const response = await apiRequest('POST', '/api/auth/login', loginData);
    
    if (!response.success || !response.data.tokens) {
      throw new Error('Login failed');
    }
    
    accessToken = response.data.tokens.accessToken;
    refreshToken = response.data.tokens.refreshToken;
  });
  
  await test('Token Refresh', async () => {
    const response = await apiRequest('POST', '/api/auth/refresh', {
      refreshToken
    });
    
    if (!response.success || !response.data.tokens) {
      throw new Error('Token refresh failed');
    }
    
    accessToken = response.data.tokens.accessToken;
  });
  
  return { accessToken, refreshToken };
}

/**
 * Test user endpoints
 */
async function testUserEndpoints(accessToken) {
  const authHeaders = { Authorization: `Bearer ${accessToken}` };
  
  await test('Get User Profile', async () => {
    const response = await apiRequest('GET', '/api/users/profile', null, authHeaders);
    
    if (!response.success || !response.data.user) {
      throw new Error('Failed to get user profile');
    }
  });
  
  await test('Update User Profile', async () => {
    const updateData = {
      full_name: 'Updated Admin User',
      phone: '+1234567890'
    };
    
    const response = await apiRequest('PUT', '/api/users/profile', updateData, authHeaders);
    
    if (!response.success) {
      throw new Error('Failed to update user profile');
    }
  });
  
  await test('Get User Orders', async () => {
    // Get admin user ID first
    const profileResponse = await apiRequest('GET', '/api/users/profile', null, authHeaders);
    const userId = profileResponse.data.user.id;
    
    const response = await apiRequest('GET', `/api/users/${userId}/orders`, null, authHeaders);
    
    if (!response.success || !Array.isArray(response.data.orders)) {
      throw new Error('Failed to get user orders');
    }
  });
}

/**
 * Test system services endpoints
 */
async function testSystemServices() {
  await test('Get System Services', async () => {
    const response = await apiRequest('GET', '/api/systems');
    
    if (!response.success || !Array.isArray(response.data.systems)) {
      throw new Error('Failed to get system services');
    }
  });
  
  await test('Get System Categories', async () => {
    const response = await apiRequest('GET', '/api/systems/categories');
    
    if (!response.success || !Array.isArray(response.data.categories)) {
      throw new Error('Failed to get system categories');
    }
  });
  
  // Test with search and filters
  await test('Search System Services', async () => {
    const response = await apiRequest('GET', '/api/systems?search=system&category=Management');
    
    if (!response.success) {
      throw new Error('Failed to search system services');
    }
  });
}

/**
 * Test technical services endpoints
 */
async function testTechnicalServices() {
  await test('Get Technical Services', async () => {
    const response = await apiRequest('GET', '/api/services/technical');
    
    if (!response.success || !Array.isArray(response.data.services)) {
      throw new Error('Failed to get technical services');
    }
  });
  
  await test('Get Premium Content', async () => {
    const response = await apiRequest('GET', '/api/services/premium');
    
    if (!response.success || !Array.isArray(response.data.premiumContent)) {
      throw new Error('Failed to get premium content');
    }
  });
  
  await test('Get Premium Packages', async () => {
    const response = await apiRequest('GET', '/api/services/packages');
    
    if (!response.success || !Array.isArray(response.data.packages)) {
      throw new Error('Failed to get premium packages');
    }
  });
}

/**
 * Test order endpoints
 */
async function testOrders(accessToken) {
  const authHeaders = { Authorization: `Bearer ${accessToken}` };
  let orderId = '';
  
  await test('Create Order', async () => {
    // First get a system service to order
    const servicesResponse = await apiRequest('GET', '/api/systems');
    
    if (servicesResponse.data.systems.length === 0) {
      throw new Error('No system services available to order');
    }
    
    const service = servicesResponse.data.systems[0];
    
    const orderData = {
      order_type: 'system_service',
      item_id: service.id,
      quantity: 1,
      notes_en: 'Test order'
    };
    
    const response = await apiRequest('POST', '/api/orders', orderData, authHeaders);
    
    if (!response.success || !response.data.order) {
      throw new Error('Failed to create order');
    }
    
    orderId = response.data.order.id;
  });
  
  await test('Get Orders', async () => {
    const response = await apiRequest('GET', '/api/orders', null, authHeaders);
    
    if (!response.success || !Array.isArray(response.data.orders)) {
      throw new Error('Failed to get orders');
    }
  });
  
  if (orderId) {
    await test('Get Order Details', async () => {
      const response = await apiRequest('GET', `/api/orders/${orderId}`, null, authHeaders);
      
      if (!response.success || !response.data.order) {
        throw new Error('Failed to get order details');
      }
    });
  }
}

/**
 * Test admin endpoints
 */
async function testAdminEndpoints(accessToken) {
  const authHeaders = { Authorization: `Bearer ${accessToken}` };
  
  await test('Get Admin Dashboard', async () => {
    const response = await apiRequest('GET', '/api/admin/dashboard', null, authHeaders);
    
    if (!response.success || !response.data.stats) {
      throw new Error('Failed to get admin dashboard');
    }
  });
  
  await test('Get All Users (Admin)', async () => {
    const response = await apiRequest('GET', '/api/admin/users', null, authHeaders);
    
    if (!response.success || !Array.isArray(response.data.users)) {
      throw new Error('Failed to get users list');
    }
  });
  
  await test('Get All Orders (Admin)', async () => {
    const response = await apiRequest('GET', '/api/admin/orders', null, authHeaders);
    
    if (!response.success || !Array.isArray(response.data.orders)) {
      throw new Error('Failed to get orders list');
    }
  });
  
  await test('Get Analytics', async () => {
    const response = await apiRequest('GET', '/api/admin/analytics?period=7d', null, authHeaders);
    
    if (!response.success || !response.data.trends) {
      throw new Error('Failed to get analytics');
    }
  });
}

/**
 * Main testing function
 */
async function runTests() {
  try {
    log('🚀 Starting Khanfashariya API Tests...', 'bright');
    log(`📡 Testing API at: ${BASE_URL}`, 'cyan');
    
    // Test server health
    log('\n📊 Testing Server Health...', 'magenta');
    await testServerHealth();
    
    // Test authentication
    log('\n🔐 Testing Authentication...', 'magenta');
    const { accessToken, refreshToken } = await testAuthentication();
    
    // Test user endpoints
    log('\n👤 Testing User Endpoints...', 'magenta');
    await testUserEndpoints(accessToken);
    
    // Test system services
    log('\n🖥️  Testing System Services...', 'magenta');
    await testSystemServices();
    
    // Test technical services
    log('\n🛠️  Testing Technical Services...', 'magenta');
    await testTechnicalServices();
    
    // Test orders
    log('\n📦 Testing Orders...', 'magenta');
    await testOrders(accessToken);
    
    // Test admin endpoints
    log('\n👑 Testing Admin Endpoints...', 'magenta');
    await testAdminEndpoints(accessToken);
    
    // Test results
    log('\n🎉 API Testing Completed!', 'bright');
    log('📋 Test Results:', 'cyan');
    log(`   ✅ Passed: ${testResults.passed}`, 'green');
    log(`   ❌ Failed: ${testResults.failed}`, 'red');
    log(`   📊 Total: ${testResults.total}`, 'blue');
    log(`   📈 Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`, 'cyan');
    
    if (testResults.failed > 0) {
      log('\n❌ Failed Tests:', 'red');
      testResults.failures.forEach(failure => {
        log(`   • ${failure.name}: ${failure.error}`, 'red');
      });
    }
    
    if (testResults.failed === 0) {
      log('\n🎯 All tests passed! API is working correctly.', 'green');
    } else {
      log('\n⚠️  Some tests failed. Please check the API implementation.', 'yellow');
    }
    
  } catch (error) {
    log(`💥 Testing failed: ${error.message}`, 'red');
    log('🔧 Troubleshooting:', 'yellow');
    log('   1. Make sure the API server is running (npm run dev:server)', 'yellow');
    log('   2. Check if database is set up and migrated', 'yellow');
    log('   3. Verify API server is accessible at the specified host/port', 'yellow');
    
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('Testing failed:', error);
    process.exit(1);
  });
}

module.exports = { runTests, testResults };
