#!/usr/bin/env node

/**
 * سكريبت تلقائي لإعداد TestSprite
 * يقوم بكل شيء تلقائياً ويعطيك البيانات المطلوبة
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 إعداد TestSprite التلقائي');
console.log('============================');

let ngrokUrl = '';

// فحص ngrok
function checkNgrok() {
  return new Promise((resolve) => {
    exec('ngrok version', (error) => {
      if (error) {
        console.log('❌ ngrok غير مثبت');
        console.log('📥 تثبيت ngrok...');
        
        // محاولة التثبيت عبر npm
        exec('npm install -g ngrok', (installError) => {
          if (installError) {
            console.log('❌ فشل التثبيت التلقائي');
            console.log('🔧 يرجى تثبيت ngrok يدوياً:');
            console.log('   npm install -g ngrok');
            console.log('   أو تحميل من: https://ngrok.com/download');
            resolve(false);
          } else {
            console.log('✅ تم تثبيت ngrok بنجاح');
            resolve(true);
          }
        });
      } else {
        console.log('✅ ngrok متاح');
        resolve(true);
      }
    });
  });
}

// فحص الخادم (Backend + Frontend)
function checkServers() {
  return new Promise((resolve) => {
    console.log('🔍 فحص الخوادم...');
    
    // فحص Backend
    exec('curl -s http://localhost:3001/health', (backendError) => {
      // فحص Frontend
      exec('curl -s http://localhost:5173', (frontendError) => {
        
        if (backendError && frontendError) {
          console.log('❌ الخوادم غير تعمل');
          console.log('🚀 تشغيل المشروع الكامل...');
          
          // تشغيل المشروع الكامل
          const fullProcess = spawn('npm', ['run', 'dev:full'], {
            stdio: 'pipe',
            shell: true
          });
          
          console.log('⏳ انتظار تشغيل الخوادم...');
          
          // انتظار تشغيل الخوادم
          setTimeout(() => {
            exec('curl -s http://localhost:3001/health', (retryBackendError) => {
              exec('curl -s http://localhost:5173', (retryFrontendError) => {
                if (retryBackendError || retryFrontendError) {
                  console.log('❌ فشل في تشغيل الخوادم');
                  console.log('🔧 يرجى تشغيل المشروع يدوياً: npm run dev:full');
                  resolve({ backend: false, frontend: false });
                } else {
                  console.log('✅ Backend يعمل على المنفذ 3001');
                  console.log('✅ Frontend يعمل على المنفذ 5173');
                  resolve({ backend: true, frontend: true });
                }
              });
            });
          }, 15000); // انتظار 15 ثانية
          
        } else if (backendError) {
          console.log('❌ Backend غير يعمل');
          console.log('✅ Frontend يعمل على المنفذ 5173');
          resolve({ backend: false, frontend: true });
        } else if (frontendError) {
          console.log('✅ Backend يعمل على المنفذ 3001');
          console.log('❌ Frontend غير يعمل');
          resolve({ backend: true, frontend: false });
        } else {
          console.log('✅ Backend يعمل على المنفذ 3001');
          console.log('✅ Frontend يعمل على المنفذ 5173');
          resolve({ backend: true, frontend: true });
        }
      });
    });
  });
}

// تشغيل ngrok للباك اند والفرونت اند
function startNgrokTunnels() {
  return new Promise((resolve) => {
    console.log('🌐 إنشاء أنفاق ngrok...');
    
    let backendUrl = '';
    let frontendUrl = '';
    let tunnelsReady = 0;
    
    // نفق للباك اند (API)
    console.log('🔧 إنشاء نفق للباك اند (API)...');
    const backendNgrok = spawn('ngrok', ['http', '3001', '--log=stdout'], {
      stdio: 'pipe'
    });
    
    backendNgrok.stdout.on('data', (data) => {
      const output = data.toString();
      const urlMatch = output.match(/https:\/\/[a-z0-9-]+\.ngrok\.io/);
      if (urlMatch && !backendUrl) {
        backendUrl = urlMatch[0];
        console.log('✅ رابط الباك اند:', backendUrl);
        tunnelsReady++;
        if (tunnelsReady === 2) {
          resolve({ backend: backendUrl, frontend: frontendUrl });
        }
      }
    });
    
    // انتظار قليل قبل إنشاء النفق الثاني
    setTimeout(() => {
      // نفق للفرونت اند
      console.log('🔧 إنشاء نفق للفرونت اند...');
      const frontendNgrok = spawn('ngrok', ['http', '5173', '--log=stdout'], {
        stdio: 'pipe'
      });
      
      frontendNgrok.stdout.on('data', (data) => {
        const output = data.toString();
        const urlMatch = output.match(/https:\/\/[a-z0-9-]+\.ngrok\.io/);
        if (urlMatch && !frontendUrl) {
          frontendUrl = urlMatch[0];
          console.log('✅ رابط الفرونت اند:', frontendUrl);
          tunnelsReady++;
          if (tunnelsReady === 2) {
            resolve({ backend: backendUrl, frontend: frontendUrl });
          }
        }
      });
    }, 5000);
    
    // timeout بعد 45 ثانية
    setTimeout(() => {
      if (tunnelsReady < 2) {
        console.log('⏰ انتهت مهلة انتظار ngrok');
        console.log('🔧 جرب تشغيل ngrok يدوياً:');
        console.log('   Terminal 1: ngrok http 3001');
        console.log('   Terminal 2: ngrok http 5173');
        resolve({ backend: backendUrl, frontend: frontendUrl });
      }
    }, 45000);
  });
}

// إنشاء ملف البيانات الشامل
function createTestSpriteData(urls) {
  const data = {
    // بيانات الباك اند API
    backend: {
      apiName: 'Khanfashariya Backend API',
      apiEndpoint: urls.backend,
      authenticationType: 'None - No authentication required',
      extraInfo: `Khanfashariya Metin2 Services Platform - Backend API

🔧 API Endpoints:
- GET /health - Health check
- GET /api/status - API status  
- POST /api/auth/login - User login
- POST /api/auth/register - User registration
- GET /api/users/profile - User profile (requires auth)
- GET /api/systems - System services
- GET /api/services - Technical services
- GET /api/orders - Orders (requires auth)
- GET /api/admin/dashboard - Admin panel (requires auth)

🔑 Test Credentials:
Email: <EMAIL>
Password: admin123

📝 Sample Requests:

Login:
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "admin123"
}

Registration:
POST /api/auth/register
{
  "username": "testuser",
  "email": "<EMAIL>", 
  "password": "password123",
  "confirmPassword": "password123"
}

🗄️ Database: MySQL (localhost:3306)
📊 Features: JWT Auth, Rate Limiting, File Upload, Admin Panel`
    },
    
    // بيانات الفرونت اند
    frontend: {
      websiteName: 'Khanfashariya Frontend',
      websiteUrl: urls.frontend,
      description: `Khanfashariya Metin2 Services Platform - Frontend Application

🌐 Full Stack React Application with:
- User Authentication & Registration
- Service Catalog & Ordering
- Admin Dashboard
- File Upload & Management
- Responsive Design
- Real-time Updates

🔗 Connected to Backend API: ${urls.backend}

📱 Pages to Test:
- / - Homepage
- /login - Login page
- /register - Registration page
- /services - Services catalog
- /profile - User profile (requires login)
- /admin - Admin dashboard (requires admin login)

🧪 Test Scenarios:
1. User Registration Flow
2. Login/Logout Process
3. Service Browsing & Ordering
4. Profile Management
5. Admin Operations
6. Responsive Design Testing
7. Error Handling
8. Performance Testing`
    },
    
    // معلومات شاملة للمشروع
    fullProject: {
      projectName: 'Khanfashariya Complete Platform',
      description: 'Full-stack Metin2 services platform with React frontend and Express.js backend',
      backendUrl: urls.backend,
      frontendUrl: urls.frontend,
      technology: 'React + Express.js + MySQL',
      features: [
        'User Authentication (JWT)',
        'Service Management',
        'Order Processing', 
        'Admin Panel',
        'File Upload',
        'Rate Limiting',
        'Security Headers',
        'Database Integration',
        'Responsive UI',
        'Real-time Updates'
      ]
    }
  };
  
  // حفظ البيانات في ملف
  fs.writeFileSync('testsprite-complete-data.json', JSON.stringify(data, null, 2));
  
  return data;
}

// عرض البيانات الشاملة
function displayData(data) {
  console.log('\n🎯 بيانات TestSprite الشاملة جاهزة!');
  console.log('==========================================');
  
  console.log('\n🔧 للاختبار الباك اند API:');
  console.log('============================');
  console.log('API Name:');
  console.log(`   ${data.backend.apiName}`);
  console.log('');
  console.log('API endpoint / URL:');
  console.log(`   ${data.backend.apiEndpoint}`);
  console.log('');
  console.log('Authentication Type:');
  console.log(`   ${data.backend.authenticationType}`);
  console.log('');
  
  console.log('\n🌐 لاختبار الفرونت اند (الموقع):');
  console.log('=================================');
  console.log('Website Name:');
  console.log(`   ${data.frontend.websiteName}`);
  console.log('');
  console.log('Website URL:');
  console.log(`   ${data.frontend.websiteUrl}`);
  console.log('');
  
  console.log('\n📋 معلومات إضافية للاختبار:');
  console.log('==============================');
  console.log(data.backend.extraInfo);
  console.log('');
  
  console.log('\n🔗 اختبر الروابط في المتصفح:');
  console.log('==============================');
  console.log(`Backend API Health: ${data.backend.apiEndpoint}/health`);
  console.log(`Frontend Website: ${data.frontend.websiteUrl}`);
  console.log('');
  
  console.log('📁 تم حفظ البيانات الكاملة في: testsprite-complete-data.json');
  console.log('');
  
  console.log('🧪 خطوات الاختبار في TestSprite:');
  console.log('==================================');
  console.log('1. أنشئ اختبار جديد للباك اند API');
  console.log('2. أنشئ اختبار منفصل للفرونت اند');
  console.log('3. اختبر التكامل بين الاثنين');
  console.log('4. راقب الأداء والأمان');
  console.log('');
  
  console.log('⚠️  مهم: لا تغلق هذا البرنامج أثناء اختبار TestSprite!');
  console.log('🔄 البرنامج يعمل... اضغط Ctrl+C للإيقاف');
}

// الدالة الرئيسية
async function main() {
  try {
    console.log('🔍 فحص المتطلبات...');
    
    const ngrokReady = await checkNgrok();
    if (!ngrokReady) {
      return;
    }
    
    const serversStatus = await checkServers();
    if (!serversStatus.backend || !serversStatus.frontend) {
      console.log('❌ بعض الخوادم لا تعمل');
      if (!serversStatus.backend) console.log('   - Backend غير متاح');
      if (!serversStatus.frontend) console.log('   - Frontend غير متاح');
      console.log('🔧 يرجى تشغيل المشروع: npm run dev:full');
      return;
    }
    
    const urls = await startNgrokTunnels();
    if (!urls.backend || !urls.frontend) {
      console.log('❌ فشل في إنشاء الأنفاق');
      console.log('🔧 جرب تشغيل ngrok يدوياً:');
      console.log('   Terminal 1: ngrok http 3001');
      console.log('   Terminal 2: ngrok http 5173');
      return;
    }
    
    const data = createTestSpriteData(urls);
    displayData(data);
    
    // إبقاء البرنامج يعمل
    console.log('🔄 البرنامج يعمل... اضغط Ctrl+C للإيقاف');
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 إيقاف البرنامج...');
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  }
}

main();