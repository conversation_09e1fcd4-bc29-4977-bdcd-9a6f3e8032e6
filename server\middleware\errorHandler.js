/**
 * Error Handling Middleware
 * 
 * Provides comprehensive error handling with:
 * - Structured error responses
 * - Error logging and monitoring
 * - Development vs production error details
 * - HTTP status code mapping
 * - Security-aware error messages
 */

const { executeQuery } = require('../config/database');

/**
 * Custom error class for API errors
 */
class APIError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR', details = null) {
    super(message);
    this.name = 'APIError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Log error to database and console
 */
async function logError(error, req = null, additionalInfo = {}) {
  try {
    const errorLog = {
      id: require('../config/database').generateUUID(),
      error_type: error.name || 'Error',
      error_message: error.message,
      error_code: error.code || 'UNKNOWN',
      status_code: error.statusCode || 500,
      stack_trace: error.stack,
      request_url: req?.originalUrl || null,
      request_method: req?.method || null,
      request_ip: req?.ip || req?.connection?.remoteAddress || null,
      user_id: req?.user?.id || null,
      user_agent: req?.get('User-Agent') || null,
      additional_info: JSON.stringify(additionalInfo),
      created_at: new Date()
    };
    
    // Log to console with colors
    console.error('🚨 ERROR OCCURRED:');
    console.error(`   Type: ${errorLog.error_type}`);
    console.error(`   Message: ${errorLog.error_message}`);
    console.error(`   Code: ${errorLog.error_code}`);
    console.error(`   Status: ${errorLog.status_code}`);
    
    if (req) {
      console.error(`   Request: ${errorLog.request_method} ${errorLog.request_url}`);
      console.error(`   IP: ${errorLog.request_ip}`);
      console.error(`   User: ${errorLog.user_id || 'Anonymous'}`);
    }
    
    if (process.env.NODE_ENV === 'development') {
      console.error(`   Stack: ${error.stack}`);
    }
    
    // Log to database (don't await to avoid blocking)
    executeQuery(
      `INSERT INTO activity_logs (id, user_id, action, entity_type, details, ip_address, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        errorLog.id,
        errorLog.user_id,
        'error_occurred',
        'system',
        JSON.stringify({
          error_type: errorLog.error_type,
          error_message: errorLog.error_message,
          error_code: errorLog.error_code,
          status_code: errorLog.status_code,
          request_url: errorLog.request_url,
          request_method: errorLog.request_method,
          additional_info: additionalInfo
        }),
        errorLog.request_ip,
        new Date()
      ]
    ).catch(dbError => {
      console.error('❌ Failed to log error to database:', dbError.message);
    });
    
  } catch (logError) {
    console.error('❌ Error logging failed:', logError.message);
  }
}

/**
 * Format error response based on environment
 */
function formatErrorResponse(error, req) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const response = {
    success: false,
    error: error.message,
    code: error.code || 'INTERNAL_ERROR',
    timestamp: new Date().toISOString()
  };
  
  // Add additional details in development
  if (isDevelopment) {
    response.details = {
      name: error.name,
      statusCode: error.statusCode,
      stack: error.stack,
      request: {
        method: req?.method,
        url: req?.originalUrl,
        ip: req?.ip,
        userAgent: req?.get('User-Agent')
      }
    };
    
    if (error.details) {
      response.errorDetails = error.details;
    }
  }
  
  // Add request ID for tracking
  if (req?.requestId) {
    response.requestId = req.requestId;
  }
  
  return response;
}

/**
 * Main error handling middleware
 */
function errorHandler(error, req, res, next) {
  // If response was already sent, delegate to default Express error handler
  if (res.headersSent) {
    return next(error);
  }
  
  // Log the error
  logError(error, req);
  
  // Determine status code
  let statusCode = 500;
  
  if (error.statusCode) {
    statusCode = error.statusCode;
  } else if (error.name === 'ValidationError') {
    statusCode = 400;
  } else if (error.name === 'UnauthorizedError' || error.name === 'JsonWebTokenError') {
    statusCode = 401;
  } else if (error.name === 'ForbiddenError') {
    statusCode = 403;
  } else if (error.name === 'NotFoundError') {
    statusCode = 404;
  } else if (error.code === 'ER_DUP_ENTRY') {
    statusCode = 409;
  } else if (error.code === 'LIMIT_FILE_SIZE') {
    statusCode = 413;
  }
  
  // Create sanitized error message for production
  let message = error.message;
  
  if (process.env.NODE_ENV === 'production') {
    // Don't expose internal errors in production
    if (statusCode === 500) {
      message = 'Internal server error';
    }
    
    // Sanitize database errors
    if (error.code && error.code.startsWith('ER_')) {
      message = 'Database operation failed';
    }
  }
  
  // Create error response
  const errorResponse = formatErrorResponse({
    ...error,
    message,
    statusCode
  }, req);
  
  // Send error response
  res.status(statusCode).json(errorResponse);
}

/**
 * 404 Not Found handler
 */
function notFoundHandler(req, res, next) {
  const error = new APIError(
    `Route not found: ${req.method} ${req.originalUrl}`,
    404,
    'ROUTE_NOT_FOUND',
    {
      method: req.method,
      url: req.originalUrl,
      availableRoutes: [
        'GET /api/status',
        'POST /api/auth/login',
        'POST /api/auth/register',
        'GET /api/users/profile',
        'GET /api/systems',
        'GET /api/services',
        'GET /api/orders',
        'GET /api/admin/dashboard'
      ]
    }
  );
  
  next(error);
}

/**
 * Async error wrapper for route handlers
 */
function asyncHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * Validation error handler
 */
function validationError(message, details = null) {
  return new APIError(message, 400, 'VALIDATION_ERROR', details);
}

/**
 * Authentication error handler
 */
function authError(message = 'Authentication required') {
  return new APIError(message, 401, 'AUTH_ERROR');
}

/**
 * Authorization error handler
 */
function forbiddenError(message = 'Access denied') {
  return new APIError(message, 403, 'FORBIDDEN_ERROR');
}

/**
 * Not found error handler
 */
function notFoundError(message = 'Resource not found') {
  return new APIError(message, 404, 'NOT_FOUND_ERROR');
}

/**
 * Conflict error handler
 */
function conflictError(message = 'Resource conflict') {
  return new APIError(message, 409, 'CONFLICT_ERROR');
}

/**
 * Rate limit error handler
 */
function rateLimitError(message = 'Too many requests') {
  return new APIError(message, 429, 'RATE_LIMIT_ERROR');
}

/**
 * Database error handler
 */
function databaseError(message = 'Database operation failed', originalError = null) {
  const details = originalError ? {
    code: originalError.code,
    errno: originalError.errno,
    sqlState: originalError.sqlState
  } : null;
  
  return new APIError(message, 500, 'DATABASE_ERROR', details);
}

/**
 * File upload error handler
 */
function fileUploadError(message = 'File upload failed') {
  return new APIError(message, 400, 'FILE_UPLOAD_ERROR');
}

/**
 * External service error handler
 */
function externalServiceError(message = 'External service unavailable') {
  return new APIError(message, 503, 'EXTERNAL_SERVICE_ERROR');
}

/**
 * Handle uncaught exceptions
 */
process.on('uncaughtException', (error) => {
  console.error('💥 UNCAUGHT EXCEPTION! Shutting down...');
  console.error('Error:', error.message);
  console.error('Stack:', error.stack);
  
  logError(error, null, { type: 'uncaughtException' });
  
  process.exit(1);
});

/**
 * Handle unhandled promise rejections
 */
process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 UNHANDLED REJECTION! Shutting down...');
  console.error('Reason:', reason);
  console.error('Promise:', promise);
  
  const error = new Error(`Unhandled Rejection: ${reason}`);
  logError(error, null, { type: 'unhandledRejection', reason, promise });
  
  process.exit(1);
});

module.exports = {
  APIError,
  errorHandler,
  notFoundHandler,
  asyncHandler,
  logError,
  formatErrorResponse,
  
  // Error creators
  validationError,
  authError,
  forbiddenError,
  notFoundError,
  conflictError,
  rateLimitError,
  databaseError,
  fileUploadError,
  externalServiceError
};
