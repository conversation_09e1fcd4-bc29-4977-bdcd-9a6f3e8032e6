import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotification } from '../../hooks/useNotification';
import {
  getAllUsers,
  getAllOrders,
  getUserOrders,
  updateUserRole,
} from '../../lib/apiServices';
import {
  User,
  Order,
  Subscription,
  getOrdersByUser,
  getSubscriptionsByUser,
} from '../../lib/database';
import { 
  Users,
  Search,
  Filter,
  Download,
  Upload,
  Edit,
  Trash2,
  Eye,
  Shield,
  Star,
  Mail,
  Phone,
  Calendar,
  Package,
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  UserPlus,
  Settings,
  ArrowLeft,
  MoreVertical,
  MessageSquare,
  FileText,
  Activity
} from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Modal from '../ui/Modal';
import Card from '../ui/Card';
import GoldenFilterGrid from '../ui/GoldenFilterGrid';
import BackButton from '../ui/BackButton';
import { useButtonActions } from '../../utils/buttonActions';
import GoldenButton from '../ui/GoldenButton';
import { USER_MANAGEMENT_FILTERS } from '../../constants/filterOptions';

interface EnhancedUserManagerProps {
  onBack?: () => void;
}

interface UserStats {
  total: number;
  admins: number;
  activeUsers: number;
  newThisMonth: number;
  totalOrders: number;
  totalRevenue: number;
}

/**
 * Enhanced User Management System with comprehensive features
 */
const EnhancedUserManager: React.FC<EnhancedUserManagerProps> = ({ onBack }) => {
  const { t, language } = useTranslation();
  const { showNotification } = useNotification();
  const buttonActions = useButtonActions();
  
  const [users, setUsers] = useState<User[]>([]);
  const [userOrders, setUserOrders] = useState<Order[]>([]);
  const [userSubscriptions, setUserSubscriptions] = useState<Subscription[]>([]);
  const [selectedUserOrders, setSelectedUserOrders] = useState<Order[]>([]);
  const [selectedUserSubscriptions, setSelectedUserSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Unified filter state
  const [filterValues, setFilterValues] = useState<Record<string, string>>({
    role: 'all',
    status: 'all',
    dateRange: 'all',
    sortBy: 'name'
  });

  // Message form state
  const [messageData, setMessageData] = useState({
    subject: '',
    message: '',
    priority: 'normal' as 'low' | 'normal' | 'high'
  });

  // Edit user form state
  const [editUserData, setEditUserData] = useState({
    username: '',
    email: '',
    full_name: '',
    role: 'user' as 'admin' | 'user'
  });
  const [userStats, setUserStats] = useState<UserStats>({
    total: 0,
    admins: 0,
    activeUsers: 0,
    newThisMonth: 0,
    totalOrders: 0,
    totalRevenue: 0
  });

  // New user form state
  const [newUserData, setNewUserData] = useState({
    username: '',
    email: '',
    password: '',
    role: 'user' as 'admin' | 'user',
    firstName: '',
    lastName: '',
    phone: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    calculateStats();
  }, [users, userOrders]);

  const loadData = async () => {
    setLoading(true);
    try {
      const usersResult = await getAllUsers();
      const ordersResult = await getAllOrders();

      if (usersResult.data) {
        setUsers(usersResult.data);
      } else if (usersResult.error) {
        console.error('Error loading users:', usersResult.error);
        showNotification({
          type: 'error',
          message: usersResult.error.message || t('notifications.loadError')
        });
      }

      if (ordersResult.data) {
        setUserOrders(ordersResult.data);
      } else if (ordersResult.error) {
        console.error('Error loading orders:', ordersResult.error);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      showNotification({
        type: 'error',
        message: t('notifications.loadError')
      });
    }
    setLoading(false);
  };

  // Load specific user data when a user is selected
  const loadUserDetails = async (userId: string) => {
    try {
      const [ordersResult, subscriptionsResult] = await Promise.all([
        getOrdersByUser(userId),
        getSubscriptionsByUser(userId)
      ]);

      if (ordersResult.data) setSelectedUserOrders(ordersResult.data);
      if (subscriptionsResult.data) setSelectedUserSubscriptions(subscriptionsResult.data);
    } catch (error) {
      console.error('Error loading user details:', error);
    }
  };

  const calculateStats = () => {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Ensure users is an array
    const usersArray = Array.isArray(users) ? users : [];
    // Ensure userOrders is an array
    const ordersArray = Array.isArray(userOrders) ? userOrders : [];

    const newUsersThisMonth = usersArray.filter(user => {
      try {
        const userDate = new Date(user.created_at || '');
        return userDate >= startOfMonth;
      } catch {
        return false;
      }
    }).length;

    const totalRevenue = ordersArray.filter(order => order.status === 'completed').reduce((sum, order) => sum + (parseFloat(order.price) || 0), 0);

    setUserStats({
      total: usersArray.length,
      admins: usersArray.filter(u => u.role === 'admin').length,
      activeUsers: usersArray.filter(u => u.role === 'user').length,
      newThisMonth: newUsersThisMonth,
      totalOrders: ordersArray.length,
      totalRevenue: totalRevenue
    });
  };

  const handleRoleUpdate = async (userId: string, newRole: 'admin' | 'user') => {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    const isPromoting = newRole === 'admin';
    const isDemoting = newRole === 'user' && user.role === 'admin';

    // Check if this is the last admin and prevent demotion
    if (isDemoting) {
      const adminCount = users.filter(u => u.role === 'admin').length;
      if (adminCount <= 1) {
        showNotification({
          type: 'error',
          message: language === 'ar'
            ? 'لا يمكن إعادة المدير الوحيد إلى مستخدم عادي. يجب وجود مدير واحد على الأقل في النظام.'
            : 'Cannot demote the last administrator. At least one admin must remain in the system.'
        });
        return;
      }
    }

    const confirmMessage = isPromoting
      ? (language === 'ar' ? `هل أنت متأكد من ترقية "${user.username}" إلى مدير؟` : `Are you sure you want to promote "${user.username}" to admin?`)
      : (language === 'ar' ? `هل أنت متأكد من إعادة "${user.username}" إلى مستخدم عادي؟` : `Are you sure you want to demote "${user.username}" to regular user?`);

    showNotification({
      type: 'confirm',
      message: confirmMessage,
      onConfirm: async () => {
        try {
          await updateUserRole(userId, newRole);
          await loadData();
          showNotification({
            type: 'success',
            message: language === 'ar'
              ? (isPromoting ? 'تم ترقية المستخدم بنجاح' : 'تم إعادة المستخدم إلى مستخدم عادي بنجاح')
              : (isPromoting ? 'User promoted successfully' : 'User demoted successfully')
          });
        } catch (error) {
          console.error('Error updating user role:', error);
          showNotification({
            type: 'error',
            message: language === 'ar' ? 'فشل في تحديث دور المستخدم' : 'Failed to update user role'
          });
        }
      }
    });
  };

  const handleCreateUser = async () => {
    try {
      // Here you would implement user creation logic
      // For now, we'll just show a success message
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم إنشاء المستخدم بنجاح' : 'User created successfully'
      });
      setShowCreateModal(false);
      setNewUserData({
        username: '',
        email: '',
        password: '',
        role: 'user',
        firstName: '',
        lastName: '',
        phone: ''
      });
    } catch (error) {
      console.error('Error creating user:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في إنشاء المستخدم' : 'Failed to create user'
      });
    }
  };

  const getUserOrders = (userId: string) => {
    const ordersArray = Array.isArray(userOrders) ? userOrders : [];
    return ordersArray.filter(order => order.user_id === userId);
  };

  const getUserTotalSpent = (userId: string) => {
    return getUserOrders(userId).reduce((sum, order) => sum + order.price, 0);
  };

  // Handle user selection and load their detailed data
  const handleUserSelect = async (user: User) => {
    setSelectedUser(user);
    await loadUserDetails(user.id);
    setShowUserModal(true);
  };

  const handleSendMessage = async () => {
    if (!selectedUser || !messageData.message.trim()) return;

    try {
      // Here you would implement message sending logic
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم إرسال الرسالة بنجاح' : 'Message sent successfully'
      });
      setShowMessageModal(false);
      setMessageData({
        subject: '',
        message: '',
        priority: 'normal'
      });
    } catch (error) {
      console.error('Error sending message:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في إرسال الرسالة' : 'Failed to send message'
      });
    }
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setEditUserData({
      username: user.username,
      email: user.email,
      full_name: user.full_name,
      role: user.role
    });
    setShowEditModal(true);
  };

  const handleUpdateUser = async () => {
    if (!selectedUser) return;

    try {
      // Update user role if it has changed
      if (editUserData.role !== selectedUser.role) {
        await updateUserRole(selectedUser.id, editUserData.role);
      }

      // Here you would implement other user data update logic
      // For now, we'll just update the role and show success
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم تحديث المستخدم بنجاح' : 'User updated successfully'
      });
      setShowEditModal(false);
      await loadData();
    } catch (error) {
      console.error('Error updating user:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في تحديث المستخدم' : 'Failed to update user'
      });
    }
  };

  // Handle filter changes
  const handleFilterChange = (key: string, value: string) => {
    setFilterValues(prev => ({ ...prev, [key]: value }));
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = searchTerm === '' ||
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.full_name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = filterValues.role === 'all' || user.role === filterValues.role;
    const matchesStatus = filterValues.status === 'all'; // Add status logic if needed

    return matchesSearch && matchesRole && matchesStatus;
  });

  // Sort filtered users
  const sortedUsers = [...filteredUsers].sort((a, b) => {
    switch (filterValues.sortBy) {
      case 'email':
        return a.email.localeCompare(b.email);
      case 'role':
        return a.role.localeCompare(b.role);
      case 'created':
        return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
      case 'lastLogin':
        return new Date(b.last_login || '').getTime() - new Date(a.last_login || '').getTime();
      case 'name':
      default:
        return a.username.localeCompare(b.username);
    }
  });

  const getRoleIcon = (role: string) => {
    return role === 'admin' ? <Shield className="w-4 h-4 text-purple-400" /> : <Users className="w-4 h-4 text-blue-400" />;
  };

  const getRoleColor = (role: string) => {
    return role === 'admin'
      ? 'bg-purple-500/20 text-purple-400 border-purple-500/30'
      : 'bg-blue-500/20 text-blue-400 border-blue-500/30';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-secondary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Enhanced Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-6 space-y-4 lg:space-y-0">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          {onBack && (
            <BackButton onClick={onBack} variant="back" size="md" />
          )}
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('admin.dashboard.users')}
            </h1>
            <p className="text-gray-400 text-sm mt-1">
              {language === 'ar' ? 'إدارة شاملة للمستخدمين والأعضاء' : 'Comprehensive user and member management'}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Button variant="outline" onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')} size="sm">
            <Users className="w-4 h-4 mr-2" />
            {viewMode === 'grid' ? 'List' : 'Grid'}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            {language === 'ar' ? 'تصدير' : 'Export'}
          </Button>
          <Button variant="primary" onClick={() => setShowCreateModal(true)}>
            <UserPlus className="w-4 h-4 mr-2" />
            {language === 'ar' ? 'إضافة مستخدم' : 'Add User'}
          </Button>
        </div>
      </div>

      {/* Enhanced Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
        <Card className="bg-gradient-to-br from-blue-500/10 to-blue-600/5 border-blue-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-300 text-sm font-medium">{language === 'ar' ? 'إجمالي المستخدمين' : 'Total Users'}</p>
                <p className="text-2xl font-bold text-white">{userStats.total}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'جميع الحسابات' : 'All Accounts'}
                </p>
              </div>
              <Users className="w-8 h-8 text-blue-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-purple-500/10 to-purple-600/5 border-purple-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-300 text-sm font-medium">{language === 'ar' ? 'المديرين' : 'Administrators'}</p>
                <p className="text-2xl font-bold text-white">{userStats.admins}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'حسابات إدارية' : 'Admin Accounts'}
                </p>
              </div>
              <Shield className="w-8 h-8 text-purple-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/10 to-green-600/5 border-green-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-300 text-sm font-medium">{language === 'ar' ? 'المستخدمين النشطين' : 'Active Users'}</p>
                <p className="text-2xl font-bold text-white">{userStats.activeUsers}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'حسابات عادية' : 'Regular Accounts'}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-purple-500/10 to-purple-600/5 border-purple-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-300 text-sm font-medium">{language === 'ar' ? 'جدد هذا الشهر' : 'New This Month'}</p>
                <p className="text-2xl font-bold text-white">{userStats.newThisMonth}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'انضموا حديثاً' : 'Recently Joined'}
                </p>
              </div>
              <UserPlus className="w-8 h-8 text-purple-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-orange-500/10 to-orange-600/5 border-orange-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-300 text-sm font-medium">{language === 'ar' ? 'إجمالي الطلبات' : 'Total Orders'}</p>
                <p className="text-2xl font-bold text-white">{userStats.totalOrders}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'من جميع المستخدمين' : 'From All Users'}
                </p>
              </div>
              <Package className="w-8 h-8 text-orange-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-accent/10 to-accent/5 border-accent/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-accent text-sm font-medium">{language === 'ar' ? 'إجمالي الإيرادات' : 'Total Revenue'}</p>
                <p className="text-2xl font-bold text-white">${userStats.totalRevenue}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'من المبيعات' : 'From Sales'}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-accent" />
            </div>
          </Card.Body>
        </Card>
      </div>

      {/* Enhanced Filters and Search - Repositioned Below Statistics */}
      <div className="mb-6">
        <GoldenFilterGrid
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          searchPlaceholder="Search users (name, email, full name)..."
          searchPlaceholderAr="البحث في المستخدمين (الاسم، البريد الإلكتروني، الاسم الكامل)..."
          filters={USER_MANAGEMENT_FILTERS}
          filterValues={filterValues}
          onFilterChange={handleFilterChange}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          resultCount={sortedUsers.length}
          onExport={() => buttonActions.exportData('users', sortedUsers)}
          onImport={() => buttonActions.importData('users')}
          onAdvancedSettings={() => buttonActions.openAdvancedSettings('user-management')}
          compact={true}
          position="horizontal"
          className="enhanced-user-management-filter"
        />
      </div>

      {/* Users Grid/List */}
      {sortedUsers.length === 0 ? (
        <Card>
          <Card.Body>
            <div className="text-center py-12">
              <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                {language === 'ar' ? 'لا توجد مستخدمين' : 'No Users Found'}
              </h3>
              <p className="text-gray-400 mb-6">
                {language === 'ar' ? 'ابدأ بإضافة مستخدم جديد' : 'Start by adding a new user'}
              </p>
              <Button variant="primary" onClick={() => setShowCreateModal(true)}>
                <UserPlus className="w-4 h-4 mr-2" />
                {language === 'ar' ? 'إضافة مستخدم' : 'Add User'}
              </Button>
            </div>
          </Card.Body>
        </Card>
      ) : (
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
          {sortedUsers.map((user) => {
            const userOrders = getUserOrders(user.id);
            const totalSpent = getUserTotalSpent(user.id);

            return (
              <Card key={user.id} className="hover:border-secondary/50 transition-all duration-300">
                <Card.Body>
                  {viewMode === 'grid' ? (
                    // Grid View
                    <div className="space-y-4">
                      {/* User Avatar and Role */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          <div className="w-12 h-12 bg-gradient-to-br from-secondary to-accent rounded-full flex items-center justify-center">
                            <span className="text-white font-bold text-lg">
                              {user.username.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-white">{user.username}</h3>
                            <p className="text-gray-400 text-sm">{user.email}</p>
                          </div>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs border ${getRoleColor(user.role)} flex items-center`}>
                          {getRoleIcon(user.role)}
                          <span className="ml-1">
                            {user.role === 'admin' ? (language === 'ar' ? 'مدير' : 'Admin') : (language === 'ar' ? 'مستخدم' : 'User')}
                          </span>
                        </span>
                      </div>

                      {/* User Stats */}
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 bg-accent/10 rounded-lg">
                          <p className="text-accent font-bold text-lg">{userOrders.length}</p>
                          <p className="text-gray-400 text-xs">{language === 'ar' ? 'الطلبات' : 'Orders'}</p>
                        </div>
                        <div className="text-center p-3 bg-secondary/10 rounded-lg">
                          <p className="text-secondary font-bold text-lg">${totalSpent}</p>
                          <p className="text-gray-400 text-xs">{language === 'ar' ? 'المبلغ المنفق' : 'Total Spent'}</p>
                        </div>
                      </div>

                      {/* Recent Activity */}
                      <div>
                        <h4 className="text-sm font-medium text-white mb-2">
                          {language === 'ar' ? 'النشاط الأخير' : 'Recent Activity'}
                        </h4>
                        {userOrders.slice(0, 2).map((order, index) => (
                          <div key={index} className="flex items-center justify-between py-1">
                            <span className="text-gray-300 text-sm truncate">{order.service_name}</span>
                            <span className="text-secondary text-sm">${order.price}</span>
                          </div>
                        ))}
                        {userOrders.length === 0 && (
                          <p className="text-gray-400 text-sm">{language === 'ar' ? 'لا توجد طلبات' : 'No orders yet'}</p>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="space-y-2">
                        <div className="flex space-x-2 rtl:space-x-reverse">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleUserSelect(user)}
                            className="flex-1"
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            {language === 'ar' ? 'عرض' : 'View'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedUser(user);
                              setShowEditModal(true);
                            }}
                            className="btn-icon-fix card-action-btn"
                          >
                            <Edit className="w-4 h-4 enhanced-icon" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // Add more options menu functionality here
                              showNotification({
                                type: 'info',
                                message: language === 'ar' ? 'المزيد من الخيارات قريباً' : 'More options coming soon'
                              });
                            }}
                          >
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </div>
                        <div className="flex space-x-2 rtl:space-x-reverse">
                          <Button
                            variant="secondary"
                            size="sm"
                            className="flex-1 text-xs"
                            onClick={() => {
                              // Add messaging functionality
                              showNotification({
                                type: 'info',
                                message: language === 'ar' ? 'ميزة الرسائل قريباً' : 'Messaging feature coming soon'
                              });
                            }}
                          >
                            <MessageSquare className="w-3 h-3 mr-1" />
                            {language === 'ar' ? 'رسالة' : 'Message'}
                          </Button>
                          {user.role !== 'admin' ? (
                            <Button
                              variant="secondary"
                              size="sm"
                              className="flex-1 text-xs bg-green-500/10 border-green-500/30 text-green-400 hover:bg-green-500/20"
                              onClick={() => handleRoleUpdate(user.id, 'admin')}
                            >
                              <Shield className="w-3 h-3 mr-1" />
                              {language === 'ar' ? 'ترقية' : 'Promote'}
                            </Button>
                          ) : (
                            <Button
                              variant="secondary"
                              size="sm"
                              className="flex-1 text-xs bg-orange-500/10 border-orange-500/30 text-orange-400 hover:bg-orange-500/20"
                              onClick={() => handleRoleUpdate(user.id, 'user')}
                            >
                              <Users className="w-3 h-3 mr-1" />
                              {language === 'ar' ? 'إعادة لمستخدم' : 'Demote'}
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ) : (
                    // List View
                    <div className="flex items-center space-x-4 rtl:space-x-reverse">
                      {/* User Avatar */}
                      <div className="w-12 h-12 bg-gradient-to-br from-secondary to-accent rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-white font-bold text-lg">
                          {user.username.charAt(0).toUpperCase()}
                        </span>
                      </div>

                      {/* User Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="text-lg font-semibold text-white truncate">{user.username}</h3>
                          <span className={`px-2 py-1 rounded-full text-xs border ${getRoleColor(user.role)} flex items-center`}>
                            {getRoleIcon(user.role)}
                            <span className="ml-1">
                              {user.role === 'admin' ? (language === 'ar' ? 'مدير' : 'Admin') : (language === 'ar' ? 'مستخدم' : 'User')}
                            </span>
                          </span>
                        </div>

                        <p className="text-gray-300 text-sm mb-2 truncate">{user.email}</p>

                        <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm">
                          <span className="text-accent">{userOrders.length} {language === 'ar' ? 'طلبات' : 'orders'}</span>
                          <span className="text-secondary">${totalSpent} {language === 'ar' ? 'منفق' : 'spent'}</span>
                          <span className="text-gray-400">
                            {language === 'ar' ? 'انضم' : 'Joined'} {new Date(user.created_at || '').toLocaleDateString()}
                          </span>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex space-x-2 rtl:space-x-reverse flex-shrink-0">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleUserSelect(user)}
                          className="btn-icon-fix card-action-btn"
                        >
                          <Eye className="w-4 h-4 enhanced-icon" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedUser(user);
                            setShowEditModal(true);
                          }}
                          className="btn-icon-fix card-action-btn"
                        >
                          <Edit className="w-4 h-4 enhanced-icon" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            showNotification({
                              type: 'info',
                              message: language === 'ar' ? 'ميزة الرسائل قريباً' : 'Messaging feature coming soon'
                            });
                          }}
                          className="btn-icon-fix card-action-btn"
                        >
                          <MessageSquare className="w-4 h-4 enhanced-icon" />
                        </Button>
                      </div>
                    </div>
                  )}
                </Card.Body>
              </Card>
            );
          })}
        </div>
      )}

      {/* Customer File Modal - Comprehensive 360-degree view */}
      {showUserModal && selectedUser && (
        <Modal
          isOpen={showUserModal}
          onClose={() => setShowUserModal(false)}
          title={`${language === 'ar' ? 'ملف العميل الشامل' : 'Comprehensive Customer File'}: ${selectedUser.username}`}
          size="xl"
        >
          <Modal.Body className="max-h-[70vh] overflow-y-auto">
            <div className="space-y-6">
              {/* Customer Profile Header */}
              <div className="bg-gradient-to-r from-secondary/20 to-accent/20 p-6 rounded-lg border border-secondary/30">
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <div className="w-20 h-20 bg-gradient-to-br from-secondary to-accent rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-3xl">
                      {selectedUser.username.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-white">{selectedUser.username}</h3>
                    <p className="text-gray-300 text-lg">{selectedUser.email}</p>
                    <div className="flex items-center space-x-4 rtl:space-x-reverse mt-2">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm border ${getRoleColor(selectedUser.role)}`}>
                        {getRoleIcon(selectedUser.role)}
                        <span className="ml-2 rtl:mr-2 rtl:ml-0">
                          {selectedUser.role === 'admin' ? (language === 'ar' ? 'مدير' : 'Administrator') : (language === 'ar' ? 'مستخدم' : 'User')}
                        </span>
                      </span>
                      <span className="text-gray-400 text-sm">
                        {language === 'ar' ? 'انضم في' : 'Joined'} {new Date(selectedUser.created_at || '').toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Customer Statistics Dashboard */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-blue-500/20 p-4 rounded-lg text-center border border-blue-500/30">
                  <Package className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                  <p className="text-blue-400 font-bold text-2xl">{selectedUserOrders.length}</p>
                  <p className="text-gray-400 text-sm">{language === 'ar' ? 'إجمالي الطلبات' : 'Total Orders'}</p>
                </div>
                <div className="bg-green-500/20 p-4 rounded-lg text-center border border-green-500/30">
                  <DollarSign className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <p className="text-green-400 font-bold text-2xl">${selectedUserOrders.reduce((sum, order) => sum + order.price, 0)}</p>
                  <p className="text-gray-400 text-sm">{language === 'ar' ? 'إجمالي الإنفاق' : 'Total Spent'}</p>
                </div>
                <div className="bg-purple-500/20 p-4 rounded-lg text-center border border-purple-500/30">
                  <Calendar className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                  <p className="text-purple-400 font-bold text-2xl">{Array.isArray(selectedUserSubscriptions) ? selectedUserSubscriptions.filter(sub => sub.status === 'active').length : 0}</p>
                  <p className="text-gray-400 text-sm">{language === 'ar' ? 'اشتراكات نشطة' : 'Active Subscriptions'}</p>
                </div>
                <div className="bg-orange-500/20 p-4 rounded-lg text-center border border-orange-500/30">
                  <Clock className="w-8 h-8 text-orange-400 mx-auto mb-2" />
                  <p className="text-orange-400 font-bold text-2xl">
                    {Array.isArray(selectedUserOrders) ? selectedUserOrders.filter(order => ['pending', 'in_progress', 'confirmed'].includes(order.status)).length : 0}
                  </p>
                  <p className="text-gray-400 text-sm">{language === 'ar' ? 'طلبات نشطة' : 'Active Orders'}</p>
                </div>
              </div>

              {/* Detailed Orders Section */}
              <div className="bg-background/50 p-6 rounded-lg border border-gray-700">
                <h4 className="text-xl font-semibold text-white mb-4 flex items-center">
                  <Package className="w-6 h-6 mr-3 text-secondary" />
                  {language === 'ar' ? 'سجل الطلبات التفصيلي' : 'Detailed Order History'}
                </h4>
                {selectedUserOrders.length > 0 ? (
                  <div className="space-y-4 max-h-80 overflow-y-auto">
                    {selectedUserOrders.map((order) => (
                      <div key={order.id} className="bg-primary/30 p-4 rounded-lg border border-gray-600">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <h5 className="text-white font-semibold text-lg">{order.service_name}</h5>
                            <p className="text-gray-400 text-sm mb-2">
                              {order.type === 'premium_custom'
                                ? (language === 'ar' ? 'نسخة مميزة مخصصة' : 'Custom Premium Edition')
                                : order.type === 'custom_request'
                                ? (language === 'ar' ? 'طلب مخصص' : 'Custom Request')
                                : (language === 'ar' ? 'طلب عادي' : 'Standard Order')
                              }
                            </p>
                            <p className="text-gray-500 text-xs">
                              {language === 'ar' ? 'تاريخ الطلب:' : 'Order Date:'} {new Date(order.purchase_date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-secondary font-bold text-xl">${order.price}</p>
                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm border ${
                              order.status === 'completed' ? 'bg-green-500/20 text-green-400 border-green-500/30' :
                              order.status === 'in_progress' ? 'bg-blue-500/20 text-blue-400 border-blue-500/30' :
                              order.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30' :
                              'bg-red-500/20 text-red-400 border-red-500/30'
                            }`}>
                              {language === 'ar' ?
                                (order.status === 'completed' ? 'مكتمل' :
                                 order.status === 'in_progress' ? 'قيد التنفيذ' :
                                 order.status === 'pending' ? 'في الانتظار' : 'ملغي') :
                                order.status.charAt(0).toUpperCase() + order.status.slice(1)
                              }
                            </span>
                          </div>
                        </div>

                        {order.details && (
                          <div className="mt-3 pt-3 border-t border-gray-600">
                            <p className="text-gray-400 text-sm mb-2">
                              {language === 'ar' ? 'تفاصيل الطلب:' : 'Order Details:'}
                            </p>
                            <div className="bg-primary/50 p-3 rounded text-xs text-gray-300 max-h-32 overflow-y-auto">
                              <pre className="whitespace-pre-wrap">
                                {JSON.stringify(JSON.parse(order.details), null, 2)}
                              </pre>
                            </div>
                          </div>
                        )}

                        {order.notes && (
                          <div className="mt-3 pt-3 border-t border-gray-600">
                            <p className="text-gray-400 text-sm mb-1">
                              {language === 'ar' ? 'ملاحظات:' : 'Notes:'}
                            </p>
                            <p className="text-gray-300 text-sm">{order.notes}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Package className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                    <p className="text-gray-400">
                      {language === 'ar' ? 'لا توجد طلبات لهذا العميل' : 'No orders found for this customer'}
                    </p>
                  </div>
                )}
              </div>

              {/* Active Subscriptions Section */}
              <div className="bg-background/50 p-6 rounded-lg border border-gray-700">
                <h4 className="text-xl font-semibold text-white mb-4 flex items-center">
                  <Calendar className="w-6 h-6 mr-3 text-purple-400" />
                  {language === 'ar' ? 'الاشتراكات النشطة' : 'Active Subscriptions'}
                </h4>
                {selectedUserSubscriptions.length > 0 ? (
                  <div className="space-y-4">
                    {selectedUserSubscriptions.map((subscription) => (
                      <div key={subscription.id} className="bg-primary/30 p-4 rounded-lg border border-gray-600">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3 rtl:space-x-reverse">
                            <div className={`w-3 h-3 rounded-full ${
                              subscription.status === 'active' ? 'bg-green-400' : 'bg-gray-400'
                            }`}></div>
                            <div>
                              <h5 className="text-white font-semibold">
                                {language === 'ar' ? 'خدمة اشتراك' : 'Subscription Service'}
                              </h5>
                              <p className="text-gray-400 text-sm">
                                {subscription.billingCycle === 'monthly'
                                  ? (language === 'ar' ? 'اشتراك شهري' : 'Monthly Subscription')
                                  : (language === 'ar' ? 'اشتراك سنوي' : 'Yearly Subscription')
                                }
                              </p>
                              <p className="text-gray-500 text-xs">
                                {language === 'ar' ? 'التجديد التالي:' : 'Next billing:'} {' '}
                                {new Date(subscription.nextBillingDate).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-purple-400 font-bold text-lg">${subscription.price}</p>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                              subscription.status === 'active'
                                ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                                : 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
                            }`}>
                              {subscription.status === 'active'
                                ? (language === 'ar' ? 'نشط' : 'Active')
                                : (language === 'ar' ? 'معلق' : 'Paused')
                              }
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Calendar className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                    <p className="text-gray-400">
                      {language === 'ar' ? 'لا توجد اشتراكات نشطة' : 'No active subscriptions'}
                    </p>
                  </div>
                )}
              </div>

              {/* User Actions */}
              <div className="border-t border-accent/20 pt-4">
                <h4 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'إجراءات المستخدم' : 'User Actions'}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => {
                      setMessageData({
                        subject: `Message to ${selectedUser.username}`,
                        message: '',
                        priority: 'normal'
                      });
                      setShowMessageModal(true);
                    }}
                  >
                    <MessageSquare className="w-4 h-4 mr-2" />
                    {language === 'ar' ? 'إرسال رسالة' : 'Send Message'}
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Mail className="w-4 h-4 mr-2" />
                    {language === 'ar' ? 'إرسال بريد إلكتروني' : 'Send Email'}
                  </Button>
                  {selectedUser.role !== 'admin' ? (
                    <Button
                      variant="secondary"
                      className="w-full bg-green-500/10 border-green-500/30 text-green-400 hover:bg-green-500/20"
                      onClick={() => handleRoleUpdate(selectedUser.id, 'admin')}
                    >
                      <Shield className="w-4 h-4 mr-2" />
                      {language === 'ar' ? 'ترقية إلى مدير' : 'Promote to Admin'}
                    </Button>
                  ) : (
                    <Button
                      variant="secondary"
                      className="w-full bg-orange-500/10 border-orange-500/30 text-orange-400 hover:bg-orange-500/20"
                      onClick={() => handleRoleUpdate(selectedUser.id, 'user')}
                    >
                      <Users className="w-4 h-4 mr-2" />
                      {language === 'ar' ? 'إعادة إلى مستخدم عادي' : 'Demote to User'}
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => handleEditUser(selectedUser)}
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    {language === 'ar' ? 'تعديل المعلومات' : 'Edit Information'}
                  </Button>
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowUserModal(false)}>
              {language === 'ar' ? 'إغلاق' : 'Close'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}

      {/* Create User Modal */}
      {showCreateModal && (
        <Modal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title={language === 'ar' ? 'إضافة مستخدم جديد' : 'Add New User'}
          size="lg"
        >
          <Modal.Body className="modal-text-fix">
            <div className="space-y-6">
              {/* Basic Information */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label={language === 'ar' ? 'اسم المستخدم' : 'Username'}
                    value={newUserData.username}
                    onChange={(e) => setNewUserData(prev => ({ ...prev, username: e.target.value }))}
                    required
                  />
                  <Input
                    label={language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                    type="email"
                    value={newUserData.email}
                    onChange={(e) => setNewUserData(prev => ({ ...prev, email: e.target.value }))}
                    required
                  />
                  <Input
                    label={language === 'ar' ? 'كلمة المرور' : 'Password'}
                    type="password"
                    value={newUserData.password}
                    onChange={(e) => setNewUserData(prev => ({ ...prev, password: e.target.value }))}
                    required
                  />
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {language === 'ar' ? 'الدور' : 'Role'}
                    </label>
                    <select
                      value={newUserData.role}
                      onChange={(e) => setNewUserData(prev => ({ ...prev, role: e.target.value as 'admin' | 'user' }))}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                    >
                      <option value="user">{language === 'ar' ? 'مستخدم' : 'User'}</option>
                      <option value="admin">{language === 'ar' ? 'مدير' : 'Administrator'}</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'معلومات إضافية' : 'Additional Information'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label={language === 'ar' ? 'الاسم الأول' : 'First Name'}
                    value={newUserData.firstName}
                    onChange={(e) => setNewUserData(prev => ({ ...prev, firstName: e.target.value }))}
                  />
                  <Input
                    label={language === 'ar' ? 'الاسم الأخير' : 'Last Name'}
                    value={newUserData.lastName}
                    onChange={(e) => setNewUserData(prev => ({ ...prev, lastName: e.target.value }))}
                  />
                  <Input
                    label={language === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
                    value={newUserData.phone}
                    onChange={(e) => setNewUserData(prev => ({ ...prev, phone: e.target.value }))}
                    leftIcon={<Phone />}
                  />
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button variant="primary" onClick={handleCreateUser}>
              <UserPlus className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'إنشاء المستخدم' : 'Create User'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}

      {/* Edit User Modal */}
      {showEditModal && selectedUser && (
        <Modal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          title={`${language === 'ar' ? 'تعديل المستخدم' : 'Edit User'} - ${selectedUser.username}`}
          size="lg"
        >
          <Modal.Body>
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label={language === 'ar' ? 'اسم المستخدم' : 'Username'}
                  value={editUserData.username}
                  onChange={(e) => setEditUserData(prev => ({ ...prev, username: e.target.value }))}
                  required
                />
                <Input
                  label={language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                  type="email"
                  value={editUserData.email}
                  onChange={(e) => setEditUserData(prev => ({ ...prev, email: e.target.value }))}
                  required
                />
                <Input
                  label={language === 'ar' ? 'الاسم الكامل' : 'Full Name'}
                  value={editUserData.full_name}
                  onChange={(e) => setEditUserData(prev => ({ ...prev, full_name: e.target.value }))}
                  required
                />
                <div>
                  <label className="block text-sm font-medium text-secondary mb-2">
                    {language === 'ar' ? 'الدور' : 'Role'}
                  </label>
                  <select
                    value={editUserData.role}
                    onChange={(e) => setEditUserData(prev => ({ ...prev, role: e.target.value as 'admin' | 'user' }))}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-secondary focus:ring-1 focus:ring-secondary"
                    style={{ backgroundColor: '#1f2937', color: '#ffffff' }}
                  >
                    <option value="user" style={{ backgroundColor: '#1f2937', color: '#ffffff' }}>
                      {language === 'ar' ? 'مستخدم' : 'User'}
                    </option>
                    <option value="admin" style={{ backgroundColor: '#1f2937', color: '#ffffff' }}>
                      {language === 'ar' ? 'مدير' : 'Admin'}
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowEditModal(false)}>
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button variant="primary" onClick={handleUpdateUser}>
              <Edit className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'حفظ التغييرات' : 'Save Changes'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}

      {/* Send Message Modal */}
      {showMessageModal && selectedUser && (
        <Modal
          isOpen={showMessageModal}
          onClose={() => setShowMessageModal(false)}
          title={`${language === 'ar' ? 'إرسال رسالة إلى' : 'Send Message to'} ${selectedUser.username}`}
          size="lg"
        >
          <Modal.Body>
            <div className="space-y-6">
              <Input
                label={language === 'ar' ? 'موضوع الرسالة' : 'Subject'}
                value={messageData.subject}
                onChange={(e) => setMessageData(prev => ({ ...prev, subject: e.target.value }))}
                required
              />

              <div>
                <label className="block text-sm font-medium text-secondary mb-2">
                  {language === 'ar' ? 'الأولوية' : 'Priority'}
                </label>
                <select
                  value={messageData.priority}
                  onChange={(e) => setMessageData(prev => ({ ...prev, priority: e.target.value as 'low' | 'normal' | 'high' }))}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-secondary focus:ring-1 focus:ring-secondary"
                  style={{ backgroundColor: '#1f2937', color: '#ffffff' }}
                >
                  <option value="low" style={{ backgroundColor: '#1f2937', color: '#ffffff' }}>
                    {language === 'ar' ? 'منخفضة' : 'Low'}
                  </option>
                  <option value="normal" style={{ backgroundColor: '#1f2937', color: '#ffffff' }}>
                    {language === 'ar' ? 'عادية' : 'Normal'}
                  </option>
                  <option value="high" style={{ backgroundColor: '#1f2937', color: '#ffffff' }}>
                    {language === 'ar' ? 'عالية' : 'High'}
                  </option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-secondary mb-2">
                  {language === 'ar' ? 'نص الرسالة' : 'Message'}
                </label>
                <textarea
                  value={messageData.message}
                  onChange={(e) => setMessageData(prev => ({ ...prev, message: e.target.value }))}
                  rows={6}
                  className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none focus:border-secondary focus:ring-1 focus:ring-secondary"
                  placeholder={language === 'ar' ? 'اكتب رسالتك هنا...' : 'Type your message here...'}
                  required
                />
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowMessageModal(false)}>
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button
              variant="primary"
              onClick={handleSendMessage}
              disabled={!messageData.message.trim()}
            >
              <MessageSquare className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'إرسال الرسالة' : 'Send Message'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}
    </div>
  );
};

export default EnhancedUserManager;
