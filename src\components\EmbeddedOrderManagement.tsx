import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { getAllOrders, getAllUsers, updateOrderStatus } from '../lib/apiServices';
import { UserService, User } from '../lib/database';
import { ShoppingCart, Search, Eye, Edit3, CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';

interface EmbeddedOrderManagementProps {
  onClose: () => void;
}

const EmbeddedOrderManagement: React.FC<EmbeddedOrderManagementProps> = ({ onClose }) => {
  const { language } = useTranslation();
  const [orders, setOrders] = useState<UserService[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | UserService['status']>('all');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [ordersResult, usersResult] = await Promise.all([
        getAllOrders(),
        getAllUsers()
      ]);

      if (ordersResult.data) {
        // Convert orders to services format for compatibility
        const servicesFromOrders = ordersResult.data.map(order => ({
          id: order.id,
          user_id: order.user_id,
          service_name: order.service_name || order.item_name_en,
          service_type: order.order_type,
          status: order.status,
          purchase_date: order.created_at,
          completion_date: order.updated_at,
          price: order.final_price,
          notes: order.notes
        }));
        setOrders(servicesFromOrders);
      }
      if (usersResult.data) setUsers(usersResult.data);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getUserById = (userId: string) => {
    return users.find(user => user.id === userId);
  };

  const getStatusIcon = (status: UserService['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'cancelled': return <XCircle className="w-5 h-5 text-red-400" />;
      case 'pending': return <Clock className="w-5 h-5 text-yellow-400" />;
      default: return <AlertCircle className="w-5 h-5 text-blue-400" />;
    }
  };

  const getStatusText = (status: UserService['status']) => {
    const statusMap = {
      'pending': language === 'ar' ? 'في الانتظار' : 'Pending',
      'confirmed': language === 'ar' ? 'مؤكد' : 'Confirmed',
      'in_progress': language === 'ar' ? 'قيد التنفيذ' : 'In Progress',
      'testing': language === 'ar' ? 'قيد الاختبار' : 'Testing',
      'completed': language === 'ar' ? 'مكتمل' : 'Completed',
      'cancelled': language === 'ar' ? 'ملغي' : 'Cancelled',
      'refunded': language === 'ar' ? 'مسترد' : 'Refunded',
      'on_hold': language === 'ar' ? 'معلق' : 'On Hold'
    };
    return statusMap[status] || status;
  };

  const handleStatusChange = async (orderId: string, newStatus: UserService['status']) => {
    try {
      const result = await updateOrderStatus(orderId, newStatus);
      if (result.data) {
        loadData();
        alert(language === 'ar' ? 'تم تحديث حالة الطلب بنجاح' : 'Order status updated successfully');
      } else {
        alert(language === 'ar' ? 'فشل في تحديث حالة الطلب' : 'Failed to update order status');
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      alert(language === 'ar' ? 'فشل في تحديث حالة الطلب' : 'Failed to update order status');
    }
  };

    const filteredOrders = orders.filter(order => {
      const user = getUserById(order.user_id);
      const matchesSearch = order.service_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user?.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user?.email.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
      return matchesSearch && matchesStatus;
    });

    if (loading) {
      return (
        <div className="p-6">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-secondary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-white">{language === 'ar' ? 'جاري التحميل...' : 'Loading...'}</p>
          </div>
        </div>
      );
    }

    return (
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <ShoppingCart className="w-8 h-8 text-secondary" />
            <h2 className="text-2xl font-bold text-white">{language === 'ar' ? 'إدارة الطلبات' : 'Order Management'}</h2>
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder={language === 'ar' ? 'البحث في الطلبات...' : 'Search orders...'}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-background border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-secondary focus:outline-none"
            />
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as 'all' | UserService['status'])}
            className="bg-background border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-secondary focus:outline-none"
          >
            <option value="all">{language === 'ar' ? 'جميع الحالات' : 'All Status'}</option>
            <option value="pending">{language === 'ar' ? 'في الانتظار' : 'Pending'}</option>
            <option value="confirmed">{language === 'ar' ? 'مؤكد' : 'Confirmed'}</option>
            <option value="in_progress">{language === 'ar' ? 'قيد التنفيذ' : 'In Progress'}</option>
            <option value="testing">{language === 'ar' ? 'قيد الاختبار' : 'Testing'}</option>
            <option value="completed">{language === 'ar' ? 'مكتمل' : 'Completed'}</option>
            <option value="cancelled">{language === 'ar' ? 'ملغي' : 'Cancelled'}</option>
          </select>
        </div>

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.map((order) => {
            const user = getUserById(order.user_id);
            return (
              <div key={order.id} className="bg-background border border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse mb-2">
                      {getStatusIcon(order.status)}
                      <h3 className="text-lg font-semibold text-white">{order.service_name}</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400">{language === 'ar' ? 'العميل:' : 'Customer:'}</span>
                        <p className="text-white">{user?.full_name || 'Unknown'}</p>
                        <p className="text-gray-400">{user?.email}</p>
                      </div>

                      <div>
                        <span className="text-gray-400">{language === 'ar' ? 'السعر:' : 'Price:'}</span>
                        <p className="text-white">${order.price}</p>
                        <span className="text-gray-400">{language === 'ar' ? 'تاريخ الطلب:' : 'Order Date:'}</span>
                        <p className="text-white">{new Date(order.purchase_date).toLocaleDateString()}</p>
                      </div>

                      <div>
                        <span className="text-gray-400">{language === 'ar' ? 'الحالة:' : 'Status:'}</span>
                        <p className="text-white">{getStatusText(order.status)}</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <select
                      value={order.status}
                      onChange={(e) => handleStatusChange(order.id, e.target.value as UserService['status'])}
                      className="bg-primary border border-gray-700 rounded-lg px-3 py-2 text-white focus:border-secondary focus:outline-none"
                    >
                      <option value="pending">{language === 'ar' ? 'في الانتظار' : 'Pending'}</option>
                      <option value="confirmed">{language === 'ar' ? 'مؤكد' : 'Confirmed'}</option>
                      <option value="in_progress">{language === 'ar' ? 'قيد التنفيذ' : 'In Progress'}</option>
                      <option value="testing">{language === 'ar' ? 'قيد الاختبار' : 'Testing'}</option>
                      <option value="completed">{language === 'ar' ? 'مكتمل' : 'Completed'}</option>
                      <option value="cancelled">{language === 'ar' ? 'ملغي' : 'Cancelled'}</option>
                      <option value="refunded">{language === 'ar' ? 'مسترد' : 'Refunded'}</option>
                      <option value="on_hold">{language === 'ar' ? 'معلق' : 'On Hold'}</option>
                    </select>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {filteredOrders.length === 0 && (
          <div className="text-center py-8">
            <ShoppingCart className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <p className="text-gray-400">{language === 'ar' ? 'لا توجد طلبات' : 'No orders found'}</p>
          </div>
        )}
      </div>
    );
  };

  export default EmbeddedOrderManagement;