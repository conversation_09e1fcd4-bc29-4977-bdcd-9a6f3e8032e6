import React from 'react';
import { ArrowLeft, Home } from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';

interface BackButtonProps {
  onClick: () => void;
  variant?: 'back' | 'home';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showIcon?: boolean;
  showText?: boolean;
}

/**
 * Enhanced Back Button Component
 * Professional back button with improved visibility and design
 */
const BackButton: React.FC<BackButtonProps> = ({
  onClick,
  variant = 'back',
  size = 'md',
  className = '',
  showIcon = true,
  showText = true
}) => {
  const { language } = useTranslation();

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2.5 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  const baseClasses = `
    inline-flex items-center justify-center gap-2
    bg-gradient-to-r from-gray-700 to-gray-800
    hover:from-gray-600 hover:to-gray-700
    border-2 border-gray-500/70 hover:border-gray-400
    text-gray-100 hover:text-white
    font-semibold tracking-wide
    rounded-lg transition-all duration-300
    shadow-lg hover:shadow-xl
    backdrop-blur-sm
    focus:outline-none focus:ring-2 focus:ring-gray-400/50 focus:ring-offset-2 focus:ring-offset-gray-900
    active:scale-95
    ${sizeClasses[size]}
    ${className}
  `;

  const Icon = variant === 'home' ? Home : ArrowLeft;
  const text = variant === 'home' 
    ? (language === 'ar' ? 'العودة للرئيسية' : 'Back to Home')
    : (language === 'ar' ? 'العودة' : 'Back');

  return (
    <button
      onClick={onClick}
      className={baseClasses}
      type="button"
    >
      {showIcon && (
        <Icon className={`${iconSizes[size]} ${language === 'ar' && variant === 'back' ? 'rotate-180' : ''}`} />
      )}
      {showText && <span>{text}</span>}
    </button>
  );
};

export default BackButton;
