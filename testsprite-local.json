{"name": "Khanfashariya Local API Tests", "baseUrl": "http://localhost:3001", "endpoints": [{"path": "/api/auth/login", "method": "POST", "description": "User login endpoint", "testCases": [{"name": "<PERSON><PERSON>", "body": {"email": "<EMAIL>", "password": "admin123"}, "expectedStatus": 200, "expectedFields": ["success", "data"]}, {"name": "Invalid Password", "body": {"email": "<EMAIL>", "password": "wrongpassword"}, "expectedStatus": 401}, {"name": "Missing Credentials", "body": {}, "expectedStatus": 400}]}, {"path": "/api/auth/register", "method": "POST", "description": "User registration endpoint", "testCases": [{"name": "Valid Registration or User Exists", "body": {"email": "testuser_{{timestamp}}@test.com", "username": "testuser_{{timestamp}}", "full_name": "Test User", "password": "password123"}, "expectedStatus": [201, 400, 409]}, {"name": "Missing Fields", "body": {"email": "<EMAIL>"}, "expectedStatus": 400}]}, {"path": "/api/systems", "method": "GET", "description": "Get technical systems", "testCases": [{"name": "Get All Systems", "expectedStatus": 200, "expectedFields": ["success", "data"]}]}, {"path": "/api/services/technical", "method": "GET", "description": "Get technical services", "testCases": [{"name": "Get All Technical Services", "expectedStatus": 200, "expectedFields": ["success", "data"]}]}, {"path": "/api/services/premium", "method": "GET", "description": "Get premium services", "testCases": [{"name": "Get All Premium Services", "expectedStatus": 200, "expectedFields": ["success", "data"]}]}]}