{"name": "Khanfashariya Complete API Testing", "description": "Comprehensive testing configuration for Khanfashariya Metin2 Services Platform", "version": "1.0.0", "timestamp": "2025-07-21T23:45:00.000Z", "environment": {"frontend_url": "https://70f354611634.ngrok-free.app", "backend_url": "https://7b93f343ea56.ngrok-free.app", "status": "active", "data_connected": true}, "global_headers": {"ngrok-skip-browser-warning": "true", "User-Agent": "TestSprite/1.0", "Content-Type": "application/json", "Accept": "application/json"}, "authentication": {"admin_credentials": {"email": "<EMAIL>", "password": "admin123"}, "test_credentials": {"email": "<EMAIL>", "password": "123456"}}, "test_suites": [{"name": "Health & Status Tests", "description": "Basic connectivity and health checks", "tests": [{"name": "Backend Health Check", "method": "GET", "url": "https://7b93f343ea56.ngrok-free.app/health", "expected_status": 200, "expected_response": {"status": "healthy", "database": "connected"}, "timeout": 5000}, {"name": "Frontend Accessibility", "method": "GET", "url": "https://70f354611634.ngrok-free.app", "expected_status": 200, "expected_content": "<PERSON><PERSON><PERSON><PERSON>", "timeout": 10000}]}, {"name": "Authentication Tests", "description": "User authentication and authorization", "tests": [{"name": "<PERSON><PERSON> - <PERSON><PERSON>", "method": "POST", "url": "https://7b93f343ea56.ngrok-free.app/api/auth/login", "body": {"email": "<EMAIL>", "password": "admin123"}, "expected_status": 200, "expected_response": {"success": true}, "save_token": true}, {"name": "Login - Invalid Credentials", "method": "POST", "url": "https://7b93f343ea56.ngrok-free.app/api/auth/login", "body": {"email": "<EMAIL>", "password": "wrongpass"}, "expected_status": 401, "expected_response": {"success": false}}, {"name": "Login - <PERSON>", "method": "POST", "url": "https://7b93f343ea56.ngrok-free.app/api/auth/login", "body": {}, "expected_status": 400}, {"name": "User Registration", "method": "POST", "url": "https://7b93f343ea56.ngrok-free.app/api/auth/register", "body": {"email": "testsprite_{{timestamp}}@test.com", "username": "testsprite_{{timestamp}}", "full_name": "TestSprite User", "password": "testpass123"}, "expected_status": [201, 400, 409], "note": "May return 400/409 if user already exists"}]}, {"name": "Technical Systems API Tests", "description": "Testing technical systems endpoints with database data", "tests": [{"name": "Get All Systems", "method": "GET", "url": "https://7b93f343ea56.ngrok-free.app/api/systems", "expected_status": 200, "expected_response": {"success": true, "data": {"systems": "array", "pagination": "object"}}, "validations": [{"field": "data.systems", "type": "array", "min_length": 1}, {"field": "data.systems[0].name_ar", "type": "string", "required": true}, {"field": "data.systems[0].price", "type": "string", "required": true}]}, {"name": "Get Systems with Pagination", "method": "GET", "url": "https://7b93f343ea56.ngrok-free.app/api/systems?page=1&limit=5", "expected_status": 200, "validations": [{"field": "data.pagination.page", "value": 1}, {"field": "data.pagination.limit", "value": 5}]}, {"name": "Get Systems by Category", "method": "GET", "url": "https://7b93f343ea56.ngrok-free.app/api/systems?category=combat", "expected_status": 200, "validations": [{"field": "data.systems", "type": "array"}]}]}, {"name": "Services API Tests", "description": "Testing technical and premium services", "tests": [{"name": "Get Technical Services", "method": "GET", "url": "https://7b93f343ea56.ngrok-free.app/api/services/technical", "expected_status": 200, "expected_response": {"success": true, "data": "array"}}, {"name": "Get Premium Services", "method": "GET", "url": "https://7b93f343ea56.ngrok-free.app/api/services/premium", "expected_status": 200, "expected_response": {"success": true, "data": "array"}}]}, {"name": "Frontend Page Tests", "description": "Testing frontend pages accessibility", "tests": [{"name": "Homepage", "method": "GET", "url": "https://70f354611634.ngrok-free.app/", "expected_status": 200, "expected_content": ["<PERSON><PERSON><PERSON><PERSON>", "الخنفشارية"]}, {"name": "<PERSON><PERSON>", "method": "GET", "url": "https://70f354611634.ngrok-free.app/login", "expected_status": 200, "expected_content": ["login", "تسجيل"]}, {"name": "Services Page", "method": "GET", "url": "https://70f354611634.ngrok-free.app/services", "expected_status": 200, "expected_content": ["services", "خدمات"]}, {"name": "Admin Page", "method": "GET", "url": "https://70f354611634.ngrok-free.app/admin", "expected_status": 200, "note": "May redirect to login if not authenticated"}]}, {"name": "Data Integration Tests", "description": "Testing data flow between frontend and backend", "tests": [{"name": "Systems Data Display", "method": "GET", "url": "https://70f354611634.ngrok-free.app/", "expected_status": 200, "note": "Check if systems data from backend appears on frontend", "custom_validation": "Check for system names in Arabic/English"}, {"name": "API Proxy Test", "method": "GET", "url": "https://70f354611634.ngrok-free.app/api/systems", "expected_status": 200, "note": "Test if frontend proxy forwards to backend correctly"}]}], "performance_tests": [{"name": "Response Time Test", "url": "https://7b93f343ea56.ngrok-free.app/api/systems", "method": "GET", "max_response_time": 2000, "concurrent_requests": 5}, {"name": "Load Test", "url": "https://7b93f343ea56.ngrok-free.app/health", "method": "GET", "concurrent_requests": 10, "duration": 30}], "security_tests": [{"name": "CORS Headers Test", "url": "https://7b93f343ea56.ngrok-free.app/api/systems", "method": "OPTIONS", "expected_headers": ["Access-Control-Allow-Origin", "Access-Control-Allow-Methods"]}, {"name": "SQL Injection Test", "url": "https://7b93f343ea56.ngrok-free.app/api/systems?category=' OR '1'='1", "method": "GET", "expected_status": [200, 400], "note": "Should handle malicious input safely"}], "documentation": {"api_docs_url": "api-documentation.json", "postman_collection": "Available on request", "swagger_ui": "Not configured", "test_data": {"sample_system": {"name_ar": "نظام حروب الروابط التلقائي", "name_en": "Automatic Guild War System", "price": "299.00", "category": "combat"}}}, "notes": ["All endpoints require ngrok-skip-browser-warning header", "Authentication endpoints return JWT tokens", "Database contains real test data", "Frontend uses environment variables to connect to backend", "CORS is configured for ngrok URLs", "Both Arabic and English content supported"]}