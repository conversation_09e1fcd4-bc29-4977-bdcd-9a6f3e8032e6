/**
 * Fix Admin Access - Complete Solution
 * 
 * This script:
 * 1. Creates/updates admin user
 * 2. Tests admin endpoints
 * 3. Verifies data display
 */

const { createAdminUser } = require('./create-admin-user');
const { testAdminEndpoints } = require('./test-admin-endpoints');

async function fixAdminAccess() {
  console.log('🔧 Fixing Admin Access - Complete Solution\n');
  console.log('=' .repeat(50));
  
  try {
    // Step 1: Create/update admin user
    console.log('\n📋 STEP 1: Setting up admin user');
    console.log('-'.repeat(30));
    await createAdminUser();
    
    // Step 2: Wait a moment for database to sync
    console.log('\n⏳ Waiting for database sync...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Step 3: Test admin endpoints
    console.log('\n📋 STEP 2: Testing admin endpoints');
    console.log('-'.repeat(30));
    await testAdminEndpoints();
    
    console.log('\n' + '='.repeat(50));
    console.log('🎉 Admin access fix completed!');
    console.log('\n📝 Next steps:');
    console.log('1. Open your browser and go to admin panel');
    console.log('2. Login with: <EMAIL> / admin123456');
    console.log('3. Check if systems and services data appears');
    console.log('4. If still having issues, check server logs');
    
  } catch (error) {
    console.error('\n💥 Fix failed:', error.message);
    console.log('\n🔍 Troubleshooting:');
    console.log('1. Make sure server is running: npm run dev:server');
    console.log('2. Check database connection');
    console.log('3. Verify admin user exists: npm run create:admin');
    console.log('4. Test endpoints manually: npm run test:endpoints');
  }
}

// Run if called directly
if (require.main === module) {
  fixAdminAccess();
}

module.exports = { fixAdminAccess };