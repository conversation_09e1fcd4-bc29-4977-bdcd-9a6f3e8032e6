import React, { useState, useEffect, useRef } from 'react';
import { 
  X, 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  Download,
  Maximize2,
  Grid3X3,
  Eye
} from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';

interface ImageGalleryModalProps {
  isOpen: boolean;
  onClose: () => void;
  images: string[];
  initialIndex?: number;
  title?: string;
}

/**
 * Professional Image Gallery Modal Component
 * Features:
 * - Full-screen image viewing with zoom and pan
 * - Thumbnail navigation
 * - Keyboard shortcuts
 * - Touch/swipe support for mobile
 * - Image rotation and download
 * - Smooth animations and transitions
 */
const ImageGalleryModal: React.FC<ImageGalleryModalProps> = ({ 
  isOpen, 
  onClose, 
  images, 
  initialIndex = 0,
  title 
}) => {
  const { language } = useTranslation();
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [showThumbnails, setShowThumbnails] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Reset states when image changes
  useEffect(() => {
    setZoom(1);
    setRotation(0);
    setImagePosition({ x: 0, y: 0 });
    setIsLoading(true);
  }, [currentIndex]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          goToPrevious();
          break;
        case 'ArrowRight':
          goToNext();
          break;
        case '+':
        case '=':
          zoomIn();
          break;
        case '-':
          zoomOut();
          break;
        case 'r':
        case 'R':
          rotate();
          break;
        case 't':
        case 'T':
          setShowThumbnails(!showThumbnails);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, currentIndex, showThumbnails]);

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : images.length - 1));
  };

  const goToNext = () => {
    setCurrentIndex((prev) => (prev < images.length - 1 ? prev + 1 : 0));
  };

  const zoomIn = () => {
    setZoom((prev) => Math.min(prev + 0.5, 5));
  };

  const zoomOut = () => {
    setZoom((prev) => Math.max(prev - 0.5, 0.5));
  };

  const rotate = () => {
    setRotation((prev) => (prev + 90) % 360);
  };

  const resetView = () => {
    setZoom(1);
    setRotation(0);
    setImagePosition({ x: 0, y: 0 });
  };

  const downloadImage = () => {
    const link = document.createElement('a');
    link.href = images[currentIndex];
    link.download = `premium-image-${currentIndex + 1}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Mouse drag handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    if (zoom <= 1) return;
    setIsDragging(true);
    setDragStart({ x: e.clientX - imagePosition.x, y: e.clientY - imagePosition.y });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || zoom <= 1) return;
    setImagePosition({
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] bg-black/95 backdrop-blur-sm">
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black/80 to-transparent p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <h3 className="text-white text-lg font-medium">
              {title || (language === 'ar' ? 'معرض الصور' : 'Image Gallery')}
            </h3>
            <span className="text-gray-300 text-sm">
              {currentIndex + 1} / {images.length}
            </span>
          </div>
          
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <button
              onClick={() => setShowThumbnails(!showThumbnails)}
              className="w-10 h-10 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center transition-colors"
              title={language === 'ar' ? 'إظهار/إخفاء المصغرات' : 'Toggle Thumbnails'}
            >
              <Grid3X3 className="w-5 h-5 text-white" />
            </button>
            
            <button
              onClick={onClose}
              className="w-10 h-10 bg-red-500/20 hover:bg-red-500/40 rounded-full flex items-center justify-center transition-colors"
              title={language === 'ar' ? 'إغلاق' : 'Close'}
            >
              <X className="w-5 h-5 text-white" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Image Container */}
      <div 
        ref={containerRef}
        className="absolute inset-0 flex items-center justify-center pt-16 pb-20"
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="w-12 h-12 border-4 border-secondary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-white">{language === 'ar' ? 'جاري التحميل...' : 'Loading...'}</p>
            </div>
          </div>
        )}
        
        <img
          ref={imageRef}
          src={images[currentIndex]}
          alt={`Image ${currentIndex + 1}`}
          className={`max-w-full max-h-full object-contain transition-all duration-300 ${
            isDragging ? 'cursor-grabbing' : zoom > 1 ? 'cursor-grab' : 'cursor-default'
          }`}
          style={{
            transform: `scale(${zoom}) rotate(${rotation}deg) translate(${imagePosition.x / zoom}px, ${imagePosition.y / zoom}px)`,
          }}
          onLoad={() => setIsLoading(false)}
          onMouseDown={handleMouseDown}
          draggable={false}
        />
      </div>

      {/* Navigation Arrows */}
      {images.length > 1 && (
        <>
          <button
            onClick={goToPrevious}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center transition-colors"
            title={language === 'ar' ? 'الصورة السابقة' : 'Previous Image'}
          >
            <ChevronLeft className="w-6 h-6 text-white" />
          </button>
          
          <button
            onClick={goToNext}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center transition-colors"
            title={language === 'ar' ? 'الصورة التالية' : 'Next Image'}
          >
            <ChevronRight className="w-6 h-6 text-white" />
          </button>
        </>
      )}

      {/* Controls */}
      <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 bg-black/70 backdrop-blur-sm rounded-full px-4 py-2">
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <button
            onClick={zoomOut}
            className="w-8 h-8 hover:bg-white/20 rounded-full flex items-center justify-center transition-colors"
            title={language === 'ar' ? 'تصغير' : 'Zoom Out'}
          >
            <ZoomOut className="w-4 h-4 text-white" />
          </button>
          
          <span className="text-white text-sm px-2">{Math.round(zoom * 100)}%</span>
          
          <button
            onClick={zoomIn}
            className="w-8 h-8 hover:bg-white/20 rounded-full flex items-center justify-center transition-colors"
            title={language === 'ar' ? 'تكبير' : 'Zoom In'}
          >
            <ZoomIn className="w-4 h-4 text-white" />
          </button>
          
          <div className="w-px h-6 bg-gray-600 mx-2"></div>
          
          <button
            onClick={rotate}
            className="w-8 h-8 hover:bg-white/20 rounded-full flex items-center justify-center transition-colors"
            title={language === 'ar' ? 'تدوير' : 'Rotate'}
          >
            <RotateCw className="w-4 h-4 text-white" />
          </button>
          
          <button
            onClick={resetView}
            className="w-8 h-8 hover:bg-white/20 rounded-full flex items-center justify-center transition-colors"
            title={language === 'ar' ? 'إعادة تعيين' : 'Reset View'}
          >
            <Maximize2 className="w-4 h-4 text-white" />
          </button>
          
          <button
            onClick={downloadImage}
            className="w-8 h-8 hover:bg-white/20 rounded-full flex items-center justify-center transition-colors"
            title={language === 'ar' ? 'تحميل' : 'Download'}
          >
            <Download className="w-4 h-4 text-white" />
          </button>
        </div>
      </div>

      {/* Thumbnails */}
      {showThumbnails && images.length > 1 && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          <div className="flex justify-center space-x-2 rtl:space-x-reverse overflow-x-auto">
            {images.map((image, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 transition-all duration-200 ${
                  index === currentIndex 
                    ? 'ring-2 ring-secondary scale-110' 
                    : 'hover:scale-105 opacity-70 hover:opacity-100'
                }`}
              >
                <img
                  src={image}
                  alt={`Thumbnail ${index + 1}`}
                  className="w-full h-full object-cover"
                />
                {index === currentIndex && (
                  <div className="absolute inset-0 bg-secondary/20 flex items-center justify-center">
                    <Eye className="w-4 h-4 text-white" />
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageGalleryModal;
