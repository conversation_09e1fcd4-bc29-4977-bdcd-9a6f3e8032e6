#!/usr/bin/env node

/**
 * Final Test Script for Khanfashariya.com
 * 
 * This script performs comprehensive testing to ensure
 * the complete migration from localStorage to MySQL API.
 */

const axios = require('axios');
require('dotenv').config();

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';

console.log('🎯 Final System Test - MySQL API Migration');
console.log(`📡 API Base URL: ${API_BASE_URL}`);

/**
 * Test authentication and get admin token
 */
async function testAuthentication() {
  console.log('\n🔐 Testing Authentication...');
  
  try {
    const response = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (response.data.success && response.data.data.tokens?.accessToken) {
      console.log('✅ Admin login successful');
      return response.data.data.tokens.accessToken;
    } else {
      console.log('❌ Login failed - no token received');
      return null;
    }
  } catch (error) {
    console.log('❌ Authentication failed:', error.message);
    return null;
  }
}

/**
 * Test system services API
 */
async function testSystemServices(token) {
  console.log('\n🖥️ Testing System Services API...');
  
  const headers = token ? { Authorization: `Bearer ${token}` } : {};
  
  try {
    const response = await axios.get(`${API_BASE_URL}/api/systems`, { headers });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      console.log(`✅ System services loaded: ${response.data.data.length} systems`);
      
      // Test specific system data
      const activeSystems = response.data.data.filter(s => s.status === 'active');
      console.log(`✅ Active systems: ${activeSystems.length}`);
      
      if (activeSystems.length > 0) {
        const firstSystem = activeSystems[0];
        console.log(`✅ Sample system: ${firstSystem.name_en || firstSystem.name_ar}`);
      }
      
      return true;
    } else {
      console.log('❌ Invalid system services response');
      return false;
    }
  } catch (error) {
    console.log('❌ System services test failed:', error.message);
    return false;
  }
}

/**
 * Test technical services API
 */
async function testTechnicalServices(token) {
  console.log('\n🛠️ Testing Technical Services API...');
  
  const headers = token ? { Authorization: `Bearer ${token}` } : {};
  
  try {
    const response = await axios.get(`${API_BASE_URL}/api/services/technical`, { headers });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      console.log(`✅ Technical services loaded: ${response.data.data.length} services`);
      
      const activeServices = response.data.data.filter(s => s.status === 'active');
      console.log(`✅ Active services: ${activeServices.length}`);
      
      return true;
    } else {
      console.log('❌ Invalid technical services response');
      return false;
    }
  } catch (error) {
    console.log('❌ Technical services test failed:', error.message);
    return false;
  }
}

/**
 * Test users API
 */
async function testUsers(token) {
  console.log('\n👥 Testing Users API...');
  
  if (!token) {
    console.log('⚠️ Skipping users test - no admin token');
    return false;
  }
  
  const headers = { Authorization: `Bearer ${token}` };
  
  try {
    const response = await axios.get(`${API_BASE_URL}/api/users`, { headers });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      console.log(`✅ Users loaded: ${response.data.data.length} users`);
      
      const adminUsers = response.data.data.filter(u => u.role === 'admin');
      console.log(`✅ Admin users: ${adminUsers.length}`);
      
      return true;
    } else {
      console.log('❌ Invalid users response');
      return false;
    }
  } catch (error) {
    console.log('❌ Users test failed:', error.message);
    return false;
  }
}

/**
 * Test orders API
 */
async function testOrders(token) {
  console.log('\n📋 Testing Orders API...');
  
  if (!token) {
    console.log('⚠️ Skipping orders test - no admin token');
    return false;
  }
  
  const headers = { Authorization: `Bearer ${token}` };
  
  try {
    const response = await axios.get(`${API_BASE_URL}/api/orders`, { headers });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      console.log(`✅ Orders loaded: ${response.data.data.length} orders`);
      return true;
    } else {
      console.log('❌ Invalid orders response');
      return false;
    }
  } catch (error) {
    console.log('❌ Orders test failed:', error.message);
    return false;
  }
}

/**
 * Test premium content API
 */
async function testPremiumContent(token) {
  console.log('\n👑 Testing Premium Content API...');
  
  const headers = token ? { Authorization: `Bearer ${token}` } : {};
  
  try {
    const response = await axios.get(`${API_BASE_URL}/api/services/premium`, { headers });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      console.log(`✅ Premium content loaded: ${response.data.data.length} items`);
      return true;
    } else {
      console.log('❌ Invalid premium content response');
      return false;
    }
  } catch (error) {
    console.log('❌ Premium content test failed:', error.message);
    return false;
  }
}

/**
 * Test database connectivity
 */
async function testDatabaseConnectivity() {
  console.log('\n🗄️ Testing Database Connectivity...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/health`);
    
    if (response.data.status === 'healthy' && response.data.database === 'connected') {
      console.log('✅ Database connection healthy');
      console.log(`✅ Database: ${response.data.database_name || 'khanfashariya_db'}`);
      return true;
    } else {
      console.log('❌ Database connection unhealthy');
      return false;
    }
  } catch (error) {
    console.log('❌ Database connectivity test failed:', error.message);
    return false;
  }
}

/**
 * Main test function
 */
async function runFinalTest() {
  console.log('🚀 Starting Final System Test...\n');
  
  const results = {
    database: false,
    auth: false,
    systems: false,
    services: false,
    users: false,
    orders: false,
    premium: false
  };
  
  // Test database connectivity
  results.database = await testDatabaseConnectivity();
  
  // Test authentication
  const token = await testAuthentication();
  results.auth = !!token;
  
  // Test all APIs
  results.systems = await testSystemServices(token);
  results.services = await testTechnicalServices(token);
  results.users = await testUsers(token);
  results.orders = await testOrders(token);
  results.premium = await testPremiumContent(token);
  
  // Summary
  console.log('\n📊 Final Test Results:');
  console.log('========================');
  
  const tests = [
    { name: 'Database Connectivity', result: results.database },
    { name: 'Authentication', result: results.auth },
    { name: 'System Services', result: results.systems },
    { name: 'Technical Services', result: results.services },
    { name: 'User Management', result: results.users },
    { name: 'Orders Management', result: results.orders },
    { name: 'Premium Content', result: results.premium }
  ];
  
  tests.forEach(test => {
    const status = test.result ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${test.name}`);
  });
  
  const passedTests = tests.filter(t => t.result).length;
  const totalTests = tests.length;
  const passRate = ((passedTests / totalTests) * 100).toFixed(1);
  
  console.log('\n🎯 Overall Results:');
  console.log(`Tests Passed: ${passedTests}/${totalTests} (${passRate}%)`);
  
  if (passedTests === totalTests) {
    console.log('🎉 ALL TESTS PASSED! MySQL API migration is successful!');
    console.log('✅ The system is ready for production use.');
    return true;
  } else {
    console.log('⚠️ Some tests failed. Please review the issues above.');
    return false;
  }
}

// Run tests if called directly
if (require.main === module) {
  runFinalTest().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { runFinalTest };
