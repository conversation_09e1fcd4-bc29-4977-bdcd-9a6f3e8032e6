/**
 * Unified Filter Options and Constants
 * Central source for all filter configurations to avoid hard coding
 */

// Status Options - Used across all admin panels
export const STATUS_OPTIONS = [
  { value: 'all', label: 'All Status', labelAr: 'جميع الحالات' },
  { value: 'active', label: 'Active', labelAr: 'نشط' },
  { value: 'inactive', label: 'Inactive', labelAr: 'غير نشط' },
  { value: 'pending', label: 'Pending', labelAr: 'في الانتظار' },
  { value: 'completed', label: 'Completed', labelAr: 'مكتمل' },
  { value: 'cancelled', label: 'Cancelled', labelAr: 'ملغي' }
];

// Category Options for Technical Systems
export const SYSTEM_CATEGORIES = [
  { value: 'all', label: 'All Categories', labelAr: 'جميع الفئات' },
  { value: 'general', label: 'General', labelAr: 'عام' },
  { value: 'combat', label: 'Combat', labelAr: 'قتال' },
  { value: 'economy', label: 'Economy', labelAr: 'اقتصاد' },
  { value: 'social', label: 'Social', labelAr: 'اجتماعي' },
  { value: 'utility', label: 'Utility', labelAr: 'أدوات' },
  { value: 'admin', label: 'Admin', labelAr: 'إدارة' },
  { value: 'premium', label: 'Premium', labelAr: 'مميز' }
];

// Price Range Options
export const PRICE_RANGES = [
  { value: 'all', label: 'All Prices', labelAr: 'جميع الأسعار' },
  { value: '0-50', label: '$0 - $50', labelAr: '0 - 50 دولار' },
  { value: '50-100', label: '$50 - $100', labelAr: '50 - 100 دولار' },
  { value: '100-200', label: '$100 - $200', labelAr: '100 - 200 دولار' },
  { value: '200-500', label: '$200 - $500', labelAr: '200 - 500 دولار' },
  { value: '500+', label: '$500+', labelAr: '500+ دولار' }
];

// Sort Options
export const SORT_OPTIONS = [
  { value: 'name', label: 'Name', labelAr: 'الاسم' },
  { value: 'price', label: 'Price', labelAr: 'السعر' },
  { value: 'category', label: 'Category', labelAr: 'الفئة' },
  { value: 'created', label: 'Created Date', labelAr: 'تاريخ الإنشاء' },
  { value: 'updated', label: 'Last Updated', labelAr: 'آخر تحديث' },
  { value: 'popularity', label: 'Popularity', labelAr: 'الشعبية' }
];

// Premium Integration Options
export const PREMIUM_OPTIONS = [
  { value: 'all', label: 'All', labelAr: 'الكل' },
  { value: 'premium', label: 'Available for Premium', labelAr: 'متاح للنسخة المميزة' },
  { value: 'standalone', label: 'Standalone Only', labelAr: 'منفرد فقط' }
];

// User Role Options
export const USER_ROLES = [
  { value: 'all', label: 'All Roles', labelAr: 'جميع الأدوار' },
  { value: 'user', label: 'User', labelAr: 'مستخدم' },
  { value: 'admin', label: 'Admin', labelAr: 'مدير' },
  { value: 'moderator', label: 'Moderator', labelAr: 'مشرف' }
];

// Date Range Options
export const DATE_RANGES = [
  { value: 'all', label: 'All Dates', labelAr: 'جميع التواريخ' },
  { value: 'today', label: 'Today', labelAr: 'اليوم' },
  { value: 'week', label: 'This Week', labelAr: 'هذا الأسبوع' },
  { value: 'month', label: 'This Month', labelAr: 'هذا الشهر' },
  { value: 'quarter', label: 'This Quarter', labelAr: 'هذا الربع' },
  { value: 'year', label: 'This Year', labelAr: 'هذا العام' }
];

// Priority Options for Messages/Tasks
export const PRIORITY_OPTIONS = [
  { value: 'low', label: 'Low', labelAr: 'منخفضة' },
  { value: 'normal', label: 'Normal', labelAr: 'عادية' },
  { value: 'high', label: 'High', labelAr: 'عالية' },
  { value: 'urgent', label: 'Urgent', labelAr: 'عاجلة' }
];

// Order Status Options
export const ORDER_STATUS_OPTIONS = [
  { value: 'all', label: 'All Status', labelAr: 'جميع الحالات' },
  { value: 'pending', label: 'Pending', labelAr: 'في الانتظار' },
  { value: 'active', label: 'Active', labelAr: 'نشط' },
  { value: 'completed', label: 'Completed', labelAr: 'مكتمل' },
  { value: 'cancelled', label: 'Cancelled', labelAr: 'ملغي' },
  { value: 'refunded', label: 'Refunded', labelAr: 'مسترد' }
];

// Content Type Options for Premium Content
export const CONTENT_TYPES = [
  { value: 'all', label: 'All Types', labelAr: 'جميع الأنواع' },
  { value: 'system', label: 'System', labelAr: 'نظام' },
  { value: 'package', label: 'Package', labelAr: 'حزمة' },
  { value: 'addon', label: 'Add-on', labelAr: 'إضافة' },
  { value: 'template', label: 'Template', labelAr: 'قالب' }
];

// Filter Configurations for Different Admin Panels
export const TECHNICAL_SYSTEMS_FILTERS = [
  {
    key: 'status',
    label: 'Status',
    labelAr: 'الحالة',
    options: STATUS_OPTIONS
  },
  {
    key: 'category',
    label: 'Category',
    labelAr: 'الفئة',
    options: SYSTEM_CATEGORIES
  },
  {
    key: 'priceRange',
    label: 'Price Range',
    labelAr: 'نطاق السعر',
    options: PRICE_RANGES
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    labelAr: 'ترتيب حسب',
    options: SORT_OPTIONS
  },
  {
    key: 'premium',
    label: 'Premium Integration',
    labelAr: 'النسخة المميزة',
    options: PREMIUM_OPTIONS
  }
];

export const ORDER_MANAGEMENT_FILTERS = [
  {
    key: 'status',
    label: 'Status',
    labelAr: 'الحالة',
    options: ORDER_STATUS_OPTIONS
  },
  {
    key: 'dateRange',
    label: 'Date Range',
    labelAr: 'نطاق التاريخ',
    options: DATE_RANGES
  },
  {
    key: 'priceRange',
    label: 'Price Range',
    labelAr: 'نطاق السعر',
    options: PRICE_RANGES
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    labelAr: 'ترتيب حسب',
    options: SORT_OPTIONS
  }
];

export const USER_MANAGEMENT_FILTERS = [
  {
    key: 'role',
    label: 'Role',
    labelAr: 'الدور',
    options: USER_ROLES
  },
  {
    key: 'status',
    label: 'Status',
    labelAr: 'الحالة',
    options: STATUS_OPTIONS.filter(opt => ['all', 'active', 'inactive'].includes(opt.value))
  },
  {
    key: 'dateRange',
    label: 'Registration Date',
    labelAr: 'تاريخ التسجيل',
    options: DATE_RANGES
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    labelAr: 'ترتيب حسب',
    options: [
      { value: 'name', label: 'Name', labelAr: 'الاسم' },
      { value: 'email', label: 'Email', labelAr: 'البريد الإلكتروني' },
      { value: 'role', label: 'Role', labelAr: 'الدور' },
      { value: 'created', label: 'Registration Date', labelAr: 'تاريخ التسجيل' },
      { value: 'lastLogin', label: 'Last Login', labelAr: 'آخر دخول' }
    ]
  }
];

export const PREMIUM_CONTENT_FILTERS = [
  {
    key: 'type',
    label: 'Content Type',
    labelAr: 'نوع المحتوى',
    options: CONTENT_TYPES
  },
  {
    key: 'status',
    label: 'Status',
    labelAr: 'الحالة',
    options: STATUS_OPTIONS.filter(opt => ['all', 'active', 'inactive'].includes(opt.value))
  },
  {
    key: 'category',
    label: 'Category',
    labelAr: 'الفئة',
    options: SYSTEM_CATEGORIES
  },
  {
    key: 'priceRange',
    label: 'Price Range',
    labelAr: 'نطاق السعر',
    options: PRICE_RANGES
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    labelAr: 'ترتيب حسب',
    options: SORT_OPTIONS
  }
];

export const TECHNICAL_SERVICES_FILTERS = [
  {
    key: 'category',
    label: 'Category',
    labelAr: 'الفئة',
    options: [
      { value: 'all', label: 'All Categories', labelAr: 'جميع الفئات' },
      { value: 'development', label: 'Development', labelAr: 'تطوير' },
      { value: 'design', label: 'Design', labelAr: 'تصميم' },
      { value: 'consultation', label: 'Consultation', labelAr: 'استشارة' },
      { value: 'maintenance', label: 'Maintenance', labelAr: 'صيانة' },
      { value: 'support', label: 'Support', labelAr: 'دعم فني' }
    ]
  },
  {
    key: 'serviceType',
    label: 'Service Type',
    labelAr: 'نوع الخدمة',
    options: [
      { value: 'all', label: 'All Types', labelAr: 'جميع الأنواع' },
      { value: 'one-time', label: 'One-time', labelAr: 'مرة واحدة' },
      { value: 'recurring', label: 'Recurring', labelAr: 'متكررة' },
      { value: 'subscription', label: 'Subscription', labelAr: 'اشتراك' }
    ]
  },
  {
    key: 'status',
    label: 'Status',
    labelAr: 'الحالة',
    options: STATUS_OPTIONS.filter(opt => ['all', 'active', 'inactive'].includes(opt.value))
  },
  {
    key: 'priceRange',
    label: 'Price Range',
    labelAr: 'نطاق السعر',
    options: PRICE_RANGES
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    labelAr: 'ترتيب حسب',
    options: SORT_OPTIONS
  }
];

// Helper function to get filter options by key
export const getFilterOptions = (filterKey: string) => {
  const optionsMap: Record<string, any[]> = {
    status: STATUS_OPTIONS,
    category: SYSTEM_CATEGORIES,
    priceRange: PRICE_RANGES,
    sortBy: SORT_OPTIONS,
    premium: PREMIUM_OPTIONS,
    role: USER_ROLES,
    dateRange: DATE_RANGES,
    priority: PRIORITY_OPTIONS,
    orderStatus: ORDER_STATUS_OPTIONS,
    contentType: CONTENT_TYPES
  };
  
  return optionsMap[filterKey] || [];
};

// Helper function to get localized label
export const getLocalizedLabel = (option: { label: string; labelAr?: string }, language: string) => {
  return language === 'ar' && option.labelAr ? option.labelAr : option.label;
};
