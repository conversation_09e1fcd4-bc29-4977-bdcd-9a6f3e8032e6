/**
 * Quick Health Check Script
 * 
 * Performs a quick health check of all system components
 */

const axios = require('axios');
const mysql = require('mysql2/promise');
require('dotenv').config();

const API_BASE_URL = 'http://localhost:3001';
const FRONTEND_URL = 'http://localhost:5173';

async function quickHealthCheck() {
  console.log('🏥 إجراء فحص سريع للنظام...\n');
  
  const results = {
    database: false,
    backend: false,
    frontend: false,
    api_endpoints: {}
  };
  
  // فحص قاعدة البيانات
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT) || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '123456',
      database: process.env.DB_NAME || 'khanfashariya_db'
    });
    
    await connection.execute('SELECT 1');
    await connection.end();
    
    results.database = true;
    console.log('✅ قاعدة البيانات: متصلة');
  } catch (error) {
    console.log('❌ قاعدة البيانات: غير متصلة -', error.message);
  }
  
  // فحص الخادم الخلفي
  try {
    const response = await axios.get(`${API_BASE_URL}/health`, { timeout: 5000 });
    results.backend = response.status === 200;
    console.log('✅ الخادم الخلفي: يعمل');
  } catch (error) {
    console.log('❌ الخادم الخلفي: لا يعمل -', error.message);
  }
  
  // فحص الخادم الأمامي
  try {
    const response = await axios.get(FRONTEND_URL, { timeout: 5000 });
    results.frontend = response.status === 200;
    console.log('✅ الخادم الأمامي: يعمل');
  } catch (error) {
    console.log('❌ الخادم الأمامي: لا يعمل -', error.message);
  }
  
  // فحص API endpoints الأساسية
  const endpoints = [
    { name: 'Systems', path: '/api/systems' },
    { name: 'Technical Services', path: '/api/services/technical' },
    { name: 'Users', path: '/api/users' }
  ];
  
  console.log('\n🔍 فحص API Endpoints:');
  for (const endpoint of endpoints) {
    try {
      const response = await axios.get(`${API_BASE_URL}${endpoint.path}`, { 
        timeout: 5000,
        validateStatus: () => true // قبول جميع الحالات
      });
      
      const working = response.status < 500;
      results.api_endpoints[endpoint.name] = working;
      
      if (working) {
        console.log(`✅ ${endpoint.name}: يعمل (${response.status})`);
      } else {
        console.log(`❌ ${endpoint.name}: خطأ خادم (${response.status})`);
      }
    } catch (error) {
      results.api_endpoints[endpoint.name] = false;
      console.log(`❌ ${endpoint.name}: غير متاح -`, error.message);
    }
  }
  
  // تقرير نهائي
  console.log('\n📊 ملخص الحالة:');
  const totalChecks = 3 + Object.keys(results.api_endpoints).length;
  let passedChecks = 0;
  
  if (results.database) passedChecks++;
  if (results.backend) passedChecks++;
  if (results.frontend) passedChecks++;
  passedChecks += Object.values(results.api_endpoints).filter(Boolean).length;
  
  const healthPercentage = Math.round((passedChecks / totalChecks) * 100);
  
  console.log(`🎯 معدل الصحة: ${healthPercentage}% (${passedChecks}/${totalChecks})`);
  
  if (healthPercentage >= 80) {
    console.log('🎉 النظام يعمل بشكل ممتاز!');
  } else if (healthPercentage >= 60) {
    console.log('⚠️  النظام يعمل مع بعض المشاكل');
  } else {
    console.log('🚨 النظام يحتاج إلى إصلاح فوري');
  }
  
  // نصائح للإصلاح
  console.log('\n💡 نصائح:');
  if (!results.database) {
    console.log('   - تأكد من تشغيل MySQL server');
    console.log('   - تحقق من بيانات الاتصال في .env');
  }
  if (!results.backend) {
    console.log('   - شغل الخادم الخلفي: npm run dev:server');
  }
  if (!results.frontend) {
    console.log('   - شغل الخادم الأمامي: npm run dev');
  }
  
  return results;
}

// تشغيل الفحص
if (require.main === module) {
  quickHealthCheck().catch(console.error);
}

module.exports = quickHealthCheck;