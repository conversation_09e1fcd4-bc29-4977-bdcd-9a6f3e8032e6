/**
 * Order Debugger Component
 * Helps debug order data display issues
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { getAllOrders } from '../../lib/apiServices';
import { Bug, Database, Eye, RefreshCw } from 'lucide-react';
import Button from '../ui/Button';

const OrderDebugger: React.FC = () => {
  const { language } = useTranslation();
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);

  const loadOrders = async () => {
    setLoading(true);
    try {
      const result = await getAllOrders();
      if (result.data) {
        setOrders(result.data);
        console.log('Raw orders data:', result.data);
      }
    } catch (error) {
      console.error('Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadOrders();
  }, []);

  const formatValue = (value: any) => {
    if (value === null || value === undefined) {
      return <span className="text-red-400">NULL</span>;
    }
    if (value === '') {
      return <span className="text-yellow-400">EMPTY</span>;
    }
    if (typeof value === 'object') {
      return <pre className="text-xs text-gray-300">{JSON.stringify(value, null, 2)}</pre>;
    }
    return <span className="text-white">{value.toString()}</span>;
  };

  const calculateOrderValue = (order: any) => {
    const finalPrice = parseFloat(order.final_price?.toString() || '0');
    const totalPrice = parseFloat(order.total_price?.toString() || '0');
    const basePrice = parseFloat(order.base_price?.toString() || '0');
    const addonsPrice = parseFloat(order.addons_price?.toString() || '0');
    const unitPrice = parseFloat(order.unit_price?.toString() || '0');
    
    return {
      finalPrice,
      totalPrice,
      basePrice,
      addonsPrice,
      unitPrice,
      calculated: basePrice + addonsPrice,
      fallback: finalPrice || totalPrice || basePrice || unitPrice || 0
    };
  };

  return (
    <div className="p-6 bg-gray-900 rounded-xl border border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Bug className="w-6 h-6 text-red-400" />
          <h2 className="text-xl font-bold text-white">
            {language === 'ar' ? 'مصحح أخطاء الطلبات' : 'Order Debugger'}
          </h2>
        </div>
        <Button onClick={loadOrders} disabled={loading}>
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          {language === 'ar' ? 'تحديث' : 'Refresh'}
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Orders List */}
        <div>
          <h3 className="text-lg font-semibold text-white mb-4">
            {language === 'ar' ? 'قائمة الطلبات' : 'Orders List'} ({orders.length})
          </h3>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {orders.map((order) => {
              const pricing = calculateOrderValue(order);
              return (
                <div
                  key={order.id}
                  className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                    selectedOrder?.id === order.id
                      ? 'bg-secondary/20 border-secondary'
                      : 'bg-gray-800 border-gray-600 hover:border-gray-500'
                  }`}
                  onClick={() => setSelectedOrder(order)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white font-medium">
                      #{order.order_number || order.id.slice(0, 8)}
                    </span>
                    <span className="text-secondary font-bold">
                      ${pricing.fallback.toFixed(2)}
                    </span>
                  </div>
                  <div className="text-sm text-gray-400">
                    <div>Type: {order.order_type}</div>
                    <div>Status: {order.status}</div>
                    <div>User: {order.username || order.user_id}</div>
                  </div>
                  {pricing.fallback === 0 && (
                    <div className="mt-2 text-xs text-red-400 bg-red-500/20 px-2 py-1 rounded">
                      ⚠️ Zero Price Issue
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Order Details */}
        <div>
          <h3 className="text-lg font-semibold text-white mb-4">
            {language === 'ar' ? 'تفاصيل الطلب' : 'Order Details'}
          </h3>
          {selectedOrder ? (
            <div className="bg-gray-800 rounded-lg border border-gray-600 p-4 max-h-96 overflow-y-auto">
              <div className="space-y-3">
                {/* Pricing Analysis */}
                <div className="bg-gray-700 p-3 rounded">
                  <h4 className="text-white font-medium mb-2">Pricing Analysis</h4>
                  {(() => {
                    const pricing = calculateOrderValue(selectedOrder);
                    return (
                      <div className="text-sm space-y-1">
                        <div>Final Price: {formatValue(pricing.finalPrice)}</div>
                        <div>Total Price: {formatValue(pricing.totalPrice)}</div>
                        <div>Base Price: {formatValue(pricing.basePrice)}</div>
                        <div>Add-ons Price: {formatValue(pricing.addonsPrice)}</div>
                        <div>Unit Price: {formatValue(pricing.unitPrice)}</div>
                        <div className="border-t border-gray-600 pt-2 mt-2">
                          <div className="text-secondary font-bold">
                            Calculated: ${pricing.calculated.toFixed(2)}
                          </div>
                          <div className="text-accent font-bold">
                            Fallback: ${pricing.fallback.toFixed(2)}
                          </div>
                        </div>
                      </div>
                    );
                  })()}
                </div>

                {/* Key Fields */}
                {[
                  'id', 'order_number', 'order_type', 'order_category', 'status',
                  'item_name_ar', 'item_name_en', 'created_at', 'updated_at',
                  'customer_requirements', 'order_details', 'selected_addons'
                ].map((field) => (
                  <div key={field} className="border-b border-gray-700 pb-2">
                    <div className="text-gray-400 text-xs uppercase">{field}</div>
                    <div className="mt-1">{formatValue(selectedOrder[field])}</div>
                  </div>
                ))}

                {/* Raw Data */}
                <div className="bg-gray-700 p-3 rounded">
                  <h4 className="text-white font-medium mb-2">Raw JSON</h4>
                  <pre className="text-xs text-gray-300 overflow-x-auto">
                    {JSON.stringify(selectedOrder, null, 2)}
                  </pre>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-gray-800 rounded-lg border border-gray-600 p-8 text-center">
              <Database className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400">
                {language === 'ar' ? 'اختر طلباً لعرض التفاصيل' : 'Select an order to view details'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Summary */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gray-800 p-4 rounded-lg border border-gray-600">
          <div className="text-gray-400 text-sm">Total Orders</div>
          <div className="text-2xl font-bold text-white">{orders.length}</div>
        </div>
        <div className="bg-gray-800 p-4 rounded-lg border border-gray-600">
          <div className="text-gray-400 text-sm">Zero Price Orders</div>
          <div className="text-2xl font-bold text-red-400">
            {orders.filter(order => calculateOrderValue(order).fallback === 0).length}
          </div>
        </div>
        <div className="bg-gray-800 p-4 rounded-lg border border-gray-600">
          <div className="text-gray-400 text-sm">Premium Orders</div>
          <div className="text-2xl font-bold text-secondary">
            {orders.filter(order => order.order_type === 'premium_package' || order.order_type === 'premium_content').length}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDebugger;
