import React, { useState, useEffect } from 'react';
import { getSystemServices, getTechnicalServices } from '../lib/apiServices';
import { SystemService, TechnicalService } from '../lib/database';

interface DebugDataDisplayProps {
  show?: boolean;
}

const DebugDataDisplay: React.FC<DebugDataDisplayProps> = ({ show = false }) => {
  const [systems, setSystems] = useState<SystemService[]>([]);
  const [services, setServices] = useState<TechnicalService[]>([]);
  const [loading, setLoading] = useState(true);
  const [errors, setErrors] = useState<string[]>([]);
  const [apiCalls, setApiCalls] = useState<any[]>([]);

  useEffect(() => {
    if (show) {
      loadAllData();
    }
  }, [show]);

  const addLog = (message: string, type: 'info' | 'error' | 'success' = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setApiCalls(prev => [...prev, { timestamp, message, type }]);
    console.log(`[${timestamp}] ${message}`);
  };

  const loadAllData = async () => {
    setLoading(true);
    setErrors([]);
    setApiCalls([]);
    
    addLog('🔄 Starting data load process...');

    try {
      // Test Systems API
      addLog('📡 Calling getSystemServices()...');
      const systemsResult = await getSystemServices();
      addLog(`📊 Systems API response: ${JSON.stringify(systemsResult).substring(0, 100)}...`);
      
      if (systemsResult.error) {
        addLog(`❌ Systems API error: ${systemsResult.error.message}`, 'error');
        setErrors(prev => [...prev, `Systems: ${systemsResult.error.message}`]);
      } else {
        const systemsData = systemsResult.data || [];
        setSystems(systemsData);
        addLog(`✅ Systems loaded: ${systemsData.length} items`, 'success');
        
        const activeSystems = systemsData.filter(s => s.status === 'active');
        addLog(`🟢 Active systems: ${activeSystems.length}`, 'success');
      }

      // Test Services API
      addLog('📡 Calling getTechnicalServices()...');
      const servicesResult = await getTechnicalServices();
      addLog(`📊 Services API response: ${JSON.stringify(servicesResult).substring(0, 100)}...`);
      
      if (servicesResult.error) {
        addLog(`❌ Services API error: ${servicesResult.error.message}`, 'error');
        setErrors(prev => [...prev, `Services: ${servicesResult.error.message}`]);
      } else {
        const servicesData = servicesResult.data || [];
        setServices(servicesData);
        addLog(`✅ Services loaded: ${servicesData.length} items`, 'success');
        
        const activeServices = servicesData.filter(s => s.status === 'active');
        addLog(`🟢 Active services: ${activeServices.length}`, 'success');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      addLog(`💥 Unexpected error: ${errorMessage}`, 'error');
      setErrors(prev => [...prev, `Unexpected: ${errorMessage}`]);
    } finally {
      setLoading(false);
      addLog('🏁 Data load process completed');
    }
  };

  if (!show) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 max-h-96 bg-gray-900 border border-gray-700 rounded-lg shadow-2xl z-50 overflow-hidden">
      <div className="bg-gray-800 px-4 py-2 border-b border-gray-700">
        <h3 className="text-white font-bold text-sm">🔧 Debug Data Display</h3>
        <button
          onClick={loadAllData}
          className="text-xs bg-blue-600 text-white px-2 py-1 rounded mt-1 hover:bg-blue-700"
        >
          Reload Data
        </button>
      </div>
      
      <div className="p-4 overflow-y-auto max-h-80">
        {loading && (
          <div className="text-yellow-400 text-sm mb-2">
            🔄 Loading data...
          </div>
        )}

        {errors.length > 0 && (
          <div className="mb-4">
            <h4 className="text-red-400 font-bold text-sm mb-1">❌ Errors:</h4>
            {errors.map((error, index) => (
              <div key={index} className="text-red-300 text-xs bg-red-900/20 p-1 rounded mb-1">
                {error}
              </div>
            ))}
          </div>
        )}

        <div className="mb-4">
          <h4 className="text-green-400 font-bold text-sm mb-1">
            📊 Data Summary:
          </h4>
          <div className="text-xs text-gray-300 space-y-1">
            <div>Systems: {systems.length} total, {systems.filter(s => s.status === 'active').length} active</div>
            <div>Services: {services.length} total, {services.filter(s => s.status === 'active').length} active</div>
          </div>
        </div>

        {systems.length > 0 && (
          <div className="mb-4">
            <h4 className="text-blue-400 font-bold text-sm mb-1">🔧 Systems:</h4>
            {systems.slice(0, 3).map((system, index) => (
              <div key={index} className="text-xs text-gray-300 bg-gray-800/50 p-1 rounded mb-1">
                {system.name_ar} - ${system.price} - {system.status}
              </div>
            ))}
          </div>
        )}

        {services.length > 0 && (
          <div className="mb-4">
            <h4 className="text-purple-400 font-bold text-sm mb-1">⚙️ Services:</h4>
            {services.slice(0, 3).map((service, index) => (
              <div key={index} className="text-xs text-gray-300 bg-gray-800/50 p-1 rounded mb-1">
                {service.name_ar} - ${service.price} - {service.status}
              </div>
            ))}
          </div>
        )}

        <div>
          <h4 className="text-gray-400 font-bold text-sm mb-1">📝 API Calls Log:</h4>
          <div className="max-h-32 overflow-y-auto">
            {apiCalls.slice(-10).map((call, index) => (
              <div key={index} className={`text-xs p-1 rounded mb-1 ${
                call.type === 'error' ? 'text-red-300 bg-red-900/20' :
                call.type === 'success' ? 'text-green-300 bg-green-900/20' :
                'text-gray-300 bg-gray-800/50'
              }`}>
                [{call.timestamp}] {call.message}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugDataDisplay;
