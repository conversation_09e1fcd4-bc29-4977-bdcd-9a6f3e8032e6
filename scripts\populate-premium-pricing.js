#!/usr/bin/env node

/**
 * Populate Premium Pricing Script
 * 
 * Creates sample premium pricing data for systems and services
 */

const mysql = require('mysql2/promise');

const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khan<PERSON><PERSON><PERSON>_db',
  charset: 'utf8mb4'
};

// Generate UUID function
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

async function populatePremiumPricing() {
  console.log('💰 Populating Premium Pricing Data');
  console.log('===================================\n');
  
  let connection;
  
  try {
    connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ Connected to MySQL database\n');
    
    // 1. Get existing systems and create premium pricing
    console.log('1️⃣ Creating premium pricing for systems...');
    
    const [systems] = await connection.execute(`
      SELECT id, name_ar, name_en, price 
      FROM system_services 
      WHERE status = 'active'
      LIMIT 10
    `);
    
    if (systems.length === 0) {
      console.log('   ⚠️  No systems found, creating sample systems first...');
      
      // Create sample systems
      const sampleSystems = [
        {
          id: generateUUID(),
          name_ar: 'نظام حروب الروابط',
          name_en: 'Guild War System',
          description_ar: 'نظام متقدم لحروب الروابط مع واجهة سهلة الاستخدام',
          description_en: 'Advanced guild war system with user-friendly interface',
          price: 299.00,
          category: 'pvp'
        },
        {
          id: generateUUID(),
          name_ar: 'نظام الأورا',
          name_en: 'Aura System',
          description_ar: 'نظام الأورا المتقدم مع تأثيرات بصرية مذهلة',
          description_en: 'Advanced aura system with stunning visual effects',
          price: 199.00,
          category: 'visual'
        },
        {
          id: generateUUID(),
          name_ar: 'نظام اللغات المتعددة',
          name_en: 'Multi-Language System',
          description_ar: 'دعم اللغات المتعددة مع واجهة إدارية شاملة',
          description_en: 'Multi-language support with comprehensive admin interface',
          price: 149.00,
          category: 'localization'
        }
      ];
      
      for (const system of sampleSystems) {
        await connection.execute(`
          INSERT INTO system_services (
            id, name_ar, name_en, description_ar, description_en, 
            price, category, type, is_premium_addon, status, 
            features_ar, features_en, tech_specs_ar, tech_specs_en,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          system.id,
          system.name_ar,
          system.name_en,
          system.description_ar,
          system.description_en,
          system.price,
          system.category,
          'regular',
          1, // is_premium_addon
          'active',
          JSON.stringify(['ميزة متقدمة', 'سهولة الاستخدام', 'دعم فني']),
          JSON.stringify(['Advanced features', 'Easy to use', 'Technical support']),
          JSON.stringify(['متوافق مع Metin2', 'قاعدة بيانات MySQL']),
          JSON.stringify(['Compatible with Metin2', 'MySQL Database'])
        ]);
      }
      
      console.log('   ✅ Sample systems created');
      
      // Re-fetch systems
      const [newSystems] = await connection.execute(`
        SELECT id, name_ar, name_en, price 
        FROM system_services 
        WHERE status = 'active'
        LIMIT 10
      `);
      systems.push(...newSystems);
    }
    
    // Create premium pricing for systems
    for (const system of systems) {
      const premiumPrice = Math.round(system.price * 0.7); // 30% discount for premium customers
      
      await connection.execute(`
        INSERT IGNORE INTO premium_system_pricing (
          id, system_id, premium_price, installation_included, 
          maintenance_included, is_available_for_premium,
          description_ar, description_en, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        generateUUID(),
        system.id,
        premiumPrice,
        1, // installation_included
        1, // maintenance_included
        1, // is_available_for_premium
        `سعر مخصص للنسخة المميزة: ${system.name_ar}`,
        `Premium edition special price: ${system.name_en}`
      ]);
    }
    
    console.log(`   ✅ Created premium pricing for ${systems.length} systems`);
    
    // 2. Get existing services and create premium pricing
    console.log('\n2️⃣ Creating premium pricing for services...');
    
    const [services] = await connection.execute(`
      SELECT id, name_ar, name_en, price, subscription_type 
      FROM technical_services 
      WHERE status = 'active'
      LIMIT 10
    `);
    
    if (services.length === 0) {
      console.log('   ⚠️  No services found, creating sample services first...');
      
      // Create sample services
      const sampleServices = [
        {
          id: generateUUID(),
          name_ar: 'دعم فني شامل',
          name_en: 'Comprehensive Technical Support',
          description_ar: 'دعم فني 24/7 مع فريق متخصص',
          description_en: '24/7 technical support with specialized team',
          price: 500.00,
          subscription_type: 'monthly',
          category: 'support'
        },
        {
          id: generateUUID(),
          name_ar: 'إعداد الخوادم',
          name_en: 'Server Setup',
          description_ar: 'إعداد وتكوين الخوادم بشكل احترافي',
          description_en: 'Professional server setup and configuration',
          price: 250.00,
          subscription_type: 'none',
          category: 'setup'
        },
        {
          id: generateUUID(),
          name_ar: 'صيانة دورية',
          name_en: 'Regular Maintenance',
          description_ar: 'صيانة دورية للخوادم والأنظمة',
          description_en: 'Regular maintenance for servers and systems',
          price: 300.00,
          subscription_type: 'yearly',
          category: 'maintenance'
        }
      ];
      
      for (const service of sampleServices) {
        await connection.execute(`
          INSERT INTO technical_services (
            id, name_ar, name_en, description_ar, description_en, 
            price, category, service_type, is_premium_addon, 
            premium_price, subscription_type, status,
            features_ar, features_en, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          service.id,
          service.name_ar,
          service.name_en,
          service.description_ar,
          service.description_en,
          service.price,
          service.category,
          'support',
          1, // is_premium_addon
          Math.round(service.price * 0.6), // 40% discount for premium
          service.subscription_type,
          'active',
          JSON.stringify(['خدمة متميزة', 'استجابة سريعة', 'ضمان الجودة']),
          JSON.stringify(['Premium service', 'Quick response', 'Quality guarantee'])
        ]);
      }
      
      console.log('   ✅ Sample services created');
      
      // Re-fetch services
      const [newServices] = await connection.execute(`
        SELECT id, name_ar, name_en, price, subscription_type 
        FROM technical_services 
        WHERE status = 'active'
        LIMIT 10
      `);
      services.push(...newServices);
    }
    
    // Create premium pricing for services
    for (const service of services) {
      const premiumPrice = Math.round(service.price * 0.6); // 40% discount for premium customers
      const discountPercentage = service.subscription_type !== 'none' ? 25 : 0;
      
      await connection.execute(`
        INSERT IGNORE INTO premium_service_pricing (
          id, service_id, premium_price, installation_included, 
          maintenance_included, is_available_for_premium,
          subscription_discount_percentage, description_ar, description_en,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        generateUUID(),
        service.id,
        premiumPrice,
        1, // installation_included
        service.subscription_type !== 'none' ? 1 : 0, // maintenance_included for subscriptions
        1, // is_available_for_premium
        discountPercentage,
        `سعر مخصص للنسخة المميزة: ${service.name_ar}`,
        `Premium edition special price: ${service.name_en}`
      ]);
    }
    
    console.log(`   ✅ Created premium pricing for ${services.length} services`);
    
    // 3. Set one premium content as active
    console.log('\n3️⃣ Setting active premium edition...');
    
    // First, set all to inactive
    await connection.execute(`UPDATE premium_content SET is_active_edition = 0`);
    
    // Set the first one as active
    await connection.execute(`
      UPDATE premium_content 
      SET is_active_edition = 1 
      WHERE id = 'premium_complete_package'
    `);
    
    console.log('   ✅ Set premium_complete_package as active edition');
    
    console.log('\n🎉 Premium pricing data populated successfully!');
    
  } catch (error) {
    console.error('❌ Population failed:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run population
populatePremiumPricing().catch(error => {
  console.error('Population failed:', error);
  process.exit(1);
});
