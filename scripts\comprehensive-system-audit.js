const axios = require('axios');
const mysql = require('mysql2/promise');

/**
 * Comprehensive System Audit
 * 
 * Performs deep analysis of:
 * 1. Database vs Frontend data consistency
 * 2. Premium Edition settings integration
 * 3. Statistics accuracy
 * 4. Soft delete vs hard delete issues
 * 5. API response consistency
 */

class SystemAudit {
  constructor() {
    this.connection = null;
    this.adminToken = null;
    this.issues = [];
    this.statistics = {};
  }

  async connect() {
    this.connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'khanfashariya_db'
    });
    
    // Get admin token
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    this.adminToken = loginResponse.data.data.tokens.accessToken;
  }

  async runCompleteAudit() {
    console.log('🔍 Starting Comprehensive System Audit');
    console.log('=' .repeat(60));
    
    await this.connect();
    
    // 1. Database Analysis
    await this.auditDatabase();
    
    // 2. API Consistency Check
    await this.auditApiConsistency();
    
    // 3. Premium Edition Settings
    await this.auditPremiumSettings();
    
    // 4. Statistics Accuracy
    await this.auditStatistics();
    
    // 5. Data Consistency
    await this.auditDataConsistency();
    
    // 6. Generate Report
    this.generateAuditReport();
    
    await this.connection.end();
  }

  async auditDatabase() {
    console.log('\n🗄️ Database Audit...');
    
    // Check system_services table
    const [systems] = await this.connection.execute(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive,
        COUNT(CASE WHEN status = 'deleted' THEN 1 END) as deleted,
        COUNT(CASE WHEN is_premium_addon = 1 THEN 1 END) as premium,
        AVG(price) as avg_price,
        SUM(price) as total_value
      FROM system_services
    `);
    
    const systemStats = systems[0];
    this.statistics.database = {
      systems: systemStats,
      timestamp: new Date().toISOString()
    };
    
    console.log(`   📊 Total Systems: ${systemStats.total}`);
    console.log(`   ✅ Active: ${systemStats.active}`);
    console.log(`   ❌ Inactive: ${systemStats.inactive}`);
    console.log(`   🗑️ Deleted: ${systemStats.deleted}`);
    console.log(`   👑 Premium: ${systemStats.premium}`);
    console.log(`   💰 Average Price: $${parseFloat(systemStats.avg_price || 0).toFixed(2)}`);
    
    // Check for orphaned or inconsistent data
    const [orphanedSystems] = await this.connection.execute(`
      SELECT id, name_ar, status, is_premium_addon, price
      FROM system_services 
      WHERE status NOT IN ('active', 'inactive', 'deleted', 'draft')
    `);
    
    if (orphanedSystems.length > 0) {
      this.issues.push({
        type: 'DATA_INCONSISTENCY',
        severity: 'HIGH',
        description: `Found ${orphanedSystems.length} systems with invalid status`,
        data: orphanedSystems
      });
    }
    
    // Check premium settings completeness
    const [incompletePremium] = await this.connection.execute(`
      SELECT id, name_ar, is_premium_addon, price
      FROM system_services 
      WHERE is_premium_addon = 1 AND (price IS NULL OR price = 0)
    `);
    
    if (incompletePremium.length > 0) {
      this.issues.push({
        type: 'PREMIUM_SETTINGS_INCOMPLETE',
        severity: 'HIGH',
        description: `Found ${incompletePremium.length} premium systems with missing price`,
        data: incompletePremium
      });
    }
  }

  async auditApiConsistency() {
    console.log('\n🌐 API Consistency Audit...');
    
    // Get data from different API endpoints
    const publicSystems = await axios.get('http://localhost:3001/api/systems');
    const adminHeaders = { Authorization: `Bearer ${this.adminToken}` };
    const adminSystems = await axios.get('http://localhost:3001/api/systems/admin', { headers: adminHeaders });
    
    const publicCount = publicSystems.data.data.systems.length;
    const adminCount = adminSystems.data.length;
    const dbActiveCount = this.statistics.database.systems.active;
    const dbTotalCount = this.statistics.database.systems.total;
    
    console.log(`   📊 Public API: ${publicCount} systems`);
    console.log(`   📊 Admin API: ${adminCount} systems`);
    console.log(`   📊 DB Active: ${dbActiveCount} systems`);
    console.log(`   📊 DB Total: ${dbTotalCount} systems`);
    
    // Check consistency
    if (publicCount !== dbActiveCount) {
      this.issues.push({
        type: 'API_DB_INCONSISTENCY',
        severity: 'CRITICAL',
        description: `Public API shows ${publicCount} systems but DB has ${dbActiveCount} active systems`,
        data: { publicCount, dbActiveCount }
      });
    }
    
    if (adminCount !== dbTotalCount) {
      this.issues.push({
        type: 'ADMIN_API_INCONSISTENCY',
        severity: 'HIGH',
        description: `Admin API shows ${adminCount} systems but DB has ${dbTotalCount} total systems`,
        data: { adminCount, dbTotalCount }
      });
    }
    
    // Check premium systems consistency
    const publicPremiumCount = publicSystems.data.data.systems.filter(s => s.is_premium_addon).length;
    const dbPremiumCount = this.statistics.database.systems.premium;
    
    if (publicPremiumCount !== dbPremiumCount) {
      this.issues.push({
        type: 'PREMIUM_COUNT_INCONSISTENCY',
        severity: 'HIGH',
        description: `Public API shows ${publicPremiumCount} premium systems but DB has ${dbPremiumCount}`,
        data: { publicPremiumCount, dbPremiumCount }
      });
    }
  }

  async auditPremiumSettings() {
    console.log('\n👑 Premium Edition Settings Audit...');
    
    // Check if premium_content table exists and has proper structure
    try {
      const [premiumContent] = await this.connection.execute('SELECT * FROM premium_content LIMIT 1');
      console.log(`   📋 Premium content table: ${premiumContent.length > 0 ? 'Has data' : 'Empty'}`);
    } catch (error) {
      this.issues.push({
        type: 'PREMIUM_TABLE_MISSING',
        severity: 'CRITICAL',
        description: 'Premium content table missing or inaccessible',
        data: { error: error.message }
      });
    }
    
    // Check premium systems for missing settings
    const [premiumSystems] = await this.connection.execute(`
      SELECT 
        id, name_ar, name_en, price, is_premium_addon,
        features_ar, features_en, tech_specs_ar, tech_specs_en
      FROM system_services 
      WHERE is_premium_addon = 1
    `);
    
    console.log(`   👑 Premium systems found: ${premiumSystems.length}`);
    
    for (const system of premiumSystems) {
      const issues = [];
      
      if (!system.price || system.price === 0) {
        issues.push('Missing price');
      }
      
      if (!system.features_ar || (typeof system.features_ar === 'string' && JSON.parse(system.features_ar).length === 0)) {
        issues.push('Missing Arabic features');
      }
      
      if (!system.features_en || (typeof system.features_en === 'string' && JSON.parse(system.features_en).length === 0)) {
        issues.push('Missing English features');
      }
      
      if (issues.length > 0) {
        this.issues.push({
          type: 'PREMIUM_SYSTEM_INCOMPLETE',
          severity: 'MEDIUM',
          description: `Premium system "${system.name_ar}" has issues: ${issues.join(', ')}`,
          data: { systemId: system.id, issues }
        });
      }
    }
  }

  async auditStatistics() {
    console.log('\n📊 Statistics Accuracy Audit...');
    
    // Calculate statistics from database
    const [statsFromDB] = await this.connection.execute(`
      SELECT 
        COUNT(*) as total_systems,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_systems,
        COUNT(CASE WHEN is_premium_addon = 1 THEN 1 END) as premium_systems,
        AVG(CASE WHEN status = 'active' THEN price END) as avg_price,
        SUM(CASE WHEN status = 'active' THEN price END) as total_value,
        MAX(price) as max_price,
        MIN(CASE WHEN price > 0 THEN price END) as min_price
      FROM system_services
    `);
    
    const dbStats = statsFromDB[0];
    
    // Get statistics from admin API
    try {
      const adminHeaders = { Authorization: `Bearer ${this.adminToken}` };
      const adminSystems = await axios.get('http://localhost:3001/api/systems/admin', { headers: adminHeaders });
      
      const apiSystems = adminSystems.data;
      const apiStats = {
        total_systems: apiSystems.length,
        active_systems: apiSystems.filter(s => s.status === 'active').length,
        premium_systems: apiSystems.filter(s => s.is_premium_addon).length,
        avg_price: apiSystems.filter(s => s.status === 'active').reduce((sum, s) => sum + parseFloat(s.price || 0), 0) / apiSystems.filter(s => s.status === 'active').length,
        total_value: apiSystems.filter(s => s.status === 'active').reduce((sum, s) => sum + parseFloat(s.price || 0), 0)
      };
      
      console.log('   📊 Database Statistics:');
      console.log(`      Total: ${dbStats.total_systems}, Active: ${dbStats.active_systems}, Premium: ${dbStats.premium_systems}`);
      console.log(`      Avg Price: $${parseFloat(dbStats.avg_price || 0).toFixed(2)}, Total Value: $${parseFloat(dbStats.total_value || 0).toFixed(2)}`);
      
      console.log('   📊 API Statistics:');
      console.log(`      Total: ${apiStats.total_systems}, Active: ${apiStats.active_systems}, Premium: ${apiStats.premium_systems}`);
      console.log(`      Avg Price: $${(apiStats.avg_price || 0).toFixed(2)}, Total Value: $${(apiStats.total_value || 0).toFixed(2)}`);
      
      // Compare statistics
      const tolerance = 0.01; // Allow small floating point differences
      
      if (Math.abs(parseFloat(dbStats.avg_price || 0) - (apiStats.avg_price || 0)) > tolerance) {
        this.issues.push({
          type: 'STATISTICS_MISMATCH',
          severity: 'HIGH',
          description: `Average price mismatch: DB=$${parseFloat(dbStats.avg_price || 0).toFixed(2)}, API=$${(apiStats.avg_price || 0).toFixed(2)}`,
          data: { dbAvgPrice: dbStats.avg_price, apiAvgPrice: apiStats.avg_price }
        });
      }
      
      if (Math.abs(parseFloat(dbStats.total_value || 0) - (apiStats.total_value || 0)) > tolerance) {
        this.issues.push({
          type: 'REVENUE_CALCULATION_ERROR',
          severity: 'HIGH',
          description: `Total value mismatch: DB=$${parseFloat(dbStats.total_value || 0).toFixed(2)}, API=$${(apiStats.total_value || 0).toFixed(2)}`,
          data: { dbTotalValue: dbStats.total_value, apiTotalValue: apiStats.total_value }
        });
      }
      
    } catch (error) {
      this.issues.push({
        type: 'STATISTICS_API_ERROR',
        severity: 'HIGH',
        description: 'Failed to retrieve statistics from API',
        data: { error: error.message }
      });
    }
  }

  async auditDataConsistency() {
    console.log('\n🔄 Data Consistency Audit...');
    
    // Check for soft delete vs hard delete issues
    const [deletedSystems] = await this.connection.execute(`
      SELECT id, name_ar, status, created_at, updated_at
      FROM system_services 
      WHERE status = 'deleted'
      ORDER BY updated_at DESC
    `);
    
    console.log(`   🗑️ Systems marked as deleted: ${deletedSystems.length}`);
    
    if (deletedSystems.length > 0) {
      console.log('   📋 Recently deleted systems:');
      deletedSystems.slice(0, 3).forEach((system, index) => {
        console.log(`      ${index + 1}. ${system.name_ar} (${system.updated_at})`);
      });
      
      this.issues.push({
        type: 'SOFT_DELETE_INCONSISTENCY',
        severity: 'MEDIUM',
        description: `Found ${deletedSystems.length} soft-deleted systems that may cause count discrepancies`,
        data: { deletedCount: deletedSystems.length, recentDeleted: deletedSystems.slice(0, 5) }
      });
    }
    
    // Check for data integrity issues
    const [integrityIssues] = await this.connection.execute(`
      SELECT 
        id, name_ar, name_en, price, status, is_premium_addon,
        CASE 
          WHEN name_ar IS NULL OR name_ar = '' THEN 'Missing Arabic name'
          WHEN name_en IS NULL OR name_en = '' THEN 'Missing English name'
          WHEN price IS NULL THEN 'Missing price'
          WHEN price < 0 THEN 'Negative price'
          WHEN is_premium_addon = 1 AND price = 0 THEN 'Premium with zero price'
          ELSE 'OK'
        END as issue
      FROM system_services
      WHERE 
        name_ar IS NULL OR name_ar = '' OR
        name_en IS NULL OR name_en = '' OR
        price IS NULL OR price < 0 OR
        (is_premium_addon = 1 AND price = 0)
    `);
    
    if (integrityIssues.length > 0) {
      this.issues.push({
        type: 'DATA_INTEGRITY_ISSUES',
        severity: 'HIGH',
        description: `Found ${integrityIssues.length} systems with data integrity issues`,
        data: integrityIssues
      });
    }
  }

  generateAuditReport() {
    console.log('\n' + '=' .repeat(60));
    console.log('📋 COMPREHENSIVE AUDIT REPORT');
    console.log('=' .repeat(60));
    
    const criticalIssues = this.issues.filter(i => i.severity === 'CRITICAL');
    const highIssues = this.issues.filter(i => i.severity === 'HIGH');
    const mediumIssues = this.issues.filter(i => i.severity === 'MEDIUM');
    
    console.log(`\n🚨 Critical Issues: ${criticalIssues.length}`);
    console.log(`⚠️ High Priority Issues: ${highIssues.length}`);
    console.log(`📝 Medium Priority Issues: ${mediumIssues.length}`);
    console.log(`📊 Total Issues Found: ${this.issues.length}`);
    
    if (this.issues.length === 0) {
      console.log('\n🎉 No issues found! System is in excellent condition.');
      return;
    }
    
    console.log('\n📋 DETAILED ISSUES:');
    
    [...criticalIssues, ...highIssues, ...mediumIssues].forEach((issue, index) => {
      const icon = issue.severity === 'CRITICAL' ? '🚨' : issue.severity === 'HIGH' ? '⚠️' : '📝';
      console.log(`\n${icon} ${index + 1}. ${issue.type} (${issue.severity})`);
      console.log(`   Description: ${issue.description}`);
      if (issue.data && typeof issue.data === 'object') {
        console.log(`   Data: ${JSON.stringify(issue.data, null, 2).substring(0, 200)}...`);
      }
    });
    
    console.log('\n🎯 RECOMMENDED ACTIONS:');
    console.log('1. Fix critical issues immediately');
    console.log('2. Address high priority issues');
    console.log('3. Plan resolution for medium priority issues');
    console.log('4. Implement monitoring to prevent future issues');
    
    console.log('\n📅 Audit completed at:', new Date().toISOString());
  }
}

// Main execution
async function main() {
  const audit = new SystemAudit();
  await audit.runCompleteAudit();
}

// Export for use in other scripts
module.exports = SystemAudit;

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
