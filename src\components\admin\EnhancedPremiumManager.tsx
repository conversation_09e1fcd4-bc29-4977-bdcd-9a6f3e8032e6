import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotification } from '../../hooks/useNotification';
import {
  getSystemServices,
  getAdminSystemServices,
  getTechnicalServices,
  getPremiumContent,
  createPremiumContent,
  updatePremiumContent,
  deletePremiumContent,
} from '../../lib/apiServices';
import {
  SystemService,
  TechnicalService,
  PremiumContent,
  TranslatedText
} from '../../lib/database';
import { 
  Crown, 
  Plus, 
  Edit, 
  Trash2,
  Eye,
  Package,
  Star,
  Image as ImageIcon,
  Play,
  DollarSign,
  CheckCircle,
  XCircle,
  Search,
  Filter,
  Download,
  Upload,
  Settings,
  ArrowLeft,
  Save,
  X,
  Percent,
  Tag,
  Calendar,
  Users,
  TrendingUp,
  BarChart3
} from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Modal from '../ui/Modal';
import Card from '../ui/Card';
import GoldenFilterGrid from '../ui/GoldenFilterGrid';
import { useButtonActions } from '../../utils/buttonActions';
import GoldenButton from '../ui/GoldenButton';
import { PREMIUM_CONTENT_FILTERS } from '../../constants/filterOptions';

interface EnhancedPremiumManagerProps {
  onBack?: () => void;
}

/**
 * Premium Edition Manager - Manages the single Premium Edition product with add-on selection
 */
const EnhancedPremiumManager: React.FC<EnhancedPremiumManagerProps> = ({ onBack }) => {
  const { t, language } = useTranslation();
  const { showNotification } = useNotification();
  const buttonActions = useButtonActions();

  const [currentView, setCurrentView] = useState<'overview' | 'systems' | 'services' | 'configuration'>('overview');
  const [premiumEdition, setPremiumEdition] = useState<PremiumContent | null>(null);
  const [availableSystems, setAvailableSystems] = useState<SystemService[]>([]);
  const [availableServices, setAvailableServices] = useState<TechnicalService[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingEdition, setEditingEdition] = useState(false);

  // Premium Edition configuration state
  const [selectedSystems, setSelectedSystems] = useState<string[]>([]);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);

  // Form state for Premium Edition
  const [formData, setFormData] = useState<Partial<PremiumContent>>({
    name: { ar: 'النسخة المميزة', en: 'Premium Edition' },
    description: { ar: 'النسخة الشاملة المميزة مع جميع الأنظمة والخدمات', en: 'Comprehensive Premium Edition with all systems and services' },
    detailed_description: { ar: '', en: '' },
    features: { ar: [], en: [] },
    technical_specs: { ar: [], en: [] },
    images: [],
    videos: [],
    price: 1999,
    original_price: 2999,
    category: 'premium_edition',
    status: 'active'
  });

  // Add-on pricing configuration
  const [addonPricing, setAddonPricing] = useState<{
    systemPrices: Record<string, number>;
    servicePrices: Record<string, number>;
  }>({
    systemPrices: {},
    servicePrices: {}
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [premiumResult, systemsResult, servicesResult] = await Promise.all([
        getPremiumContent(),
        getAdminSystemServices(), // Use admin endpoint to get all systems
        getTechnicalServices()
      ]);

      // Load existing Premium Edition or create default
      if (premiumResult.data && premiumResult.data.length > 0) {
        const existingPremium = premiumResult.data.find(item => item.category === 'premium_edition');
        if (existingPremium) {
          setPremiumEdition(existingPremium);
          setFormData(existingPremium);
        }
      }

      // Load available systems and services
      if (systemsResult.data) setAvailableSystems(systemsResult.data);
      if (servicesResult.data) {
        // Filter services that can be Premium Edition add-ons
        const premiumServices = servicesResult.data.filter(service => service.isPremiumAddon);
        setAvailableServices(premiumServices);
      }
    } catch (error) {
      console.error('Error loading premium content:', error);
      showNotification({
        type: 'error',
        message: t('notifications.loadFailed', 'Failed to load premium content')
      });
    }
    setLoading(false);
  };

  const handleEditPremiumEdition = () => {
    setEditingEdition(true);
    setShowModal(true);
  };

  const handleSystemToggle = (systemId: string) => {
    setSelectedSystems(prev =>
      prev.includes(systemId)
        ? prev.filter(id => id !== systemId)
        : [...prev, systemId]
    );
  };

  const handleServiceToggle = (serviceId: string) => {
    setSelectedServices(prev =>
      prev.includes(serviceId)
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const handleAddonPriceChange = (type: 'system' | 'service', id: string, price: number) => {
    if (type === 'system') {
      setAddonPricing(prev => ({
        ...prev,
        systemPrices: { ...prev.systemPrices, [id]: price }
      }));
    } else {
      setAddonPricing(prev => ({
        ...prev,
        servicePrices: { ...prev.servicePrices, [id]: price }
      }));
    }
  };

  const handleSavePremiumEdition = async () => {
    try {
      // Validate required fields
      if (!formData.name?.ar || !formData.name?.en ||
          !formData.description?.ar || !formData.description?.en ||
          !formData.price) {
        showNotification({
          type: 'error',
          message: language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields'
        });
        return;
      }

      // Prepare Premium Edition data
      const premiumData = {
        name_ar: formData.name?.ar || '',
        name_en: formData.name?.en || '',
        description_ar: formData.description?.ar || '',
        description_en: formData.description?.en || '',
        features_ar: formData.features?.ar || [],
        features_en: formData.features?.en || [],
        tech_specs_ar: formData.tech_specs?.ar || [],
        tech_specs_en: formData.tech_specs?.en || [],
        price: formData.price || 0,
        category: 'premium_edition',
        type: formData.type || 'regular',
        status: formData.status || 'active',
        video_url: formData.video_url || '',
        image_url: formData.image_url || '',
        gallery_images: formData.gallery_images || [],
        technical_specs: formData.technical_specs || { ar: [], en: [] },
        images: formData.images || [],
        videos: formData.videos || []
      };

      if (premiumEdition) {
        await updatePremiumContent(premiumEdition.id, premiumData);
      } else {
        const result = await createPremiumContent(premiumData as Omit<PremiumContent, 'id' | 'created_at' | 'updated_at'>);
        if (result.data) {
          setPremiumEdition(result.data);
        }
      }

      await loadData();
      setShowModal(false);
      setEditingEdition(false);

      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم حفظ النسخة المميزة بنجاح' : 'Premium Edition saved successfully'
      });
    } catch (error) {
      console.error('Error saving Premium Edition:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في حفظ النسخة المميزة' : 'Failed to save Premium Edition'
      });
    }
  };

  const handleTextChange = (lang: 'ar' | 'en', field: string, value: string) => {
    if (currentView === 'systems') {
      setSystemFormData(prev => ({
        ...prev,
        [field]: {
          ...(prev[field as keyof typeof prev] as TranslatedText || { ar: '', en: '' }),
          [lang]: value
        }
      }));
    } else {
      setPackageFormData(prev => ({
        ...prev,
        [field]: {
          ...(prev[field as keyof typeof prev] as TranslatedText || { ar: '', en: '' }),
          [lang]: value
        }
      }));
    }
  };

  const handleArrayChange = (lang: 'ar' | 'en', field: string, value: string) => {
    const items = value.split('\n').filter(item => item.trim());
    if (currentView === 'systems') {
      setSystemFormData(prev => ({
        ...prev,
        [field]: {
          ...(prev[field as keyof typeof prev] as { ar: string[]; en: string[] } || { ar: [], en: [] }),
          [lang]: items
        }
      }));
    } else {
      setPackageFormData(prev => ({
        ...prev,
        [field]: {
          ...(prev[field as keyof typeof prev] as { ar: string[]; en: string[] } || { ar: [], en: [] }),
          [lang]: items
        }
      }));
    }
  };



  const calculateDiscountedPrice = (originalPrice: number, discountPercentage: number) => {
    return originalPrice - (originalPrice * discountPercentage / 100);
  };

  // Handle filter changes
  const handleFilterChange = (key: string, value: string) => {
    setFilterValues(prev => ({ ...prev, [key]: value }));
  };

  const filteredSystems = premiumSystems.filter(system => {
    const matchesSearch = searchTerm === '' ||
      getSystemText(system, 'name', language).toLowerCase().includes(searchTerm.toLowerCase()) ||
      getSystemText(system, 'description', language).toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = filterValues.status === 'all' || system.status === filterValues.status;
    const matchesCategory = filterValues.category === 'all' || system.category === filterValues.category;

    // Price range filter
    const matchesPriceRange = filterValues.priceRange === 'all' || (() => {
      const price = system.price;
      switch (filterValues.priceRange) {
        case '0-50': return price >= 0 && price <= 50;
        case '50-100': return price > 50 && price <= 100;
        case '100-200': return price > 100 && price <= 200;
        case '200-500': return price > 200 && price <= 500;
        case '500+': return price > 500;
        default: return true;
      }
    })();

    return matchesSearch && matchesStatus && matchesCategory && matchesPriceRange;
  });

  const filteredPackages = premiumPackages.filter(pkg => {
    const matchesSearch = searchTerm === '' ||
      pkg.name[language].toLowerCase().includes(searchTerm.toLowerCase()) ||
      pkg.description[language].toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = filterValues.status === 'all' || pkg.status === filterValues.status;

    // Price range filter
    const matchesPriceRange = filterValues.priceRange === 'all' || (() => {
      const price = pkg.price;
      switch (filterValues.priceRange) {
        case '0-50': return price >= 0 && price <= 50;
        case '50-100': return price > 50 && price <= 100;
        case '100-200': return price > 100 && price <= 200;
        case '200-500': return price > 200 && price <= 500;
        case '500+': return price > 500;
        default: return true;
      }
    })();

    return matchesSearch && matchesStatus && matchesPriceRange;
  });

  // Sort filtered data
  const sortedSystems = [...filteredSystems].sort((a, b) => {
    switch (filterValues.sortBy) {
      case 'price':
        return a.price - b.price;
      case 'category':
        return a.category.localeCompare(b.category);
      case 'created':
        return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
      case 'updated':
        return new Date(b.updated_at || '').getTime() - new Date(a.updated_at || '').getTime();
      case 'name':
      default:
        return a.name[language].localeCompare(b.name[language]);
    }
  });

  const sortedPackages = [...filteredPackages].sort((a, b) => {
    switch (filterValues.sortBy) {
      case 'price':
        return a.price - b.price;
      case 'created':
        return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
      case 'updated':
        return new Date(b.updated_at || '').getTime() - new Date(a.updated_at || '').getTime();
      case 'name':
      default:
        return a.name[language].localeCompare(b.name[language]);
    }
  });

  const getStatusIcon = (status: string) => {
    return status === 'active' ? 
      <CheckCircle className="w-4 h-4 text-green-400" /> : 
      <XCircle className="w-4 h-4 text-red-400" />;
  };

  const getStatusColor = (status: string) => {
    return status === 'active' ? 
      'bg-green-500/20 text-green-400 border-green-500/30' : 
      'bg-red-500/20 text-red-400 border-red-500/30';
  };

  // Analytics data
  const analytics = {
    totalSystems: premiumSystems.length,
    activeSystems: premiumSystems.filter(s => s.status === 'active').length,
    totalPackages: premiumPackages.length,
    activePackages: premiumPackages.filter(p => p.status === 'active').length,
    totalRevenue: [...premiumSystems.filter(s => s.status === 'active'), ...premiumPackages.filter(p => p.status === 'active')].reduce((sum, item) => sum + item.price, 0),
    averageDiscount: [...premiumSystems, ...premiumPackages].reduce((sum, item) => sum + (item.discount_percentage || 0), 0) / (premiumSystems.length + premiumPackages.length)
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-secondary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          {onBack && (
            <Button variant="outline" onClick={onBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              {t('common.back')}
            </Button>
          )}
          <h1 className="text-2xl font-bold text-white flex items-center">
            <Crown className="w-6 h-6 mr-2 text-secondary" />
            {t('admin.dashboard.premium')}
          </h1>
        </div>

        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Button variant="outline" onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
            <Package className="w-4 h-4 mr-2" />
            {viewMode === 'grid' ? 'List' : 'Grid'}
          </Button>
          <Button variant="primary" onClick={handleCreate}>
            <Plus className="w-4 h-4 mr-2" />
            {currentView === 'systems'
              ? (language === 'ar' ? 'إضافة نظام مميز' : 'Add Premium System')
              : (language === 'ar' ? 'إضافة حزمة مميزة' : 'Add Premium Package')
            }
          </Button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 rtl:space-x-reverse mb-6 bg-primary/50 p-1 rounded-lg">
        <button
          onClick={() => setCurrentView('systems')}
          className={`flex-1 py-2 px-4 rounded-md transition-all duration-200 flex items-center justify-center space-x-2 rtl:space-x-reverse ${
            currentView === 'systems'
              ? 'bg-secondary text-primary font-semibold'
              : 'text-gray-300 hover:text-white'
          }`}
        >
          <Package className="w-4 h-4" />
          <span>{language === 'ar' ? 'الأنظمة المميزة' : 'Premium Systems'}</span>
        </button>
        <button
          onClick={() => setCurrentView('packages')}
          className={`flex-1 py-2 px-4 rounded-md transition-all duration-200 flex items-center justify-center space-x-2 rtl:space-x-reverse ${
            currentView === 'packages'
              ? 'bg-secondary text-primary font-semibold'
              : 'text-gray-300 hover:text-white'
          }`}
        >
          <Crown className="w-4 h-4" />
          <span>{language === 'ar' ? 'الحزم المميزة' : 'Premium Packages'}</span>
        </button>
        <button
          onClick={() => setCurrentView('analytics')}
          className={`flex-1 py-2 px-4 rounded-md transition-all duration-200 flex items-center justify-center space-x-2 rtl:space-x-reverse ${
            currentView === 'analytics'
              ? 'bg-secondary text-primary font-semibold'
              : 'text-gray-300 hover:text-white'
          }`}
        >
          <BarChart3 className="w-4 h-4" />
          <span>{language === 'ar' ? 'التحليلات' : 'Analytics'}</span>
        </button>
      </div>

      {/* Analytics View */}
      {currentView === 'analytics' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <Card>
              <Card.Body>
                <div className="text-center">
                  <Package className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                  <p className="text-gray-400 text-sm">{language === 'ar' ? 'إجمالي الأنظمة' : 'Total Systems'}</p>
                  <p className="text-2xl font-bold text-white">{analytics.totalSystems}</p>
                </div>
              </Card.Body>
            </Card>

            <Card>
              <Card.Body>
                <div className="text-center">
                  <CheckCircle className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <p className="text-gray-400 text-sm">{language === 'ar' ? 'الأنظمة النشطة' : 'Active Systems'}</p>
                  <p className="text-2xl font-bold text-white">{analytics.activeSystems}</p>
                </div>
              </Card.Body>
            </Card>

            <Card>
              <Card.Body>
                <div className="text-center">
                  <Crown className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                  <p className="text-gray-400 text-sm">{language === 'ar' ? 'إجمالي الحزم' : 'Total Packages'}</p>
                  <p className="text-2xl font-bold text-white">{analytics.totalPackages}</p>
                </div>
              </Card.Body>
            </Card>

            <Card>
              <Card.Body>
                <div className="text-center">
                  <Star className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
                  <p className="text-gray-400 text-sm">{language === 'ar' ? 'الحزم النشطة' : 'Active Packages'}</p>
                  <p className="text-2xl font-bold text-white">{analytics.activePackages}</p>
                </div>
              </Card.Body>
            </Card>

            <Card>
              <Card.Body>
                <div className="text-center">
                  <DollarSign className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <p className="text-gray-400 text-sm">{language === 'ar' ? 'إجمالي القيمة' : 'Total Value'}</p>
                  <p className="text-2xl font-bold text-white">${analytics.totalRevenue}</p>
                </div>
              </Card.Body>
            </Card>

            <Card>
              <Card.Body>
                <div className="text-center">
                  <Percent className="w-8 h-8 text-orange-400 mx-auto mb-2" />
                  <p className="text-gray-400 text-sm">{language === 'ar' ? 'متوسط الخصم' : 'Avg Discount'}</p>
                  <p className="text-2xl font-bold text-white">{analytics.averageDiscount.toFixed(1)}%</p>
                </div>
              </Card.Body>
            </Card>
          </div>

          {/* Charts and detailed analytics would go here */}
          <Card>
            <Card.Header>
              <h3 className="text-xl font-semibold text-white">
                {language === 'ar' ? 'تحليلات مفصلة' : 'Detailed Analytics'}
              </h3>
            </Card.Header>
            <Card.Body>
              <div className="text-center py-12">
                <TrendingUp className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400">
                  {language === 'ar' ? 'التحليلات المفصلة قيد التطوير' : 'Detailed analytics coming soon'}
                </p>
              </div>
            </Card.Body>
          </Card>
        </div>
      )}

      {/* Content View (Systems or Packages) */}
      {currentView !== 'analytics' && (
        <>
          {/* Enhanced Filters and Search - Golden Ratio Design */}
          <GoldenFilterGrid
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            searchPlaceholder={`Search ${currentView === 'systems' ? 'systems' : 'packages'}...`}
            searchPlaceholderAr={`البحث في ${currentView === 'systems' ? 'الأنظمة' : 'الحزم'}...`}
            filters={PREMIUM_CONTENT_FILTERS}
            filterValues={filterValues}
            onFilterChange={handleFilterChange}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
            resultCount={currentView === 'systems' ? sortedSystems.length : sortedPackages.length}
            onExport={() => buttonActions.exportData(currentView as any, currentView === 'systems' ? sortedSystems : sortedPackages)}
            onImport={() => buttonActions.importData(currentView as any)}
            onAdvancedSettings={() => buttonActions.openAdvancedSettings('premium-content')}
            compact={false}
            position="top"
            className="enhanced-premium-content-filter"
          />

          {/* Content Grid/List */}
          {(currentView === 'systems' ? sortedSystems : sortedPackages).length === 0 ? (
            <Card>
              <Card.Body>
                <div className="text-center py-12">
                  {currentView === 'systems' ?
                    <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" /> :
                    <Crown className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  }
                  <h3 className="text-xl font-semibold text-white mb-2">
                    {currentView === 'systems'
                      ? (language === 'ar' ? 'لا توجد أنظمة مميزة' : 'No Premium Systems')
                      : (language === 'ar' ? 'لا توجد حزم مميزة' : 'No Premium Packages')
                    }
                  </h3>
                  <p className="text-gray-400 mb-6">
                    {language === 'ar' ? 'ابدأ بإنشاء محتوى مميز جديد' : 'Start by creating new premium content'}
                  </p>
                  <Button variant="primary" onClick={handleCreate}>
                    <Plus className="w-4 h-4 mr-2" />
                    {currentView === 'systems'
                      ? (language === 'ar' ? 'إضافة نظام مميز' : 'Add Premium System')
                      : (language === 'ar' ? 'إضافة حزمة مميزة' : 'Add Premium Package')
                    }
                  </Button>
                </div>
              </Card.Body>
            </Card>
          ) : (
            <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
              {(currentView === 'systems' ? sortedSystems : sortedPackages).map((item) => (
                <Card key={item.id} className="hover:border-secondary/50 transition-all duration-300">
                  <Card.Body>
                    {viewMode === 'grid' ? (
                      // Grid View
                      <div className="space-y-4">
                        {/* Image */}
                        <div className="relative h-32 bg-gradient-to-br from-secondary/20 to-accent/20 rounded-lg overflow-hidden">
                          {'image_url' in item && item.image_url ? (
                            <img
                              src={item.image_url}
                              alt={item.name[language]}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              {currentView === 'systems' ?
                                <Package className="w-8 h-8 text-secondary" /> :
                                <Crown className="w-8 h-8 text-secondary" />
                              }
                            </div>
                          )}
                          <div className="absolute top-2 right-2">
                            <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(item.status)}`}>
                              {getStatusIcon(item.status)}
                              <span className="ml-1">{t(`admin.dashboard.${item.status}`)}</span>
                            </span>
                          </div>
                          {item.discount_percentage && item.discount_percentage > 0 && (
                            <div className="absolute top-2 left-2">
                              <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                                -{item.discount_percentage}%
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Content */}
                        <div>
                          <h3 className="text-lg font-semibold text-white mb-2 line-clamp-1">
                            {item.name[language]}
                          </h3>
                          <p className="text-gray-300 text-sm mb-3 line-clamp-2">
                            {item.description[language]}
                          </p>

                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-2 rtl:space-x-reverse">
                              {item.original_price && item.original_price > item.price ? (
                                <>
                                  <span className="text-secondary font-bold text-lg">${item.price}</span>
                                  <span className="text-gray-400 line-through text-sm">${item.original_price}</span>
                                </>
                              ) : (
                                <span className="text-secondary font-bold text-lg">${item.price}</span>
                              )}
                            </div>
                            {'category' in item && (
                              <span className="text-xs text-gray-400 bg-accent/20 px-2 py-1 rounded">
                                {item.category}
                              </span>
                            )}
                          </div>

                          {/* Features */}
                          <div className="space-y-1 mb-4">
                            {item.features[language].slice(0, 2).map((feature, index) => (
                              <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                                <Star className="w-3 h-3 text-accent fill-current" />
                                <span className="text-xs text-gray-400 line-clamp-1">{feature}</span>
                              </div>
                            ))}
                            {item.features[language].length > 2 && (
                              <span className="text-xs text-accent">
                                +{item.features[language].length - 2} {language === 'ar' ? 'المزيد' : 'more'}
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex space-x-2 rtl:space-x-reverse">
                          <Button variant="outline" size="sm" onClick={() => handleEdit(item)} className="flex-1 btn-icon-fix card-action-btn">
                            <Edit className="w-4 h-4 mr-1 enhanced-icon" />
                            {t('admin.dashboard.edit')}
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleDelete(item)} className="btn-icon-fix card-action-btn">
                            <Trash2 className="w-4 h-4 enhanced-icon" />
                          </Button>
                        </div>
                      </div>
                    ) : (
                      // List View
                      <div className="flex items-center space-x-4 rtl:space-x-reverse">
                        {/* Image */}
                        <div className="w-16 h-16 bg-gradient-to-br from-secondary/20 to-accent/20 rounded-lg overflow-hidden flex-shrink-0">
                          {'image_url' in item && item.image_url ? (
                            <img
                              src={item.image_url}
                              alt={item.name[language]}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              {currentView === 'systems' ?
                                <Package className="w-6 h-6 text-secondary" /> :
                                <Crown className="w-6 h-6 text-secondary" />
                              }
                            </div>
                          )}
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <h3 className="text-lg font-semibold text-white truncate">
                              {item.name[language]}
                            </h3>
                            <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(item.status)} flex items-center`}>
                              {getStatusIcon(item.status)}
                              <span className="ml-1">{t(`admin.dashboard.${item.status}`)}</span>
                            </span>
                          </div>

                          <p className="text-gray-300 text-sm mb-2 line-clamp-1">
                            {item.description[language]}
                          </p>

                          <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm">
                            <div className="flex items-center space-x-2 rtl:space-x-reverse">
                              {item.original_price && item.original_price > item.price ? (
                                <>
                                  <span className="text-secondary font-bold">${item.price}</span>
                                  <span className="text-gray-400 line-through">${item.original_price}</span>
                                  <span className="text-red-400">-{item.discount_percentage}%</span>
                                </>
                              ) : (
                                <span className="text-secondary font-bold">${item.price}</span>
                              )}
                            </div>
                            <span className="text-gray-400">
                              {item.features[language].length} {language === 'ar' ? 'ميزة' : 'features'}
                            </span>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex space-x-2 rtl:space-x-reverse flex-shrink-0">
                          <Button variant="outline" size="sm" onClick={() => handleEdit(item)}>
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleDelete(item)}>
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </Card.Body>
                </Card>
              ))}
            </div>
          )}
        </>
      )}

      {/* Create/Edit Modal */}
      {showModal && (
        <Modal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          title={editingItem
            ? (currentView === 'systems'
                ? (language === 'ar' ? 'تعديل النظام المميز' : 'Edit Premium System')
                : (language === 'ar' ? 'تعديل الحزمة المميزة' : 'Edit Premium Package'))
            : (currentView === 'systems'
                ? (language === 'ar' ? 'إضافة نظام مميز' : 'Add Premium System')
                : (language === 'ar' ? 'إضافة حزمة مميزة' : 'Add Premium Package'))
          }
          size="lg"
        >
          <Modal.Body className="modal-text-fix">
            <div className="space-y-6">
              {/* Basic Information */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label={`${language === 'ar' ? 'الاسم' : 'Name'} (العربية)`}
                    value={currentView === 'systems' ? systemFormData.name?.ar || '' : packageFormData.name?.ar || ''}
                    onChange={(e) => handleTextChange('ar', 'name', e.target.value)}
                    required
                  />
                  <Input
                    label={`${language === 'ar' ? 'الاسم' : 'Name'} (English)`}
                    value={currentView === 'systems' ? systemFormData.name?.en || '' : packageFormData.name?.en || ''}
                    onChange={(e) => handleTextChange('en', 'name', e.target.value)}
                    required
                  />
                </div>
              </div>

              {/* Description */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'الوصف' : 'Description'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {language === 'ar' ? 'الوصف' : 'Description'} (العربية)
                    </label>
                    <textarea
                      value={currentView === 'systems' ? systemFormData.description?.ar || '' : packageFormData.description?.ar || ''}
                      onChange={(e) => handleTextChange('ar', 'description', e.target.value)}
                      rows={4}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {language === 'ar' ? 'الوصف' : 'Description'} (English)
                    </label>
                    <textarea
                      value={currentView === 'systems' ? systemFormData.description?.en || '' : packageFormData.description?.en || ''}
                      onChange={(e) => handleTextChange('en', 'description', e.target.value)}
                      rows={4}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                    />
                  </div>
                </div>
              </div>

              {/* Pricing */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'التسعير' : 'Pricing'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Input
                    label={language === 'ar' ? 'السعر الحالي' : 'Current Price'}
                    type="number"
                    value={currentView === 'systems' ? systemFormData.price || 0 : packageFormData.price || 0}
                    onChange={(e) => {
                      const value = Number(e.target.value);
                      if (currentView === 'systems') {
                        setSystemFormData(prev => ({ ...prev, price: value }));
                      } else {
                        setPackageFormData(prev => ({ ...prev, price: value }));
                      }
                    }}
                    leftIcon={<DollarSign />}
                  />
                  <Input
                    label={language === 'ar' ? 'السعر الأصلي' : 'Original Price'}
                    type="number"
                    value={currentView === 'systems' ? systemFormData.original_price || 0 : packageFormData.original_price || 0}
                    onChange={(e) => {
                      const value = Number(e.target.value);
                      if (currentView === 'systems') {
                        setSystemFormData(prev => ({ ...prev, original_price: value }));
                      } else {
                        setPackageFormData(prev => ({ ...prev, original_price: value }));
                      }
                    }}
                    leftIcon={<Tag />}
                  />
                  <Input
                    label={language === 'ar' ? 'نسبة الخصم %' : 'Discount %'}
                    type="number"
                    value={currentView === 'systems' ? systemFormData.discount_percentage || 0 : packageFormData.discount_percentage || 0}
                    onChange={(e) => {
                      const value = Number(e.target.value);
                      if (currentView === 'systems') {
                        setSystemFormData(prev => ({ ...prev, discount_percentage: value }));
                      } else {
                        setPackageFormData(prev => ({ ...prev, discount_percentage: value }));
                      }
                    }}
                    leftIcon={<Percent />}
                  />
                </div>
              </div>

              {/* System-specific fields */}
              {currentView === 'systems' && (
                <>
                  {/* Category and Status */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-secondary mb-2">
                        {language === 'ar' ? 'الفئة' : 'Category'}
                      </label>
                      <select
                        value={systemFormData.category || 'premium'}
                        onChange={(e) => setSystemFormData(prev => ({ ...prev, category: e.target.value }))}
                        className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                      >
                        <option value="premium">Premium</option>
                        <option value="exclusive">Exclusive</option>
                        <option value="limited">Limited</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-secondary mb-2">
                        {language === 'ar' ? 'الحالة' : 'Status'}
                      </label>
                      <select
                        value={systemFormData.status || 'active'}
                        onChange={(e) => setSystemFormData(prev => ({ ...prev, status: e.target.value as 'active' | 'inactive' }))}
                        className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                      >
                        <option value="active">{t('admin.dashboard.active')}</option>
                        <option value="inactive">{t('admin.dashboard.inactive')}</option>
                      </select>
                    </div>
                  </div>

                  {/* Media URLs */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label={language === 'ar' ? 'رابط الصورة' : 'Image URL'}
                      value={systemFormData.image_url || ''}
                      onChange={(e) => setSystemFormData(prev => ({ ...prev, image_url: e.target.value }))}
                      leftIcon={<ImageIcon />}
                    />
                    <Input
                      label={language === 'ar' ? 'رابط الفيديو' : 'Video URL'}
                      value={systemFormData.video_url || ''}
                      onChange={(e) => setSystemFormData(prev => ({ ...prev, video_url: e.target.value }))}
                      leftIcon={<Play />}
                    />
                  </div>
                </>
              )}

              {/* Package-specific fields */}
              {currentView === 'packages' && (
                <>
                  {/* Systems Selection */}
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">
                      {language === 'ar' ? 'الأنظمة المضمنة' : 'Included Systems'}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                      {premiumSystems.map(system => (
                        <label key={system.id} className="flex items-center space-x-2 rtl:space-x-reverse p-2 hover:bg-primary/30 rounded">
                          <input
                            type="checkbox"
                            checked={packageFormData.systems?.includes(system.id) || false}
                            onChange={(e) => {
                              const systems = packageFormData.systems || [];
                              if (e.target.checked) {
                                setPackageFormData(prev => ({ ...prev, systems: [...systems, system.id] }));
                              } else {
                                setPackageFormData(prev => ({ ...prev, systems: systems.filter(id => id !== system.id) }));
                              }
                            }}
                            className="rounded border-accent/30"
                          />
                          <span className="text-white text-sm">{getSystemText(system, 'name', language)}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Status */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {language === 'ar' ? 'الحالة' : 'Status'}
                    </label>
                    <select
                      value={packageFormData.status || 'active'}
                      onChange={(e) => setPackageFormData(prev => ({ ...prev, status: e.target.value as 'active' | 'inactive' }))}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                    >
                      <option value="active">{t('admin.dashboard.active')}</option>
                      <option value="inactive">{t('admin.dashboard.inactive')}</option>
                    </select>
                  </div>
                </>
              )}

              {/* Features */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'الميزات' : 'Features'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {language === 'ar' ? 'الميزات' : 'Features'} (العربية) - {language === 'ar' ? 'سطر واحد لكل ميزة' : 'One per line'}
                    </label>
                    <textarea
                      value={currentView === 'systems'
                        ? systemFormData.features?.ar?.join('\n') || ''
                        : packageFormData.features?.ar?.join('\n') || ''
                      }
                      onChange={(e) => handleArrayChange('ar', 'features', e.target.value)}
                      rows={6}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {language === 'ar' ? 'الميزات' : 'Features'} (English) - One per line
                    </label>
                    <textarea
                      value={currentView === 'systems'
                        ? systemFormData.features?.en?.join('\n') || ''
                        : packageFormData.features?.en?.join('\n') || ''
                      }
                      onChange={(e) => handleArrayChange('en', 'features', e.target.value)}
                      rows={6}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                    />
                  </div>
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowModal(false)}>
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button variant="primary" onClick={handleSave}>
              <Save className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'حفظ' : 'Save'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}
    </div>
  );
};

export default EnhancedPremiumManager;
