/* Import fonts first */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Golden Ratio Design System */
/* φ (phi) = 1.618 - The Golden Ratio */

/* Unified Color System for Khan Fashariya */
/* Professional Dark Theme with Enhanced Contrast */
:root {
  /* Legacy Colors - Keep for compatibility */
  --primary: #1e1a2b;
  --secondary: #D4AF37;
  --background: #0f0e17;
  --accent: #F5DEB3;

    /* Enhanced Theme System - Better Contrast */
  /* Background colors */
  --bg-primary: #0F172A;      /* slate-900 - Main background */
  --bg-secondary: #1E293B;    /* slate-800 - Secondary background */
  --bg-tertiary: #334155;     /* slate-700 - Tertiary background */
  --bg-card: #1E293B;         /* Card background */
  --bg-hover: #334155;        /* Hover state */
  --bg-active: #475569;       /* Active state */
  --bg-border: #475569;       /* Border color */
  --bg-input: #1E293B;        /* Input background */
  --bg-disabled: #64748B;     /* Disabled state */

  /* Text colors - Enhanced contrast */
  --text-primary: #F8FAFC;    /* slate-50 - High contrast primary text */
  --text-secondary: #CBD5E1;  /* slate-300 - Good contrast secondary text */
  --text-tertiary: #94A3B8;   /* slate-400 - Muted text */
  --text-inverse: #0F172A;    /* Dark text for light backgrounds */
  --text-accent: #38BDF8;     /* sky-400 - Accent text */
  --text-success: #34D399;    /* emerald-400 - Success text */
  --text-warning: #FBBF24;    /* amber-400 - Warning text */
  --text-error: #F87171;      /* red-400 - Error text */
  --text-info: #60A5FA;       /* blue-400 - Info text */
  --text-disabled: #64748B;   /* Disabled text */

  /* Interactive colors */
  --interactive-primary: #3B82F6;      /* blue-500 - Primary buttons */
  --interactive-primary-hover: #2563EB; /* blue-600 - Primary hover */
  --interactive-primary-active: #1D4ED8; /* blue-700 - Primary active */
  --interactive-secondary: #6B7280;    /* gray-500 - Secondary buttons */
  --interactive-secondary-hover: #4B5563; /* gray-600 - Secondary hover */
  --interactive-secondary-active: #374151; /* gray-700 - Secondary active */
  --interactive-accent: #38BDF8;       /* sky-400 - Accent elements */
  --interactive-accent-hover: #0EA5E9;  /* sky-500 - Accent hover */
  --interactive-accent-active: #0284C7; /* sky-600 - Accent active */

  /* Status colors */
  --status-success: #34D399;      /* emerald-400 */
  --status-success-bg: #064E3B;   /* emerald-900 */
  --status-warning: #FBBF24;      /* amber-400 */
  --status-warning-bg: #78350F;   /* amber-900 */
  --status-error: #F87171;        /* red-400 */
  --status-error-bg: #7F1D1D;     /* red-900 */
  --status-info: #60A5FA;         /* blue-400 */
  --status-info-bg: #1E3A8A;      /* blue-900 */

  /* Button Colors - Enhanced Contrast */
  --btn-primary-bg: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  --btn-primary-hover: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  --btn-primary-border: rgba(59, 130, 246, 0.3);

  --btn-secondary-bg: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  --btn-secondary-hover: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
  --btn-secondary-border: rgba(139, 92, 246, 0.3);

  --btn-success-bg: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --btn-success-hover: linear-gradient(135deg, #059669 0%, #047857 100%);
  --btn-success-border: rgba(16, 185, 129, 0.3);

  --btn-danger-bg: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --btn-danger-hover: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  --btn-danger-border: rgba(239, 68, 68, 0.3);

  --btn-outline-bg: rgba(31, 41, 55, 0.8);
  --btn-outline-hover: rgba(55, 65, 81, 0.9);
  --btn-outline-border: rgba(156, 163, 175, 0.5);
  --btn-outline-text: rgba(243, 244, 246, 0.9);

  /* Status Colors */
  --status-success: #10b981;
  --status-warning: #f59e0b;
  --status-error: #ef4444;
  --status-info: #3b82f6;

  /* Shadow Colors */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.15);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.25);
  --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.3);

  /* Interactive States */
  --hover-overlay: rgba(255, 255, 255, 0.05);
  --active-overlay: rgba(255, 255, 255, 0.1);
  --focus-ring: rgba(59, 130, 246, 0.5);

  /* Backdrop Effects */
  --backdrop-blur: blur(12px);
  --backdrop-saturate: saturate(180%);

  /* Golden Ratio Base Values */
  --golden-ratio: 1.618;
  --golden-ratio-inverse: 0.618;

  /* Base Unit (16px) */
  --base-unit: 1rem;

  /* Golden Ratio Spacing Scale */
  --space-xs: calc(var(--base-unit) * 0.382);     /* ~6px */
  --space-sm: calc(var(--base-unit) * 0.618);     /* ~10px */
  --space-md: calc(var(--base-unit) * 1);         /* 16px */
  --space-lg: calc(var(--base-unit) * 1.618);     /* ~26px */
  --space-xl: calc(var(--base-unit) * 2.618);     /* ~42px */
  --space-2xl: calc(var(--base-unit) * 4.236);    /* ~68px */
  --space-3xl: calc(var(--base-unit) * 6.854);    /* ~110px */

  /* Golden Ratio Font Sizes */
  --text-xs: calc(var(--base-unit) * 0.618);      /* ~10px */
  --text-sm: calc(var(--base-unit) * 0.764);      /* ~12px */
  --text-base: var(--base-unit);                  /* 16px */
  --text-lg: calc(var(--base-unit) * 1.236);      /* ~20px */
  --text-xl: calc(var(--base-unit) * 1.618);      /* ~26px */
  --text-2xl: calc(var(--base-unit) * 2.000);     /* ~32px */
  --text-3xl: calc(var(--base-unit) * 2.618);     /* ~42px */
  --text-4xl: calc(var(--base-unit) * 4.236);     /* ~68px */

  /* Golden Ratio Widths & Heights */
  --width-xs: calc(var(--base-unit) * 6.854);     /* ~110px */
  --width-sm: calc(var(--base-unit) * 11.090);    /* ~178px */
  --width-md: calc(var(--base-unit) * 17.944);    /* ~287px */
  --width-lg: calc(var(--base-unit) * 29.034);    /* ~465px */
  --width-xl: calc(var(--base-unit) * 46.978);    /* ~752px */
  --width-2xl: calc(var(--base-unit) * 76.012);   /* ~1216px */

  /* Golden Ratio Border Radius */
  --radius-xs: calc(var(--base-unit) * 0.236);    /* ~4px */
  --radius-sm: calc(var(--base-unit) * 0.382);    /* ~6px */
  --radius-md: calc(var(--base-unit) * 0.618);    /* ~10px */
  --radius-lg: calc(var(--base-unit) * 1.000);    /* 16px */
  --radius-xl: calc(var(--base-unit) * 1.618);    /* ~26px */

  /* Golden Ratio Shadows */
  --shadow-sm: 0 calc(var(--base-unit) * 0.062) calc(var(--base-unit) * 0.125) rgba(0, 0, 0, 0.1);
  --shadow-md: 0 calc(var(--base-unit) * 0.125) calc(var(--base-unit) * 0.382) rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 calc(var(--base-unit) * 0.382) calc(var(--base-unit) * 0.618) rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 calc(var(--base-unit) * 0.618) calc(var(--base-unit) * 1.618) rgba(0, 0, 0, 0.25);
}

/* Golden Ratio Utility Classes */
.golden-container {
  max-width: var(--width-2xl);
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

.golden-grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-lg);
}

.golden-grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-lg);
}

.golden-grid-5 {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: var(--space-md);
}

.golden-flex {
  display: flex;
  gap: var(--space-md);
}

.golden-spacing-xs { padding: var(--space-xs); }
.golden-spacing-sm { padding: var(--space-sm); }
.golden-spacing-md { padding: var(--space-md); }
.golden-spacing-lg { padding: var(--space-lg); }
.golden-spacing-xl { padding: var(--space-xl); }

.golden-text-xs { font-size: var(--text-xs); }
.golden-text-sm { font-size: var(--text-sm); }
.golden-text-base { font-size: var(--text-base); }
.golden-text-lg { font-size: var(--text-lg); }
.golden-text-xl { font-size: var(--text-xl); }
.golden-text-2xl { font-size: var(--text-2xl); }
.golden-text-3xl { font-size: var(--text-3xl); }

.golden-radius-xs { border-radius: var(--radius-xs); }
.golden-radius-sm { border-radius: var(--radius-sm); }
.golden-radius-md { border-radius: var(--radius-md); }
.golden-radius-lg { border-radius: var(--radius-lg); }
.golden-radius-xl { border-radius: var(--radius-xl); }

.golden-shadow-sm { box-shadow: var(--shadow-sm); }
.golden-shadow-md { box-shadow: var(--shadow-md); }
.golden-shadow-lg { box-shadow: var(--shadow-lg); }
.golden-shadow-xl { box-shadow: var(--shadow-xl); }

/* Golden Ratio Button Sizes */
.golden-btn-sm {
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--text-sm);
  border-radius: var(--radius-sm);
  min-height: calc(var(--base-unit) * 1.618);
}

.golden-btn-md {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--text-base);
  border-radius: var(--radius-md);
  min-height: calc(var(--base-unit) * 2.618);
}

.golden-btn-lg {
  padding: var(--space-md) var(--space-lg);
  font-size: var(--text-lg);
  border-radius: var(--radius-lg);
  min-height: calc(var(--base-unit) * 4.236);
}

/* Golden Ratio Input & Select */
.golden-input {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--text-base);
  border-radius: var(--radius-md);
  min-height: calc(var(--base-unit) * 2.618);
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.95) !important;
  transition: all 0.3s ease;
}

.golden-input:focus {
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  outline: none;
  box-shadow: var(--shadow-md);
  color: rgba(255, 255, 255, 0.98) !important;
}

.golden-input::placeholder {
  color: rgba(156, 163, 175, 0.7) !important;
}

.golden-select {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--text-base);
  border-radius: var(--radius-md);
  min-height: calc(var(--base-unit) * 2.618);
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(31, 41, 55, 1);
  color: rgba(255, 255, 255, 0.95) !important;
  cursor: pointer;
  transition: all 0.3s ease;
}

.golden-select:hover {
  border-color: rgba(255, 255, 255, 0.2);
  background: rgba(55, 65, 81, 1);
  color: rgba(255, 255, 255, 0.98) !important;
}

.golden-select:focus {
  border-color: rgba(255, 255, 255, 0.3);
  outline: none;
  box-shadow: var(--shadow-md);
  color: rgba(255, 255, 255, 0.98) !important;
}

.golden-select option {
  background: rgba(31, 41, 55, 1) !important;
  color: rgba(255, 255, 255, 0.95) !important;
  padding: var(--space-sm);
}

/* Golden Ratio Modal & Card */
.golden-modal-xl {
  width: var(--width-2xl);
  max-width: 98vw;
}

.golden-card {
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-lg);
}

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  background-color: var(--background);
}

/* Arabic font for Arabic content */
[dir="rtl"] body {
  font-family: 'Noto Sans Arabic', sans-serif;
}

/* Custom utilities */
.font-arabic {
  font-family: 'Noto Sans Arabic', sans-serif;
}

.font-english {
  font-family: 'Inter', sans-serif;
}

/* RTL-specific utilities */
.rtl\:space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

/* Smooth transitions with layout stability */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Language switching transition system */
html {
  transition: direction 0.3s ease;
}

body {
  transition: all 0.3s ease;
}

/* Prevent layout shifts during language switching */
.language-switching {
  pointer-events: none;
}

.language-switching * {
  transition: all 0.3s ease !important;
}

/* Prevent background scroll when modal is open */
.modal-open {
  overflow: hidden;
}

/* Modal backdrop */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 50;
  overflow: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--primary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary);
}

/* Grid pattern background */
.bg-grid-pattern {
  background-image: 
    linear-gradient(rgba(255, 178, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 178, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Line clamp utility */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, var(--secondary) 0%, var(--accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Animation for floating elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* Glow effects */
.glow-secondary {
  box-shadow: 0 0 20px rgba(255, 178, 0, 0.3);
}

.glow-accent {
  box-shadow: 0 0 20px rgba(33, 192, 255, 0.3);
}

/* Responsive text sizing */
@media (max-width: 640px) {
  .responsive-text {
    font-size: 0.875rem;
  }
}

/* Focus styles */
button:focus,
a:focus,
input:focus,
textarea:focus {
  outline: 2px solid var(--accent);
  outline-offset: 2px;
}

/* Loading animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading {
  animation: spin 1s linear infinite;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
}

/* Card shadows */
.card-shadow {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-shadow-hover {
  box-shadow: 0 20px 25px -5px rgba(255, 178, 0, 0.1), 0 10px 10px -5px rgba(33, 192, 255, 0.04);
}

/* RTL specific styles - REMOVED PROBLEMATIC MIRROR EFFECT */
/* The scaleX(-1) transform was causing text to appear mirrored/flipped */
/* Replaced with proper RTL-friendly animations */

[dir="rtl"] .transform-none {
  transform: none;
}

/* Beautiful Premium Button Animation - RTL Safe */
.premium-button {
  position: relative;
  overflow: hidden;
}

.premium-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.premium-button:hover::before {
  left: 100%;
}

.premium-button:hover {
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
}

/* Glow effect for premium elements */
.premium-glow {
  animation: premium-pulse 2s ease-in-out infinite alternate;
}

@keyframes premium-pulse {
  0% {
    box-shadow: 0 0 5px rgba(255, 215, 0, 0.3);
  }
  100% {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6), 0 0 30px rgba(255, 215, 0, 0.4);
  }
}

/* Horizontal Layout for Filter Grid */
.golden-horizontal-layout {
  max-width: 100%;
  padding: 1rem;
}

.golden-horizontal-layout .golden-card-body {
  padding: 1rem;
}

.golden-horizontal-layout .grid {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.golden-compact {
  padding: 0.75rem;
}

.golden-compact .golden-card-body {
  padding: 0.75rem;
}

/* Background patterns */
.bg-pattern {
  background-image: radial-gradient(circle at 1px 1px, rgba(255, 178, 0, 0.15) 1px, transparent 0);
  background-size: 20px 20px;
}

/* Gradient backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, var(--secondary) 0%, var(--accent) 100%);
}

.gradient-bg-alt {
  background: linear-gradient(135deg, var(--accent) 0%, var(--secondary) 100%);
}

/* Text shadow for better readability */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.text-shadow-strong {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
}

/* Backdrop blur support */
.backdrop-blur-fallback {
  background-color: rgba(30, 26, 43, 0.9);
}

@supports (backdrop-filter: blur(0)) {
  .backdrop-blur-fallback {
    background-color: rgba(30, 26, 43, 0.8);
    backdrop-filter: blur(10px);
  }
}

/* Pulse animation for tech elements */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Enhanced slide animations for Arabic */
@keyframes slideUpFade {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDownFade {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(100%);
  }
}

.slide-up-enter {
  animation: slideUpFade 0.5s ease-out forwards;
}

.slide-down-exit {
  animation: slideDownFade 0.5s ease-out forwards;
}

/* Smooth scale animations */
@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* Better hover transitions for cards */
.card-hover-effect {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-effect:hover {
  transform: translateY(-8px) scale(1.02);
}

/* Futuristic button styles */
.btn-futuristic {
  position: relative;
  overflow: hidden;
}

.btn-futuristic::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-futuristic:hover::before {
  left: 100%;
}

/* Enhanced Arabic text rendering */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .text-center {
  text-align: center;
}

/* Improved animations for RTL */
[dir="rtl"] .hover\:translate-x-1:hover {
  transform: translateX(-0.25rem);
}

[dir="rtl"] .hover\:-translate-x-1:hover {
  transform: translateX(0.25rem);
}

/* Custom animation timing for better UX */
.animation-delay-100 {
  animation-delay: 100ms;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

/* Enhanced Button System */
.btn-unified {
  font-weight: 600;
  letter-spacing: 0.025em;
  transition: all 0.3s ease;
  backdrop-filter: var(--backdrop-blur);
  position: relative;
  overflow: hidden;
}

.btn-unified::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--hover-overlay);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.btn-unified:hover::before {
  opacity: 1;
}

.btn-primary-unified {
  background: var(--btn-primary-bg);
  border: 1px solid var(--btn-primary-border);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary-unified:hover {
  background: var(--btn-primary-hover);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.btn-outline-unified {
  background: var(--btn-outline-bg);
  border: 2px solid var(--btn-outline-border);
  color: var(--btn-outline-text);
  box-shadow: var(--shadow-sm);
}

.btn-outline-unified:hover {
  background: var(--btn-outline-hover);
  border-color: var(--border-hover);
  color: var(--text-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* Enhanced Card System */
.card-unified {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  backdrop-filter: var(--backdrop-blur);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.card-unified:hover {
  background: var(--bg-card-hover);
  border-color: var(--border-hover);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Enhanced Input System */
.input-unified {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  backdrop-filter: var(--backdrop-blur);
  transition: all 0.3s ease;
}

.input-unified:focus {
  border-color: var(--border-accent);
  box-shadow: 0 0 0 3px var(--focus-ring);
  background: var(--bg-card-hover);
}

.input-unified::placeholder {
  color: var(--text-tertiary);
}

/* Enhanced Text System */
.text-primary-unified {
  color: var(--text-primary);
  font-weight: 600;
  letter-spacing: 0.025em;
}

.text-secondary-unified {
  color: var(--text-secondary);
  font-weight: 500;
  letter-spacing: 0.025em;
}

.text-accent-unified {
  color: var(--text-accent);
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* Dark Theme Text Visibility Classes - Global Fix for Black Text */
.text-dark-theme {
  color: var(--text-primary) !important;
}

.text-dark-theme-secondary {
  color: var(--text-secondary) !important;
}

.text-dark-theme-muted {
  color: var(--text-tertiary) !important;
}

/* Force white text on dark backgrounds */
.text-white-force {
  color: rgba(255, 255, 255, 0.95) !important;
}

.text-gray-light-force {
  color: rgba(229, 231, 235, 0.9) !important;
}

/* Specific fixes for common problematic elements */
.modal-text-fix {
  color: var(--text-primary) !important;
}

.modal-text-fix h1,
.modal-text-fix h2,
.modal-text-fix h3,
.modal-text-fix h4,
.modal-text-fix h5,
.modal-text-fix h6 {
  color: var(--text-primary) !important;
}

.modal-text-fix p,
.modal-text-fix span,
.modal-text-fix div,
.modal-text-fix label {
  color: var(--text-secondary) !important;
}

.modal-text-fix .text-gray-600,
.modal-text-fix .text-gray-700,
.modal-text-fix .text-gray-800,
.modal-text-fix .text-gray-900,
.modal-text-fix .text-black {
  color: var(--text-secondary) !important;
}

/* Card text fixes */
.card-text-fix {
  color: var(--text-primary) !important;
}

.card-text-fix .text-gray-600,
.card-text-fix .text-gray-700,
.card-text-fix .text-gray-800,
.card-text-fix .text-gray-900,
.card-text-fix .text-black {
  color: var(--text-secondary) !important;
}

/* Form text fixes */
.form-text-fix input,
.form-text-fix textarea,
.form-text-fix select {
  color: var(--text-primary) !important;
  background-color: var(--bg-card) !important;
}

.form-text-fix label {
  color: var(--text-secondary) !important;
}

/* Enhanced form element fixes for golden classes */
input.golden-input,
textarea.golden-input,
select.golden-select {
  color: rgba(255, 255, 255, 0.95) !important;
}

input.golden-input::placeholder,
textarea.golden-input::placeholder {
  color: rgba(156, 163, 175, 0.7) !important;
}

/* Fix for all input and textarea elements in modals */
.modal-text-fix input,
.modal-text-fix textarea,
.modal-text-fix select {
  color: rgba(255, 255, 255, 0.95) !important;
}

.modal-text-fix input::placeholder,
.modal-text-fix textarea::placeholder {
  color: rgba(156, 163, 175, 0.7) !important;
}

/* Button text fixes */
.btn-text-fix {
  color: rgba(255, 255, 255, 0.95) !important;
}

/* Fix for outline button icons to inherit text color */
button svg,
button .lucide {
  color: currentColor;
}

/* Global override for any remaining black text */
.dark-theme-override * {
  color: var(--text-primary);
}

.dark-theme-override .text-black,
.dark-theme-override .text-gray-900,
.dark-theme-override .text-gray-800,
.dark-theme-override .text-gray-700 {
  color: var(--text-secondary) !important;
}

/* Global fix for all form elements to ensure visibility */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="url"],
textarea,
select {
  color: rgba(255, 255, 255, 0.95) !important;
}

input[type="text"]::placeholder,
input[type="email"]::placeholder,
input[type="password"]::placeholder,
input[type="number"]::placeholder,
input[type="url"]::placeholder,
textarea::placeholder {
  color: rgba(156, 163, 175, 0.7) !important;
}

/* Ensure all labels are visible */
label {
  color: rgba(255, 255, 255, 0.9) !important;
}





/* Enhanced floating label fixes */
.floating-label-fix {
  position: relative;
}

.floating-label-fix label {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(30, 26, 43, 0.9);
  padding: 0 8px;
  color: rgba(156, 163, 175, 0.8);
  font-size: 14px;
  font-weight: 500;
  pointer-events: none;
  transition: all 0.2s ease;
  z-index: 10;
  border-radius: 4px;
}

.floating-label-fix input:focus + label,
.floating-label-fix input:not(:placeholder-shown) + label,
.floating-label-fix textarea:focus + label,
.floating-label-fix textarea:not(:placeholder-shown) + label {
  top: -8px;
  left: 8px;
  font-size: 12px;
  color: rgba(255, 178, 0, 0.9);
  background: rgba(30, 26, 43, 0.95);
  border: 1px solid rgba(255, 178, 0, 0.3);
}

/* RTL support for floating labels */
[dir="rtl"] .floating-label-fix label {
  left: auto;
  right: 12px;
}

[dir="rtl"] .floating-label-fix input:focus + label,
[dir="rtl"] .floating-label-fix input:not(:placeholder-shown) + label,
[dir="rtl"] .floating-label-fix textarea:focus + label,
[dir="rtl"] .floating-label-fix textarea:not(:placeholder-shown) + label {
  left: auto;
  right: 8px;
}

/* Additional label fixes for forms */
.form-label-fix label {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
  display: block !important;
}

/* Modal form label fixes */
.modal-text-fix label {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 600 !important;
}

/* Ensure proper label positioning in golden inputs */
.golden-input-container {
  position: relative;
}

.golden-input-container label {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(30, 26, 43, 0.9);
  padding: 0 6px;
  color: rgba(156, 163, 175, 0.8);
  font-size: 14px;
  pointer-events: none;
  transition: all 0.2s ease;
  z-index: 10;
  border-radius: 3px;
}

.golden-input-container input:focus + label,
.golden-input-container input:not(:placeholder-shown) + label {
  top: -8px;
  left: 8px;
  font-size: 12px;
  color: rgba(255, 178, 0, 0.9);
  background: rgba(30, 26, 43, 0.95);
  border: 1px solid rgba(255, 178, 0, 0.3);
}

/* Enhanced Status System */
.status-success {
  color: var(--status-success);
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-warning {
  color: var(--status-warning);
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-error {
  color: var(--status-error);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-info {
  color: var(--status-info);
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

/* Enhanced Utility Classes */
.shadow-unified-sm { box-shadow: var(--shadow-sm); }
.shadow-unified-md { box-shadow: var(--shadow-md); }
.shadow-unified-lg { box-shadow: var(--shadow-lg); }
.shadow-unified-xl { box-shadow: var(--shadow-xl); }

.backdrop-unified {
  backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
}

.border-unified { border-color: var(--border-primary); }
.border-unified-hover { border-color: var(--border-hover); }
.border-unified-accent { border-color: var(--border-accent); }

/* Responsive Enhancements */
@media (max-width: 768px) {
  .btn-unified {
    min-height: 44px;
    min-width: 44px;
  }

  .input-unified {
    min-height: 44px;
  }
}

/* Dark Theme Specific Enhancements */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: rgba(255, 255, 255, 0.95);
    --text-secondary: rgba(203, 213, 225, 0.9);
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
    --border-primary: rgba(156, 163, 175, 0.6);
    --btn-outline-border: rgba(203, 213, 225, 0.7);
  }
}

/* English Language Icon-Text Spacing Fix */
[dir="ltr"] .flex.items-center.space-x-2 > svg + span,
[dir="ltr"] .flex.items-center.space-x-3 > svg + span,
[dir="ltr"] .flex.items-center.space-x-4 > svg + span {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

[dir="ltr"] .flex.items-center.space-x-2 > span + svg,
[dir="ltr"] .flex.items-center.space-x-3 > span + svg,
[dir="ltr"] .flex.items-center.space-x-4 > span + svg {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

/* Arabic Language Icon-Text Spacing Fix */
[dir="rtl"] .flex.items-center.space-x-2 > svg + span,
[dir="rtl"] .flex.items-center.space-x-3 > svg + span,
[dir="rtl"] .flex.items-center.space-x-4 > svg + span {
  margin-right: 0.5rem !important;
  margin-left: 0 !important;
}

[dir="rtl"] .flex.items-center.space-x-2 > span + svg,
[dir="rtl"] .flex.items-center.space-x-3 > span + svg,
[dir="rtl"] .flex.items-center.space-x-4 > span + svg {
  margin-right: 0.5rem !important;
  margin-left: 0 !important;
}

/* Fix Arabic icon spacing in navigation and buttons */
[dir="rtl"] .nav-button .nav-icon {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}

/* Removed - now handled in systematic hero feature layout section */

[dir="rtl"] .premium-feature-item .premium-icon {
  margin-right: 0 !important;
  margin-left: 0.75rem !important;
}

/* Ensure proper spacing for all icon-text combinations */
.icon-text-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

[dir="ltr"] .icon-text-container {
  flex-direction: row;
}

[dir="rtl"] .icon-text-container {
  flex-direction: row-reverse;
}

/* Force proper spacing in flex containers */
[dir="ltr"] .flex.items-center > svg {
  margin-right: 0.5rem;
  margin-left: 0;
}

[dir="rtl"] .flex.items-center > svg {
  margin-left: 0.5rem;
  margin-right: 0;
}

/* Override any conflicting Tailwind classes */
[dir="ltr"] .space-x-reverse > * + * {
  margin-right: 0.5rem !important;
  margin-left: 0 !important;
}

[dir="rtl"] .space-x-reverse > * + * {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

/* Ensure proper spacing for English text with icons */
[dir="ltr"] .icon-text-spacing {
  gap: 0.5rem;
}

[dir="ltr"] .icon-text-spacing-lg {
  gap: 0.75rem;
}

/* Fix for contact section and other icon-text combinations */
[dir="ltr"] .contact-item,
[dir="ltr"] .nav-item,
[dir="ltr"] .button-with-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Responsive icon-text spacing for English */
@media (max-width: 768px) {
  [dir="ltr"] .flex.items-center.space-x-2 > svg + span,
  [dir="ltr"] .flex.items-center.space-x-3 > svg + span {
    margin-left: 0.375rem !important;
  }
}

/* Premium Section Layout Fix for English */
[dir="ltr"] .premium-section .grid.lg\\:grid-cols-2 {
  grid-template-columns: 1fr 1fr;
}

[dir="ltr"] .premium-section .grid.lg\\:grid-cols-2 > div:first-child {
  order: 1;
}

[dir="ltr"] .premium-section .grid.lg\\:grid-cols-2 > div:last-child {
  order: 2;
}

/* Premium content alignment for English */
[dir="ltr"] .premium-content {
  text-align: left;
}

[dir="ltr"] .premium-content h1,
[dir="ltr"] .premium-content h2,
[dir="ltr"] .premium-content h3 {
  text-align: left;
}

/* Premium media section positioning for English */
[dir="ltr"] .premium-media {
  position: relative;
}

[dir="ltr"] .premium-media .play-button {
  left: 50%;
  transform: translateX(-50%);
}

/* Premium button alignment for English */
[dir="ltr"] .premium-button-container {
  justify-content: flex-start;
}

[dir="ltr"] .premium-price-container {
  justify-content: flex-start;
}

/* Font and Icon Size Optimization for English */
[dir="ltr"] {
  font-family: 'Inter', sans-serif;
}

/* Responsive font sizes for English */
[dir="ltr"] h1 {
  font-size: clamp(1.875rem, 4vw, 3.75rem);
  line-height: 1.2;
}

[dir="ltr"] h2 {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
  line-height: 1.3;
}

[dir="ltr"] h3 {
  font-size: clamp(1.25rem, 2.5vw, 1.875rem);
  line-height: 1.4;
}

[dir="ltr"] p {
  font-size: clamp(0.875rem, 1.5vw, 1.125rem);
  line-height: 1.6;
}

/* Icon sizing for English layout */
[dir="ltr"] .icon-sm {
  width: 1rem;
  height: 1rem;
}

[dir="ltr"] .icon-md {
  width: 1.25rem;
  height: 1.25rem;
}

[dir="ltr"] .icon-lg {
  width: 1.5rem;
  height: 1.5rem;
}

[dir="ltr"] .icon-xl {
  width: 2rem;
  height: 2rem;
}

/* Responsive icon sizes */
@media (max-width: 768px) {
  [dir="ltr"] .icon-responsive {
    width: 1rem;
    height: 1rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  [dir="ltr"] .icon-responsive {
    width: 1.25rem;
    height: 1.25rem;
  }
}

@media (min-width: 1025px) {
  [dir="ltr"] .icon-responsive {
    width: 1.5rem;
    height: 1.5rem;
  }
}

/* Button text and icon sizing for English */
[dir="ltr"] .btn-text {
  font-size: clamp(0.875rem, 1.2vw, 1rem);
  font-weight: 500;
}

[dir="ltr"] .btn-icon {
  width: 1.125rem;
  height: 1.125rem;
  flex-shrink: 0;
}

/* Navigation text sizing for English */
[dir="ltr"] .nav-text {
  font-size: clamp(0.875rem, 1vw, 1rem);
  font-weight: 500;
}

/* Contact section optimization for English */
[dir="ltr"] .contact-text {
  font-size: clamp(0.875rem, 1.2vw, 1.125rem);
  line-height: 1.5;
}

/* Prevent text overflow in English */
[dir="ltr"] .text-container {
  word-break: break-word;
  overflow-wrap: break-word;
}

/* Ensure proper spacing in flex containers for English */
[dir="ltr"] .flex-container-en {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Navigation Button Size Consistency */
.nav-button {
  min-width: 120px;
  max-width: 160px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.75rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* English navigation button adjustments */
[dir="ltr"] .nav-button {
  min-width: 100px;
  max-width: 140px;
}

/* Arabic navigation button adjustments */
[dir="rtl"] .nav-button {
  min-width: 80px;
  max-width: 120px;
}

/* Responsive navigation buttons */
@media (max-width: 768px) {
  .nav-button {
    min-width: 80px;
    max-width: 100px;
    height: 36px;
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .nav-button {
    min-width: 100px;
    max-width: 130px;
    height: 38px;
  }
}

@media (min-width: 1025px) {
  .nav-button {
    min-width: 120px;
    max-width: 160px;
    height: 40px;
  }
}

/* Systematic Button Text Animation System */
.nav-button-text {
  white-space: nowrap;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  max-width: 100%;
}

/* Default state - show text with fade if too long */
.nav-button-text::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(to right, transparent, var(--primary));
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Show fade gradient when text overflows */
.nav-button-text.text-overflow::after {
  opacity: 1;
}

/* RTL fade gradient */
[dir="rtl"] .nav-button-text::after {
  right: auto;
  left: 0;
  background: linear-gradient(to left, transparent, var(--primary));
}

/* Hover animation - slide text to reveal full content */
.nav-button:hover .nav-button-text {
  overflow: visible;
  animation: slideText 2s ease-in-out;
}

@keyframes slideText {
  0%, 20% { transform: translateX(0); }
  50%, 80% { transform: translateX(-20px); }
  100% { transform: translateX(0); }
}

/* RTL slide animation */
[dir="rtl"] .nav-button:hover .nav-button-text {
  animation: slideTextRTL 2s ease-in-out;
}

@keyframes slideTextRTL {
  0%, 20% { transform: translateX(0); }
  50%, 80% { transform: translateX(20px); }
  100% { transform: translateX(0); }
}

/* Hide fade gradient on hover */
.nav-button:hover .nav-button-text::after {
  opacity: 0;
}

/* Universal button text animation system - can be applied to any button */
.btn-text-animated {
  white-space: nowrap;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  max-width: 100%;
}

.btn-text-animated::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(to right, transparent, currentColor);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

[dir="rtl"] .btn-text-animated::after {
  right: auto;
  left: 0;
  background: linear-gradient(to left, transparent, currentColor);
}

.btn-text-animated.text-overflow::after {
  opacity: 0.3;
}

.btn-text-animated:hover {
  overflow: visible;
  animation: slideButtonText 2s ease-in-out;
}

[dir="rtl"] .btn-text-animated:hover {
  animation: slideButtonTextRTL 2s ease-in-out;
}

.btn-text-animated:hover::after {
  opacity: 0;
}

@keyframes slideButtonText {
  0%, 20% { transform: translateX(0); }
  50%, 80% { transform: translateX(-15px); }
  100% { transform: translateX(0); }
}

@keyframes slideButtonTextRTL {
  0%, 20% { transform: translateX(0); }
  50%, 80% { transform: translateX(15px); }
  100% { transform: translateX(0); }
}

/* Language Switching Layout Stability System */
/* Prevent icon displacement during language switching */
.icon-stable {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* Prevent horizontal line disappearance */
.divider-stable {
  position: relative;
  transition: all 0.3s ease;
  opacity: 1 !important;
}

.divider-stable::before,
.divider-stable::after {
  transition: all 0.3s ease;
}

/* Flex container stability during language switching */
.flex-stable {
  display: flex !important;
  transition: all 0.3s ease;
}

.flex-stable > * {
  transition: all 0.3s ease;
}

/* Grid stability during language switching */
.grid-stable {
  display: grid !important;
  transition: all 0.3s ease;
}

/* Text alignment stability */
.text-stable {
  transition: text-align 0.3s ease;
}

[dir="rtl"] .text-stable {
  text-align: right;
}

[dir="ltr"] .text-stable {
  text-align: left;
}

.text-stable.center {
  text-align: center !important;
}

/* Margin and padding stability */
.spacing-stable {
  transition: margin 0.3s ease, padding 0.3s ease;
}

/* Border stability */
.border-stable {
  transition: border 0.3s ease;
}

/* Position stability for absolutely positioned elements */
.position-stable {
  transition: left 0.3s ease, right 0.3s ease, top 0.3s ease, bottom 0.3s ease;
}

/* ========================================
   SYSTEMATIC LAYOUT UTILITY CLASSES
   Based on Golden Ratio (φ = 1.618) Design System
   ======================================== */

/* Golden Ratio Spacing System */
.space-phi-xs { gap: 0.382rem; } /* φ^-2 */
.space-phi-sm { gap: 0.618rem; } /* φ^-1 */
.space-phi-md { gap: 1rem; }     /* φ^0 */
.space-phi-lg { gap: 1.618rem; } /* φ^1 */
.space-phi-xl { gap: 2.618rem; } /* φ^2 */
.space-phi-2xl { gap: 4.236rem; } /* φ^3 */

/* Golden Ratio Padding System */
.p-phi-xs { padding: 0.382rem; }
.p-phi-sm { padding: 0.618rem; }
.p-phi-md { padding: 1rem; }
.p-phi-lg { padding: 1.618rem; }
.p-phi-xl { padding: 2.618rem; }
.p-phi-2xl { padding: 4.236rem; }

/* Golden Ratio Margin System */
.m-phi-xs { margin: 0.382rem; }
.m-phi-sm { margin: 0.618rem; }
.m-phi-md { margin: 1rem; }
.m-phi-lg { margin: 1.618rem; }
.m-phi-xl { margin: 2.618rem; }
.m-phi-2xl { margin: 4.236rem; }

/* Directional Flexbox Utilities */
.flex-dir-aware {
  display: flex;
  align-items: center;
}

[dir="ltr"] .flex-dir-aware {
  flex-direction: row;
}

[dir="rtl"] .flex-dir-aware {
  flex-direction: row-reverse;
}

.flex-dir-aware-reverse {
  display: flex;
  align-items: center;
}

[dir="ltr"] .flex-dir-aware-reverse {
  flex-direction: row-reverse;
}

[dir="rtl"] .flex-dir-aware-reverse {
  flex-direction: row;
}

/* Directional Text Alignment */
.text-dir-start {
  text-align: left;
}

[dir="rtl"] .text-dir-start {
  text-align: right;
}

.text-dir-end {
  text-align: right;
}

[dir="rtl"] .text-dir-end {
  text-align: left;
}

/* Directional Positioning */
.pos-dir-start {
  left: 0;
  right: auto;
}

[dir="rtl"] .pos-dir-start {
  right: 0;
  left: auto;
}

.pos-dir-end {
  right: 0;
  left: auto;
}

[dir="rtl"] .pos-dir-end {
  left: 0;
  right: auto;
}

/* Icon-Text Relationship Utilities */
.icon-text-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.icon-text-container .icon {
  flex-shrink: 0;
}

.icon-text-container .text {
  flex: 1;
  min-width: 0;
}

/* Responsive Icon Sizes */
.icon-xs { width: 0.75rem; height: 0.75rem; }
.icon-sm { width: 1rem; height: 1rem; }
.icon-md { width: 1.25rem; height: 1.25rem; }
.icon-lg { width: 1.5rem; height: 1.5rem; }
.icon-xl { width: 2rem; height: 2rem; }
.icon-2xl { width: 2.5rem; height: 2.5rem; }

/* Button Size System */
.btn-phi-sm {
  padding: 0.382rem 0.618rem;
  font-size: 0.875rem;
  min-height: 2.618rem;
}

.btn-phi-md {
  padding: 0.618rem 1rem;
  font-size: 1rem;
  min-height: 2.854rem; /* φ * 44px for accessibility */
}

.btn-phi-lg {
  padding: 1rem 1.618rem;
  font-size: 1.125rem;
  min-height: 3.236rem;
}

/* Card and Container System */
.container-phi {
  padding: 1.618rem;
  border-radius: 0.618rem;
}

.card-phi {
  padding: 1rem 1.618rem;
  border-radius: 0.618rem;
  box-shadow: 0 0.382rem 1.618rem rgba(0, 0, 0, 0.1);
}

/* Grid System with Golden Ratio */
.grid-phi-2 {
  display: grid;
  grid-template-columns: 1fr 1.618fr;
  gap: 1rem;
}

.grid-phi-3 {
  display: grid;
  grid-template-columns: 1fr 1.618fr 1fr;
  gap: 1rem;
}

/* Responsive Typography Scale */
.text-phi-xs { font-size: 0.618rem; line-height: 1.618; }
.text-phi-sm { font-size: 0.875rem; line-height: 1.618; }
.text-phi-base { font-size: 1rem; line-height: 1.618; }
.text-phi-lg { font-size: 1.125rem; line-height: 1.618; }
.text-phi-xl { font-size: 1.25rem; line-height: 1.618; }
.text-phi-2xl { font-size: 1.618rem; line-height: 1.618; }
.text-phi-3xl { font-size: 2.618rem; line-height: 1.382; }
.text-phi-4xl { font-size: 4.236rem; line-height: 1.236; }

/* Responsive Design Patterns */
@media (max-width: 640px) {
  .responsive-stack {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .responsive-text-center {
    text-align: center !important;
  }

  .responsive-full-width {
    width: 100% !important;
  }
}

@media (min-width: 641px) {
  .responsive-flex-row {
    flex-direction: row !important;
  }
}

/* Animation Utilities */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* RTL-aware animations */
[dir="rtl"] .animate-slide-in-left {
  animation: slideInRight 0.5s ease-out;
}

[dir="rtl"] .animate-slide-in-right {
  animation: slideInLeft 0.5s ease-out;
}

/* Accessibility Utilities */
.focus-visible-only {
  outline: none;
}

.focus-visible-only:focus-visible {
  outline: 2px solid var(--accent);
  outline-offset: 2px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Touch-friendly utilities */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.touch-target-lg {
  min-height: 48px;
  min-width: 48px;
}

/* Layout debugging utilities (remove in production) */
.debug-layout * {
  outline: 1px solid red;
}

.debug-flex {
  background: rgba(255, 0, 0, 0.1);
}

.debug-grid {
  background: rgba(0, 255, 0, 0.1);
}

/* Icon spacing in navigation buttons */
.nav-button .nav-icon {
  flex-shrink: 0;
  margin-right: 0.375rem;
}

[dir="rtl"] .nav-button .nav-icon {
  margin-right: 0;
  margin-left: 0.375rem;
}

/* Site Name Size Adjustments - Unified for both languages */
.site-name {
  font-size: clamp(1.25rem, 2.5vw, 1.75rem) !important;
  line-height: 1.2;
  white-space: nowrap;
  overflow: visible;
  text-overflow: unset;
  min-width: fit-content;
  max-width: none;
}

/* Arabic specific adjustments */
[dir="rtl"] .site-name {
  font-size: clamp(1.5rem, 3vw, 2rem) !important;
}

/* Site Tagline Size Adjustments - Unified for both languages */
.site-tagline {
  font-size: clamp(0.75rem, 1vw, 0.875rem) !important;
  letter-spacing: 0.05em;
  white-space: nowrap;
  overflow: visible;
  text-overflow: unset;
  min-width: fit-content;
  max-width: none;
}

/* Arabic specific adjustments */
[dir="rtl"] .site-tagline {
  font-size: clamp(0.875rem, 1.2vw, 1rem) !important;
  letter-spacing: 0.1em;
}

/* Responsive site name adjustments */
@media (max-width: 640px) {
  [dir="ltr"] .site-name {
    font-size: 1.125rem !important;
  }

  [dir="ltr"] .site-tagline {
    font-size: 0.75rem !important;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  [dir="ltr"] .site-name {
    font-size: 1.375rem !important;
  }

  [dir="ltr"] .site-tagline {
    font-size: 0.8125rem !important;
  }
}

@media (min-width: 1025px) {
  [dir="ltr"] .site-name {
    font-size: 1.5rem !important;
  }

  [dir="ltr"] .site-tagline {
    font-size: 0.875rem !important;
  }
}

/* Systematic Hero Feature Items Layout */
.hero-feature-item {
  display: flex;
  align-items: center;
  gap: 1rem !important;
  min-width: fit-content;
  padding: 1rem 1.5rem;
  justify-content: center;
}

.hero-feature-item .feature-icon {
  flex-shrink: 0;
}

.hero-feature-item .feature-text {
  white-space: nowrap;
  min-width: 0;
  flex: 1;
  text-align: center;
}

/* Language-specific adjustments */
[dir="ltr"] .hero-feature-item .feature-icon {
  margin-right: 0.75rem;
  margin-left: 0;
}

[dir="rtl"] .hero-feature-item .feature-icon {
  margin-left: 0.75rem;
  margin-right: 0;
}

/* Premium Section Icon-Text Spacing */
[dir="ltr"] .premium-feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem !important;
  padding: 0.75rem;
}

[dir="ltr"] .premium-feature-item .premium-icon {
  flex-shrink: 0;
  width: 1.5rem;
  height: 1.5rem;
}

[dir="ltr"] .premium-feature-item .premium-text {
  flex: 1;
  min-width: 0;
}

/* ========================================
   UNIFIED ICON SYSTEM - SYSTEMATIC SOLUTION
   ======================================== */

/* Base Icon Container - Universal for all icon layouts */
.icon-container {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

/* Icon positioning utilities */
.icon-left {
  margin-right: 0.5rem;
  margin-left: 0;
}

.icon-right {
  margin-left: 0.5rem;
  margin-right: 0;
}

/* RTL Icon positioning - automatic reversal */
[dir="rtl"] .icon-left {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .icon-right {
  margin-left: 0;
  margin-right: 0.5rem;
}

/* Hero Section Icons - Centered and Stable */
.hero-icons-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: nowrap;
  width: 100%;
}

.hero-icon-item {
  position: relative;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Social Media Icons - Directional but Consistent */
.social-icons-container {
  display: flex;
  align-items: center;
  gap: 1rem !important;
  flex-direction: row;
}

.social-icon {
  flex-shrink: 0;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Centered social icons - always center regardless of language */
.social-icons-center {
  justify-content: center !important;
}

/* Directional social icons - follows language direction */
.social-icons-directional {
  justify-content: flex-start;
}

[dir="rtl"] .social-icons-directional {
  justify-content: flex-start; /* Keep consistent - start from right in RTL */
}

/* Footer social icons should be directional */
.footer .social-icons-container {
  justify-content: flex-start;
}

[dir="rtl"] .footer .social-icons-container {
  justify-content: flex-start; /* Consistent positioning */
}

/* Card Icon System - Unified for all card types */
.card-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  border-radius: 0.75rem;
  background: linear-gradient(135deg, var(--secondary), var(--accent));
  margin-bottom: 1rem;
}

.card-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: var(--primary);
}

/* Card Title and Content Layout */
.card-header-container {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 1rem;
  gap: 1rem;
}

.card-title {
  flex: 1;
  min-width: 0; /* Prevents text overflow */
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.4;
  color: white;
}

.card-price {
  flex-shrink: 0;
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--secondary);
}

/* Button Container - Consistent positioning */
.button-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: auto;
  padding-top: 1rem;
}

[dir="rtl"] .button-container {
  justify-content: flex-start;
}

/* Form Button Container - Always at bottom */
.form-button-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

[dir="rtl"] .form-button-container {
  justify-content: flex-start;
}

/* Systematic Star Rating Positioning */
.star-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem !important;
  justify-content: center;
  flex-direction: row;
}

.star-rating .star-icon {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
}

/* Centered star rating - always center regardless of language */
.star-rating-center {
  justify-content: center !important;
}

/* Directional star rating - follows language direction */
.star-rating-directional {
  justify-content: flex-start;
}

[dir="rtl"] .star-rating-directional {
  justify-content: flex-end;
}

[dir="rtl"] .star-rating .star-icon {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
}

/* Feature List Spacing */
[dir="ltr"] .feature-list-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem !important;
  margin-bottom: 0.75rem;
}

[dir="ltr"] .feature-list-item .feature-check-icon {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: 0.125rem;
}

[dir="ltr"] .feature-list-item .feature-description {
  flex: 1;
  min-width: 0;
  line-height: 1.5;
}

/* ========================================
   TEXT OVERFLOW AND TRUNCATION SYSTEM
   ======================================== */

/* Prevent text overflow in all containers */
.text-container {
  min-width: 0;
  overflow: hidden;
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-wrap {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Site name and branding - prevent truncation */
.site-name {
  white-space: nowrap;
  overflow: visible;
  text-overflow: unset;
  min-width: fit-content;
}

.site-tagline {
  white-space: nowrap;
  overflow: visible;
  text-overflow: unset;
  min-width: fit-content;
}

/* Header container adjustments */
.header-brand-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 0;
  flex: 1;
}

.header-brand-text {
  min-width: fit-content;
  flex-shrink: 0;
}

/* Card content protection */
.card-content {
  display: flex;
  flex-direction: column;
  min-height: 0;
  flex: 1;
}

.card-text {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

/* Responsive adjustments for hero features */
@media (max-width: 768px) {
  .hero-feature-item {
    padding: 0.75rem 1rem;
    gap: 0.5rem !important;
  }

  .hero-feature-item .feature-text {
    font-size: 0.875rem;
  }

  [dir="ltr"] .social-icons-container {
    gap: 0.75rem !important;
  }

  [dir="ltr"] .social-icon {
    width: 2rem;
    height: 2rem;
  }

  /* Arabic (RTL) responsive adjustments */
  [dir="rtl"] .social-icons-container {
    gap: 0.75rem !important;
  }

  [dir="rtl"] .social-icon {
    width: 2rem;
    height: 2rem;
  }

  /* Mobile text adjustments */
  .site-name {
    font-size: 1.25rem !important;
  }

  .site-tagline {
    font-size: 0.75rem !important;
  }
}

/* ========================================
   UNIFIED ICON SYSTEM - FINAL IMPLEMENTATION
   ======================================== */

/* Universal Icon Utilities */
.icon-stable {
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.icon-sm { width: 1rem; height: 1rem; }
.icon-md { width: 1.25rem; height: 1.25rem; }
.icon-lg { width: 1.5rem; height: 1.5rem; }
.icon-xl { width: 2rem; height: 2rem; }

/* Flex Container Stability */
.flex-stable {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

/* Icon-Text Combinations - Language Aware */
.icon-text-combo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

[dir="rtl"] .icon-text-combo {
  flex-direction: row-reverse;
}

/* Button Icon Positioning */
.btn-icon-left {
  margin-right: 0.5rem;
  margin-left: 0;
}

.btn-icon-right {
  margin-left: 0.5rem;
  margin-right: 0;
}

[dir="rtl"] .btn-icon-left {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .btn-icon-right {
  margin-left: 0;
  margin-right: 0.5rem;
}

/* Navigation Icon Consistency */
.nav-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

/* Premium Section Animations */
@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
  50% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.6); }
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Enhanced Theme Utility Classes */
.bg-background-primary { background-color: var(--bg-primary); }
.bg-background-secondary { background-color: var(--bg-secondary); }
.bg-background-tertiary { background-color: var(--bg-tertiary); }
.bg-background-card { background-color: var(--bg-card); }
.bg-background-hover { background-color: var(--bg-hover); }
.bg-background-active { background-color: var(--bg-active); }
.bg-background-border { background-color: var(--bg-border); }
.bg-background-input { background-color: var(--bg-input); }
.bg-background-disabled { background-color: var(--bg-disabled); }

.text-text-primary { color: var(--text-primary); }
.text-text-secondary { color: var(--text-secondary); }
.text-text-tertiary { color: var(--text-tertiary); }
.text-text-inverse { color: var(--text-inverse); }
.text-text-accent { color: var(--text-accent); }
.text-text-success { color: var(--text-success); }
.text-text-warning { color: var(--text-warning); }
.text-text-error { color: var(--text-error); }
.text-text-info { color: var(--text-info); }
.text-text-disabled { color: var(--text-disabled); }

.bg-interactive-primary { background-color: var(--interactive-primary); }
.bg-interactive-primary:hover { background-color: var(--interactive-primary-hover); }
.bg-interactive-primary:active { background-color: var(--interactive-primary-active); }
.bg-interactive-secondary { background-color: var(--interactive-secondary); }
.bg-interactive-secondary:hover { background-color: var(--interactive-secondary-hover); }
.bg-interactive-secondary:active { background-color: var(--interactive-secondary-active); }
.bg-interactive-accent { background-color: var(--interactive-accent); }
.bg-interactive-accent:hover { background-color: var(--interactive-accent-hover); }
.bg-interactive-accent:active { background-color: var(--interactive-accent-active); }

.bg-status-success { background-color: var(--status-success); }
.bg-status-success-bg { background-color: var(--status-success-bg); }
.bg-status-warning { background-color: var(--status-warning); }
.bg-status-warning-bg { background-color: var(--status-warning-bg); }
.bg-status-error { background-color: var(--status-error); }
.bg-status-error-bg { background-color: var(--status-error-bg); }
.bg-status-info { background-color: var(--status-info); }
.bg-status-info-bg { background-color: var(--status-info-bg); }

.border-background-border { border-color: var(--bg-border); }
.border-status-success { border-color: var(--status-success); }
.border-status-warning { border-color: var(--status-warning); }
.border-status-error { border-color: var(--status-error); }
.border-status-info { border-color: var(--status-info); }

/* Touch-friendly minimum sizes */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

.touch-target-large {
  min-height: 48px;
  min-width: 48px;
}

/* Enhanced focus styles for accessibility */
.focus-enhanced:focus {
  outline: 2px solid var(--interactive-primary);
  outline-offset: 2px;
}

/* Better focus management */
.focus-visible:focus-visible {
  outline: 2px solid var(--interactive-primary);
  outline-offset: 2px;
}

/* Improved button states */
.btn-enhanced {
  transition: all 0.2s ease-in-out;
  transform: translateY(0);
}

.btn-enhanced:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-enhanced:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}