#!/usr/bin/env node

/**
 * LocalStorage Cleanup Script
 * 
 * This script removes all localStorage usage from the project
 * and ensures complete migration to MySQL API.
 */

const fs = require('fs').promises;
const path = require('path');

// Files to check for localStorage usage
const SEARCH_PATTERNS = [
  /localStorage\./g,
  /getItem\(/g,
  /setItem\(/g,
  /removeItem\(/g,
  /khanfashariya_db/g,
  /from.*lib\/database/g,
  /import.*database/g
];

// Directories to search
const SEARCH_DIRS = [
  'src/components',
  'src/hooks',
  'src/lib',
  'src/store',
  'src/utils'
];

// Files to exclude from search
const EXCLUDE_FILES = [
  'database.ts', // Keep for type definitions
  'forceApiUsage.ts', // Handles localStorage override
  'dataAdapter.ts' // Migration adapter
];

async function findLocalStorageUsage() {
  console.log('🔍 Searching for localStorage usage...\n');
  
  const results = [];
  
  for (const dir of SEARCH_DIRS) {
    const dirPath = path.join(process.cwd(), dir);
    
    try {
      await searchDirectory(dirPath, results);
    } catch (error) {
      console.warn(`Warning: Could not search directory ${dir}:`, error.message);
    }
  }
  
  return results;
}

async function searchDirectory(dirPath, results) {
  const entries = await fs.readdir(dirPath, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry.name);
    
    if (entry.isDirectory()) {
      await searchDirectory(fullPath, results);
    } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
      // Skip excluded files
      if (EXCLUDE_FILES.some(excluded => entry.name.includes(excluded))) {
        continue;
      }
      
      await searchFile(fullPath, results);
    }
  }
}

async function searchFile(filePath, results) {
  try {
    const content = await fs.readFile(filePath, 'utf8');
    const relativePath = path.relative(process.cwd(), filePath);
    
    const fileResults = {
      file: relativePath,
      issues: []
    };
    
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      SEARCH_PATTERNS.forEach((pattern, patternIndex) => {
        if (pattern.test(line)) {
          fileResults.issues.push({
            line: index + 1,
            content: line.trim(),
            pattern: pattern.source,
            type: getIssueType(patternIndex)
          });
        }
      });
    });
    
    if (fileResults.issues.length > 0) {
      results.push(fileResults);
    }
  } catch (error) {
    console.warn(`Warning: Could not read file ${filePath}:`, error.message);
  }
}

function getIssueType(patternIndex) {
  const types = [
    'localStorage usage',
    'getItem usage',
    'setItem usage', 
    'removeItem usage',
    'legacy database key',
    'database import',
    'database import'
  ];
  return types[patternIndex] || 'unknown';
}

async function generateCleanupReport(results) {
  console.log('📊 LocalStorage Usage Report');
  console.log('============================\n');
  
  if (results.length === 0) {
    console.log('✅ No localStorage usage found! Migration is complete.\n');
    return;
  }
  
  let totalIssues = 0;
  
  results.forEach(fileResult => {
    console.log(`📄 ${fileResult.file}`);
    console.log('─'.repeat(fileResult.file.length + 2));
    
    fileResult.issues.forEach(issue => {
      console.log(`  Line ${issue.line}: ${issue.type}`);
      console.log(`    ${issue.content}`);
      console.log('');
      totalIssues++;
    });
    
    console.log('');
  });
  
  console.log(`📈 Summary: ${totalIssues} issues found in ${results.length} files\n`);
  
  // Generate recommendations
  console.log('🔧 Recommended Actions:');
  console.log('─'.repeat(23));
  
  const recommendations = new Set();
  
  results.forEach(fileResult => {
    fileResult.issues.forEach(issue => {
      if (issue.type === 'localStorage usage') {
        recommendations.add(`• Replace localStorage calls with API calls in ${fileResult.file}`);
      } else if (issue.type === 'database import') {
        recommendations.add(`• Update imports in ${fileResult.file} to use apiServices instead of database`);
      } else if (issue.type === 'legacy database key') {
        recommendations.add(`• Remove legacy database keys in ${fileResult.file}`);
      }
    });
  });
  
  recommendations.forEach(rec => console.log(rec));
  console.log('');
}

async function checkApiServices() {
  console.log('🔌 Checking API Services...\n');
  
  try {
    const apiServicesPath = path.join(process.cwd(), 'src/lib/apiServices.ts');
    const content = await fs.readFile(apiServicesPath, 'utf8');
    
    // Check for required functions
    const requiredFunctions = [
      'getSystemServices',
      'getTechnicalServices', 
      'getAllUsers',
      'getAllOrders',
      'getCurrentUser',
      'signIn',
      'signOut'
    ];
    
    const missingFunctions = requiredFunctions.filter(func => 
      !content.includes(`export const ${func}`) && !content.includes(`export async function ${func}`)
    );
    
    if (missingFunctions.length === 0) {
      console.log('✅ All required API functions are available\n');
    } else {
      console.log('❌ Missing API functions:');
      missingFunctions.forEach(func => console.log(`  • ${func}`));
      console.log('');
    }
    
  } catch (error) {
    console.log('❌ Could not check API services:', error.message);
  }
}

async function checkForceApiUsage() {
  console.log('🔒 Checking Force API Usage...\n');
  
  try {
    const forceApiPath = path.join(process.cwd(), 'src/lib/forceApiUsage.ts');
    const mainPath = path.join(process.cwd(), 'src/main.tsx');
    
    const forceApiExists = await fs.access(forceApiPath).then(() => true).catch(() => false);
    const mainContent = await fs.readFile(mainPath, 'utf8');
    
    if (forceApiExists && mainContent.includes('initializeApiForcing')) {
      console.log('✅ Force API usage is properly configured\n');
    } else {
      console.log('⚠️ Force API usage is not properly configured');
      console.log('   Make sure forceApiUsage.ts exists and is imported in main.tsx\n');
    }
    
  } catch (error) {
    console.log('❌ Could not check force API usage:', error.message);
  }
}

async function main() {
  console.log('🧹 LocalStorage Cleanup Analysis');
  console.log('=================================\n');
  
  // Find localStorage usage
  const results = await findLocalStorageUsage();
  
  // Generate report
  await generateCleanupReport(results);
  
  // Check API services
  await checkApiServices();
  
  // Check force API usage
  await checkForceApiUsage();
  
  // Final status
  if (results.length === 0) {
    console.log('🎉 Cleanup Complete!');
    console.log('✅ No localStorage usage found');
    console.log('✅ Project is ready for production');
  } else {
    console.log('⚠️ Cleanup Required');
    console.log(`❌ ${results.length} files still use localStorage`);
    console.log('🔧 Please address the issues above');
  }
  
  process.exit(results.length === 0 ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Error during cleanup analysis:', error);
    process.exit(1);
  });
}

module.exports = { findLocalStorageUsage, generateCleanupReport };
