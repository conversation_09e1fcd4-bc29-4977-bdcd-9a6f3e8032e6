const axios = require('axios');

async function verifyFixes() {
  console.log('🔍 VERIFYING MYSQL INTEGRATION FIXES\n');
  
  try {
    // 1. Verify Systems API returns tech_specs
    console.log('1️⃣ Testing Systems API with tech_specs...');
    const systemsResponse = await axios.get('http://localhost:3001/api/systems');
    const firstSystem = systemsResponse.data.data.systems[0];
    
    console.log('   📊 System fields:', Object.keys(firstSystem));
    console.log('   🔧 tech_specs_ar:', Array.isArray(firstSystem.tech_specs_ar) ? '✅ Array' : '❌ Missing');
    console.log('   🔧 tech_specs_en:', Array.isArray(firstSystem.tech_specs_en) ? '✅ Array' : '❌ Missing');
    console.log('   🎯 features_ar:', Array.isArray(firstSystem.features_ar) ? '✅ Array' : '❌ Missing');
    
    // 2. Verify Services API returns premium fields
    console.log('\n2️⃣ Testing Services API with premium fields...');
    const servicesResponse = await axios.get('http://localhost:3001/api/services/technical');
    const firstService = servicesResponse.data.data.services[0];
    
    console.log('   📊 Service fields:', Object.keys(firstService));
    console.log('   💎 is_premium_addon:', firstService.is_premium_addon !== undefined ? '✅ Present' : '❌ Missing');
    console.log('   💰 premium_price:', firstService.premium_price !== undefined ? '✅ Present' : '❌ Missing');
    console.log('   📅 subscription_type:', firstService.subscription_type !== undefined ? '✅ Present' : '❌ Missing');
    console.log('   🔧 tech_specs_ar:', Array.isArray(firstService.tech_specs_ar) ? '✅ Array' : '❌ Missing');
    
    // 3. Verify Admin Endpoints
    console.log('\n3️⃣ Testing Admin Endpoints...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.data.tokens.accessToken;
    const headers = { Authorization: `Bearer ${token}` };
    
    const adminSystemsResponse = await axios.get('http://localhost:3001/api/systems/admin', { headers });
    const adminFirstSystem = adminSystemsResponse.data[0];
    console.log('   🖥️ Admin systems tech_specs:', Array.isArray(adminFirstSystem.tech_specs_ar) ? '✅ Array' : '❌ Missing');
    
    const adminServicesResponse = await axios.get('http://localhost:3001/api/services/admin/technical', { headers });
    const adminFirstService = adminServicesResponse.data[0];
    console.log('   🛠️ Admin services premium fields:', adminFirstService.is_premium_addon !== undefined ? '✅ Present' : '❌ Missing');
    
    // 4. Test Data Counts
    console.log('\n4️⃣ Data Counts Verification...');
    console.log(`   🖥️ Systems: ${systemsResponse.data.data.systems.length} (Expected: 8+)`);
    console.log(`   🛠️ Services: ${servicesResponse.data.data.services.length} (Expected: 11+)`);
    console.log(`   👑 Admin Systems: ${adminSystemsResponse.data.length} (Expected: 8+)`);
    console.log(`   🔧 Admin Services: ${adminServicesResponse.data.length} (Expected: 11+)`);
    
    console.log('\n✅ ALL FIXES VERIFIED SUCCESSFULLY!');
    console.log('\n🌐 Website URLs:');
    console.log('   Homepage: http://localhost:5173');
    console.log('   Admin Panel: http://localhost:5173/admin');
    console.log('   Login: <EMAIL> / admin123');
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  }
}

verifyFixes();
