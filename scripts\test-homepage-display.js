const puppeteer = require('puppeteer');

async function testHomepageDisplay() {
  let browser;
  
  try {
    console.log('🌐 Testing Homepage Display...\n');
    
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Set viewport
    await page.setViewport({ width: 1920, height: 1080 });
    
    console.log('📱 Loading homepage...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    // Wait for React to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Test Systems Section
    console.log('\n🖥️ Testing Systems Section...');
    
    const systemsSection = await page.$('#systems');
    if (systemsSection) {
      console.log('✅ Systems section found');
      
      // Count system cards
      const systemCards = await page.$$('#systems .group');
      console.log(`   📊 Found ${systemCards.length} system cards`);
      
      if (systemCards.length > 0) {
        // Get first system details
        const firstSystemTitle = await page.$eval('#systems .group:first-child h3', el => el.textContent);
        const firstSystemPrice = await page.$eval('#systems .group:first-child .text-accent', el => el.textContent);
        console.log(`   📝 First system: ${firstSystemTitle} - ${firstSystemPrice}`);
      }
    } else {
      console.log('❌ Systems section not found');
    }
    
    // Test Services Section
    console.log('\n🛠️ Testing Services Section...');
    
    // Look for services section by different selectors
    const servicesSelectors = [
      '#services',
      '[data-testid="services-section"]',
      '.services-section',
      'section:has(h2:contains("خدمات"))',
      'section:has(h2:contains("Services"))'
    ];
    
    let servicesSection = null;
    for (const selector of servicesSelectors) {
      try {
        servicesSection = await page.$(selector);
        if (servicesSection) {
          console.log(`✅ Services section found with selector: ${selector}`);
          break;
        }
      } catch (e) {
        // Continue to next selector
      }
    }
    
    if (servicesSection) {
      // Count service cards
      const serviceCards = await page.$$('section .group, section .card, section [class*="service"]');
      console.log(`   📊 Found ${serviceCards.length} service cards`);
      
      if (serviceCards.length > 0) {
        console.log('   ✅ Services are displaying');
      } else {
        console.log('   ⚠️ Services section exists but no service cards found');
      }
    } else {
      console.log('❌ Services section not found');
      
      // Check if services data is loaded in console
      const servicesData = await page.evaluate(() => {
        return window.localStorage.getItem('services') || 'No services in localStorage';
      });
      console.log('   🔍 Services in localStorage:', servicesData.substring(0, 100) + '...');
    }
    
    // Test Premium Content Section
    console.log('\n👑 Testing Premium Content Section...');
    
    const premiumSection = await page.$('[class*="premium"], [id*="premium"]');
    if (premiumSection) {
      console.log('✅ Premium section found');
      
      const premiumCards = await page.$$('[class*="premium"] .group, [class*="premium"] .card');
      console.log(`   📊 Found ${premiumCards.length} premium cards`);
    } else {
      console.log('❌ Premium section not found');
    }
    
    // Check for any JavaScript errors
    console.log('\n🔍 Checking for JavaScript errors...');
    
    const errors = await page.evaluate(() => {
      return window.errors || [];
    });
    
    if (errors.length === 0) {
      console.log('✅ No JavaScript errors detected');
    } else {
      console.log(`⚠️ Found ${errors.length} JavaScript errors:`, errors);
    }
    
    // Test API calls
    console.log('\n📡 Testing API calls from frontend...');
    
    const apiCalls = await page.evaluate(async () => {
      const results = {};
      
      try {
        // Test systems API
        const systemsResponse = await fetch('/api/systems');
        const systemsData = await systemsResponse.json();
        results.systems = {
          status: systemsResponse.status,
          count: systemsData.data?.systems?.length || systemsData.data?.length || 0
        };
      } catch (e) {
        results.systems = { error: e.message };
      }
      
      try {
        // Test services API
        const servicesResponse = await fetch('/api/services/technical');
        const servicesData = await servicesResponse.json();
        results.services = {
          status: servicesResponse.status,
          count: servicesData.data?.services?.length || servicesData.data?.length || 0
        };
      } catch (e) {
        results.services = { error: e.message };
      }
      
      return results;
    });
    
    console.log('   🖥️ Systems API:', apiCalls.systems);
    console.log('   🛠️ Services API:', apiCalls.services);
    
    console.log('\n🎉 Homepage display testing completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testHomepageDisplay();
