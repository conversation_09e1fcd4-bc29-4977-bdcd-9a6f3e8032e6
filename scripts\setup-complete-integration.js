/**
 * Complete MySQL Integration Setup Script
 * 
 * This script ensures complete integration between frontend and MySQL:
 * 1. Verifies database setup
 * 2. Seeds necessary data
 * 3. Tests API endpoints
 * 4. Validates frontend integration
 */

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'khanfashariya_db'
};

class CompleteIntegrationSetup {
  constructor() {
    this.connection = null;
  }

  async run() {
    console.log('🚀 Setting up Complete MySQL Integration...\n');
    
    try {
      await this.connectDatabase();
      await this.verifyTables();
      await this.seedInitialData();
      await this.createTestData();
      await this.verifyIntegration();
      
      console.log('\n✅ Complete MySQL Integration setup finished successfully!');
      console.log('\n📋 Next Steps:');
      console.log('1. Start the server: npm run dev:server');
      console.log('2. Start the frontend: npm run dev');
      console.log('3. Test admin <NAME_EMAIL> / admin123');
      console.log('4. Test user <NAME_EMAIL> / user123');
      
    } catch (error) {
      console.error('❌ Setup failed:', error.message);
      throw error;
    } finally {
      if (this.connection) {
        await this.connection.end();
      }
    }
  }

  async connectDatabase() {
    console.log('📊 Connecting to database...');
    this.connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ Database connected successfully');
  }

  async verifyTables() {
    console.log('\n🔍 Verifying database tables...');
    
    const requiredTables = [
      'users', 'orders', 'system_services', 'technical_services', 
      'premium_content', 'premium_packages', 'user_sessions', 'activity_logs'
    ];
    
    for (const table of requiredTables) {
      try {
        const [rows] = await this.connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`✅ Table '${table}' exists with ${rows[0].count} records`);
      } catch (error) {
        console.log(`❌ Table '${table}' missing or inaccessible`);
        throw new Error(`Required table '${table}' is missing`);
      }
    }
  }

  async seedInitialData() {
    console.log('\n🌱 Seeding initial data...');
    
    // Create admin user if not exists
    const [adminUsers] = await this.connection.execute(
      'SELECT id FROM users WHERE email = ?',
      ['<EMAIL>']
    );
    
    if (adminUsers.length === 0) {
      const hashedPassword = await bcrypt.hash('admin123', 10);
      await this.connection.execute(`
        INSERT INTO users (id, email, username, full_name, password_hash, role, status, email_verified, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        this.generateUUID(),
        '<EMAIL>',
        'admin',
        'System Administrator',
        hashedPassword,
        'admin',
        'active',
        true
      ]);
      console.log('✅ Admin user created');
    } else {
      console.log('✅ Admin user already exists');
    }
    
    // Create test user if not exists
    const [testUsers] = await this.connection.execute(
      'SELECT id FROM users WHERE email = ?',
      ['<EMAIL>']
    );
    
    if (testUsers.length === 0) {
      const hashedPassword = await bcrypt.hash('user123', 10);
      await this.connection.execute(`
        INSERT INTO users (id, email, username, full_name, password_hash, role, status, email_verified, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        this.generateUUID(),
        '<EMAIL>',
        'testuser',
        'Test User',
        hashedPassword,
        'user',
        'active',
        true
      ]);
      console.log('✅ Test user created');
    } else {
      console.log('✅ Test user already exists');
    }
  }

  async createTestData() {
    console.log('\n🧪 Creating test data...');
    
    // Create sample system services
    const [systemServices] = await this.connection.execute('SELECT COUNT(*) as count FROM system_services');
    if (systemServices[0].count === 0) {
      const sampleServices = [
        {
          name_ar: 'نظام إدارة المحتوى',
          name_en: 'Content Management System',
          description_ar: 'نظام شامل لإدارة المحتوى الرقمي',
          description_en: 'Comprehensive digital content management system',
          price: 299.99,
          category: 'web_development'
        },
        {
          name_ar: 'نظام التجارة الإلكترونية',
          name_en: 'E-commerce Platform',
          description_ar: 'منصة متكاملة للتجارة الإلكترونية',
          description_en: 'Complete e-commerce platform solution',
          price: 499.99,
          category: 'e_commerce'
        }
      ];
      
      for (const service of sampleServices) {
        await this.connection.execute(`
          INSERT INTO system_services (
            id, name_ar, name_en, description_ar, description_en, 
            price, category, features_ar, features_en, tech_specs_ar, tech_specs_en,
            status, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          this.generateUUID(),
          service.name_ar,
          service.name_en,
          service.description_ar,
          service.description_en,
          service.price,
          service.category,
          JSON.stringify(['ميزة 1', 'ميزة 2', 'ميزة 3']),
          JSON.stringify(['Feature 1', 'Feature 2', 'Feature 3']),
          JSON.stringify(['مواصفة 1', 'مواصفة 2']),
          JSON.stringify(['Spec 1', 'Spec 2']),
          'active'
        ]);
      }
      console.log('✅ Sample system services created');
    }
    
    // Create sample technical services
    const [techServices] = await this.connection.execute('SELECT COUNT(*) as count FROM technical_services');
    if (techServices[0].count === 0) {
      const sampleTechServices = [
        {
          name_ar: 'تطوير تطبيقات الجوال',
          name_en: 'Mobile App Development',
          description_ar: 'تطوير تطبيقات الجوال للأندرويد والآيفون',
          description_en: 'Mobile application development for Android and iOS',
          price: 799.99,
          category: 'mobile_development'
        },
        {
          name_ar: 'تصميم واجهات المستخدم',
          name_en: 'UI/UX Design',
          description_ar: 'تصميم واجهات مستخدم احترافية وتجربة مستخدم مميزة',
          description_en: 'Professional UI design and exceptional user experience',
          price: 399.99,
          category: 'design'
        }
      ];
      
      for (const service of sampleTechServices) {
        await this.connection.execute(`
          INSERT INTO technical_services (
            id, name_ar, name_en, description_ar, description_en, 
            price, category, features_ar, features_en, tech_specs_ar, tech_specs_en,
            status, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          this.generateUUID(),
          service.name_ar,
          service.name_en,
          service.description_ar,
          service.description_en,
          service.price,
          service.category,
          JSON.stringify(['ميزة 1', 'ميزة 2', 'ميزة 3']),
          JSON.stringify(['Feature 1', 'Feature 2', 'Feature 3']),
          JSON.stringify(['مواصفة 1', 'مواصفة 2']),
          JSON.stringify(['Spec 1', 'Spec 2']),
          'active'
        ]);
      }
      console.log('✅ Sample technical services created');
    }
    
    // Create sample orders
    const [orders] = await this.connection.execute('SELECT COUNT(*) as count FROM orders');
    if (orders[0].count === 0) {
      const [users] = await this.connection.execute('SELECT id FROM users WHERE role = "user" LIMIT 1');
      if (users.length > 0) {
        const userId = users[0].id;
        const [services] = await this.connection.execute('SELECT id, name_en, price FROM system_services LIMIT 1');
        
        if (services.length > 0) {
          const service = services[0];
          await this.connection.execute(`
            INSERT INTO orders (
              id, user_id, order_number, order_type, item_id, item_name_ar, item_name_en,
              quantity, unit_price, final_price, status, payment_status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
          `, [
            this.generateUUID(),
            userId,
            'ORD-' + Date.now(),
            'system_service',
            service.id,
            'نظام إدارة المحتوى',
            service.name_en,
            1,
            service.price,
            service.price,
            'pending',
            'pending'
          ]);
          console.log('✅ Sample order created');
        }
      }
    }
  }

  async verifyIntegration() {
    console.log('\n🔍 Verifying integration...');
    
    // Check data counts
    const tables = ['users', 'orders', 'system_services', 'technical_services'];
    for (const table of tables) {
      const [rows] = await this.connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
      console.log(`✅ ${table}: ${rows[0].count} records`);
    }
    
    // Verify admin user
    const [adminUser] = await this.connection.execute(
      'SELECT email, role FROM users WHERE email = ?',
      ['<EMAIL>']
    );
    
    if (adminUser.length > 0 && adminUser[0].role === 'admin') {
      console.log('✅ Admin user verified');
    } else {
      throw new Error('Admin user verification failed');
    }
    
    // Verify test user
    const [testUser] = await this.connection.execute(
      'SELECT email, role FROM users WHERE email = ?',
      ['<EMAIL>']
    );
    
    if (testUser.length > 0 && testUser[0].role === 'user') {
      console.log('✅ Test user verified');
    } else {
      throw new Error('Test user verification failed');
    }
  }

  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
}

// Run the setup
if (require.main === module) {
  const setup = new CompleteIntegrationSetup();
  setup.run().catch(error => {
    console.error('Setup failed:', error);
    process.exit(1);
  });
}

module.exports = CompleteIntegrationSetup;