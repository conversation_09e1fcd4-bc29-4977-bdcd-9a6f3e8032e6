/**
 * Fix Admin Components Script
 * 
 * This script fixes language data access issues in all admin components
 * by adding safe data access helpers and normalizing data formats.
 */

const fs = require('fs');
const path = require('path');

const ADMIN_COMPONENTS_DIR = path.join(__dirname, '..', 'src', 'components', 'admin');

// Helper functions to add to each component
const HELPER_FUNCTIONS = `
  // Helper function to safely get text in current language
  const getSystemText = (system: any, field: string): string => {
    if (!system) return '';
    
    // Try new format first (nested object)
    if (system[field] && typeof system[field] === 'object') {
      return system[field][language] || '';
    }
    
    // Try old format (separate fields)
    const fieldKey = \`\${field}_\${language}\`;
    return system[fieldKey] || '';
  };

  // Helper function to safely get array in current language
  const getSystemArray = (system: any, field: string): string[] => {
    if (!system) return [];
    
    // Try new format first (nested object)
    if (system[field] && typeof system[field] === 'object' && Array.isArray(system[field][language])) {
      return system[field][language];
    }
    
    // Try old format (separate fields)
    const fieldKey = \`\${field}_\${language}\`;
    return Array.isArray(system[fieldKey]) ? system[fieldKey] : [];
  };

  // Helper function to safely normalize system data
  const normalizeSystemData = (system: any): any => {
    if (!system) return {};
    
    return {
      ...system,
      name: system.name || { ar: system.name_ar || '', en: system.name_en || '' },
      description: system.description || { ar: system.description_ar || '', en: system.description_en || '' },
      features: system.features || { ar: system.features_ar || [], en: system.features_en || [] },
      tech_specs: system.tech_specs || { ar: system.tech_specs_ar || [], en: system.tech_specs_en || [] },
      name_ar: system.name_ar || system.name?.ar || '',
      name_en: system.name_en || system.name?.en || '',
      description_ar: system.description_ar || system.description?.ar || '',
      description_en: system.description_en || system.description?.en || '',
      features_ar: system.features_ar || system.features?.ar || [],
      features_en: system.features_en || system.features?.en || [],
      tech_specs_ar: system.tech_specs_ar || system.tech_specs?.ar || [],
      tech_specs_en: system.tech_specs_en || system.tech_specs?.en || [],
      price: system.price || 0,
      category: system.category || 'general',
      status: system.status || 'active',
      video_url: system.video_url || '',
      image_url: system.image_url || '',
      gallery_images: system.gallery_images || [],
      created_at: system.created_at || new Date().toISOString(),
      updated_at: system.updated_at || new Date().toISOString()
    };
  };
`;

// Patterns to replace
const REPLACEMENTS = [
  // Replace direct field access with helper functions
  {
    pattern: /language === 'ar' \? system\.name_ar : system\.name_en/g,
    replacement: 'getSystemText(system, \'name\')'
  },
  {
    pattern: /language === 'ar' \? system\.description_ar : system\.description_en/g,
    replacement: 'getSystemText(system, \'description\')'
  },
  {
    pattern: /language === 'ar' \? \(Array\.isArray\(system\.features_ar\) \? system\.features_ar : \[\]\) : \(Array\.isArray\(system\.features_en\) \? system\.features_en : \[\]\)/g,
    replacement: 'getSystemArray(system, \'features\')'
  },
  {
    pattern: /language === 'ar' \? \(system\.features_ar \|\| \[\]\) : \(system\.features_en \|\| \[\]\)/g,
    replacement: 'getSystemArray(system, \'features\')'
  },
  // Add more patterns as needed
];

class AdminComponentsFixer {
  constructor() {
    this.fixedFiles = [];
    this.errors = [];
  }

  async fixAllComponents() {
    console.log('🔧 Starting Admin Components Fix...\n');
    
    try {
      const files = await this.getAdminComponentFiles();
      
      for (const file of files) {
        await this.fixComponent(file);
      }
      
      this.printResults();
    } catch (error) {
      console.error('❌ Fix failed:', error.message);
      throw error;
    }
  }

  async getAdminComponentFiles() {
    const files = [];
    
    if (fs.existsSync(ADMIN_COMPONENTS_DIR)) {
      const dirFiles = fs.readdirSync(ADMIN_COMPONENTS_DIR);
      
      for (const file of dirFiles) {
        if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          files.push(path.join(ADMIN_COMPONENTS_DIR, file));
        }
      }
    }
    
    return files;
  }

  async fixComponent(filePath) {
    try {
      const fileName = path.basename(filePath);
      console.log(`🔍 Checking ${fileName}...`);
      
      if (!fs.existsSync(filePath)) {
        console.log(`⚠️  ${fileName}: File not found, skipping`);
        return;
      }
      
      let content = fs.readFileSync(filePath, 'utf8');
      
      if (content.trim().length === 0) {
        console.log(`⚠️  ${fileName}: Empty file, skipping`);
        return;
      }
      
      let modified = false;
      
      // Check if helper functions already exist
      if (!content.includes('getSystemText')) {
        // Find the right place to insert helper functions
        const componentMatch = content.match(/const\s+\w+:\s*React\.FC/);
        if (componentMatch) {
          const insertIndex = componentMatch.index;
          content = content.slice(0, insertIndex) + HELPER_FUNCTIONS + '\n' + content.slice(insertIndex);
          modified = true;
        }
      }
      
      // Apply replacements
      for (const replacement of REPLACEMENTS) {
        const originalContent = content;
        content = content.replace(replacement.pattern, replacement.replacement);
        if (content !== originalContent) {
          modified = true;
        }
      }
      
      // Add safe data loading
      if (content.includes('setSystems(systemsData)') && !content.includes('normalizeSystemData')) {
        content = content.replace(
          'setSystems(systemsData)',
          'setSystems(systemsData.map(normalizeSystemData))'
        );
        modified = true;
      }
      
      if (modified) {
        fs.writeFileSync(filePath, content);
        this.fixedFiles.push(fileName);
        console.log(`✅ ${fileName}: Fixed`);
      } else {
        console.log(`✨ ${fileName}: Already good`);
      }
      
    } catch (error) {
      const fileName = path.basename(filePath);
      this.errors.push({ file: fileName, error: error.message });
      console.log(`❌ ${fileName}: Error - ${error.message}`);
    }
  }

  printResults() {
    console.log('\n📋 Fix Results:');
    console.log('================');
    
    console.log(`✅ Fixed files: ${this.fixedFiles.length}`);
    if (this.fixedFiles.length > 0) {
      this.fixedFiles.forEach(file => console.log(`   - ${file}`));
    }
    
    console.log(`❌ Errors: ${this.errors.length}`);
    if (this.errors.length > 0) {
      this.errors.forEach(({ file, error }) => console.log(`   - ${file}: ${error}`));
    }
    
    if (this.errors.length === 0) {
      console.log('\n🎉 All admin components have been fixed successfully!');
      console.log('\n💡 Next steps:');
      console.log('1. Test the admin dashboard');
      console.log('2. Check all admin functions');
      console.log('3. Verify language switching works');
    } else {
      console.log('\n⚠️  Some components had issues. Please check the errors above.');
    }
  }
}

// Run the fixer
if (require.main === module) {
  const fixer = new AdminComponentsFixer();
  fixer.fixAllComponents().catch(console.error);
}

module.exports = AdminComponentsFixer;