#!/usr/bin/env node

/**
 * إعداد جدول token blacklist لتحسين session management
 */

const { executeQuery } = require('../server/config/database');
const fs = require('fs');
const path = require('path');

async function setupTokenBlacklist() {
  try {
    console.log('🔧 إعداد جدول token blacklist...');
    
    // قراءة SQL script
    const sqlPath = path.join(__dirname, 'create-token-blacklist-table.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // تنفيذ SQL
    await executeQuery(sql);
    
    console.log('✅ تم إنشاء جدول token_blacklist بنجاح');
    
    // اختبار الجدول
    const { rows } = await executeQuery('DESCRIBE token_blacklist');
    console.log('📋 بنية الجدول:');
    rows.forEach(row => {
      console.log(`  - ${row.Field}: ${row.Type} ${row.Null === 'NO' ? 'NOT NULL' : ''}`);
    });
    
    console.log('🎉 إعداد token blacklist مكتمل!');
    
  } catch (error) {
    console.error('❌ خطأ في إعداد token blacklist:', error.message);
    process.exit(1);
  }
}

// تشغيل السكريبت
if (require.main === module) {
  setupTokenBlacklist();
}

module.exports = { setupTokenBlacklist };
