import React from 'react';
import { Search, Download, Upload, Settings, Filter, Grid, List } from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';
import Input from './Input';
import GoldenButton from './GoldenButton';
import GoldenFilter from './GoldenFilter';
import Card from './Card';

interface FilterConfig {
  key: string;
  label: string;
  labelAr: string;
  options: Array<{
    value: string;
    label: string;
    labelAr: string;
  }>;
}

interface GoldenFilterGridProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  searchPlaceholder?: string;
  searchPlaceholderAr?: string;
  filters: FilterConfig[];
  filterValues: Record<string, string>;
  onFilterChange: (key: string, value: string) => void;
  viewMode?: 'grid' | 'list';
  onViewModeChange?: (mode: 'grid' | 'list') => void;
  showViewToggle?: boolean;
  showExportButtons?: boolean;
  onExport?: () => void;
  onImport?: () => void;
  onAdvancedSettings?: () => void;
  resultCount?: number;
  className?: string;
  compact?: boolean;
  position?: 'top' | 'bottom' | 'horizontal';
}

/**
 * Enhanced Golden Ratio Filter Grid Component
 * Unified filter system with improved design and accessibility
 */
const GoldenFilterGrid: React.FC<GoldenFilterGridProps> = ({
  searchTerm,
  onSearchChange,
  searchPlaceholder,
  searchPlaceholderAr,
  filters,
  filterValues,
  onFilterChange,
  viewMode = 'grid',
  onViewModeChange,
  showViewToggle = true,
  showExportButtons = true,
  onExport,
  onImport,
  onAdvancedSettings,
  resultCount,
  className = '',
  compact = false,
  position = 'top'
}) => {
  const { language } = useTranslation();

  const displaySearchPlaceholder = language === 'ar' && searchPlaceholderAr
    ? searchPlaceholderAr
    : searchPlaceholder || (language === 'ar' ? 'البحث...' : 'Search...');

  const cardClasses = `
    golden-card
    ${position === 'top' ? 'mb-6' : position === 'bottom' ? 'mt-6' : 'mb-4'}
    golden-shadow-lg
    ${compact ? 'golden-compact' : ''}
    ${position === 'horizontal' ? 'golden-horizontal-layout' : ''}
    ${className}
  `;

  return (
    <Card className={cardClasses}>
      <Card.Body className={compact ? "golden-spacing-md" : "golden-spacing-lg"}>
        <div className={compact ? "space-y-4" : "space-y-6"}>
          {/* Enhanced Search and Quick Actions */}
          <div className="golden-flex flex-col xl:flex-row items-stretch xl:items-center gap-4">
            {/* Search Section */}
            <div className="flex-1 min-w-0">
              <div className="relative">
                <Input
                  placeholder={displaySearchPlaceholder}
                  value={searchTerm}
                  onChange={(e) => onSearchChange(e.target.value)}
                  leftIcon={<Search className="w-5 h-5 text-gray-400" />}
                  className="golden-input w-full pr-4 pl-12 py-3 bg-gray-800/50 border-gray-600 focus:border-blue-500 focus:ring-blue-500/20 text-white placeholder-gray-400"
                />

              </div>
            </div>

            {/* Action Buttons */}
            {showExportButtons && (
              <div className="flex flex-wrap gap-3 justify-end">
                {onExport && (
                  <GoldenButton
                    variant="info"
                    size={compact ? "sm" : "md"}
                    icon={Download}
                    onClick={onExport}
                    className="enhanced-export-btn"
                  >
                    {language === 'ar' ? 'تصدير' : 'Export'}
                  </GoldenButton>
                )}

                {onImport && (
                  <GoldenButton
                    variant="success"
                    size={compact ? "sm" : "md"}
                    icon={Upload}
                    onClick={onImport}
                    className="enhanced-import-btn"
                  >
                    {language === 'ar' ? 'استيراد' : 'Import'}
                  </GoldenButton>
                )}

                {onAdvancedSettings && (
                  <GoldenButton
                    variant="outline"
                    size={compact ? "sm" : "md"}
                    icon={Settings}
                    onClick={onAdvancedSettings}
                    className="enhanced-settings-btn"
                  >
                    {language === 'ar' ? 'إعدادات متقدمة' : 'Advanced Settings'}
                  </GoldenButton>
                )}

                {/* View Mode Toggle */}
                {showViewToggle && onViewModeChange && (
                  <div className="flex border border-gray-600 rounded-lg overflow-hidden">
                    <button
                      onClick={() => onViewModeChange('grid')}
                      className={`px-3 py-2 transition-all duration-200 ${
                        viewMode === 'grid'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                      title={language === 'ar' ? 'عرض الشبكة' : 'Grid View'}
                    >
                      <Grid className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => onViewModeChange('list')}
                      className={`px-3 py-2 transition-all duration-200 ${
                        viewMode === 'list'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                      title={language === 'ar' ? 'عرض القائمة' : 'List View'}
                    >
                      <List className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Enhanced Filters Row - Responsive Golden Ratio Grid */}
          {filters.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm font-medium text-gray-300">
                <Filter className="w-4 h-4" />
                <span>{language === 'ar' ? 'تصفية النتائج' : 'Filter Results'}</span>
              </div>
              <div className={`
                grid gap-4
                ${filters.length === 1 ? 'grid-cols-1' : ''}
                ${filters.length === 2 ? 'grid-cols-1 md:grid-cols-2' : ''}
                ${filters.length === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : ''}
                ${filters.length === 4 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4' : ''}
                ${filters.length >= 5 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5' : ''}
              `}>
                {filters.map((filter) => (
                  <GoldenFilter
                    key={filter.key}
                    label={filter.label}
                    labelAr={filter.labelAr}
                    value={filterValues[filter.key] || ''}
                    onChange={(value) => onFilterChange(filter.key, value)}
                    options={filter.options}
                    className="enhanced-filter"
                  />
                ))}
              </div>
            </div>
          )}

          {/* View Mode Toggle and Result Count */}
          {(showViewToggle || resultCount !== undefined) && (
            <div className="flex items-center justify-between pt-6 border-t border-accent/20">
              {showViewToggle && onViewModeChange && (
                <div className="golden-flex items-center">
                  <span className="golden-text-sm font-semibold text-secondary mr-4 rtl:mr-0 rtl:ml-4">
                    {language === 'ar' ? 'طريقة العرض:' : 'View Mode:'}
                  </span>
                  <div className="golden-flex bg-gray-800/50 rounded-xl p-1">
                    <button
                      onClick={() => onViewModeChange('grid')}
                      className={`golden-btn-sm px-6 font-semibold transition-all duration-300 ${
                        viewMode === 'grid'
                          ? 'bg-gradient-to-r from-secondary to-accent text-white shadow-lg scale-105'
                          : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                      }`}
                    >
                      {language === 'ar' ? 'شبكة' : 'Grid'}
                    </button>
                    <button
                      onClick={() => onViewModeChange('list')}
                      className={`golden-btn-sm px-6 font-semibold transition-all duration-300 ${
                        viewMode === 'list'
                          ? 'bg-gradient-to-r from-secondary to-accent text-white shadow-lg scale-105'
                          : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                      }`}
                    >
                      {language === 'ar' ? 'قائمة' : 'List'}
                    </button>
                  </div>
                </div>
              )}
              
              {resultCount !== undefined && (
                <div className="golden-text-sm text-white bg-accent/20 px-3 py-1 rounded-lg border border-accent/30">
                  <span className="font-medium">{resultCount}</span> {language === 'ar' ? 'نتيجة' : 'results'}
                </div>
              )}
            </div>
          )}
        </div>
      </Card.Body>
    </Card>
  );
};

export default GoldenFilterGrid;
