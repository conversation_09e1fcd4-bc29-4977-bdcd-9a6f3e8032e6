/**
 * Database Health Monitoring Script
 * 
 * This script continuously monitors database health and logs issues
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'khanfashariya_db'
};

class DatabaseHealthMonitor {
  constructor() {
    this.pool = null;
    this.healthStats = {
      totalChecks: 0,
      successfulChecks: 0,
      failedChecks: 0,
      lastError: null,
      lastSuccessTime: null,
      consecutiveFailures: 0,
      maxConsecutiveFailures: 0
    };
    this.isRunning = false;
  }

  async initialize() {
    console.log('🔍 Initializing Database Health Monitor...');
    
    try {
      this.pool = mysql.createPool({
        ...DB_CONFIG,
        connectionLimit: 5,
        queueLimit: 0
      });
      
      console.log('✅ Database Health Monitor initialized');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize monitor:', error.message);
      return false;
    }
  }

  async checkHealth() {
    this.healthStats.totalChecks++;
    
    try {
      const connection = await this.pool.getConnection();
      
      // Test basic connectivity
      await connection.ping();
      
      // Test query execution
      const [rows] = await connection.execute('SELECT 1 as test, NOW() as server_time');
      
      // Test database access
      const [tableCheck] = await connection.execute(`
        SELECT COUNT(*) as table_count 
        FROM information_schema.tables 
        WHERE table_schema = ?
      `, [DB_CONFIG.database]);
      
      connection.release();
      
      // Update success stats
      this.healthStats.successfulChecks++;
      this.healthStats.lastSuccessTime = new Date();
      this.healthStats.consecutiveFailures = 0;
      
      return {
        status: 'healthy',
        responseTime: Date.now(),
        tableCount: tableCheck[0].table_count,
        serverTime: rows[0].server_time
      };
      
    } catch (error) {
      this.healthStats.failedChecks++;
      this.healthStats.lastError = error.message;
      this.healthStats.consecutiveFailures++;
      
      if (this.healthStats.consecutiveFailures > this.healthStats.maxConsecutiveFailures) {
        this.healthStats.maxConsecutiveFailures = this.healthStats.consecutiveFailures;
      }
      
      return {
        status: 'unhealthy',
        error: error.message,
        code: error.code,
        consecutiveFailures: this.healthStats.consecutiveFailures
      };
    }
  }

  async startMonitoring(intervalSeconds = 30) {
    if (this.isRunning) {
      console.log('⚠️  Monitor is already running');
      return;
    }
    
    console.log(`🚀 Starting database health monitoring (every ${intervalSeconds}s)...`);
    this.isRunning = true;
    
    const monitorLoop = async () => {
      if (!this.isRunning) return;
      
      const health = await this.checkHealth();
      const timestamp = new Date().toLocaleString();
      
      if (health.status === 'healthy') {
        console.log(`✅ [${timestamp}] Database healthy - Tables: ${health.tableCount}`);
        
        // Log success rate periodically
        if (this.healthStats.totalChecks % 10 === 0) {
          const successRate = ((this.healthStats.successfulChecks / this.healthStats.totalChecks) * 100).toFixed(2);
          console.log(`📊 Health Stats: ${successRate}% success rate (${this.healthStats.successfulChecks}/${this.healthStats.totalChecks})`);
        }
      } else {
        console.error(`❌ [${timestamp}] Database unhealthy: ${health.error}`);
        console.error(`🔥 Consecutive failures: ${health.consecutiveFailures}`);
        
        // Alert on critical failures
        if (health.consecutiveFailures >= 3) {
          console.error('🚨 CRITICAL: Database has been unhealthy for multiple checks!');
          console.error('🚨 This may cause data loss or service interruption!');
        }
      }
      
      // Schedule next check
      setTimeout(monitorLoop, intervalSeconds * 1000);
    };
    
    // Start monitoring
    monitorLoop();
  }

  stopMonitoring() {
    console.log('🛑 Stopping database health monitoring...');
    this.isRunning = false;
    
    if (this.pool) {
      this.pool.end();
    }
  }

  getStats() {
    const successRate = this.healthStats.totalChecks > 0 
      ? ((this.healthStats.successfulChecks / this.healthStats.totalChecks) * 100).toFixed(2)
      : 0;
    
    return {
      ...this.healthStats,
      successRate: `${successRate}%`,
      uptime: this.healthStats.lastSuccessTime 
        ? `Last success: ${this.healthStats.lastSuccessTime.toLocaleString()}`
        : 'Never successful'
    };
  }

  printStats() {
    const stats = this.getStats();
    
    console.log('\n📊 Database Health Statistics:');
    console.log('================================');
    console.log(`Total Checks: ${stats.totalChecks}`);
    console.log(`Successful: ${stats.successfulChecks}`);
    console.log(`Failed: ${stats.failedChecks}`);
    console.log(`Success Rate: ${stats.successRate}`);
    console.log(`Max Consecutive Failures: ${stats.maxConsecutiveFailures}`);
    console.log(`Current Consecutive Failures: ${stats.consecutiveFailures}`);
    console.log(`${stats.uptime}`);
    
    if (stats.lastError) {
      console.log(`Last Error: ${stats.lastError}`);
    }
  }
}

// Run monitor if called directly
if (require.main === module) {
  const monitor = new DatabaseHealthMonitor();
  
  monitor.initialize().then(success => {
    if (success) {
      monitor.startMonitoring(30); // Check every 30 seconds
      
      // Print stats every 5 minutes
      setInterval(() => {
        monitor.printStats();
      }, 5 * 60 * 1000);
      
      // Graceful shutdown
      process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down monitor...');
        monitor.printStats();
        monitor.stopMonitoring();
        process.exit(0);
      });
      
    } else {
      console.error('❌ Failed to start monitoring');
      process.exit(1);
    }
  });
}

module.exports = DatabaseHealthMonitor;