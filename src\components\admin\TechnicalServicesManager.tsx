import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotification } from '../../hooks/useNotification';
import { 
  getTechnicalServices,
  createTechnicalService,
  updateTechnicalService,
  deleteTechnicalService
} from '../../lib/apiServices';
import {
  TechnicalService,
  TranslatedText
} from '../../lib/database';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Search,
  Filter,
  Download,
  Upload,
  Star,
  Wrench,
  Settings,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  Image as ImageIcon,
  Video,
  Save,
  X,
  ArrowLeft,
  Users,
  Calendar,
  AlertCircle
} from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Modal from '../ui/Modal';
import Card from '../ui/Card';
import Pagination from '../ui/Pagination';
import GoldenFilterGrid from '../ui/GoldenFilterGrid';
import BackButton from '../ui/BackButton';
import { useButtonActions } from '../../utils/buttonActions';

interface TechnicalServicesManagerProps {
  onBack?: () => void;
}

const categories = ['general', 'server', 'client', 'database', 'security', 'performance'];
const serviceTypes = ['installation', 'maintenance', 'customization', 'support', 'emergency', 'consultation'];

// Helper function to safely get text in current language
const getServiceText = (service: any, field: string, language: string): string => {
  if (!service) return '';
  
  // Try new format first (nested object)
  if (service[field] && typeof service[field] === 'object') {
    return service[field][language] || '';
  }
  
  // Try old format (separate fields)
  const fieldKey = `${field}_${language}`;
  return service[fieldKey] || '';
};

// Helper function to safely get array in current language
const getServiceArray = (service: any, field: string, language: string): string[] => {
  if (!service) return [];
  
  // Try new format first (nested object)
  if (service[field] && typeof service[field] === 'object' && Array.isArray(service[field][language])) {
    return service[field][language];
  }
  
  // Try old format (separate fields)
  const fieldKey = `${field}_${language}`;
  return Array.isArray(service[fieldKey]) ? service[fieldKey] : [];
};

// Helper function to safely normalize service data
const normalizeServiceData = (service: any): TechnicalService => {
  if (!service) return {} as TechnicalService;
  
  return {
    ...service,
    name: service.name || { ar: service.name_ar || '', en: service.name_en || '' },
    description: service.description || { ar: service.description_ar || '', en: service.description_en || '' },
    features: service.features || { ar: service.features_ar || [], en: service.features_en || [] },
    tech_specs: service.tech_specs || { ar: service.tech_specs_ar || [], en: service.tech_specs_en || [] },
    name_ar: service.name_ar || service.name?.ar || '',
    name_en: service.name_en || service.name?.en || '',
    description_ar: service.description_ar || service.description?.ar || '',
    description_en: service.description_en || service.description?.en || '',
    features_ar: service.features_ar || service.features?.ar || [],
    features_en: service.features_en || service.features?.en || [],
    tech_specs_ar: service.tech_specs_ar || service.tech_specs?.ar || [],
    tech_specs_en: service.tech_specs_en || service.tech_specs?.en || [],
    price: service.price || 0,
    category: service.category || 'general',
    status: service.status || 'active',
    video_url: service.video_url || '',
    image_url: service.image_url || '',
    gallery_images: service.gallery_images || [],
    created_at: service.created_at || new Date().toISOString(),
    updated_at: service.updated_at || new Date().toISOString()
  };
};

// Filter configuration for GoldenFilterGrid
const TECHNICAL_SERVICES_FILTERS = [
  {
    key: 'status',
    label: 'Status',
    labelAr: 'الحالة',
    options: [
      { value: 'all', label: 'All Status', labelAr: 'جميع الحالات' },
      { value: 'active', label: 'Active', labelAr: 'نشط' },
      { value: 'inactive', label: 'Inactive', labelAr: 'غير نشط' }
    ]
  },
  {
    key: 'category',
    label: 'Category',
    labelAr: 'الفئة',
    options: [
      { value: 'all', label: 'All Categories', labelAr: 'جميع الفئات' },
      { value: 'general', label: 'General', labelAr: 'عام' },
      { value: 'server', label: 'Server', labelAr: 'سيرفر' },
      { value: 'client', label: 'Client', labelAr: 'كلاينت' },
      { value: 'database', label: 'Database', labelAr: 'قاعدة بيانات' },
      { value: 'security', label: 'Security', labelAr: 'أمان' },
      { value: 'performance', label: 'Performance', labelAr: 'أداء' }
    ]
  },
  {
    key: 'serviceType',
    label: 'Service Type',
    labelAr: 'نوع الخدمة',
    options: [
      { value: 'all', label: 'All Types', labelAr: 'جميع الأنواع' },
      { value: 'installation', label: 'Installation', labelAr: 'تنصيب' },
      { value: 'maintenance', label: 'Maintenance', labelAr: 'صيانة' },
      { value: 'customization', label: 'Customization', labelAr: 'تخصيص' },
      { value: 'support', label: 'Support', labelAr: 'دعم فني' },
      { value: 'emergency', label: 'Emergency', labelAr: 'طوارئ' },
      { value: 'consultation', label: 'Consultation', labelAr: 'استشارة' }
    ]
  },
  {
    key: 'priceRange',
    label: 'Price Range',
    labelAr: 'نطاق السعر',
    options: [
      { value: 'all', label: 'All Prices', labelAr: 'جميع الأسعار' },
      { value: '0-50', label: '$0 - $50', labelAr: '$0 - $50' },
      { value: '50-100', label: '$50 - $100', labelAr: '$50 - $100' },
      { value: '100-200', label: '$100 - $200', labelAr: '$100 - $200' },
      { value: '200-500', label: '$200 - $500', labelAr: '$200 - $500' },
      { value: '500+', label: '$500+', labelAr: '$500+' }
    ]
  },
  {
    key: 'sortBy',
    label: 'Sort By',
    labelAr: 'ترتيب حسب',
    options: [
      { value: 'name', label: 'Name', labelAr: 'الاسم' },
      { value: 'price', label: 'Price', labelAr: 'السعر' },
      { value: 'category', label: 'Category', labelAr: 'الفئة' },
      { value: 'created_at', label: 'Date Created', labelAr: 'تاريخ الإنشاء' }
    ]
  }
];

/**
 * Enhanced Technical Services Manager with professional service management features
 */
const TechnicalServicesManager: React.FC<TechnicalServicesManagerProps> = ({ onBack }) => {
  const { t, language } = useTranslation();
  const { showNotification } = useNotification();
  const buttonActions = useButtonActions();
  
  const [services, setServices] = useState<TechnicalService[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingService, setEditingService] = useState<TechnicalService | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewService, setPreviewService] = useState<TechnicalService | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(6);

  // Unified filter state
  const [filterValues, setFilterValues] = useState<Record<string, string>>({
    status: 'all',
    category: 'all',
    serviceType: 'all',
    priceRange: 'all',
    sortBy: 'name'
  });

  // Form state
  const [formData, setFormData] = useState<Partial<TechnicalService>>({
    name: { ar: '', en: '' },
    description: { ar: '', en: '' },
    features: { ar: [], en: [] },
    tech_specs: { ar: [], en: [] }, // Add tech_specs field
    price: 0,
    category: 'general',
    isPremiumAddon: false, // New field for Premium Edition eligibility
    premiumPrice: 0, // New field for Premium Edition pricing
    subscriptionType: 'none', // New field for subscription type
    video_url: '',
    image_url: '',
    status: 'active'
  });

  // Service-specific state
  const [serviceSettings, setServiceSettings] = useState({
    requiresClientFiles: false,
    requiresServerAccess: false,
    includesSupport: true,
    supportDuration: '30', // days
    emergencyService: false,
    customPricing: false
  });

  useEffect(() => {
    loadServices();
  }, []);

  const loadServices = async () => {
    setLoading(true);
    try {
      const result = await getTechnicalServices();
      console.log('Technical Services API result:', result); // Debug log

      // Check if data is in result.data.services or result.data directly
      let servicesData = [];
      if (result.data) {
        if (Array.isArray(result.data)) {
          servicesData = result.data;
        } else if (result.data.services && Array.isArray(result.data.services)) {
          servicesData = result.data.services;
        } else if (result.data.data && Array.isArray(result.data.data)) {
          servicesData = result.data.data;
        }
      }
      
      if (servicesData.length >= 0) {
        // Normalize all service data to ensure consistent format
        const normalizedServices = servicesData.map(normalizeServiceData);
        setServices(normalizedServices);
        console.log('Technical services loaded successfully:', normalizedServices.length);
      } else {
        console.warn('Invalid services data:', result);
        setServices([]);
        if (result.error) {
          showNotification({
            type: 'error',
            message: result.error.message || t('notifications.loadError')
          });
        }
      }
    } catch (error) {
      console.error('Error loading services:', error);
      showNotification({
        type: 'error',
        message: t('notifications.loadError')
      });
      setServices([]);
    }
    setLoading(false);
  };

  const handleCreate = () => {
    setEditingService(null);
    setFormData({
      name: { ar: '', en: '' },
      description: { ar: '', en: '' },
      features: { ar: [], en: [] },
      tech_specs: { ar: [], en: [] }, // Add tech_specs field
      price: 0,
      category: 'general',
      isPremiumAddon: false, // Default to not available as Premium Edition add-on
      premiumPrice: 0, // Default premium price
      subscriptionType: 'none', // Default to one-time service
      video_url: '',
      image_url: '',
      status: 'active'
    });
    setServiceSettings({
      requiresClientFiles: false,
      requiresServerAccess: false,
      includesSupport: true,
      supportDuration: '30',
      emergencyService: false,
      customPricing: false
    });
    setShowModal(true);
  };

  const handleEdit = (service: TechnicalService) => {
    setEditingService(service);
    setFormData(service);
    setShowModal(true);
  };

  const handlePreview = (service: TechnicalService) => {
    setPreviewService(service);
    setShowPreviewModal(true);
  };

  const handleRequestService = async (service: TechnicalService) => {
    try {
      const serviceName = language === 'ar' ? (service.name_ar || '') : (service.name_en || '');
      const result = await buttonActions.requestService(service.id, serviceName);
      if (result.success) {
        showNotification({
          type: 'success',
          message: language === 'ar' ? 'تم طلب الخدمة بنجاح' : 'Service requested successfully'
        });
      }
    } catch (error) {
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في طلب الخدمة' : 'Failed to request service'
      });
    }
  };

  const handleDelete = (service: TechnicalService) => {
    showNotification({
      type: 'confirm',
      message: t('notifications.deleteServiceConfirm'),
      onConfirm: async () => {
        try {
          await deleteTechnicalService(service.id);
          await loadServices();
          showNotification({
            type: 'success',
            message: language === 'ar' ? 'تم حذف الخدمة بنجاح' : 'Service deleted successfully'
          });
        } catch (error) {
          console.error('Error deleting service:', error);
          showNotification({
            type: 'error',
            message: language === 'ar' ? 'فشل في حذف الخدمة' : 'Failed to delete service'
          });
        }
      }
    });
  };

  const handleSave = async () => {
    try {
      // Validate required fields with safe access
      const nameAr = formData.name?.ar || '';
      const nameEn = formData.name?.en || '';
      const descAr = formData.description?.ar || '';
      const descEn = formData.description?.en || '';
      const price = formData.price || 0;

      if (!nameAr || !nameEn || !descAr || !descEn || price <= 0) {
        showNotification({
          type: 'error',
          message: language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields'
        });
        return;
      }

      // Prepare data in the format expected by the server
      const serviceData = {
        name_ar: formData.name?.ar || '',
        name_en: formData.name?.en || '',
        description_ar: formData.description?.ar || '',
        description_en: formData.description?.en || '',
        features_ar: formData.features?.ar || [],
        features_en: formData.features?.en || [],
        tech_specs_ar: formData.tech_specs?.ar || [],
        tech_specs_en: formData.tech_specs?.en || [],
        price: formData.price || 0,
        category: formData.category || 'general',
        type: formData.type || 'regular',
        status: formData.status || 'active',
        video_url: formData.video_url || '',
        image_url: formData.image_url || '',
        gallery_images: formData.gallery_images || []
      };

      if (editingService) {
        const result = await updateTechnicalService(editingService.id, serviceData);
        if (result.error) {
          throw new Error(result.error.message || 'Update failed');
        }
        showNotification({
          type: 'success',
          message: language === 'ar' ? 'تم تحديث الخدمة بنجاح' : 'Service updated successfully'
        });
      } else {
        const result = await createTechnicalService(serviceData);
        if (result.error) {
          throw new Error(result.error.message || 'Create failed');
        }
        showNotification({
          type: 'success',
          message: language === 'ar' ? 'تم إنشاء الخدمة بنجاح' : 'Service created successfully'
        });
      }
      setShowModal(false);
      await loadServices();
    } catch (error: any) {
      console.error('Error saving service:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? `فشل في حفظ الخدمة: ${error.message}` : `Failed to save service: ${error.message}`
      });
    }
  };

  const handleTextChange = (lang: 'ar' | 'en', field: keyof TranslatedText, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: {
        ...(prev[field] as TranslatedText || { ar: '', en: '' }),
        [lang]: value
      }
    }));
  };

  const handleArrayChange = (lang: 'ar' | 'en', field: 'features' | 'requirements', value: string) => {
    const items = value.split('\n').filter(item => item.trim());
    setFormData(prev => ({
      ...prev,
      [field]: {
        ...(prev[field] as { ar: string[]; en: string[] } || { ar: [], en: [] }),
        [lang]: items
      }
    }));
  };

  // Handle filter changes
  const handleFilterChange = (key: string, value: string) => {
    setFilterValues(prev => ({ ...prev, [key]: value }));
  };

  const filteredServices = services.filter(service => {
    // Safe access to service fields
    const name = getServiceText(service, 'name', language);
    const description = getServiceText(service, 'description', language);
    
    const matchesSearch = name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterValues.status === 'all' || service.status === filterValues.status;
    const matchesCategory = filterValues.category === 'all' || service.category === filterValues.category;
    const matchesServiceType = filterValues.serviceType === 'all' || service.service_type === filterValues.serviceType;

    // Price range filter
    const matchesPriceRange = filterValues.priceRange === 'all' || (() => {
      const price = service.price;
      switch (filterValues.priceRange) {
        case '0-50': return price >= 0 && price <= 50;
        case '50-100': return price > 50 && price <= 100;
        case '100-200': return price > 100 && price <= 200;
        case '200-500': return price > 200 && price <= 500;
        case '500+': return price > 500;
        default: return true;
      }
    })();

    return matchesSearch && matchesStatus && matchesCategory && matchesServiceType && matchesPriceRange;
  });

  // Sort services
  const sortedServices = [...filteredServices].sort((a, b) => {
    switch (filterValues.sortBy) {
      case 'price':
        return a.price - b.price;
      case 'category':
        return a.category.localeCompare(b.category);
      case 'created_at':
        return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
      case 'name':
      default:
        return a.name[language].localeCompare(b.name[language]);
    }
  });

  // Pagination logic
  const totalPages = Math.ceil(sortedServices.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedServices = sortedServices.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterValues]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'inactive': return <XCircle className="w-4 h-4 text-red-400" />;
      default: return <Clock className="w-4 h-4 text-yellow-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'inactive': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
    }
  };

  const getServiceTypeIcon = (type: string) => {
    switch (type) {
      case 'installation': return <Settings className="w-4 h-4" />;
      case 'maintenance': return <Wrench className="w-4 h-4" />;
      case 'customization': return <Edit className="w-4 h-4" />;
      case 'support': return <Users className="w-4 h-4" />;
      case 'emergency': return <AlertCircle className="w-4 h-4" />;
      default: return <Wrench className="w-4 h-4" />;
    }
  };

  const categories = ['general', 'server', 'client', 'database', 'security', 'performance'];
  const serviceTypes = ['installation', 'maintenance', 'customization', 'support', 'emergency', 'consultation'];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-secondary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          {onBack && (
            <BackButton onClick={onBack} variant="back" size="md" />
          )}
          <h1 className="text-2xl font-bold text-white">
            {t('admin.dashboard.services')}
          </h1>
        </div>
        
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Button variant="outline" onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
            <Wrench className="w-4 h-4 mr-2" />
            {viewMode === 'grid' ? 'List' : 'Grid'}
          </Button>
          <Button variant="primary" onClick={handleCreate}>
            <Plus className="w-4 h-4 mr-2" />
            {t('admin.dashboard.addNewService')}
          </Button>
        </div>
      </div>

      {/* Enhanced Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <Card className="bg-gradient-to-br from-blue-500/10 to-blue-600/5 border-blue-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-300 text-sm font-medium">{t('admin.dashboard.totalServices')}</p>
                <p className="text-2xl font-bold text-white">{services.length}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'إجمالي الخدمات' : 'Total Services'}
                </p>
              </div>
              <Wrench className="w-8 h-8 text-blue-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/10 to-green-600/5 border-green-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-300 text-sm font-medium">{t('admin.dashboard.activeServices')}</p>
                <p className="text-2xl font-bold text-white">{services.filter(s => s.status === 'active').length}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'نشطة حالياً' : 'Currently Active'}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-purple-500/10 to-purple-600/5 border-purple-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-300 text-sm font-medium">{language === 'ar' ? 'متوسط السعر' : 'Average Price'}</p>
                <p className="text-2xl font-bold text-white">
                  ${services.length > 0 ? Math.round(services.reduce((sum, s) => sum + s.price, 0) / services.length) : 0}
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'للخدمة الواحدة' : 'Per Service'}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-purple-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-accent/10 to-accent/5 border-accent/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-accent text-sm font-medium">{language === 'ar' ? 'أنواع الخدمات' : 'Service Types'}</p>
                <p className="text-2xl font-bold text-white">{new Set(services.map(s => s.service_type)).size}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'أنواع مختلفة' : 'Different Types'}
                </p>
              </div>
              <Filter className="w-8 h-8 text-accent" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-orange-500/10 to-orange-600/5 border-orange-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-300 text-sm font-medium">{language === 'ar' ? 'خدمات الطوارئ' : 'Emergency Services'}</p>
                <p className="text-2xl font-bold text-white">{services.filter(s => s.service_type === 'emergency').length}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'متاحة 24/7' : 'Available 24/7'}
                </p>
              </div>
              <AlertCircle className="w-8 h-8 text-orange-400" />
            </div>
          </Card.Body>
        </Card>
      </div>

      {/* Enhanced Filters and Search - Repositioned Below Statistics */}
      <div className="mb-6">
        <GoldenFilterGrid
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          searchPlaceholder="Search services (name, description, type)..."
          searchPlaceholderAr="البحث في الخدمات (الاسم، الوصف، النوع)..."
          filters={TECHNICAL_SERVICES_FILTERS}
          filterValues={filterValues}
          onFilterChange={handleFilterChange}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          resultCount={sortedServices.length}
          onExport={() => buttonActions.exportData('services', sortedServices)}
          onImport={() => buttonActions.importData('services')}
          onAdvancedSettings={() => buttonActions.openAdvancedSettings('technical-services')}
          compact={true}
          position="horizontal"
          className="enhanced-technical-services-filter"
        />
      </div>

      {/* Services Grid/List */}
      {sortedServices.length === 0 ? (
        <Card>
          <Card.Body>
            <div className="text-center py-12">
              <Wrench className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                {language === 'ar' ? 'لا توجد خدمات' : 'No Services Found'}
              </h3>
              <p className="text-gray-400 mb-6">
                {language === 'ar' ? 'ابدأ بإنشاء خدمة جديدة' : 'Start by creating a new service'}
              </p>
              <Button variant="primary" onClick={handleCreate}>
                <Plus className="w-4 h-4 mr-2" />
                {t('admin.dashboard.addNewService')}
              </Button>
            </div>
          </Card.Body>
        </Card>
      ) : (
        <>
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
            {paginatedServices.map((service) => (
            <Card key={service.id} className="hover:border-secondary/50 transition-all duration-300">
              <Card.Body>
                {viewMode === 'grid' ? (
                  // Grid View
                  <div className="space-y-4">
                    {/* Service Type Badge and Status */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        {getServiceTypeIcon(service.service_type)}
                        <span className="text-sm font-medium text-secondary">
                          {language === 'ar' ?
                            (service.service_type === 'installation' ? 'تنصيب' :
                             service.service_type === 'maintenance' ? 'صيانة' :
                             service.service_type === 'customization' ? 'تخصيص' :
                             service.service_type === 'support' ? 'دعم فني' :
                             service.service_type === 'emergency' ? 'طوارئ' :
                             service.service_type === 'consultation' ? 'استشارة' : service.service_type) :
                            service.service_type.charAt(0).toUpperCase() + service.service_type.slice(1)
                          }
                        </span>
                        {/* Premium Badge */}
                        {service.isPremiumAddon && (
                          <span className="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-full text-xs flex items-center space-x-1 rtl:space-x-reverse">
                            <Star className="w-3 h-3" />
                            <span>{language === 'ar' ? 'مميز' : 'Premium'}</span>
                          </span>
                        )}
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(service.status)}`}>
                        {getStatusIcon(service.status)}
                        <span className="ml-1">{t(`admin.dashboard.${service.status}`)}</span>
                      </span>
                    </div>

                    {/* Service Image */}
                    <div className="relative h-32 bg-gradient-to-br from-primary to-background rounded-lg overflow-hidden">
                      {service.image_url ? (
                        <img
                          src={service.image_url}
                          alt={getServiceText(service, 'name', language)}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Wrench className="w-8 h-8 text-gray-400" />
                        </div>
                      )}
                      {service.service_type === 'emergency' && (
                        <div className="absolute top-2 left-2">
                          <span className="bg-red-500/20 text-red-400 px-2 py-1 rounded text-xs border border-red-500/30">
                            <AlertCircle className="w-3 h-3 inline mr-1" />
                            {language === 'ar' ? 'طوارئ' : 'Emergency'}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Content */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-2 line-clamp-1">
                        {getServiceText(service, 'name', language)}
                      </h3>
                      <p className="text-gray-300 text-sm mb-3 line-clamp-2">
                        {getServiceText(service, 'description', language)}
                      </p>

                      <div className="flex items-center justify-between mb-3">
                        <div className="flex flex-col">
                          <span className="text-secondary font-bold text-lg">${service.price}</span>
                          <span className="text-xs text-gray-400">
                            {language === 'ar' ? 'سعر الخدمة' : 'Service Price'}
                          </span>
                        </div>
                        <div className="flex flex-col items-end space-y-1">
                          <span className="text-xs text-gray-400 bg-accent/20 px-2 py-1 rounded">
                            {language === 'ar' ?
                              (service.category === 'general' ? 'عام' :
                               service.category === 'server' ? 'سيرفر' :
                               service.category === 'client' ? 'كلاينت' :
                               service.category === 'database' ? 'قاعدة بيانات' :
                               service.category === 'security' ? 'أمان' :
                               service.category === 'performance' ? 'أداء' : service.category) :
                              service.category.charAt(0).toUpperCase() + service.category.slice(1)
                            }
                          </span>
                          {service.estimated_duration && (
                            <span className="text-xs text-gray-400 flex items-center">
                              <Clock className="w-3 h-3 mr-1" />
                              {service.estimated_duration}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="space-y-2">
                      <div className="flex space-x-2 rtl:space-x-reverse">
                        <Button variant="outline" size="sm" onClick={() => handlePreview(service)} className="flex-1">
                          <Eye className="w-4 h-4 mr-1" />
                          {language === 'ar' ? 'تفاصيل' : 'Details'}
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleEdit(service)}>
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleDelete(service)}>
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                      <div className="flex space-x-2 rtl:space-x-reverse">
                        <Button
                          variant="secondary"
                          size="sm"
                          className="flex-1 text-xs"
                          onClick={() => handlePreview(service)}
                        >
                          <Eye className="w-3 h-3 mr-1" />
                          {language === 'ar' ? 'معاينة' : 'Preview'}
                        </Button>
                        <Button
                          variant="secondary"
                          size="sm"
                          className="flex-1 text-xs"
                          onClick={() => handleRequestService(service)}
                        >
                          <Users className="w-3 h-3 mr-1" />
                          {language === 'ar' ? 'طلب الخدمة' : 'Request Service'}
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  // List View - Similar structure but horizontal layout
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    {/* Service Icon */}
                    <div className="w-16 h-16 bg-gradient-to-br from-primary to-background rounded-lg overflow-hidden flex-shrink-0">
                      {service.image_url ? (
                        <img
                          src={service.image_url}
                          alt={getServiceText(service, 'name', language)}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          {getServiceTypeIcon(service.service_type)}
                        </div>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <h3 className="text-lg font-semibold text-white truncate">
                            {getServiceText(service, 'name', language)}
                          </h3>
                          {service.isPremiumAddon && (
                            <span className="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-full text-xs flex items-center space-x-1 rtl:space-x-reverse">
                              <Star className="w-3 h-3" />
                              <span>{language === 'ar' ? 'مميز' : 'Premium'}</span>
                            </span>
                          )}
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(service.status)} flex items-center`}>
                          {getStatusIcon(service.status)}
                          <span className="ml-1">{t(`admin.dashboard.${service.status}`)}</span>
                        </span>
                      </div>

                      <p className="text-gray-300 text-sm mb-2 line-clamp-1">
                        {getSystemText(service, 'description', language)}
                      </p>

                      <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm">
                        <span className="text-secondary font-bold">${service.price}</span>
                        <span className="text-gray-400">{service.category}</span>
                        <span className="text-gray-400">
                          {language === 'ar' ?
                            (service.service_type === 'installation' ? 'تنصيب' :
                             service.service_type === 'maintenance' ? 'صيانة' :
                             service.service_type === 'customization' ? 'تخصيص' :
                             service.service_type === 'support' ? 'دعم فني' :
                             service.service_type === 'emergency' ? 'طوارئ' :
                             service.service_type === 'consultation' ? 'استشارة' : service.service_type) :
                            service.service_type.charAt(0).toUpperCase() + service.service_type.slice(1)
                          }
                        </span>
                        {service.estimated_duration && (
                          <span className="text-gray-400 flex items-center">
                            <Clock className="w-3 h-3 mr-1" />
                            {service.estimated_duration}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2 rtl:space-x-reverse flex-shrink-0">
                      <Button variant="outline" size="sm" onClick={() => handlePreview(service)} className="btn-icon-fix card-action-btn">
                        <Eye className="w-4 h-4 enhanced-icon" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleEdit(service)} className="btn-icon-fix card-action-btn">
                        <Edit className="w-4 h-4 enhanced-icon" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleDelete(service)} className="btn-icon-fix card-action-btn">
                        <Trash2 className="w-4 h-4 enhanced-icon" />
                      </Button>
                    </div>
                  </div>
                )}
              </Card.Body>
            </Card>
          ))}
        </div>

          {/* Pagination */}
          {sortedServices.length > 0 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={sortedServices.length}
              itemsPerPage={itemsPerPage}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
              itemsPerPageOptions={[3, 6, 9, 12]}
              className="mt-6"
            />
          )}
        </>
      )}

      {/* Create/Edit Modal */}
      {showModal && (
        <Modal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          title={editingService ? t('admin.dashboard.editService') : t('admin.dashboard.addNewService')}
          size="xl"
          className="max-h-[90vh] overflow-y-auto"
        >
          <Modal.Body className="max-h-[70vh] overflow-y-auto modal-text-fix">
            <div className="space-y-8">
              {/* Basic Information */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label={`${t('admin.dashboard.serviceName')} (العربية)`}
                    value={formData.name?.ar || ''}
                    onChange={(e) => handleTextChange('ar', 'name', e.target.value)}
                    required
                  />
                  <Input
                    label={`${t('admin.dashboard.serviceName')} (English)`}
                    value={formData.name?.en || ''}
                    onChange={(e) => handleTextChange('en', 'name', e.target.value)}
                    required
                  />
                </div>
              </div>

              {/* Description */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('admin.dashboard.description')}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {t('admin.dashboard.description')} (العربية)
                    </label>
                    <textarea
                      value={formData.description?.ar || ''}
                      onChange={(e) => handleTextChange('ar', 'description', e.target.value)}
                      rows={4}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {t('admin.dashboard.description')} (English)
                    </label>
                    <textarea
                      value={formData.description?.en || ''}
                      onChange={(e) => handleTextChange('en', 'description', e.target.value)}
                      rows={4}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                    />
                  </div>
                </div>
              </div>

              {/* Service Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Input
                  label={t('admin.dashboard.price')}
                  type="number"
                  value={formData.price || 0}
                  onChange={(e) => setFormData(prev => ({ ...prev, price: Number(e.target.value) }))}
                  leftIcon={<DollarSign />}
                />
                <div>
                  <label className="block text-sm font-medium text-secondary mb-2">
                    {t('admin.dashboard.category')}
                  </label>
                  <select
                    value={formData.category || 'general'}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                  >
                    {categories.map(cat => (
                      <option key={cat} value={cat}>
                        {language === 'ar' ?
                          (cat === 'general' ? 'عام' :
                           cat === 'server' ? 'سيرفر' :
                           cat === 'client' ? 'كلاينت' :
                           cat === 'database' ? 'قاعدة بيانات' :
                           cat === 'security' ? 'أمان' :
                           cat === 'performance' ? 'أداء' : cat) :
                          cat.charAt(0).toUpperCase() + cat.slice(1)
                        }
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary mb-2">
                    {language === 'ar' ? 'نوع الاشتراك' : 'Subscription Type'}
                  </label>
                  <select
                    value={formData.subscriptionType || 'none'}
                    onChange={(e) => setFormData(prev => ({ ...prev, subscriptionType: e.target.value as 'none' | 'monthly' | 'yearly' }))}
                    className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                  >
                    <option value="none">{language === 'ar' ? 'خدمة لمرة واحدة' : 'One-time Service'}</option>
                    <option value="monthly">{language === 'ar' ? 'اشتراك شهري' : 'Monthly Subscription'}</option>
                    <option value="yearly">{language === 'ar' ? 'اشتراك سنوي' : 'Yearly Subscription'}</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary mb-2">
                    {t('admin.dashboard.status')}
                  </label>
                  <select
                    value={formData.status || 'active'}
                    onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as 'active' | 'inactive' }))}
                    className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                  >
                    <option value="active">{t('admin.dashboard.active')}</option>
                    <option value="inactive">{t('admin.dashboard.inactive')}</option>
                  </select>
                </div>
              </div>

              {/* Premium Edition Settings */}
              <div className="border-t border-accent/20 pt-6">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'إعدادات النسخة المميزة' : 'Premium Edition Settings'}
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <input
                      type="checkbox"
                      id="isPremiumAddon"
                      checked={formData.isPremiumAddon || false}
                      onChange={(e) => setFormData(prev => ({ ...prev, isPremiumAddon: e.target.checked }))}
                      className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                    />
                    <label htmlFor="isPremiumAddon" className="text-white">
                      {language === 'ar' ? 'متاح كإضافة للنسخة المميزة' : 'Available as Premium Edition Add-on'}
                    </label>
                  </div>

                  {formData.isPremiumAddon && (
                    <div className="ml-6 rtl:ml-0 rtl:mr-6">
                      <Input
                        label={language === 'ar' ? 'السعر في النسخة المميزة' : 'Premium Edition Price'}
                        type="number"
                        value={formData.premiumPrice || 0}
                        onChange={(e) => setFormData(prev => ({ ...prev, premiumPrice: Number(e.target.value) }))}
                        leftIcon={<DollarSign />}
                        placeholder={language === 'ar' ? 'أدخل 0 للمجاني' : 'Enter 0 for free'}
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Duration and Media */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Input
                  label={language === 'ar' ? 'المدة المتوقعة' : 'Estimated Duration'}
                  value={formData.estimated_duration || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, estimated_duration: e.target.value }))}
                  placeholder={language === 'ar' ? 'مثال: 2-3 أيام' : 'e.g., 2-3 days'}
                  leftIcon={<Calendar />}
                />
                <Input
                  label={t('admin.dashboard.imageUrl')}
                  value={formData.image_url || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, image_url: e.target.value }))}
                  leftIcon={<ImageIcon />}
                />
                <Input
                  label={t('admin.dashboard.videoUrl')}
                  value={formData.video_url || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, video_url: e.target.value }))}
                  leftIcon={<Video />}
                />
              </div>

              {/* Service Settings */}
              <div className="border-t border-accent/20 pt-6">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'إعدادات الخدمة' : 'Service Settings'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <input
                        type="checkbox"
                        id="requiresClientFiles"
                        checked={serviceSettings.requiresClientFiles}
                        onChange={(e) => setServiceSettings(prev => ({
                          ...prev,
                          requiresClientFiles: e.target.checked
                        }))}
                        className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                      />
                      <label htmlFor="requiresClientFiles" className="text-white">
                        {language === 'ar' ? 'يتطلب ملفات الكلاينت' : 'Requires Client Files'}
                      </label>
                    </div>
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <input
                        type="checkbox"
                        id="requiresServerAccess"
                        checked={serviceSettings.requiresServerAccess}
                        onChange={(e) => setServiceSettings(prev => ({
                          ...prev,
                          requiresServerAccess: e.target.checked
                        }))}
                        className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                      />
                      <label htmlFor="requiresServerAccess" className="text-white">
                        {language === 'ar' ? 'يتطلب الوصول للسيرفر' : 'Requires Server Access'}
                      </label>
                    </div>
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <input
                        type="checkbox"
                        id="emergencyService"
                        checked={serviceSettings.emergencyService}
                        onChange={(e) => setServiceSettings(prev => ({
                          ...prev,
                          emergencyService: e.target.checked
                        }))}
                        className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                      />
                      <label htmlFor="emergencyService" className="text-white">
                        {language === 'ar' ? 'خدمة طوارئ (24/7)' : 'Emergency Service (24/7)'}
                      </label>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <input
                        type="checkbox"
                        id="includesSupport"
                        checked={serviceSettings.includesSupport}
                        onChange={(e) => setServiceSettings(prev => ({
                          ...prev,
                          includesSupport: e.target.checked
                        }))}
                        className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                      />
                      <label htmlFor="includesSupport" className="text-white">
                        {language === 'ar' ? 'يشمل الدعم الفني' : 'Includes Technical Support'}
                      </label>
                    </div>
                    {serviceSettings.includesSupport && (
                      <Input
                        label={language === 'ar' ? 'مدة الدعم (بالأيام)' : 'Support Duration (days)'}
                        type="number"
                        value={serviceSettings.supportDuration}
                        onChange={(e) => setServiceSettings(prev => ({
                          ...prev,
                          supportDuration: e.target.value
                        }))}
                        placeholder="30"
                      />
                    )}
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <input
                        type="checkbox"
                        id="customPricing"
                        checked={serviceSettings.customPricing}
                        onChange={(e) => setServiceSettings(prev => ({
                          ...prev,
                          customPricing: e.target.checked
                        }))}
                        className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                      />
                      <label htmlFor="customPricing" className="text-white">
                        {language === 'ar' ? 'تسعير مخصص' : 'Custom Pricing'}
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* Features */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('admin.dashboard.features')}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {t('admin.dashboard.features')} (العربية) - {language === 'ar' ? 'سطر واحد لكل ميزة' : 'One per line'}
                    </label>
                    <textarea
                      value={formData.features?.ar?.join('\n') || ''}
                      onChange={(e) => handleArrayChange('ar', 'features', e.target.value)}
                      rows={6}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {t('admin.dashboard.features')} (English) - One per line
                    </label>
                    <textarea
                      value={formData.features?.en?.join('\n') || ''}
                      onChange={(e) => handleArrayChange('en', 'features', e.target.value)}
                      rows={6}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                    />
                  </div>
                </div>
              </div>

              {/* Requirements */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'المتطلبات' : 'Requirements'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {language === 'ar' ? 'المتطلبات' : 'Requirements'} (العربية) - {language === 'ar' ? 'سطر واحد لكل متطلب' : 'One per line'}
                    </label>
                    <textarea
                      value={formData.requirements?.ar?.join('\n') || ''}
                      onChange={(e) => handleArrayChange('ar', 'requirements', e.target.value)}
                      rows={6}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      Requirements (English) - One per line
                    </label>
                    <textarea
                      value={formData.requirements?.en?.join('\n') || ''}
                      onChange={(e) => handleArrayChange('en', 'requirements', e.target.value)}
                      rows={6}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                    />
                  </div>
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowModal(false)}>
              {t('admin.dashboard.cancel')}
            </Button>
            <Button variant="primary" onClick={handleSave}>
              <Save className="w-4 h-4 mr-2" />
              {t('admin.dashboard.save')}
            </Button>
          </Modal.Footer>
        </Modal>
      )}

      {/* Preview Modal */}
      {showPreviewModal && previewService && (
        <Modal
          isOpen={showPreviewModal}
          onClose={() => setShowPreviewModal(false)}
          title={`${language === 'ar' ? 'معاينة الخدمة' : 'Service Preview'} - ${previewService.name[language]}`}
          size="xl"
        >
          <Modal.Body className="max-h-[70vh] overflow-y-auto modal-text-fix">
            <div className="space-y-6">
              {/* Service Image */}
              {previewService.image_url && (
                <div className="w-full h-64 bg-gradient-to-br from-primary to-background rounded-lg overflow-hidden">
                  <img
                    src={previewService.image_url}
                    alt={previewService.name[language]}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}

              {/* Service Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">
                    {language === 'ar' ? 'معلومات الخدمة' : 'Service Information'}
                  </h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm text-dark-theme-secondary">{language === 'ar' ? 'الاسم' : 'Name'}</label>
                      <p className="text-white font-medium">{language === 'ar' ? (previewService.name_ar || '') : (previewService.name_en || '')}</p>
                    </div>
                    <div>
                      <label className="text-sm text-dark-theme-secondary">{language === 'ar' ? 'الفئة' : 'Category'}</label>
                      <p className="text-white capitalize">{previewService.category}</p>
                    </div>
                    <div>
                      <label className="text-sm text-dark-theme-secondary">{language === 'ar' ? 'نوع الخدمة' : 'Service Type'}</label>
                      <p className="text-white capitalize">{previewService.service_type}</p>
                    </div>
                    <div>
                      <label className="text-sm text-dark-theme-secondary">{language === 'ar' ? 'السعر' : 'Price'}</label>
                      <p className="text-secondary font-bold text-lg">${previewService.price}</p>
                    </div>
                    <div>
                      <label className="text-sm text-dark-theme-secondary">{language === 'ar' ? 'المدة المقدرة' : 'Estimated Duration'}</label>
                      <p className="text-white">{previewService.estimated_duration}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">
                    {language === 'ar' ? 'الوصف' : 'Description'}
                  </h3>
                  <p className="text-dark-theme-secondary leading-relaxed">
                    {previewService.description[language]}
                  </p>
                </div>
              </div>

              {/* Features */}
              {(language === 'ar' ? previewService.features_ar : previewService.features_en) && (language === 'ar' ? previewService.features_ar : previewService.features_en)?.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">
                    {language === 'ar' ? 'الميزات' : 'Features'}
                  </h3>
                  <ul className="space-y-2">
                    {(language === 'ar' ? previewService.features_ar : previewService.features_en)?.map((feature, index) => (
                      <li key={index} className="flex items-start space-x-3 rtl:space-x-reverse">
                        <div className="w-2 h-2 bg-secondary rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-dark-theme-secondary">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Requirements */}
              {previewService.requirements && previewService.requirements[language] && previewService.requirements[language].length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">
                    {language === 'ar' ? 'المتطلبات' : 'Requirements'}
                  </h3>
                  <ul className="space-y-2">
                    {previewService.requirements[language].map((requirement, index) => (
                      <li key={index} className="flex items-start space-x-3 rtl:space-x-reverse">
                        <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-dark-theme-secondary">{requirement}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Video Preview */}
              {previewService.video_url && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">
                    {language === 'ar' ? 'فيديو توضيحي' : 'Demo Video'}
                  </h3>
                  <div className="aspect-video bg-black rounded-lg overflow-hidden">
                    <iframe
                      src={previewService.video_url.includes('youtube.com') || previewService.video_url.includes('youtu.be')
                        ? previewService.video_url.replace('watch?v=', 'embed/').replace('youtu.be/', 'youtube.com/embed/')
                        : previewService.video_url}
                      className="w-full h-full"
                      allowFullScreen
                      title={`${previewService.name[language]} Demo`}
                    />
                  </div>
                </div>
              )}
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowPreviewModal(false)}>
              {language === 'ar' ? 'إغلاق' : 'Close'}
            </Button>
            <Button variant="primary" onClick={() => handleRequestService(previewService)}>
              <Users className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'طلب الخدمة' : 'Request Service'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}
    </div>
  );
};

export default TechnicalServicesManager;
