/**
 * Restart Server and Clean Rate Limiting
 * 
 * This script helps restart the server and clear any rate limiting issues
 */

const { exec } = require('child_process');
const path = require('path');

console.log('🔄 Restarting server and cleaning rate limits...\n');

// Kill any existing node processes
console.log('1️⃣ Stopping existing server processes...');
exec('taskkill /f /im node.exe', (error) => {
  if (error && !error.message.includes('not found')) {
    console.log('⚠️  Warning stopping processes:', error.message);
  } else {
    console.log('✅ Existing processes stopped');
  }
  
  // Wait a moment then start the server
  setTimeout(() => {
    console.log('\n2️⃣ Starting server...');
    
    const serverProcess = exec('npm run dev:server', {
      cwd: path.resolve(__dirname, '..')
    });
    
    serverProcess.stdout.on('data', (data) => {
      console.log(data.toString());
    });
    
    serverProcess.stderr.on('data', (data) => {
      console.error(data.toString());
    });
    
    serverProcess.on('close', (code) => {
      console.log(`Server process exited with code ${code}`);
    });
    
    // Give server time to start then test endpoints
    setTimeout(() => {
      console.log('\n3️⃣ Testing endpoints...');
      exec('npm run test:endpoints', (error, stdout, stderr) => {
        if (error) {
          console.error('❌ Endpoint test failed:', error.message);
        } else {
          console.log(stdout);
        }
      });
    }, 5000);
    
  }, 2000);
});

console.log('\n📝 Rate limiting has been increased to 1000 requests per 15 minutes');
console.log('🔧 Admin endpoints have been fixed to return data directly');
console.log('⏳ Server will restart in a moment...\n');