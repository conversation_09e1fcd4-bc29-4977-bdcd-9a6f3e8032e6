#!/usr/bin/env node

/**
 * سكريبت لإعداد رابطين منفصلين - واحد للفرونت اند وآخر للباك اند
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 إعداد روابط ngrok منفصلة للفرونت اند والباك اند');
console.log('='.repeat(60));

async function checkPort(port) {
  return new Promise((resolve) => {
    exec(`netstat -an | findstr :${port}`, (error, stdout) => {
      resolve(stdout.includes('LISTENING'));
    });
  });
}

async function getNgrokTunnels() {
  return new Promise((resolve) => {
    exec('curl -s http://127.0.0.1:4040/api/tunnels', (error, stdout) => {
      if (error) {
        resolve({ tunnels: [] });
      } else {
        try {
          resolve(JSON.parse(stdout));
        } catch (e) {
          resolve({ tunnels: [] });
        }
      }
    });
  });
}

async function startNgrokTunnel(port, name) {
  return new Promise((resolve, reject) => {
    console.log(`🔗 بدء نفق ${name} على المنفذ ${port}...`);
    
    const ngrok = spawn('ngrok', ['http', port.toString()], {
      detached: true,
      stdio: 'ignore'
    });
    
    ngrok.unref();
    
    // انتظار قليل للسماح لـ ngrok بالبدء
    setTimeout(async () => {
      const tunnels = await getNgrokTunnels();
      const tunnel = tunnels.tunnels.find(t => t.config.addr === `http://localhost:${port}`);
      
      if (tunnel) {
        console.log(`✅ ${name} جاهز: ${tunnel.public_url}`);
        resolve({
          name,
          port,
          url: tunnel.public_url,
          local: `http://localhost:${port}`
        });
      } else {
        reject(new Error(`فشل في إنشاء نفق ${name}`));
      }
    }, 3000);
  });
}

async function main() {
  try {
    // التحقق من الخوادم
    console.log('🔍 التحقق من الخوادم...');
    
    const backendRunning = await checkPort(3001);
    const frontendRunning = await checkPort(5173);
    
    console.log(`📡 الباك اند (3001): ${backendRunning ? '✅ يعمل' : '❌ متوقف'}`);
    console.log(`🌐 الفرونت اند (5173): ${frontendRunning ? '✅ يعمل' : '❌ متوقف'}`);
    
    if (!backendRunning || !frontendRunning) {
      console.log('⚠️ تأكد من تشغيل الخوادم أولاً: npm run dev:full');
      process.exit(1);
    }
    
    // إيقاف جميع أنفاق ngrok الحالية
    console.log('🛑 إيقاف أنفاق ngrok الحالية...');
    exec('taskkill /f /im ngrok.exe', () => {
      setTimeout(async () => {
        try {
          // بدء الأنفاق
          const backendTunnel = await startNgrokTunnel(3001, 'Backend API');
          const frontendTunnel = await startNgrokTunnel(5173, 'Frontend');
          
          const config = {
            timestamp: new Date().toISOString(),
            backend: backendTunnel,
            frontend: frontendTunnel,
            testsprite_ready: true
          };
          
          // حفظ التكوين
          fs.writeFileSync('dual-ngrok-config.json', JSON.stringify(config, null, 2));
          
          console.log('\n🎉 تم إعداد الروابط بنجاح!');
          console.log('='.repeat(40));
          console.log(`🔗 رابط الباك اند: ${backendTunnel.url}`);
          console.log(`🌐 رابط الفرونت اند: ${frontendTunnel.url}`);
          console.log('='.repeat(40));
          console.log('📁 تم حفظ التكوين في: dual-ngrok-config.json');
          
        } catch (error) {
          console.error('❌ خطأ في إعداد الأنفاق:', error.message);
          process.exit(1);
        }
      }, 2000);
    });
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
    process.exit(1);
  }
}

main();
