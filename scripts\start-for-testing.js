#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to start the server for TestSprite testing
 * This ensures the server is ready for external testing tools
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Khanfashariya server for TestSprite testing...');

// Start the full development environment
const serverProcess = spawn('npm', ['run', 'dev:full'], {
  stdio: 'inherit',
  shell: true,
  cwd: path.resolve(__dirname, '..')
});

serverProcess.on('error', (error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

serverProcess.on('close', (code) => {
  console.log(`🛑 Server process exited with code ${code}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  serverProcess.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down server...');
  serverProcess.kill('SIGTERM');
  process.exit(0);
});

console.log('📋 Server starting...');
console.log('🌐 API will be available at: http://localhost:3001');
console.log('🔧 Use Ctrl+C to stop the server');