const axios = require('axios');

async function testOrdersSystem() {
  try {
    console.log('🔐 Logging in as admin...');
    
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.data.tokens.accessToken;
    const userId = loginResponse.data.data.user.id;
    console.log('✅ Login successful!');
    
    const headers = { Authorization: `Bearer ${token}` };
    
    // Test viewing existing orders
    console.log('\n📦 Testing Orders Viewing...');
    
    const ordersResponse = await axios.get('http://localhost:3001/api/orders', { headers });
    console.log('✅ Orders retrieved successfully');
    
    if (ordersResponse.data.success && ordersResponse.data.data) {
      const orders = ordersResponse.data.data.orders || ordersResponse.data.data;
      console.log(`   📊 Found ${orders.length} orders`);
      
      if (orders.length > 0) {
        const firstOrder = orders[0];
        console.log(`   📝 Sample order: ${firstOrder.order_number}`);
        console.log(`   💰 Price: $${firstOrder.final_price}`);
        console.log(`   📊 Status: ${firstOrder.status}`);
        console.log(`   🛍️ Item: ${firstOrder.item_name_ar}`);
        console.log(`   📅 Created: ${firstOrder.created_at}`);
      }
    }
    
    // Test creating a new order
    console.log('\n🛒 Testing Order Creation...');
    
    // First get a system to order
    const systemsResponse = await axios.get('http://localhost:3001/api/systems');
    const systems = systemsResponse.data.data.systems || systemsResponse.data.data;
    
    if (systems.length > 0) {
      const testSystem = systems[0];
      console.log(`   🎯 Testing order for: ${testSystem.name_ar}`);
      
      const orderData = {
        order_type: 'system_service',
        item_id: testSystem.id,
        item_name_ar: testSystem.name_ar,
        item_name_en: testSystem.name_en,
        final_price: testSystem.price,
        quantity: 1,
        notes_ar: 'طلب تجريبي من النظام',
        notes_en: 'Test order from system'
      };
      
      try {
        const createOrderResponse = await axios.post('http://localhost:3001/api/orders', orderData, { headers });
        console.log('✅ Order created successfully!');
        console.log(`   📝 Order Number: ${createOrderResponse.data.data.order_number}`);
        console.log(`   🆔 Order ID: ${createOrderResponse.data.data.id}`);
      } catch (createError) {
        console.log('⚠️ Order creation issue:', createError.response?.data?.error || createError.message);
      }
    }
    
    // Test premium content ordering
    console.log('\n👑 Testing Premium Content Ordering...');
    
    const premiumResponse = await axios.get('http://localhost:3001/api/services/premium');
    const premiumData = premiumResponse.data.data.premiumContent || [];
    
    if (premiumData.length > 0) {
      const testPremium = premiumData[0];
      console.log(`   🎯 Testing premium order for: ${testPremium.title_ar}`);
      
      const premiumOrderData = {
        order_type: 'premium_content',
        item_id: testPremium.id,
        item_name_ar: testPremium.title_ar,
        item_name_en: testPremium.title_en,
        final_price: testPremium.price,
        quantity: 1,
        notes_ar: 'طلب محتوى مميز تجريبي',
        notes_en: 'Test premium content order'
      };
      
      try {
        const createPremiumOrderResponse = await axios.post('http://localhost:3001/api/orders', premiumOrderData, { headers });
        console.log('✅ Premium order created successfully!');
        console.log(`   📝 Order Number: ${createPremiumOrderResponse.data.data.order_number}`);
      } catch (premiumError) {
        console.log('⚠️ Premium order creation issue:', premiumError.response?.data?.error || premiumError.message);
      }
    }
    
    // Test order status updates (admin function)
    console.log('\n🔄 Testing Order Status Updates...');
    
    const updatedOrdersResponse = await axios.get('http://localhost:3001/api/orders', { headers });
    const updatedOrders = updatedOrdersResponse.data.data.orders || updatedOrdersResponse.data.data;
    
    if (updatedOrders.length > 0) {
      const orderToUpdate = updatedOrders[0];
      console.log(`   🎯 Testing status update for order: ${orderToUpdate.order_number}`);
      
      try {
        const updateResponse = await axios.put(`http://localhost:3001/api/orders/${orderToUpdate.id}/status`, {
          status: 'completed',
          admin_notes: 'Order completed successfully - Test'
        }, { headers });

        console.log('✅ Order status updated successfully!');
      } catch (updateError) {
        console.log('⚠️ Order update issue:', updateError.response?.data?.error || updateError.message);
      }
    }
    
    console.log('\n🎉 Orders system testing completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testOrdersSystem();
