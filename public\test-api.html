<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #333; border-radius: 8px; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        button { padding: 10px 20px; margin: 5px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #1976D2; }
        pre { background: #2a2a2a; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .status { font-weight: bold; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Khanfashariya API Test Page</h1>
        
        <div class="test-section">
            <h2>Environment Variables</h2>
            <div id="env-vars"></div>
        </div>
        
        <div class="test-section">
            <h2>API Connectivity Tests</h2>
            <button onclick="testHealth()">Test Health</button>
            <button onclick="testSystems()">Test Systems</button>
            <button onclick="testServices()">Test Services</button>
            <button onclick="testLogin()">Test Login</button>
            <button onclick="runAllTests()">Run All Tests</button>
            <div id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h2>Frontend Component Test</h2>
            <button onclick="testFrontendComponents()">Test Components</button>
            <div id="component-results"></div>
        </div>
    </div>

    <script>
        // Display environment variables
        function displayEnvVars() {
            const envDiv = document.getElementById('env-vars');
            const envInfo = {
                'Current URL': window.location.href,
                'User Agent': navigator.userAgent,
                'Referrer': document.referrer || 'None'
            };
            
            let html = '<pre>';
            for (const [key, value] of Object.entries(envInfo)) {
                html += `${key}: ${value}\n`;
            }
            html += '</pre>';
            envDiv.innerHTML = html;
        }

        // Test functions
        async function testHealth() {
            const resultDiv = document.getElementById('test-results');
            resultDiv.innerHTML += '<div class="status">🔍 Testing Health Endpoint...</div>';
            
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML += `<div class="success">✅ Health: ${data.status}</div>`;
                    resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML += `<div class="error">❌ Health failed: ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="error">❌ Health error: ${error.message}</div>`;
            }
        }

        async function testSystems() {
            const resultDiv = document.getElementById('test-results');
            resultDiv.innerHTML += '<div class="status">🔍 Testing Systems Endpoint...</div>';
            
            try {
                const response = await fetch('/api/systems');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const systems = data.data?.systems || data.data || [];
                    resultDiv.innerHTML += `<div class="success">✅ Systems: Found ${systems.length} systems</div>`;
                    
                    if (systems.length > 0) {
                        systems.slice(0, 2).forEach((system, index) => {
                            resultDiv.innerHTML += `<div>   ${index + 1}. ${system.name_ar} - $${system.price}</div>`;
                        });
                    }
                    
                    resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2).substring(0, 500)}...</pre>`;
                } else {
                    resultDiv.innerHTML += `<div class="error">❌ Systems failed: ${response.status}</div>`;
                    resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="error">❌ Systems error: ${error.message}</div>`;
            }
        }

        async function testServices() {
            const resultDiv = document.getElementById('test-results');
            resultDiv.innerHTML += '<div class="status">🔍 Testing Services Endpoint...</div>';
            
            try {
                const response = await fetch('/api/services/technical');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const services = data.data || [];
                    resultDiv.innerHTML += `<div class="success">✅ Services: Found ${services.length} services</div>`;
                } else {
                    resultDiv.innerHTML += `<div class="error">❌ Services failed: ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="error">❌ Services error: ${error.message}</div>`;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('test-results');
            resultDiv.innerHTML += '<div class="status">🔍 Testing Login Endpoint...</div>';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML += `<div class="success">✅ Login: Success</div>`;
                    resultDiv.innerHTML += `<div>   User: ${data.data.user.full_name} (${data.data.user.role})</div>`;
                } else {
                    resultDiv.innerHTML += `<div class="error">❌ Login failed: ${response.status}</div>`;
                    resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="error">❌ Login error: ${error.message}</div>`;
            }
        }

        async function runAllTests() {
            const resultDiv = document.getElementById('test-results');
            resultDiv.innerHTML = '<div class="status">🚀 Running All Tests...</div>';
            
            await testHealth();
            await testSystems();
            await testServices();
            await testLogin();
            
            resultDiv.innerHTML += '<div class="status">✅ All tests completed!</div>';
        }

        async function testFrontendComponents() {
            const resultDiv = document.getElementById('component-results');
            resultDiv.innerHTML = '<div class="status">🔍 Testing Frontend Components...</div>';
            
            // Check if React components are loaded
            if (window.React) {
                resultDiv.innerHTML += '<div class="success">✅ React is loaded</div>';
            } else {
                resultDiv.innerHTML += '<div class="warning">⚠️ React not detected</div>';
            }
            
            // Check for main app element
            const appElement = document.getElementById('root');
            if (appElement) {
                resultDiv.innerHTML += '<div class="success">✅ Root element found</div>';
                resultDiv.innerHTML += `<div>   Content length: ${appElement.innerHTML.length} characters</div>`;
            } else {
                resultDiv.innerHTML += '<div class="error">❌ Root element not found</div>';
            }
            
            // Check for systems in DOM
            setTimeout(() => {
                const systemElements = document.querySelectorAll('[class*="system"], [class*="grid"]');
                if (systemElements.length > 0) {
                    resultDiv.innerHTML += `<div class="success">✅ Found ${systemElements.length} potential system elements</div>`;
                } else {
                    resultDiv.innerHTML += '<div class="warning">⚠️ No system elements found in DOM</div>';
                }
                
                // Check for loading states
                const loadingElements = document.querySelectorAll('[class*="loading"], [class*="spinner"]');
                if (loadingElements.length > 0) {
                    resultDiv.innerHTML += `<div class="warning">⚠️ Found ${loadingElements.length} loading elements (might be stuck)</div>`;
                }
            }, 2000);
        }

        // Initialize
        window.onload = function() {
            displayEnvVars();
            
            // Auto-run basic tests after 1 second
            setTimeout(() => {
                runAllTests();
            }, 1000);
        };
    </script>
</body>
</html>
