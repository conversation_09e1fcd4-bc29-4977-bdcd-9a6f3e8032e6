import React from 'react';
import { useTranslation } from '../../hooks/useTranslation';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  message?: string;
  fullScreen?: boolean;
  className?: string;
}

/**
 * Loading spinner component with customizable size and message
 * 
 * Features:
 * - Multiple sizes (sm, md, lg)
 * - Optional loading message
 * - Full screen overlay option
 * - Bilingual support
 * - Accessible with proper ARIA attributes
 */
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  message,
  fullScreen = false,
  className = ''
}) => {
  const { language, t } = useTranslation();

  const sizeClasses = {
    sm: 'w-4 h-4 border-2',
    md: 'w-8 h-8 border-2',
    lg: 'w-12 h-12 border-4'
  };

  const containerClasses = fullScreen
    ? 'fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50'
    : 'flex items-center justify-center p-8';

  const defaultMessage = t('common.loading');

  return (
    <div 
      className={`${containerClasses} ${className}`}
      role="status"
      aria-live="polite"
      aria-label={message || defaultMessage}
    >
      <div className="text-center">
        <div 
          className={`
            ${sizeClasses[size]} 
            border-secondary 
            border-t-transparent 
            rounded-full 
            animate-spin 
            mx-auto 
            mb-4
          `}
          aria-hidden="true"
        />
        {(message || fullScreen) && (
          <p className="text-gray-300 text-sm font-medium">
            {message || defaultMessage}
          </p>
        )}
      </div>
    </div>
  );
};

export default LoadingSpinner;
