#!/usr/bin/env node

const { exec, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class NgrokTunnelFixer {
  constructor() {
    this.tunnels = [];
    this.processes = [];
  }

  log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  async killNgrokProcesses() {
    return new Promise((resolve) => {
      this.log('🔍 Finding ngrok processes...', 'yellow');
      
      exec('tasklist /FI "IMAGENAME eq ngrok.exe" /FO CSV', (error, stdout) => {
        if (error || !stdout.includes('ngrok.exe')) {
          this.log('✅ No ngrok processes found', 'green');
          resolve();
          return;
        }

        this.log('🔪 Killing existing ngrok processes...', 'red');
        exec('taskkill /F /IM ngrok.exe', (killError) => {
          if (killError) {
            this.log(`⚠️ Error killing ngrok: ${killError.message}`, 'yellow');
          } else {
            this.log('✅ Ngrok processes killed', 'green');
          }
          setTimeout(resolve, 2000); // Wait 2 seconds
        });
      });
    });
  }

  async checkNgrokInstallation() {
    return new Promise((resolve) => {
      exec('npx ngrok version', (error, stdout) => {
        if (error) {
          this.log('❌ Ngrok not found, installing...', 'red');
          exec('npm install -g ngrok', (installError) => {
            if (installError) {
              this.log(`❌ Failed to install ngrok: ${installError.message}`, 'red');
              resolve(false);
            } else {
              this.log('✅ Ngrok installed successfully', 'green');
              resolve(true);
            }
          });
        } else {
          this.log(`✅ Ngrok version: ${stdout.trim()}`, 'green');
          resolve(true);
        }
      });
    });
  }

  async setupNgrokConfig() {
    const configPath = path.join(process.cwd(), 'ngrok-config.yml');
    
    if (!fs.existsSync(configPath)) {
      this.log('📝 Creating ngrok config file...', 'yellow');
      
      const config = `version: "2"
authtoken: 30CbmlwFOIM0eGBkPN3Qo0ryb65_HJXsM9B4y5otyscuQvza
tunnels:
  backend:
    addr: 3001
    proto: http
    name: "Khanfashariya Backend API"
    inspect: false
    bind_tls: true
  frontend:
    addr: 5173
    proto: http
    name: "Khanfashariya Frontend"
    inspect: false
    bind_tls: true`;

      fs.writeFileSync(configPath, config);
      this.log('✅ Ngrok config created', 'green');
    } else {
      this.log('✅ Ngrok config exists', 'green');
    }

    // Set authtoken
    return new Promise((resolve) => {
      exec('npx ngrok config add-authtoken 30CbmlwFOIM0eGBkPN3Qo0ryb65_HJXsM9B4y5otyscuQvza', (error) => {
        if (error) {
          this.log(`⚠️ Warning: Could not set authtoken: ${error.message}`, 'yellow');
        } else {
          this.log('✅ Authtoken configured', 'green');
        }
        resolve();
      });
    });
  }

  async startTunnel(port, name) {
    return new Promise((resolve) => {
      this.log(`🚇 Starting ${name} tunnel on port ${port}...`, 'cyan');
      
      const process = spawn('npx', ['ngrok', 'http', port.toString()], {
        stdio: ['ignore', 'pipe', 'pipe'],
        shell: true
      });

      let output = '';
      let tunnelStarted = false;

      const checkOutput = (data) => {
        output += data.toString();
        
        if (output.includes('started tunnel') || 
            output.includes('forwarding') || 
            output.includes('https://')) {
          tunnelStarted = true;
          this.log(`✅ ${name} tunnel started`, 'green');
          resolve({ success: true, process });
        }
      };

      process.stdout.on('data', checkOutput);
      process.stderr.on('data', checkOutput);

      process.on('error', (error) => {
        this.log(`❌ Failed to start ${name} tunnel: ${error.message}`, 'red');
        resolve({ success: false, error: error.message });
      });

      process.on('exit', (code) => {
        if (!tunnelStarted) {
          this.log(`❌ ${name} tunnel exited with code ${code}`, 'red');
          resolve({ success: false, error: `Process exited with code ${code}` });
        }
      });

      // Timeout after 20 seconds
      setTimeout(() => {
        if (!tunnelStarted) {
          this.log(`⏰ ${name} tunnel startup timeout`, 'yellow');
          process.kill();
          resolve({ success: false, error: 'Startup timeout' });
        }
      }, 20000);

      this.processes.push({ name, process });
    });
  }

  async getTunnelInfo() {
    return new Promise((resolve) => {
      // Wait a bit for tunnels to be ready
      setTimeout(() => {
        exec('curl -s http://127.0.0.1:4040/api/tunnels', (error, stdout) => {
          if (error) {
            this.log('⚠️ Could not get tunnel info', 'yellow');
            resolve([]);
            return;
          }

          try {
            const data = JSON.parse(stdout);
            resolve(data.tunnels || []);
          } catch (parseError) {
            this.log('⚠️ Could not parse tunnel info', 'yellow');
            resolve([]);
          }
        });
      }, 3000);
    });
  }

  async fixNgrokTunnels() {
    this.log('\n🔧 Starting Ngrok Tunnel Fix...', 'bright');
    this.log('=' * 50, 'blue');

    try {
      // Step 1: Kill existing ngrok processes
      await this.killNgrokProcesses();

      // Step 2: Check ngrok installation
      const ngrokInstalled = await this.checkNgrokInstallation();
      if (!ngrokInstalled) {
        throw new Error('Ngrok installation failed');
      }

      // Step 3: Setup config
      await this.setupNgrokConfig();

      // Step 4: Start tunnels
      this.log('\n🚇 Starting tunnels...', 'magenta');
      
      const backendResult = await this.startTunnel(3001, 'Backend');
      if (!backendResult.success) {
        this.log(`⚠️ Backend tunnel failed: ${backendResult.error}`, 'yellow');
      }

      // Wait between tunnel starts
      await new Promise(resolve => setTimeout(resolve, 3000));

      const frontendResult = await this.startTunnel(5173, 'Frontend');
      if (!frontendResult.success) {
        this.log(`⚠️ Frontend tunnel failed: ${frontendResult.error}`, 'yellow');
      }

      // Step 5: Get tunnel information
      this.log('\n📋 Getting tunnel information...', 'magenta');
      const tunnels = await this.getTunnelInfo();
      
      if (tunnels.length > 0) {
        this.log('\n🔗 Active Tunnels:', 'green');
        tunnels.forEach(tunnel => {
          this.log(`  ${tunnel.name}: ${tunnel.public_url}`, 'blue');
        });
        
        // Save tunnel info for TestSprite
        const tunnelInfo = {
          timestamp: new Date().toISOString(),
          tunnels: tunnels.map(t => ({
            name: t.name,
            public_url: t.public_url,
            local_addr: t.config.addr
          }))
        };
        
        fs.writeFileSync('tunnel-info.json', JSON.stringify(tunnelInfo, null, 2));
        this.log('\n💾 Tunnel info saved to tunnel-info.json', 'blue');
      } else {
        this.log('\n⚠️ No tunnel information available', 'yellow');
      }

      this.log('\n🎉 Ngrok tunnel fix completed!', 'green');
      this.log('🔗 Tunnels are ready for TestSprite', 'green');

    } catch (error) {
      this.log(`❌ Ngrok fix failed: ${error.message}`, 'red');
      throw error;
    }
  }

  cleanup() {
    this.log('\n🧹 Cleaning up ngrok processes...', 'yellow');
    this.processes.forEach(({ name, process }) => {
      if (process && !process.killed) {
        this.log(`🔪 Stopping ${name} tunnel...`, 'yellow');
        process.kill();
      }
    });
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received interrupt signal, cleaning up...');
  process.exit(0);
});

// Main execution
async function main() {
  const fixer = new NgrokTunnelFixer();
  
  try {
    await fixer.fixNgrokTunnels();
    
    console.log('\n✅ Ngrok tunnels are ready!');
    console.log('🔗 You can now use TestSprite with the tunnel URLs');
    console.log('\n⏳ Keeping tunnels running... Press Ctrl+C to stop');
    
    // Keep alive
    setInterval(() => {
      // Just keep the process alive
    }, 60000);
    
  } catch (error) {
    console.error(`❌ Ngrok fix failed: ${error.message}`);
    fixer.cleanup();
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error(`❌ Fatal error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = NgrokTunnelFixer;
