/**
 * API Test Component for Khanfashariya.com
 * 
 * This component tests the new API integration and provides
 * debugging information for the migration from localStorage to API.
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import apiClient from '../lib/apiClient';
import * as apiServices from '../lib/apiServices';
import dataAdapter from '../lib/dataAdapter';
import { 
  TestTube, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Database, 
  Server, 
  Wifi, 
  WifiOff,
  RefreshCw,
  X
} from 'lucide-react';

interface ApiTestComponentProps {
  onClose: () => void;
}

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error' | 'warning';
  message: string;
  details?: any;
  duration?: number;
}

const ApiTestComponent: React.FC<ApiTestComponentProps> = ({ onClose }) => {
  const { language } = useTranslation();
  const [tests, setTests] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>('');

  useEffect(() => {
    runAllTests();
  }, []);

  const addTestResult = (result: TestResult) => {
    setTests(prev => [...prev, result]);
  };

  const updateTestResult = (name: string, updates: Partial<TestResult>) => {
    setTests(prev => prev.map(test => 
      test.name === name ? { ...test, ...updates } : test
    ));
  };

  const runTest = async (
    name: string, 
    testFn: () => Promise<any>, 
    description: string
  ): Promise<void> => {
    const startTime = Date.now();
    setCurrentTest(description);
    
    addTestResult({
      name,
      status: 'pending',
      message: description
    });

    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      updateTestResult(name, {
        status: 'success',
        message: `${description} - نجح`,
        details: result,
        duration
      });
    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      updateTestResult(name, {
        status: 'error',
        message: `${description} - فشل: ${error.message}`,
        details: error,
        duration
      });
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTests([]);
    setCurrentTest('');

    try {
      // Test 1: API Client Connection
      await runTest(
        'api-connection',
        async () => {
          const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/health`);
          if (!response.ok) throw new Error(`HTTP ${response.status}`);
          return await response.json();
        },
        language === 'ar' ? 'اختبار الاتصال بالخادم' : 'Server Connection Test'
      );

      // Test 2: API Client Authentication Check
      await runTest(
        'api-auth-check',
        async () => {
          return {
            isAuthenticated: apiClient.isAuthenticated(),
            hasToken: !!apiClient.getAccessToken(),
            baseURL: import.meta.env.VITE_API_BASE_URL
          };
        },
        language === 'ar' ? 'فحص حالة المصادقة' : 'Authentication Status Check'
      );

      // Test 3: System Services API
      await runTest(
        'system-services',
        async () => {
          const result = await apiServices.getSystemServices();
          if (result.error) throw new Error(result.error.message);
          return {
            count: result.data?.length || 0,
            systems: result.data?.slice(0, 3) // First 3 for preview
          };
        },
        language === 'ar' ? 'اختبار خدمات الأنظمة' : 'System Services Test'
      );

      // Test 4: Technical Services API
      await runTest(
        'technical-services',
        async () => {
          const result = await apiServices.getTechnicalServices();
          if (result.error) throw new Error(result.error.message);
          return {
            count: result.data?.length || 0,
            services: result.data?.slice(0, 3) // First 3 for preview
          };
        },
        language === 'ar' ? 'اختبار الخدمات التقنية' : 'Technical Services Test'
      );

      // Test 5: User Management API (if authenticated)
      if (apiClient.isAuthenticated()) {
        await runTest(
          'user-management',
          async () => {
            const result = await apiServices.getAllUsers();
            if (result.error) throw new Error(result.error.message);
            return {
              count: result.data?.length || 0,
              users: result.data?.slice(0, 3).map(u => ({ id: u.id, email: u.email, role: u.role }))
            };
          },
          language === 'ar' ? 'اختبار إدارة المستخدمين' : 'User Management Test'
        );
      }

      // Test 6: Data Adapter Functionality
      await runTest(
        'data-adapter',
        async () => {
          const dataSource = dataAdapter.getDataSource();
          const systemsResult = await dataAdapter.getSystemServices();
          
          return {
            dataSource,
            systemsCount: systemsResult.data?.length || 0,
            hasError: !!systemsResult.error
          };
        },
        language === 'ar' ? 'اختبار محول البيانات' : 'Data Adapter Test'
      );

      // Test 7: Environment Variables
      await runTest(
        'environment',
        async () => {
          return {
            apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
            useApi: import.meta.env.VITE_USE_API,
            nodeEnv: import.meta.env.NODE_ENV,
            mode: import.meta.env.MODE
          };
        },
        language === 'ar' ? 'فحص متغيرات البيئة' : 'Environment Variables Check'
      );

    } catch (error) {
      console.error('Test suite error:', error);
    } finally {
      setIsRunning(false);
      setCurrentTest('');
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'pending':
        return <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />;
      default:
        return <TestTube className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'border-green-500 bg-green-50';
      case 'error':
        return 'border-red-500 bg-red-50';
      case 'warning':
        return 'border-yellow-500 bg-yellow-50';
      case 'pending':
        return 'border-blue-500 bg-blue-50';
      default:
        return 'border-gray-300 bg-gray-50';
    }
  };

  const successCount = tests.filter(t => t.status === 'success').length;
  const errorCount = tests.filter(t => t.status === 'error').length;
  const totalTests = tests.length;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary to-secondary p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="p-2 bg-white/20 rounded-lg">
                <TestTube className="w-6 h-6" />
              </div>
              <div>
                <h2 className="text-xl font-bold">
                  {language === 'ar' ? 'اختبار تكامل API' : 'API Integration Test'}
                </h2>
                <p className="text-white/80 text-sm">
                  {language === 'ar' ? 'فحص الانتقال من localStorage إلى API' : 'Testing migration from localStorage to API'}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Progress Summary */}
          <div className="mt-4 flex items-center space-x-4 rtl:space-x-reverse">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Server className="w-4 h-4" />
              <span className="text-sm">
                {language === 'ar' ? 'الخادم:' : 'Server:'} {import.meta.env.VITE_API_BASE_URL}
              </span>
            </div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              {apiClient.isAuthenticated() ? (
                <>
                  <Wifi className="w-4 h-4 text-green-300" />
                  <span className="text-sm text-green-300">
                    {language === 'ar' ? 'متصل' : 'Connected'}
                  </span>
                </>
              ) : (
                <>
                  <WifiOff className="w-4 h-4 text-red-300" />
                  <span className="text-sm text-red-300">
                    {language === 'ar' ? 'غير متصل' : 'Not Connected'}
                  </span>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Test Results */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {/* Current Test */}
          {isRunning && currentTest && (
            <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />
                <span className="text-blue-700 font-medium">{currentTest}</span>
              </div>
            </div>
          )}

          {/* Test Results List */}
          <div className="space-y-3">
            {tests.map((test, index) => (
              <div
                key={index}
                className={`p-4 border rounded-lg transition-all duration-200 ${getStatusColor(test.status)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 rtl:space-x-reverse flex-1">
                    {getStatusIcon(test.status)}
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{test.message}</div>
                      {test.duration && (
                        <div className="text-sm text-gray-500 mt-1">
                          {language === 'ar' ? 'المدة:' : 'Duration:'} {test.duration}ms
                        </div>
                      )}
                      {test.details && test.status === 'success' && (
                        <div className="mt-2 text-sm text-gray-600">
                          <pre className="bg-gray-100 p-2 rounded text-xs overflow-x-auto">
                            {JSON.stringify(test.details, null, 2)}
                          </pre>
                        </div>
                      )}
                      {test.details && test.status === 'error' && (
                        <div className="mt-2 text-sm text-red-600">
                          {test.details.message || 'Unknown error'}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm">
              <span className="text-green-600">
                ✓ {successCount} {language === 'ar' ? 'نجح' : 'Passed'}
              </span>
              <span className="text-red-600">
                ✗ {errorCount} {language === 'ar' ? 'فشل' : 'Failed'}
              </span>
              <span className="text-gray-600">
                {totalTests} {language === 'ar' ? 'إجمالي' : 'Total'}
              </span>
            </div>
            <div className="flex space-x-3 rtl:space-x-reverse">
              <button
                onClick={runAllTests}
                disabled={isRunning}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 disabled:opacity-50 flex items-center space-x-2 rtl:space-x-reverse"
              >
                <RefreshCw className={`w-4 h-4 ${isRunning ? 'animate-spin' : ''}`} />
                <span>{language === 'ar' ? 'إعادة الاختبار' : 'Rerun Tests'}</span>
              </button>
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                {language === 'ar' ? 'إغلاق' : 'Close'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiTestComponent;
