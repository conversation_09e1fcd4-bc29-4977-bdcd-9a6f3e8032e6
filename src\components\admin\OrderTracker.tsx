import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { 
  Clock, CheckCircle, AlertCircle, Package, Truck, 
  MessageSquare, FileText, Calendar, User, Tag
} from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Modal from '../ui/Modal';
import Input from '../ui/Input';
import Select from '../ui/Select';

interface OrderTimelineEvent {
  id: string;
  type: 'created' | 'confirmed' | 'started' | 'progress' | 'completed' | 'delivered' | 'cancelled';
  timestamp: Date;
  title: string;
  description: string;
  user: string;
  automated: boolean;
  attachments?: string[];
}

interface OrderTracking {
  orderId: string;
  customerName: string;
  customerEmail: string;
  serviceType: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'delivered' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  progress: number; // 0-100
  estimatedCompletion: Date;
  assignedTo?: string;
  timeline: OrderTimelineEvent[];
  files: string[];
  communications: OrderMessage[];
  tags: string[];
}

interface OrderMessage {
  id: string;
  sender: string;
  recipient: string;
  message: string;
  timestamp: Date;
  type: 'internal' | 'customer';
  attachments?: string[];
}

interface OrderTrackerProps {
  orderId: string;
  onClose: () => void;
}

/**
 * Advanced Order Tracker component for comprehensive order management
 * 
 * Features:
 * - Real-time order status tracking
 * - Timeline visualization
 * - Progress indicators
 * - Communication system
 * - File management
 * - Status updates
 * - Estimated completion tracking
 * - Priority management
 */
const OrderTracker: React.FC<OrderTrackerProps> = ({ orderId, onClose }) => {
  const { language, t } = useTranslation();
  const [order, setOrder] = useState<OrderTracking | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'timeline' | 'communications' | 'files' | 'details'>('timeline');
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [newMessage, setNewMessage] = useState('');

  const isRTL = language === 'ar';

  // Mock data - replace with actual API call
  useEffect(() => {
    const fetchOrderData = async () => {
      setLoading(true);
      
      // Simulate API call
      setTimeout(() => {
        const mockOrder: OrderTracking = {
          orderId: orderId,
          customerName: 'أحمد محمد',
          customerEmail: '<EMAIL>',
          serviceType: 'نظام PvP متقدم',
          status: 'in_progress',
          priority: 'high',
          progress: 65,
          estimatedCompletion: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          assignedTo: 'المطور الرئيسي',
          timeline: [
            {
              id: '1',
              type: 'created',
              timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
              title: language === 'ar' ? 'تم إنشاء الطلب' : 'Order Created',
              description: language === 'ar' ? 'تم استلام الطلب وبدء المعالجة' : 'Order received and processing started',
              user: 'النظام',
              automated: true
            },
            {
              id: '2',
              type: 'confirmed',
              timestamp: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
              title: language === 'ar' ? 'تم تأكيد الطلب' : 'Order Confirmed',
              description: language === 'ar' ? 'تم مراجعة المتطلبات وتأكيد الطلب' : 'Requirements reviewed and order confirmed',
              user: 'مدير المشروع',
              automated: false
            },
            {
              id: '3',
              type: 'started',
              timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
              title: language === 'ar' ? 'بدء العمل' : 'Work Started',
              description: language === 'ar' ? 'تم تعيين المطور وبدء العمل على المشروع' : 'Developer assigned and work started',
              user: 'المطور الرئيسي',
              automated: false
            },
            {
              id: '4',
              type: 'progress',
              timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
              title: language === 'ar' ? 'تحديث التقدم' : 'Progress Update',
              description: language === 'ar' ? 'تم إنجاز 65% من المشروع' : '65% of project completed',
              user: 'المطور الرئيسي',
              automated: false
            }
          ],
          files: [
            'requirements.pdf',
            'design_mockup.png',
            'progress_report.docx'
          ],
          communications: [
            {
              id: '1',
              sender: 'المطور الرئيسي',
              recipient: 'أحمد محمد',
              message: language === 'ar' ? 'تم إنجاز الجزء الأساسي من النظام، سيتم إرسال نسخة تجريبية قريباً' : 'Core system completed, demo version will be sent soon',
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
              type: 'customer'
            }
          ],
          tags: ['عالي الأولوية', 'PvP', 'متقدم']
        };
        
        setOrder(mockOrder);
        setLoading(false);
      }, 1000);
    };

    fetchOrderData();
  }, [orderId, language]);

  // Status color mapping
  const statusColors = {
    pending: 'text-warning-500 bg-warning-500/10',
    confirmed: 'text-info-500 bg-info-500/10',
    in_progress: 'text-accent-500 bg-accent-500/10',
    completed: 'text-success-500 bg-success-500/10',
    delivered: 'text-success-600 bg-success-600/10',
    cancelled: 'text-error-500 bg-error-500/10'
  };

  // Priority color mapping
  const priorityColors = {
    low: 'text-gray-500 bg-gray-500/10',
    medium: 'text-warning-500 bg-warning-500/10',
    high: 'text-error-500 bg-error-500/10',
    urgent: 'text-error-600 bg-error-600/10'
  };

  // Timeline icon mapping
  const timelineIcons = {
    created: Package,
    confirmed: CheckCircle,
    started: Clock,
    progress: Clock,
    completed: CheckCircle,
    delivered: Truck,
    cancelled: AlertCircle
  };

  const handleStatusUpdate = (newStatus: string) => {
    if (!order) return;
    
    // Update order status
    setOrder(prev => prev ? { ...prev, status: newStatus as any } : null);
    
    // Add timeline event
    const newEvent: OrderTimelineEvent = {
      id: Date.now().toString(),
      type: newStatus as any,
      timestamp: new Date(),
      title: `Status updated to ${newStatus}`,
      description: `Order status changed to ${newStatus}`,
      user: 'Current User',
      automated: false
    };
    
    setOrder(prev => prev ? {
      ...prev,
      timeline: [...prev.timeline, newEvent]
    } : null);
    
    setShowUpdateModal(false);
  };

  const handleSendMessage = () => {
    if (!newMessage.trim() || !order) return;
    
    const message: OrderMessage = {
      id: Date.now().toString(),
      sender: 'Current User',
      recipient: order.customerName,
      message: newMessage,
      timestamp: new Date(),
      type: 'customer'
    };
    
    setOrder(prev => prev ? {
      ...prev,
      communications: [...prev.communications, message]
    } : null);
    
    setNewMessage('');
  };

  if (loading) {
    return (
      <Modal isOpen={true} onClose={onClose} size="xl" title={t('admin.orderTracker', 'Order Tracker')}>
        <Modal.Body>
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="w-12 h-12 border-4 border-secondary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
              <p className="text-text-secondary">{t('common.loading', 'Loading...')}</p>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    );
  }

  if (!order) {
    return (
      <Modal isOpen={true} onClose={onClose} size="xl" title={t('admin.orderTracker', 'Order Tracker')}>
        <Modal.Body>
          <div className="text-center py-12">
            <AlertCircle className="w-16 h-16 text-error-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-text-primary mb-2">
              {t('admin.orderNotFound', 'Order Not Found')}
            </h3>
            <p className="text-text-secondary">
              {t('admin.orderNotFoundDesc', 'The requested order could not be found.')}
            </p>
          </div>
        </Modal.Body>
      </Modal>
    );
  }

  return (
    <>
      <Modal isOpen={true} onClose={onClose} size="xl" title={`${t('admin.orderTracker', 'Order Tracker')} - ${order.orderId}`}>
        <Modal.Body className="p-0">
          {/* Header */}
          <div className="p-6 border-b border-accent-500/20">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div>
                <h2 className="text-xl font-bold text-text-primary">{order.serviceType}</h2>
                <p className="text-text-secondary">{order.customerName} • {order.customerEmail}</p>
              </div>
              
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${statusColors[order.status]}`}>
                  {t(`status.${order.status}`, order.status)}
                </span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${priorityColors[order.priority]}`}>
                  {t(`priority.${order.priority}`, order.priority)}
                </span>
              </div>
            </div>
            
            {/* Progress bar */}
            <div className="mt-4">
              <div className="flex justify-between text-sm text-text-secondary mb-2">
                <span>{t('admin.progress', 'Progress')}</span>
                <span>{order.progress}%</span>
              </div>
              <div className="w-full bg-gray-200/20 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-secondary-500 to-accent-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${order.progress}%` }}
                />
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-accent-500/20">
            <nav className="flex space-x-8 rtl:space-x-reverse px-6">
              {[
                { id: 'timeline', label: t('admin.timeline', 'Timeline'), icon: Clock },
                { id: 'communications', label: t('admin.communications', 'Communications'), icon: MessageSquare },
                { id: 'files', label: t('admin.files', 'Files'), icon: FileText },
                { id: 'details', label: t('admin.details', 'Details'), icon: Tag }
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center space-x-2 rtl:space-x-reverse py-4 border-b-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-accent-500 text-accent-500'
                        : 'border-transparent text-text-secondary hover:text-text-primary'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab content */}
          <div className="p-6 max-h-96 overflow-y-auto">
            {activeTab === 'timeline' && (
              <div className="space-y-4">
                {order.timeline.map((event, index) => {
                  const Icon = timelineIcons[event.type];
                  return (
                    <div key={event.id} className="flex items-start space-x-4 rtl:space-x-reverse">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-accent-500/20 rounded-full flex items-center justify-center">
                          <Icon className="w-5 h-5 text-accent-500" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium text-text-primary">{event.title}</h4>
                          <span className="text-xs text-text-tertiary">
                            {event.timestamp.toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US', {
                              calendar: 'gregory',
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit'
                            })}
                          </span>
                        </div>
                        <p className="text-sm text-text-secondary mt-1">{event.description}</p>
                        <p className="text-xs text-text-tertiary mt-1">
                          {t('admin.by', 'By')}: {event.user}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {activeTab === 'communications' && (
              <div className="space-y-4">
                {order.communications.map((comm) => (
                  <Card key={comm.id} variant="outlined" padding="sm">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-text-primary">{comm.sender}</h4>
                        <p className="text-sm text-text-secondary mt-1">{comm.message}</p>
                      </div>
                      <span className="text-xs text-text-tertiary">
                        {comm.timestamp.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </span>
                    </div>
                  </Card>
                ))}
                
                <div className="border-t border-accent-500/20 pt-4">
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <Input
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder={t('admin.typeMessage', 'Type a message...')}
                      className="flex-1"
                    />
                    <Button onClick={handleSendMessage} disabled={!newMessage.trim()}>
                      {t('common.send', 'Send')}
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'files' && (
              <div className="space-y-2">
                {order.files.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-primary-500/30 rounded-lg">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <FileText className="w-5 h-5 text-accent-500" />
                      <span className="text-text-primary">{file}</span>
                    </div>
                    <Button variant="ghost" size="sm">
                      {t('common.download', 'Download')}
                    </Button>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'details' && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-text-secondary">
                      {t('admin.assignedTo', 'Assigned To')}
                    </label>
                    <p className="text-text-primary">{order.assignedTo || t('common.unassigned', 'Unassigned')}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-text-secondary">
                      {t('admin.estimatedCompletion', 'Estimated Completion')}
                    </label>
                    <p className="text-text-primary">
                      {order.estimatedCompletion.toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                    </p>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-text-secondary mb-2 block">
                    {t('admin.tags', 'Tags')}
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {order.tags.map((tag, index) => (
                      <span key={index} className="px-2 py-1 bg-accent-500/20 text-accent-400 rounded-md text-sm">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="outline" onClick={onClose}>
            {t('common.close', 'Close')}
          </Button>
          <Button onClick={() => setShowUpdateModal(true)}>
            {t('admin.updateStatus', 'Update Status')}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Update Status Modal */}
      {showUpdateModal && (
        <Modal isOpen={true} onClose={() => setShowUpdateModal(false)} title={t('admin.updateOrderStatus', 'Update Order Status')}>
          <Modal.Body>
            <Select
              label={t('admin.newStatus', 'New Status')}
              options={[
                { value: 'pending', label: t('status.pending', 'Pending') },
                { value: 'confirmed', label: t('status.confirmed', 'Confirmed') },
                { value: 'in_progress', label: t('status.in_progress', 'In Progress') },
                { value: 'completed', label: t('status.completed', 'Completed') },
                { value: 'delivered', label: t('status.delivered', 'Delivered') },
                { value: 'cancelled', label: t('status.cancelled', 'Cancelled') }
              ]}
              value={order.status}
              onChange={(value) => handleStatusUpdate(value)}
            />
          </Modal.Body>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowUpdateModal(false)}>
              {t('common.cancel', 'Cancel')}
            </Button>
          </Modal.Footer>
        </Modal>
      )}
    </>
  );
};

export default OrderTracker;
