import React, { useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import Navigation from './Navigation';
import Footer from './Footer';


interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { language, loading, t } = useTranslation();

  // Utility function to detect text overflow and apply appropriate classes
  useEffect(() => {
    const detectTextOverflow = () => {
      // Handle navigation button text
      const navButtonTexts = document.querySelectorAll('.nav-button-text');
      navButtonTexts.forEach((element) => {
        const el = element as HTMLElement;
        if (el.scrollWidth > el.clientWidth) {
          el.classList.add('text-overflow');
        } else {
          el.classList.remove('text-overflow');
        }
      });

      // Handle any other button text with the animated class
      const animatedButtonTexts = document.querySelectorAll('.btn-text-animated');
      animatedButtonTexts.forEach((element) => {
        const el = element as HTMLElement;
        if (el.scrollWidth > el.clientWidth) {
          el.classList.add('text-overflow');
        } else {
          el.classList.remove('text-overflow');
        }
      });
    };

    // Run on mount and when language changes
    detectTextOverflow();

    // Run on window resize
    window.addEventListener('resize', detectTextOverflow);

    // Run after a short delay to ensure all content is loaded
    const timeoutId = setTimeout(detectTextOverflow, 100);

    return () => {
      window.removeEventListener('resize', detectTextOverflow);
      clearTimeout(timeoutId);
    };
  }, [language]); // Re-run when language changes

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-secondary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">{t('common.loading', 'Loading...')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${language === 'ar' ? 'font-arabic' : 'font-english'}`}>
      <Navigation />
      <main className="pt-20">
        {children}
      </main>
      <Footer />
    </div>
  );
};

export default Layout;