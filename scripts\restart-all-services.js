#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class ServiceRestarter {
  constructor() {
    this.processes = [];
    this.restartLog = [];
  }

  log(message, color = 'reset') {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(`${colors[color]}${logEntry}${colors.reset}`);
    this.restartLog.push(logEntry);
  }

  async killProcessOnPort(port, serviceName) {
    return new Promise((resolve) => {
      this.log(`🔍 Checking for processes on port ${port} (${serviceName})...`, 'yellow');
      
      exec(`netstat -ano | findstr :${port}`, (error, stdout) => {
        if (error || !stdout.trim()) {
          this.log(`✅ No process found on port ${port}`, 'green');
          resolve(true);
          return;
        }

        const lines = stdout.trim().split('\n');
        const pids = new Set();
        
        lines.forEach(line => {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 5) {
            const pid = parts[parts.length - 1];
            if (pid && pid !== '0') {
              pids.add(pid);
            }
          }
        });

        if (pids.size === 0) {
          this.log(`✅ No active processes on port ${port}`, 'green');
          resolve(true);
          return;
        }

        this.log(`🔪 Killing ${pids.size} process(es) on port ${port}...`, 'red');
        
        let killedCount = 0;
        pids.forEach(pid => {
          exec(`taskkill /F /PID ${pid}`, (killError) => {
            killedCount++;
            if (killError) {
              this.log(`⚠️ Failed to kill PID ${pid}: ${killError.message}`, 'yellow');
            } else {
              this.log(`✅ Killed process PID ${pid}`, 'green');
            }
            
            if (killedCount === pids.size) {
              setTimeout(() => resolve(true), 2000); // Wait 2 seconds after killing
            }
          });
        });
      });
    });
  }

  async startService(command, serviceName, cwd = process.cwd()) {
    return new Promise((resolve) => {
      this.log(`🚀 Starting ${serviceName}...`, 'cyan');
      
      const [cmd, ...args] = command.split(' ');
      const process = spawn(cmd, args, {
        cwd,
        stdio: ['ignore', 'pipe', 'pipe'],
        shell: true
      });

      let started = false;
      let output = '';

      const checkStartup = (data) => {
        output += data.toString();
        
        // Check for various startup indicators
        const startupIndicators = [
          'server started',
          'listening on',
          'ready on',
          'dev server running',
          'local:',
          'network:',
          'started tunnel',
          'forwarding'
        ];

        if (!started && startupIndicators.some(indicator => 
          output.toLowerCase().includes(indicator.toLowerCase())
        )) {
          started = true;
          this.log(`✅ ${serviceName} started successfully`, 'green');
          resolve({ success: true, process, output });
        }
      };

      process.stdout.on('data', checkStartup);
      process.stderr.on('data', checkStartup);

      process.on('error', (error) => {
        this.log(`❌ Failed to start ${serviceName}: ${error.message}`, 'red');
        resolve({ success: false, error: error.message });
      });

      process.on('exit', (code) => {
        if (!started) {
          this.log(`❌ ${serviceName} exited with code ${code}`, 'red');
          resolve({ success: false, error: `Process exited with code ${code}` });
        }
      });

      // Timeout after 30 seconds
      setTimeout(() => {
        if (!started) {
          this.log(`⏰ ${serviceName} startup timeout`, 'yellow');
          resolve({ success: false, error: 'Startup timeout', process, output });
        }
      }, 30000);

      this.processes.push({ name: serviceName, process });
    });
  }

  async waitForService(url, serviceName, maxAttempts = 10) {
    this.log(`⏳ Waiting for ${serviceName} to be ready...`, 'yellow');
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await fetch(url);
        if (response.ok) {
          this.log(`✅ ${serviceName} is ready!`, 'green');
          return true;
        }
      } catch (error) {
        // Service not ready yet
      }
      
      this.log(`🔄 Attempt ${attempt}/${maxAttempts} - ${serviceName} not ready yet...`, 'blue');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    this.log(`❌ ${serviceName} failed to become ready after ${maxAttempts} attempts`, 'red');
    return false;
  }

  async restartAllServices() {
    this.log('\n🔄 Starting Complete Service Restart...', 'bright');
    this.log('=' * 60, 'blue');

    try {
      // Step 1: Kill existing processes
      this.log('\n📋 Step 1: Stopping existing services...', 'magenta');
      await this.killProcessOnPort(3001, 'Backend Server');
      await this.killProcessOnPort(5173, 'Frontend Server');
      await this.killProcessOnPort(4040, 'Ngrok');

      // Step 2: Start backend
      this.log('\n📋 Step 2: Starting backend server...', 'magenta');
      const backendResult = await this.startService('npm run start', 'Backend Server');
      
      if (!backendResult.success) {
        throw new Error(`Failed to start backend: ${backendResult.error}`);
      }

      // Wait for backend to be ready
      const backendReady = await this.waitForService('http://localhost:3001/health', 'Backend');
      if (!backendReady) {
        throw new Error('Backend failed to become ready');
      }

      // Step 3: Start frontend
      this.log('\n📋 Step 3: Starting frontend server...', 'magenta');
      const frontendResult = await this.startService('npm run dev', 'Frontend Server');
      
      if (!frontendResult.success) {
        this.log('⚠️ Frontend startup issue, but continuing...', 'yellow');
      }

      // Step 4: Start ngrok
      this.log('\n📋 Step 4: Starting ngrok tunnels...', 'magenta');
      const ngrokResult = await this.startService(
        'npx ngrok start --config ngrok-config.yml --all', 
        'Ngrok Tunnels'
      );
      
      if (!ngrokResult.success) {
        this.log('⚠️ Ngrok startup issue, trying alternative...', 'yellow');
        
        // Try starting individual tunnels
        await this.startService('npx ngrok http 3001', 'Ngrok Backend');
        await new Promise(resolve => setTimeout(resolve, 3000));
        await this.startService('npx ngrok http 5173', 'Ngrok Frontend');
      }

      // Step 5: Verify all services
      this.log('\n📋 Step 5: Verifying services...', 'magenta');
      await this.verifyAllServices();

      this.log('\n🎉 Service restart completed!', 'green');
      this.saveRestartLog();

    } catch (error) {
      this.log(`❌ Service restart failed: ${error.message}`, 'red');
      this.saveRestartLog();
      throw error;
    }
  }

  async verifyAllServices() {
    const checks = [
      { url: 'http://localhost:3001/health', name: 'Backend Health' },
      { url: 'http://localhost:5173', name: 'Frontend' },
      { url: 'http://127.0.0.1:4040/api/tunnels', name: 'Ngrok API' }
    ];

    for (const check of checks) {
      try {
        const response = await fetch(check.url);
        if (response.ok) {
          this.log(`✅ ${check.name} is working`, 'green');
        } else {
          this.log(`⚠️ ${check.name} returned status ${response.status}`, 'yellow');
        }
      } catch (error) {
        this.log(`❌ ${check.name} is not accessible: ${error.message}`, 'red');
      }
    }
  }

  saveRestartLog() {
    const logFile = path.join(__dirname, '..', 'restart-log.txt');
    const logContent = this.restartLog.join('\n') + '\n';
    fs.writeFileSync(logFile, logContent);
    this.log(`💾 Restart log saved to: ${logFile}`, 'blue');
  }

  cleanup() {
    this.log('\n🧹 Cleaning up...', 'yellow');
    this.processes.forEach(({ name, process }) => {
      if (process && !process.killed) {
        this.log(`🔪 Stopping ${name}...`, 'yellow');
        process.kill();
      }
    });
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received interrupt signal, cleaning up...');
  process.exit(0);
});

// Main execution
async function main() {
  const restarter = new ServiceRestarter();
  
  try {
    await restarter.restartAllServices();
    console.log('\n✅ All services restarted successfully!');
    console.log('🔗 You can now use TestSprite or run local tests');
    
    // Keep the process running to maintain services
    console.log('\n⏳ Keeping services running... Press Ctrl+C to stop all services');
    
    // Keep alive
    setInterval(() => {
      // Just keep the process alive
    }, 60000);
    
  } catch (error) {
    console.error(`❌ Restart failed: ${error.message}`);
    restarter.cleanup();
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error(`❌ Fatal error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = ServiceRestarter;
