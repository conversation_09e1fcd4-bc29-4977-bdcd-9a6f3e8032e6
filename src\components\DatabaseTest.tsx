import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import {
  getSystemServices,
  createSystemService,
  updateSystemService,
  deleteSystemService
} from '../lib/apiServices';
import { SystemService } from '../lib/database';
import { X, Database, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';

interface DatabaseTestProps {
  onClose: () => void;
}

/**
 * Database test component to verify CRUD operations
 */
const DatabaseTest: React.FC<DatabaseTestProps> = ({ onClose }) => {
  const { language } = useTranslation();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [systems, setSystems] = useState<SystemService[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
    setTestResults(prev => [...prev, `${timestamp} ${icon} ${message}`]);
  };

  const testGetSystems = async () => {
    addResult('Testing getSystemServices...');
    try {
      const result = getSystemServices();
      console.log('getSystemServices result:', result);
      
      if (result.data) {
        setSystems(result.data);
        addResult(`Found ${result.data.length} systems`, 'success');
        result.data.forEach((system, index) => {
          addResult(`System ${index + 1}: ${system.name.en} (ID: ${system.id})`);
        });
      } else {
        addResult('No systems data returned', 'error');
      }
    } catch (error: any) {
      addResult(`Error: ${error.message}`, 'error');
      console.error('getSystemServices error:', error);
    }
  };

  const testCreateSystem = async () => {
    addResult('Testing createSystemService...');
    try {
      const testData = {
        name: { ar: 'نظام اختبار', en: 'Test System' },
        description: { ar: 'وصف النظام التجريبي', en: 'Test system description' },
        price: 99,
        category: 'test',
        features: { ar: ['ميزة 1', 'ميزة 2'], en: ['Feature 1', 'Feature 2'] },
        tech_specs: { ar: ['مواصفة 1'], en: ['Spec 1'] },
        video_url: '',
        image_url: '',
        status: 'active' as const
      };

      const result = await createSystemService(testData);
      console.log('createSystemService result:', result);
      
      if (result.data) {
        addResult(`Created system: ${result.data.name.en}`, 'success');
        await testGetSystems(); // Refresh list
      } else {
        addResult(`Create failed: ${result.error?.message}`, 'error');
      }
    } catch (error: any) {
      addResult(`Create error: ${error.message}`, 'error');
      console.error('createSystemService error:', error);
    }
  };

  const testUpdateSystem = async () => {
    if (systems.length === 0) {
      addResult('No systems to update', 'error');
      return;
    }

    addResult('Testing updateSystemService...');
    try {
      const systemToUpdate = systems[0];
      const updateData = {
        name: { ar: 'نظام محدث', en: 'Updated System' },
        price: 150
      };

      const result = await updateSystemService(systemToUpdate.id, updateData);
      console.log('updateSystemService result:', result);
      
      if (result.data) {
        addResult(`Updated system: ${result.data.name.en}`, 'success');
        await testGetSystems(); // Refresh list
      } else {
        addResult(`Update failed: ${result.error?.message}`, 'error');
      }
    } catch (error: any) {
      addResult(`Update error: ${error.message}`, 'error');
      console.error('updateSystemService error:', error);
    }
  };

  const testDeleteSystem = async () => {
    const testSystems = systems.filter(s => s.name.en.includes('Test') || s.name.en.includes('Updated'));
    if (testSystems.length === 0) {
      addResult('No test systems to delete', 'error');
      return;
    }

    addResult('Testing deleteSystemService...');
    try {
      const systemToDelete = testSystems[0];
      const result = await deleteSystemService(systemToDelete.id);
      console.log('deleteSystemService result:', result);
      
      if (result.data) {
        addResult(`Deleted system: ${systemToDelete.name.en}`, 'success');
        await testGetSystems(); // Refresh list
      } else {
        addResult(`Delete failed: ${result.error?.message}`, 'error');
      }
    } catch (error: any) {
      addResult(`Delete error: ${error.message}`, 'error');
      console.error('deleteSystemService error:', error);
    }
  };

  const runAllTests = async () => {
    setLoading(true);
    setTestResults([]);
    addResult('Starting database tests...');
    
    await testGetSystems();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testCreateSystem();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testUpdateSystem();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testDeleteSystem();
    
    addResult('All tests completed!', 'success');
    setLoading(false);
  };

  const clearResults = () => {
    setTestResults([]);
    setSystems([]);
  };

  const checkApiConnection = async () => {
    addResult('Checking API connection...');
    try {
      const result = await getSystemServices();
      if (result.error) {
        addResult(`API error: ${result.error.message}`, 'error');
      } else {
        addResult(`API connected successfully - found ${result.data.length} systems`);
        addResult('API is working properly with MySQL database');
      }
    } catch (error: any) {
      addResult(`API connection error: ${error.message}`, 'error');
    }
  };

  useEffect(() => {
    testGetSystems();
  }, []);

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 overflow-y-auto">
      <div className="min-h-screen py-8">
        <div className="max-w-4xl mx-auto bg-primary rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Database className="w-8 h-8 text-white" />
                <h2 className="text-2xl font-bold text-white">
                  {language === 'ar' ? 'اختبار قاعدة البيانات' : 'Database Test'}
                </h2>
              </div>
              <button
                onClick={onClose}
                className="w-10 h-10 bg-white/20 hover:bg-white/40 rounded-full flex items-center justify-center transition-colors"
              >
                <X className="w-6 h-6 text-white" />
              </button>
            </div>
          </div>

          <div className="p-6">
            {/* Controls */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <button
                onClick={runAllTests}
                disabled={loading}
                className="px-4 py-2 bg-green-500/20 text-green-400 border border-green-500/30 rounded-lg hover:bg-green-500/30 transition-colors disabled:opacity-50"
              >
                {loading ? 'Running...' : 'Run All Tests'}
              </button>
              
              <button
                onClick={testGetSystems}
                className="px-4 py-2 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded-lg hover:bg-blue-500/30 transition-colors"
              >
                Test Get
              </button>
              
              <button
                onClick={checkApiConnection}
                className="px-4 py-2 bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 rounded-lg hover:bg-yellow-500/30 transition-colors"
              >
                Check API
              </button>
              
              <button
                onClick={clearResults}
                className="px-4 py-2 bg-red-500/20 text-red-400 border border-red-500/30 rounded-lg hover:bg-red-500/30 transition-colors"
              >
                Clear
              </button>
            </div>

            {/* Current Systems */}
            <div className="mb-6 bg-background/50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-3">
                Current Systems ({systems.length})
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                {systems.map((system, index) => (
                  <div key={system.id} className="text-sm text-gray-300 p-2 bg-primary/30 rounded">
                    <span className="font-medium">{system.name.en}</span>
                    <span className="text-gray-400 ml-2">${system.price}</span>
                  </div>
                ))}
                {systems.length === 0 && (
                  <div className="text-gray-500 text-sm">No systems found</div>
                )}
              </div>
            </div>

            {/* Test Results */}
            <div className="bg-black/50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-3">
                Test Results ({testResults.length})
              </h3>
              <div className="h-96 overflow-y-auto space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm text-gray-300 font-mono">
                    {result}
                  </div>
                ))}
                {testResults.length === 0 && (
                  <div className="text-gray-500 text-sm">
                    No test results yet. Click "Run All Tests" to start.
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DatabaseTest;
