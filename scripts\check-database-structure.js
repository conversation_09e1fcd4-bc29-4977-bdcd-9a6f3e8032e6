/**
 * Database Structure Checker
 * 
 * This script checks the actual database structure to understand
 * what fields exist and their data types.
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'khanfashariya_db'
};

class DatabaseStructureChecker {
  constructor() {
    this.connection = null;
  }

  async checkStructure() {
    console.log('🔍 Checking database structure...\n');
    
    try {
      this.connection = await mysql.createConnection(DB_CONFIG);
      console.log('✅ Connected to database successfully\n');
      
      await this.checkSystemServicesTable();
      await this.checkTechnicalServicesTable();
      await this.checkSampleData();
      
    } catch (error) {
      console.error('❌ Database check failed:', error.message);
      throw error;
    } finally {
      if (this.connection) {
        await this.connection.end();
      }
    }
  }

  async checkSystemServicesTable() {
    console.log('📋 System Services Table Structure:');
    console.log('=====================================');
    
    try {
      // Get table structure
      const [columns] = await this.connection.execute(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'system_services'
        ORDER BY ORDINAL_POSITION
      `, [DB_CONFIG.database]);
      
      if (columns.length === 0) {
        console.log('❌ Table system_services does not exist!');
        return;
      }
      
      columns.forEach(col => {
        console.log(`  ${col.COLUMN_NAME.padEnd(20)} | ${col.DATA_TYPE.padEnd(15)} | ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
      });
      
      // Get sample record
      const [sampleData] = await this.connection.execute(`
        SELECT * FROM system_services LIMIT 1
      `);
      
      if (sampleData.length > 0) {
        console.log('\n📄 Sample Record:');
        console.log('==================');
        const sample = sampleData[0];
        Object.keys(sample).forEach(key => {
          const value = sample[key];
          const displayValue = typeof value === 'string' && value.length > 50 
            ? value.substring(0, 50) + '...' 
            : value;
          console.log(`  ${key.padEnd(20)} | ${displayValue}`);
        });
      }
      
    } catch (error) {
      console.error('❌ Error checking system_services table:', error.message);
    }
    
    console.log('\n');
  }

  async checkTechnicalServicesTable() {
    console.log('📋 Technical Services Table Structure:');
    console.log('======================================');
    
    try {
      // Get table structure
      const [columns] = await this.connection.execute(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'technical_services'
        ORDER BY ORDINAL_POSITION
      `, [DB_CONFIG.database]);
      
      if (columns.length === 0) {
        console.log('❌ Table technical_services does not exist!');
        return;
      }
      
      columns.forEach(col => {
        console.log(`  ${col.COLUMN_NAME.padEnd(20)} | ${col.DATA_TYPE.padEnd(15)} | ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
      });
      
      // Get sample record
      const [sampleData] = await this.connection.execute(`
        SELECT * FROM technical_services LIMIT 1
      `);
      
      if (sampleData.length > 0) {
        console.log('\n📄 Sample Record:');
        console.log('==================');
        const sample = sampleData[0];
        Object.keys(sample).forEach(key => {
          const value = sample[key];
          const displayValue = typeof value === 'string' && value.length > 50 
            ? value.substring(0, 50) + '...' 
            : value;
          console.log(`  ${key.padEnd(20)} | ${displayValue}`);
        });
      }
      
    } catch (error) {
      console.error('❌ Error checking technical_services table:', error.message);
    }
    
    console.log('\n');
  }

  async checkSampleData() {
    console.log('🔍 Checking specific record (guildWar):');
    console.log('=======================================');
    
    try {
      const [guildWarData] = await this.connection.execute(`
        SELECT * FROM system_services WHERE id = 'guildWar'
      `);
      
      if (guildWarData.length > 0) {
        console.log('✅ Found guildWar record:');
        const record = guildWarData[0];
        Object.keys(record).forEach(key => {
          const value = record[key];
          console.log(`  ${key.padEnd(20)} | ${value}`);
        });
      } else {
        console.log('❌ guildWar record not found');
        
        // Check what records exist
        const [allRecords] = await this.connection.execute(`
          SELECT id, name_ar, name_en FROM system_services LIMIT 5
        `);
        
        console.log('\n📋 Available records:');
        allRecords.forEach(record => {
          console.log(`  ID: ${record.id} | AR: ${record.name_ar} | EN: ${record.name_en}`);
        });
      }
      
    } catch (error) {
      console.error('❌ Error checking sample data:', error.message);
    }
  }
}

// Run the checker
if (require.main === module) {
  const checker = new DatabaseStructureChecker();
  checker.checkStructure().catch(console.error);
}

module.exports = DatabaseStructureChecker;