/**
 * Enhanced Order Management Types
 * Comprehensive type definitions for the new order system
 */

// Base Order Status Types
export type OrderStatus = 
  | 'pending' 
  | 'confirmed' 
  | 'in_progress' 
  | 'testing' 
  | 'completed' 
  | 'cancelled' 
  | 'refunded' 
  | 'on_hold' 
  | 'expired';

export type PaymentStatus = 
  | 'pending' 
  | 'paid' 
  | 'partial' 
  | 'failed' 
  | 'refunded' 
  | 'disputed';

export type OrderPriority = 'low' | 'medium' | 'high' | 'urgent';

export type OrderType = 
  | 'system_service' 
  | 'technical_service' 
  | 'premium_content' 
  | 'premium_package' 
  | 'custom_request';

export type OrderCategory = 
  | 'standard' 
  | 'premium_base' 
  | 'premium_custom' 
  | 'subscription' 
  | 'maintenance';

export type SubscriptionType = 
  | 'none' 
  | 'monthly' 
  | 'quarterly' 
  | 'yearly' 
  | 'lifetime';

export type SupportLevel = 'basic' | 'standard' | 'premium' | 'enterprise';

export type ItemType = 
  | 'base_product' 
  | 'addon_system' 
  | 'addon_service' 
  | 'customization' 
  | 'maintenance';

// Enhanced Order Interface
export interface EnhancedOrder {
  // Basic Information
  id: string;
  user_id: string;
  order_number: string;
  
  // Classification
  order_type: OrderType;
  order_category: OrderCategory;
  
  // Item Information
  item_id: string;
  item_name_ar: string;
  item_name_en: string;
  
  // Pricing Structure
  quantity: number;
  unit_price: number;
  base_price: number;
  addons_price: number;
  subscription_price: number;
  maintenance_price: number;
  total_price: number;
  discount_amount: number;
  tax_amount: number;
  final_price: number;
  currency: string;
  
  // Status Management
  status: OrderStatus;
  priority: OrderPriority;
  progress_percentage: number;
  
  // Payment Information
  payment_status: PaymentStatus;
  payment_method?: string;
  payment_reference?: string;
  payment_gateway?: string;
  
  // Subscription Management
  subscription_type: SubscriptionType;
  subscription_duration?: number;
  subscription_start_date?: string;
  subscription_end_date?: string;
  auto_renewal: boolean;
  
  // Service Details
  maintenance_included: boolean;
  installation_included: boolean;
  support_level: SupportLevel;
  
  // Complex Order Structure
  order_details?: OrderDetails;
  selected_addons?: SelectedAddon[];
  customization_options?: CustomizationOption[];
  
  // Timeline
  created_at: string;
  updated_at: string;
  confirmed_at?: string;
  started_at?: string;
  estimated_completion?: string;
  completed_at?: string;
  delivery_date?: string;
  
  // Notes and Communication
  notes_ar?: string;
  notes_en?: string;
  admin_notes?: string;
  customer_requirements?: string;
  
  // File Attachments
  attached_files?: AttachedFile[];
  
  // Audit Trail
  created_by?: string;
  last_modified_by?: string;
  
  // User Information (from JOIN)
  username?: string;
  email?: string;
  full_name?: string;
}

// Order Details Structure
export interface OrderDetails {
  is_premium_edition?: boolean;
  base_package?: {
    id: string;
    name_ar: string;
    name_en: string;
    price: number;
    features: string[];
  };
  selected_systems?: {
    id: string;
    name_ar: string;
    name_en: string;
    price: number;
    premium_price?: number;
    installation_included: boolean;
    maintenance_included: boolean;
  }[];
  selected_services?: {
    id: string;
    name_ar: string;
    name_en: string;
    price: number;
    premium_price?: number;
    subscription_type: SubscriptionType;
    installation_included: boolean;
    maintenance_included: boolean;
  }[];
  custom_requirements?: {
    description_ar: string;
    description_en: string;
    estimated_hours: number;
    hourly_rate: number;
  };
  delivery_preferences?: {
    delivery_method: 'download' | 'installation' | 'cloud_setup';
    preferred_date?: string;
    special_instructions?: string;
  };
}

// Selected Add-on Interface
export interface SelectedAddon {
  id: string;
  type: 'system' | 'service';
  name_ar: string;
  name_en: string;
  price: number;
  premium_price?: number;
  quantity: number;
  configuration?: Record<string, any>;
}

// Customization Options
export interface CustomizationOption {
  id: string;
  name_ar: string;
  name_en: string;
  type: 'feature' | 'design' | 'integration' | 'performance';
  value: any;
  additional_cost: number;
  estimated_time: number;
}

// Attached Files
export interface AttachedFile {
  id: string;
  filename: string;
  original_name: string;
  file_type: string;
  file_size: number;
  upload_date: string;
  uploaded_by: string;
  description?: string;
}

// Order Item Interface
export interface OrderItem {
  id: string;
  order_id: string;
  item_type: ItemType;
  item_id: string;
  item_name_ar: string;
  item_name_en: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  item_details?: Record<string, any>;
  created_at: string;
}

// Order Status History
export interface OrderStatusHistory {
  id: string;
  order_id: string;
  old_status?: string;
  new_status: string;
  changed_by: string;
  change_reason?: string;
  notes?: string;
  created_at: string;
}

// Subscription Interface
export interface Subscription {
  id: string;
  order_id: string;
  user_id: string;
  subscription_type: SubscriptionType;
  status: 'active' | 'paused' | 'cancelled' | 'expired' | 'pending';
  start_date: string;
  end_date?: string;
  next_billing_date?: string;
  billing_amount: number;
  auto_renewal: boolean;
  payment_method_id?: string;
  created_at: string;
  updated_at: string;
}

// Order Communication
export interface OrderCommunication {
  id: string;
  order_id: string;
  sender_type: 'admin' | 'customer' | 'system';
  sender_id?: string;
  message_type: 'note' | 'status_update' | 'payment_update' | 'delivery_update' | 'custom';
  subject_ar?: string;
  subject_en?: string;
  message_ar?: string;
  message_en?: string;
  attachments?: AttachedFile[];
  is_internal: boolean;
  created_at: string;
}

// Order Creation Request
export interface CreateOrderRequest {
  order_type: OrderType;
  order_category?: OrderCategory;
  item_id: string;
  quantity?: number;
  
  // Premium Edition specific
  selected_systems?: string[];
  selected_services?: string[];
  customization_options?: Partial<CustomizationOption>[];
  
  // Subscription options
  subscription_type?: SubscriptionType;
  subscription_duration?: number;
  auto_renewal?: boolean;
  
  // Service options
  maintenance_included?: boolean;
  installation_included?: boolean;
  support_level?: SupportLevel;
  
  // Customer input
  notes_ar?: string;
  notes_en?: string;
  customer_requirements?: string;
  
  // Delivery preferences
  delivery_preferences?: OrderDetails['delivery_preferences'];
}

// Order Update Request
export interface UpdateOrderRequest {
  status?: OrderStatus;
  priority?: OrderPriority;
  progress_percentage?: number;
  payment_status?: PaymentStatus;
  estimated_completion?: string;
  admin_notes?: string;
  attached_files?: AttachedFile[];
}

// Order Statistics
export interface OrderStatistics {
  total_orders: number;
  pending_orders: number;
  in_progress_orders: number;
  completed_orders: number;
  cancelled_orders: number;
  total_revenue: number;
  pending_revenue: number;
  average_order_value: number;
  completion_rate: number;
  average_completion_time: number;
  premium_orders_count: number;
  subscription_orders_count: number;
}

// Order Filter Options
export interface OrderFilters {
  status?: OrderStatus[];
  order_type?: OrderType[];
  order_category?: OrderCategory[];
  payment_status?: PaymentStatus[];
  priority?: OrderPriority[];
  subscription_type?: SubscriptionType[];
  date_range?: {
    start: string;
    end: string;
  };
  price_range?: {
    min: number;
    max: number;
  };
  search?: string;
  user_id?: string;
}

// API Response Types
export interface OrderResponse {
  success: boolean;
  data?: EnhancedOrder;
  error?: string;
  message?: string;
}

export interface OrderListResponse {
  success: boolean;
  data?: {
    orders: EnhancedOrder[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
    statistics?: OrderStatistics;
  };
  error?: string;
  message?: string;
}
