{"site": {"name": "Khanfashariya Systems", "tagline": "Future Tech for Digital Solutions"}, "nav": {"home": "Home", "systems": "Systems", "services": "Services", "premium": "Premium", "customOrder": "Custom Order", "support": "Support", "contact": "Contact", "login": "<PERSON><PERSON>", "dashboard": "Dashboard", "logout": "Logout", "openMenu": "Open navigation menu", "closeMenu": "Close navigation menu", "mobileMenu": "Mobile navigation menu"}, "hero": {"title": "Khanfashariya Systems", "subtitle": "Crafting the future with exceptional technologies and advanced digital solutions that redefine the gaming world", "description": "Elite platform for exclusive systems and advanced technologies - where luxury meets innovation", "cta": "Explore the World of Excellence", "features": {"enhanced": "Exclusive Technologies", "advanced": "Advanced Innovation", "code": "Premium Quality Code"}}, "systems": {"title": "Technical Systems Arsenal", "subtitle": "Discover a comprehensive collection of systems developed with cutting-edge technology", "readMore": "System Details", "orderNow": "Order Now"}, "services": {"title": "Technical Services", "subtitle": "We provide comprehensive services for developing and enhancing Metin2 servers", "crashFix": "Bug fixes and crash resolution", "customization": "System code modification and customization according to requirements", "customSystems": "Custom technical system design", "installation": "Professional content and tool installation with speed and professionalism", "translation": "System translation to Arabic", "questDev": "Quest and interactive system development", "freebsd": "Optimized FreeBSD server setup", "scripts": "Protection and monitoring scripts", "sourceCode": "Enhanced and guaranteed source codes"}, "contact": {"title": "Contact Team", "subtitle": "We are here to enhance your server with powerful systems", "name": "Name", "email": "Email", "message": "Project Details", "send": "Send Request", "sending": "Sending...", "quickContact": "Quick Contact", "whatsapp": "WhatsApp", "discord": "Discord", "workingHours": "Working Hours", "workingHoursDesc": "We are available for technical support and consultations", "mondayToFriday": "Monday - Friday: 9:00 AM - 6:00 PM", "saturday": "Saturday: 10:00 AM - 4:00 PM", "sunday": "Sunday: Closed"}, "auth": {"createAccount": "Create New Account", "joinDescription": "Join <PERSON> and get the most powerful systems", "loginDescription": "Access your account to view your services", "fullName": "Full Name", "enterFullName": "Enter your full name", "username": "Username", "enterUsername": "Enter username", "enterEmail": "Enter your email", "password": "Password", "enterPassword": "Enter password", "createAccountButton": "Create Account", "loginButton": "<PERSON><PERSON>", "haveAccount": "Have an account? Login", "noAccount": "Don't have an account? Sign up", "quickTest": "Quick Test (<EMAIL>)"}, "admin": {"dashboard": {"title": "Admin Dashboard", "overview": "Overview", "systems": "Manage Technical Systems", "services": "Manage Technical Services", "orders": "Orders", "users": "User Management", "premium": "Premium Content", "settings": "System Settings", "backToSite": "Back to Site", "totalUsers": "Total Users", "activeOrders": "Active Orders", "pendingOrders": "Pending Orders", "totalRevenue": "Total Revenue", "technicalSystems": "Technical Systems", "totalSystems": "Total Systems", "activeSystems": "Active Systems", "technicalServices": "Technical Services", "totalServices": "Total Services", "activeServices": "Active Services", "recentOrders": "Recent Orders", "viewAll": "View All", "noOrders": "No orders yet", "addNewSystem": "Add New System", "addNewService": "Add New Service", "editSystem": "Edit System", "editService": "Edit Service", "systemName": "System Name", "serviceName": "Service Name", "description": "Description", "price": "Price", "category": "Category", "status": "Status", "features": "Features", "techSpecs": "Technical Specifications", "videoUrl": "Video URL", "imageUrl": "Image URL", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "active": "Active", "inactive": "Inactive", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "premiumIntegration": "Premium Integration Settings", "canAddToPremium": "Can be added to Premium Edition", "additionalPremiumPrice": "Additional Premium Price", "installationIncluded": "Installation Included", "maintenanceIncluded": "Maintenance Included", "premiumDescription": "Additional Premium Description", "premiumReady": "Premium Ready", "standalonePrice": "Standalone Price", "addToPremium": "Add to Premium", "preview": "Preview", "advancedSettings": "Advanced Settings", "export": "Export", "import": "Import", "priceRange": "Price Range", "sortBy": "Sort By", "allPrices": "All Prices", "allCategories": "All Categories", "allStatus": "All Status", "serviceType": "Service Type", "estimatedDuration": "Estimated Duration", "requirements": "Requirements", "serviceSettings": "Service Settings", "requiresClientFiles": "Requires Client Files", "requiresServerAccess": "Requires Server Access", "includesSupport": "Includes Technical Support", "supportDuration": "Support Duration", "emergencyService": "Emergency Service", "customPricing": "Custom Pricing", "requestService": "Request Service"}}, "footer": {"privacy": "Privacy Policy", "terms": "Terms & Conditions", "rights": "All Rights Reserved to Khanfashariya", "quickLinks": "Quick Links", "workingHours": "Working Hours", "workingHoursDesc": "We are available for technical support and consultations", "mondayToFriday": "Monday - Friday: 9:00 AM - 6:00 PM", "saturday": "Saturday: 10:00 AM - 4:00 PM", "sunday": "Sunday: Closed", "quickContact": "Quick Contact", "quickContactDesc": "Contact us directly for support", "madeWith": "Made with", "forDevelopers": "for developers"}, "systemNames": {"guildWar": "Automatic Guild War System", "duel": "Automatic Duel System", "contest": "Automatic Contest System", "wiki": "Technical Wiki System", "autoShop": "Automatic Shop System", "petLevel": "Advanced Companion System", "autoAttack": "Auto Attack System", "quickSwitch": "Quick Tool Switch System", "itemFilter": "Advanced Equipment Filter"}, "premium": {"title": "Premium Edition", "subtitle": "Premium edition with all advanced features", "description": "Get all systems and services with lifetime support", "features": "Features", "price": "Price", "originalPrice": "Original Price", "discount": "Discount", "exploreEdition": "Explore Premium Edition", "watchDemo": "Watch Demo", "includedSystems": "Included Systems", "includedServices": "Included Services", "supportIncluded": "Lifetime Technical Support", "updatesIncluded": "Free Updates", "installationIncluded": "Free Installation", "customizationIncluded": "Custom Modifications", "loading": "Loading...", "noDataAvailable": "No data available", "errorLoading": "Error loading data", "viewDetails": "View Details", "addToCart": "Add to Cart", "buyNow": "Buy Now", "learnMore": "Learn More", "premiumFeatures": "Premium Features", "exclusiveContent": "Exclusive Content", "prioritySupport": "Priority Support", "unlimitedAccess": "Unlimited Access", "advancedTools": "Advanced Tools", "customization": "Full Customization", "analytics": "Advanced Analytics", "security": "Maximum Security", "performance": "Optimized Performance", "integration": "Seamless Integration"}, "notifications": {"loginRequired": "Please login first to continue.", "profileError": "Error loading user profile.", "purchaseSuccess": "Purchase request sent successfully!", "purchaseError": "An error occurred during purchase.", "requestSuccess": "Your request has been sent successfully!", "requestError": "An error occurred while sending the request.", "messageSuccess": "Message sent successfully!", "messageError": "Error sending message. Please try again.", "accountCreateSuccess": "Account created successfully!", "logoutConfirm": "Are you sure you want to logout?", "orderStatusUpdateSuccess": "Order status updated successfully.", "orderStatusUpdateError": "Failed to update order status.", "roleUpdateSuccess": "User role updated successfully.", "roleUpdateError": "Failed to update user role.", "deleteServiceConfirm": "Are you sure you want to delete this service?", "deletePackageConfirm": "Are you sure you want to delete this package?", "deleteSystemConfirm": "Are you sure you want to delete this system?", "userAddSuccess": "User added successfully.", "fillAllFields": "Please fill all required fields.", "deleteSuccess": "Deleted successfully", "deleteFailed": "Failed to delete", "loadFailed": "Failed to load data", "updateSuccess": "Updated successfully", "createSuccess": "Created successfully", "saveFailed": "Failed to save", "loadError": "Error loading data", "saveSuccess": "Data saved successfully", "saveError": "Failed to save data", "deleteError": "Failed to delete data", "updateError": "Failed to update data", "createError": "Failed to create data", "exportSuccess": "Data exported successfully", "exportError": "Failed to export data", "importSuccess": "Data imported successfully", "importError": "Failed to import data", "settingsOpened": "Advanced settings opened", "serviceRequested": "Service requested successfully", "serviceRequestError": "Failed to request service", "systemOrdered": "System ordered successfully", "orderError": "Failed to place order"}, "systemSettings": {"title": "System Settings", "languageSettings": "Language Settings", "languageDescription": "Choose your preferred interface language", "switchToArabic": "Switch to Arabic", "componentTesting": "Component Testing", "componentTestingDescription": "Test all UI components and ensure they work correctly", "openTestPage": "Open Test Page", "notificationTesting": "Notification Testing", "systemInformation": "System Information", "systemVersion": "System Version:", "lastUpdate": "Last Update:", "systemStatus": "System Status:", "runningNormally": "Running Normally", "backToSettings": "Back to Settings", "buttonTests": "Button Tests", "variants": "Variants", "sizes": "Sizes", "states": "States", "primary": "Primary", "secondary": "Secondary", "outline": "Outline", "ghost": "Ghost", "danger": "Danger", "small": "Small", "medium": "Medium", "large": "Large", "testLoading": "Test Loading", "loading": "Loading...", "disabled": "Disabled", "inputTests": "Input Tests", "name": "Name", "enterName": "Enter your name", "email": "Email", "enterEmail": "Enter your email", "invalidEmail": "Invalid email", "password": "Password", "enterPassword": "Enter password", "search": "Search", "searchPlaceholder": "Search...", "withLoading": "With Loading", "loadingPlaceholder": "Loading...", "success": "Success", "validData": "Valid data", "dataIsValid": "Data is valid", "modalTests": "Modal Tests", "openModal": "Open Modal", "testModal": "Test Modal", "modalDescription": "This is a test modal. You can close it by clicking the close button or pressing Escape.", "ok": "OK", "error": "Error", "warning": "Warning", "info": "Info"}, "cta": {"readyToStart": "Ready to start your project?", "joinKhanfashariya": "<PERSON><PERSON>", "contactTeam": "Contact the team", "getStarted": "Get Started", "learnMore": "Learn More", "orderNow": "Order Now", "viewDetails": "View Details"}, "common": {"ok": "OK", "confirm": "Confirm", "cancel": "Cancel", "back": "Back", "close": "Close", "loading": "Loading...", "noData": "No data available", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "english": "English", "arabic": "عربي", "switchToEnglish": "Switch to English", "switchToArabic": "Switch to Arabic", "languageSettings": "Language Settings", "chooseLanguage": "Choose your preferred interface language", "readyToStart": "Ready to start the project?", "joinUs": "Join <PERSON> and get the most powerful technical systems", "loginError": "<PERSON><PERSON>", "registrationError": "Registration Error", "unexpectedError": "Unexpected Error", "loginSuccess": "Logged in successfully", "invalidCredentials": "Invalid email or password", "unexpectedErrorOccurred": "An unexpected error occurred"}}