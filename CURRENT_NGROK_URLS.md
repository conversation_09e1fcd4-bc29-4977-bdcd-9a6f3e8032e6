# 🔗 روابط Ngrok الحالية للمشروع

## 📅 تاريخ التحديث: 2025-07-22

---

## 🌐 الروابط الأساسية

### 🎨 الفرونت اند (Frontend)
- **الرابط الجديد**: `https://7bdecd66f690.ngrok-free.app`
- **المنفذ المحلي**: `http://localhost:5173`
- **الحالة**: ✅ جاهز للاستخدام

### 🔧 الباك اند (Backend API)
- **الرابط الحالي**: `https://72e29761aabe.ngrok-free.app`
- **المنفذ المحلي**: `http://localhost:3001`
- **الحالة**: ✅ يعمل بشكل صحيح

---

## 🧪 روابط TestSprite

### 📡 API Endpoints للاختبار:
1. **Health Check**: `https://72e29761aabe.ngrok-free.app/health`
2. **Admin Login**: `https://72e29761aabe.ngrok-free.app/api/auth/login`
3. **Systems**: `https://72e29761aabe.ngrok-free.app/api/systems`
4. **Technical Services**: `https://72e29761aabe.ngrok-free.app/api/services/technical`
5. **Premium Services**: `https://72e29761aabe.ngrok-free.app/api/services/premium`

### 🌐 Frontend Pages للاختبار:
1. **الصفحة الرئيسية**: `https://7bdecd66f690.ngrok-free.app/`
2. **تسجيل الدخول**: `https://7bdecd66f690.ngrok-free.app/login`
3. **التسجيل**: `https://7bdecd66f690.ngrok-free.app/register`
4. **الخدمات**: `https://7bdecd66f690.ngrok-free.app/services`
5. **الملف الشخصي**: `https://7bdecd66f690.ngrok-free.app/profile`
6. **لوحة الإدارة**: `https://7bdecd66f690.ngrok-free.app/admin`

---

## ⚙️ إعدادات TestSprite

```json
{
  "base_url": "https://72e29761aabe.ngrok-free.app",
  "headers": {
    "ngrok-skip-browser-warning": "true",
    "User-Agent": "TestSprite/1.0",
    "Content-Type": "application/json",
    "Accept": "application/json"
  }
}
```

---

## 🔐 بيانات الاختبار

### Admin Login:
- **Email**: `<EMAIL>`
- **Password**: `admin123`

### Test User:
- **Email**: `<EMAIL>`
- **Password**: `123456`

---

## 📋 حالة الخوادم

- ✅ **Backend Server (Port 3001)**: يعمل بشكل صحيح
- ✅ **Frontend Server (Port 5173)**: يعمل بشكل صحيح
- ✅ **MySQL Database**: متصل ويعمل
- ⚠️ **Ngrok Interface**: غير متاح (طبيعي عند عدم تشغيل ngrok محلياً)

---

## 🚀 كيفية البدء

1. **تأكد من تشغيل الخوادم**:
   ```bash
   npm run dev:full
   ```

2. **استخدم الروابط أعلاه مع TestSprite**

3. **للحصول على روابط جديدة**:
   ```bash
   npm run ngrok:stable
   ```

---

## 📝 ملاحظات مهمة

- الرابط الثابت للفرونت اند: `https://duly-enough-mayfly.ngrok-free.app`
- رابط الباك اند قد يتغير عند إعادة تشغيل ngrok
- جميع الروابط جاهزة للاستخدام مع TestSprite
- تأكد من تشغيل WAMP/XAMPP لقاعدة البيانات

---

## 🎯 جاهز للاختبار مع TestSprite! ✅
