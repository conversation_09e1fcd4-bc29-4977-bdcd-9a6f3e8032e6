import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuth } from '../hooks/useAuth';
import { X, CheckCircle, AlertTriangle, ArrowRight, Settings, Eye, Edit } from 'lucide-react';

interface AdminPanelTestProps {
  onClose: () => void;
}

/**
 * Admin Panel Test Guide - Interactive guide for testing admin features
 */
const AdminPanelTest: React.FC<AdminPanelTestProps> = ({ onClose }) => {
  const { language } = useTranslation();
  const { isAdmin } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  const markStepComplete = (stepIndex: number) => {
    if (!completedSteps.includes(stepIndex)) {
      setCompletedSteps(prev => [...prev, stepIndex]);
    }
    if (stepIndex < testSteps.length - 1) {
      setCurrentStep(stepIndex + 1);
    }
  };

  const testSteps = [
    {
      title: language === 'ar' ? 'الوصول للوحة التحكم' : 'Access Admin Dashboard',
      description: language === 'ar' 
        ? 'انقر على أيقونة الدرع في الشريط العلوي للوصول للوحة التحكم الإدارية'
        : 'Click the shield icon in the top navigation to access the admin dashboard',
      action: language === 'ar' ? 'انقر على أيقونة الدرع' : 'Click Shield Icon',
      icon: Settings,
      color: 'blue'
    },
    {
      title: language === 'ar' ? 'فتح إدارة الأنظمة التقنية' : 'Open Technical Systems Management',
      description: language === 'ar'
        ? 'في لوحة التحكم، انقر على "إدارة الأنظمة التقنية" في الشريط الجانبي'
        : 'In the admin dashboard, click "Technical Systems Management" in the sidebar',
      action: language === 'ar' ? 'انقر على إدارة الأنظمة' : 'Click Systems Management',
      icon: Settings,
      color: 'green'
    },
    {
      title: language === 'ar' ? 'اختبار خاصية المعاينة' : 'Test Preview Feature',
      description: language === 'ar'
        ? 'انقر على زر "معاينة" لأي نظام في القائمة. يجب أن تفتح نافذة معاينة تعرض تفاصيل النظام'
        : 'Click the "Preview" button for any system in the list. A preview modal should open showing system details',
      action: language === 'ar' ? 'انقر على معاينة' : 'Click Preview',
      icon: Eye,
      color: 'purple'
    },
    {
      title: language === 'ar' ? 'اختبار خاصية التعديل من المعاينة' : 'Test Edit from Preview',
      description: language === 'ar'
        ? 'في نافذة المعاينة، انقر على زر "تعديل". يجب أن تنتقل إلى نافذة التعديل'
        : 'In the preview modal, click the "Edit" button. It should transition to the edit modal',
      action: language === 'ar' ? 'انقر على تعديل' : 'Click Edit',
      icon: Edit,
      color: 'orange'
    },
    {
      title: language === 'ar' ? 'اختبار خاصية التعديل المباشر' : 'Test Direct Edit Feature',
      description: language === 'ar'
        ? 'أغلق النافذة وانقر على زر "تعديل" مباشرة لأي نظام. يجب أن تفتح نافذة التعديل'
        : 'Close the modal and click the "Edit" button directly for any system. The edit modal should open',
      action: language === 'ar' ? 'انقر على تعديل مباشر' : 'Click Direct Edit',
      icon: Edit,
      color: 'red'
    },
    {
      title: language === 'ar' ? 'اختبار حفظ التعديلات' : 'Test Save Changes',
      description: language === 'ar'
        ? 'في نافذة التعديل، قم بتغيير أي بيانات وانقر "حفظ". يجب أن تظهر رسالة نجاح'
        : 'In the edit modal, change any data and click "Save". A success message should appear',
      action: language === 'ar' ? 'احفظ التغييرات' : 'Save Changes',
      icon: CheckCircle,
      color: 'green'
    },
    {
      title: language === 'ar' ? 'اختبار إضافة نظام جديد' : 'Test Add New System',
      description: language === 'ar'
        ? 'انقر على "إضافة نظام جديد" واملأ البيانات ثم احفظ. يجب أن يظهر النظام الجديد في القائمة'
        : 'Click "Add New System", fill in the data and save. The new system should appear in the list',
      action: language === 'ar' ? 'أضف نظام جديد' : 'Add New System',
      icon: Settings,
      color: 'blue'
    }
  ];

  if (!isAdmin) {
    return (
      <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-primary rounded-xl max-w-md w-full border border-red-500/30 p-6">
          <div className="text-center">
            <AlertTriangle className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-white mb-2">
              {language === 'ar' ? 'غير مصرح' : 'Access Denied'}
            </h3>
            <p className="text-gray-300 mb-4">
              {language === 'ar' 
                ? 'يجب تسجيل الدخول كمدير لاستخدام هذا الاختبار'
                : 'You must be logged in as admin to use this test'
              }
            </p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-red-500/20 text-red-400 border border-red-500/30 rounded-lg hover:bg-red-500/30 transition-colors"
            >
              {language === 'ar' ? 'إغلاق' : 'Close'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 overflow-y-auto">
      <div className="min-h-screen py-8">
        <div className="max-w-4xl mx-auto bg-primary rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-white">
                {language === 'ar' ? 'دليل اختبار لوحة التحكم' : 'Admin Panel Test Guide'}
              </h2>
              <button
                onClick={onClose}
                className="w-10 h-10 bg-white/20 hover:bg-white/40 rounded-full flex items-center justify-center transition-colors"
              >
                <X className="w-6 h-6 text-white" />
              </button>
            </div>
          </div>

          <div className="p-6">
            {/* Progress Bar */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-300">
                  {language === 'ar' ? 'التقدم' : 'Progress'}
                </span>
                <span className="text-sm font-medium text-gray-300">
                  {completedSteps.length}/{testSteps.length}
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(completedSteps.length / testSteps.length) * 100}%` }}
                />
              </div>
            </div>

            {/* Test Steps */}
            <div className="space-y-4">
              {testSteps.map((step, index) => {
                const IconComponent = step.icon;
                const isCompleted = completedSteps.includes(index);
                const isCurrent = currentStep === index;
                const colorClasses = {
                  blue: 'border-blue-500/30 bg-blue-500/10 text-blue-400',
                  green: 'border-green-500/30 bg-green-500/10 text-green-400',
                  purple: 'border-purple-500/30 bg-purple-500/10 text-purple-400',
                  orange: 'border-orange-500/30 bg-orange-500/10 text-orange-400',
                  red: 'border-red-500/30 bg-red-500/10 text-red-400'
                };

                return (
                  <div
                    key={index}
                    className={`border rounded-lg p-4 transition-all duration-300 ${
                      isCompleted 
                        ? 'border-green-500/50 bg-green-500/10' 
                        : isCurrent 
                          ? `${colorClasses[step.color as keyof typeof colorClasses]} ring-2 ring-opacity-50` 
                          : 'border-gray-600 bg-gray-800/50'
                    }`}
                  >
                    <div className="flex items-start space-x-4 rtl:space-x-reverse">
                      {/* Step Number/Icon */}
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 ${
                        isCompleted 
                          ? 'bg-green-500 text-white' 
                          : isCurrent 
                            ? `bg-${step.color}-500 text-white` 
                            : 'bg-gray-600 text-gray-300'
                      }`}>
                        {isCompleted ? (
                          <CheckCircle className="w-6 h-6" />
                        ) : (
                          <IconComponent className="w-6 h-6" />
                        )}
                      </div>

                      {/* Step Content */}
                      <div className="flex-1">
                        <h3 className={`text-lg font-semibold mb-2 ${
                          isCompleted ? 'text-green-400' : isCurrent ? 'text-white' : 'text-gray-400'
                        }`}>
                          {step.title}
                        </h3>
                        <p className={`text-sm mb-3 ${
                          isCompleted ? 'text-green-300' : isCurrent ? 'text-gray-300' : 'text-gray-500'
                        }`}>
                          {step.description}
                        </p>
                        
                        {/* Action Button */}
                        {isCurrent && !isCompleted && (
                          <button
                            onClick={() => markStepComplete(index)}
                            className={`inline-flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 rounded-lg transition-colors ${colorClasses[step.color as keyof typeof colorClasses]} hover:opacity-80`}
                          >
                            <span>{step.action}</span>
                            <ArrowRight className="w-4 h-4" />
                          </button>
                        )}

                        {isCompleted && (
                          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse text-green-400">
                            <CheckCircle className="w-4 h-4" />
                            <span className="text-sm font-medium">
                              {language === 'ar' ? 'مكتمل' : 'Completed'}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Completion Message */}
            {completedSteps.length === testSteps.length && (
              <div className="mt-8 p-6 bg-gradient-to-r from-green-500/20 to-blue-500/20 border border-green-500/30 rounded-lg text-center">
                <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">
                  {language === 'ar' ? 'تم الاختبار بنجاح!' : 'Test Completed Successfully!'}
                </h3>
                <p className="text-gray-300 mb-4">
                  {language === 'ar' 
                    ? 'تم اختبار جميع خصائص لوحة التحكم بنجاح. جميع الوظائف تعمل كما هو مطلوب!'
                    : 'All admin panel features have been tested successfully. All functions are working as expected!'
                  }
                </p>
                <button
                  onClick={onClose}
                  className="px-6 py-2 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-lg hover:from-green-600 hover:to-blue-600 transition-colors"
                >
                  {language === 'ar' ? 'إنهاء الاختبار' : 'Finish Test'}
                </button>
              </div>
            )}

            {/* Instructions */}
            <div className="mt-8 p-4 bg-gray-800/50 border border-gray-600 rounded-lg">
              <h4 className="text-lg font-semibold text-white mb-2">
                {language === 'ar' ? 'تعليمات' : 'Instructions'}
              </h4>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• {language === 'ar' ? 'اتبع الخطوات بالترتيب المحدد' : 'Follow the steps in the specified order'}</li>
                <li>• {language === 'ar' ? 'انقر على "تم" بعد إكمال كل خطوة' : 'Click "Done" after completing each step'}</li>
                <li>• {language === 'ar' ? 'تأكد من أن كل خاصية تعمل كما هو متوقع' : 'Make sure each feature works as expected'}</li>
                <li>• {language === 'ar' ? 'إذا واجهت مشكلة، أعد المحاولة أو اتصل بالدعم' : 'If you encounter issues, retry or contact support'}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminPanelTest;
