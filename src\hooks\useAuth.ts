import { useState, useEffect } from 'react';
import { getCurrentUser, signOut, getUserProfile } from '../lib/apiServices';
import { User } from '../lib/database';

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Get current user from localStorage (for auth tokens)
        const currentUser = getCurrentUser();
        if (currentUser) {
          setUser(currentUser);
          setUserProfile(currentUser);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Listen for storage changes (for multi-tab support)
    const handleStorageChange = (e: StorageEvent) => {
      // Only handle changes to our auth tokens
      if (e.key === 'khanfashariya_current_user' || e.key === 'khanfashariya_access_token' || e.key === null) {
        const updatedUser = getCurrentUser();
        setUser(updatedUser);
        setUserProfile(updatedUser);
      }
    };

    // Also listen for custom logout events
    const handleLogoutEvent = () => {
      setUser(null);
      setUserProfile(null);
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('khanfashariya_logout', handleLogoutEvent);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('khanfashariya_logout', handleLogoutEvent);
    };
  }, []);

  const isAdmin = userProfile?.role === 'admin';

  const logout = async () => {
    try {
      // Call the API signOut function
      await signOut();
    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      // Clear local state regardless of API call result
      setUser(null);
      setUserProfile(null);

      // Dispatch custom event for other components/tabs
      window.dispatchEvent(new CustomEvent('khanfashariya_logout'));
    }
  };

  return {
    user,
    userProfile,
    loading,
    isAdmin,
    isAuthenticated: !!user,
    logout
  };
};