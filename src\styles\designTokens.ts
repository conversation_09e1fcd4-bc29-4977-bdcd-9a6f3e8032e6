/**
 * Design Tokens for Khanfashariya Design System
 * 
 * This file contains all design tokens including colors, spacing, typography,
 * and other design-related constants used throughout the application.
 * 
 * Features:
 * - Comprehensive color palette with semantic naming
 * - Responsive spacing scale
 * - Typography system with Arabic and English fonts
 * - Animation and transition tokens
 * - Breakpoint definitions
 */

// Color palette with semantic naming
export const colors = {
  // Primary colors - Main brand colors
  primary: {
    50: '#f8f7fc',
    100: '#f1effa',
    200: '#e3dff5',
    300: '#d5cfef',
    400: '#c7bfea',
    500: '#1e1a2b', // Main primary color
    600: '#1a1726',
    700: '#161421',
    800: '#12111c',
    900: '#0a0810'
  },

  // Secondary colors - Accent and highlights
  secondary: {
    50: '#fefdf8',
    100: '#fdf9e8',
    200: '#faf2c7',
    300: '#f6e89f',
    400: '#f1dc6b',
    500: '#D4AF37', // Main secondary color - Luxurious Gold
    600: '#B8941F',
    700: '#9A7A15',
    800: '#7D610F',
    900: '#5C4709'
  },

  // Accent colors - Interactive elements
  accent: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#21c0ff', // Main accent color
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e'
  },

  // Background colors - Enhanced dark theme
  background: {
    primary: '#0F172A',      // Main dark background
    secondary: '#1E293B',    // Secondary dark background
    tertiary: '#334155',     // Tertiary background
    card: '#1E293B',         // Card background
    hover: '#334155',        // Hover state background
    active: '#475569',       // Active state background
    border: '#475569',       // Border color
    input: '#1E293B',        // Input background
    disabled: '#64748B'      // Disabled state
  },

  // Semantic colors
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    500: '#22c55e',
    600: '#16a34a',
    900: '#14532d'
  },

  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    500: '#f59e0b',
    600: '#d97706',
    900: '#92400e'
  },

  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    500: '#ef4444',
    600: '#dc2626',
    900: '#7f1d1d'
  },

  info: {
    50: '#eff6ff',
    100: '#dbeafe',
    500: '#3b82f6',
    600: '#2563eb',
    900: '#1e3a8a'
  },

  // Neutral colors
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827'
  },

  // Text colors - Enhanced for better contrast
  text: {
    primary: '#F8FAFC',      // Improved contrast for primary text
    secondary: '#CBD5E1',    // Better visibility for secondary text
    tertiary: '#94A3B8',     // Clearer muted text
    inverse: '#0F172A',      // Dark text for light backgrounds
    accent: '#21c0ff',       // Accent text color
    success: '#22c55e',      // Success text
    warning: '#f59e0b',      // Warning text
    error: '#ef4444',        // Error text
    info: '#3b82f6'          // Info text
  }
};

// Spacing scale based on 4px grid
export const spacing = {
  0: '0',
  px: '1px',
  0.5: '0.125rem', // 2px
  1: '0.25rem',    // 4px
  1.5: '0.375rem', // 6px
  2: '0.5rem',     // 8px
  2.5: '0.625rem', // 10px
  3: '0.75rem',    // 12px
  3.5: '0.875rem', // 14px
  4: '1rem',       // 16px
  5: '1.25rem',    // 20px
  6: '1.5rem',     // 24px
  7: '1.75rem',    // 28px
  8: '2rem',       // 32px
  9: '2.25rem',    // 36px
  10: '2.5rem',    // 40px
  11: '2.75rem',   // 44px
  12: '3rem',      // 48px
  14: '3.5rem',    // 56px
  16: '4rem',      // 64px
  20: '5rem',      // 80px
  24: '6rem',      // 96px
  28: '7rem',      // 112px
  32: '8rem',      // 128px
  36: '9rem',      // 144px
  40: '10rem',     // 160px
  44: '11rem',     // 176px
  48: '12rem',     // 192px
  52: '13rem',     // 208px
  56: '14rem',     // 224px
  60: '15rem',     // 240px
  64: '16rem',     // 256px
  72: '18rem',     // 288px
  80: '20rem',     // 320px
  96: '24rem'      // 384px
};

// Typography system
export const typography = {
  fontFamily: {
    arabic: ['Noto Sans Arabic', 'sans-serif'],
    english: ['Inter', 'sans-serif'],
    mono: ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace']
  },

  fontSize: {
    xs: ['0.75rem', { lineHeight: '1rem' }],      // 12px
    sm: ['0.875rem', { lineHeight: '1.25rem' }],  // 14px
    base: ['1rem', { lineHeight: '1.5rem' }],     // 16px
    lg: ['1.125rem', { lineHeight: '1.75rem' }],  // 18px
    xl: ['1.25rem', { lineHeight: '1.75rem' }],   // 20px
    '2xl': ['1.5rem', { lineHeight: '2rem' }],    // 24px
    '3xl': ['1.875rem', { lineHeight: '2.25rem' }], // 30px
    '4xl': ['2.25rem', { lineHeight: '2.5rem' }], // 36px
    '5xl': ['3rem', { lineHeight: '1' }],         // 48px
    '6xl': ['3.75rem', { lineHeight: '1' }],      // 60px
    '7xl': ['4.5rem', { lineHeight: '1' }],       // 72px
    '8xl': ['6rem', { lineHeight: '1' }],         // 96px
    '9xl': ['8rem', { lineHeight: '1' }]          // 128px
  },

  fontWeight: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900'
  },

  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0em',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em'
  },

  lineHeight: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2'
  }
};

// Border radius scale
export const borderRadius = {
  none: '0',
  sm: '0.125rem',   // 2px
  base: '0.25rem',  // 4px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  '3xl': '1.5rem',  // 24px
  full: '9999px'
};

// Shadow system
export const boxShadow = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  base: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
  glow: {
    primary: '0 0 20px rgba(30, 26, 43, 0.3)',
    secondary: '0 0 20px rgba(255, 178, 0, 0.3)',
    accent: '0 0 20px rgba(33, 192, 255, 0.3)'
  }
};

// Animation and transition tokens
export const animation = {
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
    slower: '750ms'
  },

  easing: {
    linear: 'linear',
    in: 'cubic-bezier(0.4, 0, 1, 1)',
    out: 'cubic-bezier(0, 0, 0.2, 1)',
    inOut: 'cubic-bezier(0.4, 0, 0.2, 1)'
  }
};

// Breakpoints for responsive design
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
};

// Z-index scale
export const zIndex = {
  hide: -1,
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800
};

// Component-specific tokens
export const components = {
  button: {
    height: {
      sm: spacing[10],  // 40px
      md: spacing[11],  // 44px
      lg: spacing[12]   // 48px
    },
    padding: {
      sm: `${spacing[2]} ${spacing[4]}`,
      md: `${spacing[3]} ${spacing[6]}`,
      lg: `${spacing[4]} ${spacing[8]}`
    }
  },

  input: {
    height: {
      sm: spacing[10],  // 40px
      md: spacing[11],  // 44px
      lg: spacing[12]   // 48px
    }
  },

  card: {
    padding: {
      sm: spacing[4],
      md: spacing[6],
      lg: spacing[8]
    }
  }
};

// Export all tokens as a single object
export const designTokens = {
  colors,
  spacing,
  typography,
  borderRadius,
  boxShadow,
  animation,
  breakpoints,
  zIndex,
  components
};

export default designTokens;
