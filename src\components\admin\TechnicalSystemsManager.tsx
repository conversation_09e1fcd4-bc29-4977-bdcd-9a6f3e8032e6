import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotification } from '../../hooks/useNotification';
import { useActivityLogger } from '../../hooks/useActivityLogger';
import {
  getSystemServices,
  getAdminSystemServices,
  createSystemService,
  updateSystemService,
  deleteSystemService
} from '../../lib/apiServices';
import { SystemService, TranslatedText } from '../../lib/database';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Filter,
  Download,
  Upload,
  Star,
  Package,
  Settings,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  Image as ImageIcon,
  Video,
  Save,
  X,
  ArrowLeft,
  FileText
} from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Modal from '../ui/Modal';
import Card from '../ui/Card';
import Pagination from '../ui/Pagination';
import GoldenFilterGrid from '../ui/GoldenFilterGrid';
import Tooltip from '../ui/Tooltip';
import GoldenButton from '../ui/GoldenButton';
import BackButton from '../ui/BackButton';
import { useButtonActions } from '../../utils/buttonActions';
import { TECHNICAL_SYSTEMS_FILTERS } from '../../constants/filterOptions';

// YouTube URL parsing utility
const parseYouTubeURL = (url: string): string | null => {
  if (!url) return null;

  // Handle different YouTube URL formats
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
    /youtube\.com\/watch\?.*v=([^&\n?#]+)/
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return `https://www.youtube.com/embed/${match[1]}`;
    }
  }

  return null;
};

interface TechnicalSystemsManagerProps {
  onBack?: () => void;
}

/**
 * Enhanced Technical Systems Manager with advanced features
 */
const TechnicalSystemsManager: React.FC<TechnicalSystemsManagerProps> = ({ onBack }) => {
  const { t, language } = useTranslation();
  const { showNotification } = useNotification();
  const buttonActions = useButtonActions();
  const { logSystemCreate, logSystemUpdate, logSystemDelete } = useActivityLogger();
  
  const [systems, setSystems] = useState<SystemService[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewSystem, setPreviewSystem] = useState<SystemService | null>(null);
  const [newImageUrl, setNewImageUrl] = useState('');
  const [editingSystem, setEditingSystem] = useState<SystemService | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(6);

  // Unified filter state
  const [filterValues, setFilterValues] = useState<Record<string, string>>({
    status: 'all',
    category: 'all',
    priceRange: 'all',
    sortBy: 'name',
    premium: 'all'
  });

  // Form state
  const [formData, setFormData] = useState<Partial<SystemService>>({
    name: { ar: '', en: '' },
    description: { ar: '', en: '' },
    features: { ar: [], en: [] },
    tech_specs: { ar: [], en: [] },
    price: 0,
    category: 'general',
    type: 'regular', // New field for system type
    isPremiumAddon: false, // Use TypeScript interface field name
    video_url: '',
    image_url: '',
    gallery_images: [],
    status: 'active'
  });

  // Premium integration state
  const [showPremiumIntegration, setShowPremiumIntegration] = useState(false);
  const [premiumIntegrationData, setPremiumIntegrationData] = useState({
    canAddToPremium: true,
    premiumPrice: 0,
    premiumDescription: { ar: '', en: '' },
    installationIncluded: false,
    maintenanceIncluded: false
  });

  useEffect(() => {
    loadSystems();
  }, []);

  // Helper function to safely get text in current language
  const getSystemText = (system: any, field: string, lang?: string): string => {
    if (!system) return '';
    const currentLang = lang || language;

    try {
      // Try new format first (nested object)
      if (system[field] && typeof system[field] === 'object' && system[field][currentLang]) {
        return system[field][currentLang] || '';
      }

      // Try old format (separate fields)
      const fieldKey = `${field}_${currentLang}`;
      if (system[fieldKey]) {
        return system[fieldKey] || '';
      }

      // Fallback to any available language
      if (system[field] && typeof system[field] === 'object') {
        return system[field]['ar'] || system[field]['en'] || '';
      }

      return '';
    } catch (error) {
      console.warn(`Error getting system text for field ${field}:`, error);
      return '';
    }
  };

  // Helper function to safely get array in current language
  const getSystemArray = (system: any, field: string): string[] => {
    if (!system) return [];

    try {
      // Try new format first (nested object)
      if (system[field] && typeof system[field] === 'object' && Array.isArray(system[field][language])) {
        return system[field][language];
      }

      // Try old format (separate fields)
      const fieldKey = `${field}_${language}`;
      if (Array.isArray(system[fieldKey])) {
        return system[fieldKey];
      }

      // Fallback to any available language
      if (system[field] && typeof system[field] === 'object') {
        return system[field]['ar'] || system[field]['en'] || [];
      }

      return [];
    } catch (error) {
      console.warn(`Error getting system array for field ${field}:`, error);
      return [];
    }
  };

  // Helper function to safely normalize system data
  const normalizeSystemData = (system: any): SystemService => {
    // Parse JSON fields if they are strings
    const parseJsonField = (field: any) => {
      if (typeof field === 'string') {
        try {
          return JSON.parse(field);
        } catch {
          return [];
        }
      }
      return Array.isArray(field) ? field : [];
    };

    return {
      ...system,
      id: system.id,
      name: system.name || { ar: system.name_ar || '', en: system.name_en || '' },
      description: system.description || { ar: system.description_ar || '', en: system.description_en || '' },
      features: system.features || {
        ar: parseJsonField(system.features_ar),
        en: parseJsonField(system.features_en)
      },
      tech_specs: system.tech_specs || {
        ar: parseJsonField(system.tech_specs_ar),
        en: parseJsonField(system.tech_specs_en)
      },
      price: parseFloat(system.price) || 0,
      category: system.category || 'general',
      type: system.type || 'regular',
      status: system.status || 'active',
      video_url: system.video_url || '',
      image_url: system.image_url || '',
      gallery_images: parseJsonField(system.gallery_images),
      isPremiumAddon: Boolean(system.isPremiumAddon || system.is_premium_addon),
      created_at: system.created_at || new Date().toISOString(),
      updated_at: system.updated_at || new Date().toISOString()
    };
  };

  const loadSystems = async () => {
    // Check authentication first
    const token = localStorage.getItem('khanfashariya_access_token');
    if (!token) {
      console.warn('No authentication token found');
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const result = await getAdminSystemServices();

      if (result.data && Array.isArray(result.data)) {
        // Normalize all system data to ensure consistent format
        const normalizedSystems = result.data.map(normalizeSystemData);
        setSystems(normalizedSystems);
      } else {
        setSystems([]);
        if (result.error) {
          showNotification({
            type: 'error',
            message: result.error.message || t('notifications.loadError')
          });
        }
      }
    } catch (error) {
      console.error('Error loading systems:', error);
      showNotification({
        type: 'error',
        message: t('notifications.loadError')
      });
      setSystems([]);
    }
    setLoading(false);
  };

  const handleCreate = () => {
    setEditingSystem(null);
    setFormData({
      name: { ar: '', en: '' },
      description: { ar: '', en: '' },
      features: { ar: [], en: [] },
      tech_specs: { ar: [], en: [] },
      price: 0,
      category: 'general',
      type: 'regular', // Default to regular system type
      video_url: '',
      image_url: '',
      gallery_images: [],
      status: 'active'
    });
    setShowModal(true);
  };

  const handleEdit = (system: SystemService) => {
    setEditingSystem(system);
    
    // Safely normalize the system data for editing
    const normalizedSystem = normalizeSystemData(system);
    
    setFormData({
      ...normalizedSystem,
      gallery_images: normalizedSystem.gallery_images || []
    });
    
    // Load premium integration data
    setPremiumIntegrationData({
      canAddToPremium: system.isPremiumAddon || false,
      premiumPrice: 0,
      premiumDescription: { ar: '', en: '' },
      installationIncluded: false,
      maintenanceIncluded: false
    });
    setShowModal(true);
  };

  const handlePreview = (system: SystemService) => {
    setPreviewSystem(system);
    setShowPreviewModal(true);
  };

  const handleDelete = (system: SystemService) => {
    showNotification({
      type: 'confirm',
      message: t('notifications.deleteSystemConfirm'),
      onConfirm: async () => {
        try {
          const result = await deleteSystemService(system.id);
          if (!result.error) {
            // Log activity
            logSystemDelete(system.id, getSystemText(system, 'name', language));

            loadSystems();
            showNotification({
              type: 'success',
              message: language === 'ar' ? 'تم حذف النظام بنجاح' : 'System deleted successfully'
            });
          } else {
            throw new Error(result.error?.message || 'Delete failed');
          }
        } catch (error: any) {
          console.error('Error deleting system:', error);
          showNotification({
            type: 'error',
            message: language === 'ar' ? `فشل في حذف النظام: ${error.message}` : `Failed to delete system: ${error.message}`
          });
        }
      }
    });
  };

  const handleSave = async () => {
    try {
      // Validate required fields with safe access
      const nameAr = formData.name?.ar || '';
      const nameEn = formData.name?.en || '';
      const descAr = formData.description?.ar || '';
      const descEn = formData.description?.en || '';
      const price = formData.price || 0;

      if (!nameAr || !nameEn || !descAr || !descEn || price <= 0) {
        showNotification({
          type: 'error',
          message: language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields'
        });
        return;
      }

      let result;
      if (editingSystem) {
        // Prepare update data in the format expected by the server
        const updateData = {
          name_ar: formData.name?.ar || '',
          name_en: formData.name?.en || '',
          description_ar: formData.description?.ar || '',
          description_en: formData.description?.en || '',
          features_ar: formData.features?.ar || [],
          features_en: formData.features?.en || [],
          tech_specs_ar: formData.tech_specs?.ar || [],
          tech_specs_en: formData.tech_specs?.en || [],
          price: formData.price || 0,
          category: formData.category || 'general',
          type: formData.type || 'regular',
          is_premium_addon: formData.isPremiumAddon || false,
          status: formData.status || 'active',
          video_url: formData.video_url || '',
          image_url: formData.image_url || '',
          gallery_images: formData.gallery_images || []
        };

        result = await updateSystemService(editingSystem.id, updateData);
        if (result.data) {
          // Log activity
          logSystemUpdate(editingSystem.id, language === 'ar' ? nameAr : nameEn);

          showNotification({
            type: 'success',
            message: language === 'ar' ? 'تم تحديث النظام بنجاح' : 'System updated successfully'
          });
        } else {
          throw new Error(result.error?.message || 'Update failed');
        }
      } else {
        // Prepare create data in the format expected by the server
        const createData = {
          name_ar: nameAr,
          name_en: nameEn,
          description_ar: descAr,
          description_en: descEn,
          features_ar: formData.features?.ar || [],
          features_en: formData.features?.en || [],
          tech_specs_ar: formData.tech_specs?.ar || [],
          tech_specs_en: formData.tech_specs?.en || [],
          price: price,
          category: formData.category || 'general',
          type: formData.type || 'regular',
          is_premium_addon: formData.isPremiumAddon || false,
          status: formData.status || 'active',
          video_url: formData.video_url || '',
          image_url: formData.image_url || '',
          gallery_images: formData.gallery_images || []
        };

        result = await createSystemService(createData);
        if (result.data) {
          // Log activity
          logSystemCreate(result.data.id, language === 'ar' ? nameAr : nameEn);

          showNotification({
            type: 'success',
            message: language === 'ar' ? 'تم إنشاء النظام بنجاح' : 'System created successfully'
          });
        } else {
          throw new Error(result.error?.message || 'Create failed');
        }
      }
      setShowModal(false);
      setEditingSystem(null);
      setNewImageUrl('');
      loadSystems();
    } catch (error: any) {
      console.error('Error saving system:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? `فشل في حفظ النظام: ${error.message}` : `Failed to save system: ${error.message}`
      });
    }
  };

  const handleTextChange = (lang: 'ar' | 'en', field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: {
        ...(prev[field as keyof typeof prev] as TranslatedText || { ar: '', en: '' }),
        [lang]: value
      }
    }));
  };

  const handleArrayChange = (lang: 'ar' | 'en', field: 'features' | 'tech_specs', value: string) => {
    const items = value.split('\n').filter(item => item.trim());
    setFormData(prev => ({
      ...prev,
      [field]: {
        ...(prev[field] as { ar: string[]; en: string[] } || { ar: [], en: [] }),
        [lang]: items
      }
    }));
  };

  // Handle filter changes
  const handleFilterChange = (key: string, value: string) => {
    setFilterValues(prev => ({ ...prev, [key]: value }));
  };

  const filteredSystems = (Array.isArray(systems) ? systems : []).filter(system => {
    // Use helper functions for safe access
    const features = getSystemArray(system, 'features');
    const name = getSystemText(system, 'name', language);
    const description = getSystemText(system, 'description', language);

    const matchesSearch = searchTerm === '' ||
      name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      features.some(feature =>
        (feature || '').toLowerCase().includes(searchTerm.toLowerCase())
      );

    const matchesStatus = filterValues.status === 'all' || system.status === filterValues.status;
    const matchesCategory = filterValues.category === 'all' || system.category === filterValues.category;

    // Price range filter
    const matchesPriceRange = filterValues.priceRange === 'all' || (() => {
      const price = system.price || 0;
      switch (filterValues.priceRange) {
        case '0-50': return price >= 0 && price <= 50;
        case '50-100': return price > 50 && price <= 100;
        case '100-200': return price > 100 && price <= 200;
        case '200-500': return price > 200 && price <= 500;
        case '500+': return price > 500;
        default: return true;
      }
    })();

    return matchesSearch && matchesStatus && matchesCategory && matchesPriceRange;
  });



  // Sort filtered systems
  const sortedSystems = [...filteredSystems].sort((a, b) => {
    switch (filterValues.sortBy) {
      case 'price':
        return (a.price || 0) - (b.price || 0);
      case 'category':
        return (a.category || '').localeCompare(b.category || '');
      case 'created':
        return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
      case 'updated':
        return new Date(b.updated_at || '').getTime() - new Date(a.updated_at || '').getTime();
      case 'name':
      default:
        const nameA = getSystemText(a, 'name', language);
        const nameB = getSystemText(b, 'name', language);
        return nameA.localeCompare(nameB);
    }
  });

  // Pagination logic
  const totalPages = Math.ceil(sortedSystems.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedSystems = sortedSystems.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterValues]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'inactive': return <XCircle className="w-4 h-4 text-red-400" />;
      default: return <Clock className="w-4 h-4 text-yellow-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'inactive': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
    }
  };

  const categories = ['general', 'combat', 'economy', 'social', 'utility', 'admin'];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-secondary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="p-6">


      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          {onBack && (
            <BackButton onClick={onBack} variant="back" size="md" />
          )}
          <h1 className="text-2xl font-bold text-white">
            {t('admin.dashboard.systems')}
          </h1>
        </div>
        
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Button variant="outline" onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
            <Package className="w-4 h-4 mr-2" />
            {viewMode === 'grid' ? 'List' : 'Grid'}
          </Button>
          <Button variant="primary" onClick={handleCreate}>
            <Plus className="w-4 h-4 mr-2" />
            {t('admin.dashboard.addNewSystem')}
          </Button>
        </div>
      </div>

      {/* Enhanced Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <Card className="bg-gradient-to-br from-blue-500/10 to-blue-600/5 border-blue-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-300 text-sm font-medium">{t('admin.dashboard.totalSystems')}</p>
                <p className="text-2xl font-bold text-white">{systems.length}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'إجمالي الأنظمة' : 'Total Systems'}
                </p>
              </div>
              <Package className="w-8 h-8 text-blue-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/10 to-green-600/5 border-green-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-300 text-sm font-medium">{t('admin.dashboard.activeSystems')}</p>
                <p className="text-2xl font-bold text-white">{(Array.isArray(systems) ? systems : []).filter(s => s.status === 'active').length}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'نشطة حالياً' : 'Currently Active'}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-purple-500/10 to-purple-600/5 border-purple-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-300 text-sm font-medium">{language === 'ar' ? 'متوسط السعر' : 'Average Price'}</p>
                <p className="text-2xl font-bold text-white">
                  ${(() => {
                    if (!Array.isArray(systems) || systems.length === 0) return 0;
                    const validPrices = systems.filter(s => s && typeof s.price === 'number' && !isNaN(s.price));
                    if (validPrices.length === 0) return 0;
                    const average = validPrices.reduce((sum, s) => sum + s.price, 0) / validPrices.length;
                    return Math.round(average);
                  })()}
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'للنظام الواحد' : 'Per System'}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-purple-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-accent/10 to-accent/5 border-accent/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-accent text-sm font-medium">{language === 'ar' ? 'الفئات' : 'Categories'}</p>
                <p className="text-2xl font-bold text-white">{new Set((Array.isArray(systems) ? systems : []).map(s => s.category)).size}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'فئات مختلفة' : 'Different Categories'}
                </p>
              </div>
              <Filter className="w-8 h-8 text-accent" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-pink-500/10 to-pink-600/5 border-pink-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-pink-300 text-sm font-medium">{language === 'ar' ? 'متاح للمميزة' : 'Premium Ready'}</p>
                <p className="text-2xl font-bold text-white">{systems.length}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'يمكن إضافتها' : 'Can be Added'}
                </p>
              </div>
              <Star className="w-8 h-8 text-pink-400" />
            </div>
          </Card.Body>
        </Card>
      </div>

      {/* Enhanced Filters and Search - Repositioned Below Statistics */}
      <div className="mb-6">
        <GoldenFilterGrid
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          searchPlaceholder="Search systems (name, description, features)..."
          searchPlaceholderAr="البحث في الأنظمة (الاسم، الوصف، الميزات)..."
          filters={TECHNICAL_SYSTEMS_FILTERS}
          filterValues={filterValues}
          onFilterChange={handleFilterChange}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          resultCount={sortedSystems.length}
          onExport={() => buttonActions.exportData('systems', sortedSystems)}
          onImport={() => buttonActions.importData('systems')}
          onAdvancedSettings={() => buttonActions.openAdvancedSettings('technical-systems')}
          compact={true}
          position="horizontal"
          className="enhanced-technical-systems-filter-repositioned"
        />
      </div>

      {/* Authentication Check */}
      {!localStorage.getItem('khanfashariya_access_token') && (
        <Card className="mb-6">
          <Card.Body>
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                {language === 'ar' ? 'مطلوب تسجيل الدخول' : 'Authentication Required'}
              </h3>
              <p className="text-gray-400 mb-6">
                {language === 'ar' ? 'يجب تسجيل الدخول للوصول إلى إدارة الأنظمة التقنية' : 'Please log in to access technical systems management'}
              </p>
              <Button variant="primary" onClick={() => window.location.href = '/admin'}>
                {language === 'ar' ? 'تسجيل الدخول' : 'Login'}
              </Button>
            </div>
          </Card.Body>
        </Card>
      )}

      {/* Systems Grid/List */}
      {filteredSystems.length === 0 ? (
        <Card>
          <Card.Body>
            <div className="text-center py-12">
              <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                {language === 'ar' ? 'لا توجد أنظمة' : 'No Systems Found'}
              </h3>
              <p className="text-gray-400 mb-6">
                {language === 'ar' ? 'ابدأ بإنشاء نظام جديد' : 'Start by creating a new system'}
              </p>
              <Button variant="primary" onClick={handleCreate}>
                <Plus className="w-4 h-4 mr-2" />
                {t('admin.dashboard.addNewSystem')}
              </Button>
            </div>
          </Card.Body>
        </Card>
      ) : (
        <>
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
            {paginatedSystems.map((system) => (
            <Card key={system.id} className="hover:border-secondary/50 hover:shadow-lg hover:shadow-secondary/20 transition-all duration-300 group">
              <Card.Body>
                {viewMode === 'grid' ? (
                  // Grid View
                  <div className="space-y-4">
                    {/* Image */}
                    <div className="relative h-32 bg-gradient-to-br from-primary to-background rounded-lg overflow-hidden group-hover:scale-105 transition-transform duration-300">
                      {system.image_url ? (
                        <img
                          src={system.image_url}
                          alt={getSystemText(system, 'name', language)}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <ImageIcon className="w-8 h-8 text-gray-400" />
                        </div>
                      )}
                      <div className="absolute top-2 right-2 z-10">
                        <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(system.status)} flex items-center shadow-lg backdrop-blur-sm`}>
                          {getStatusIcon(system.status)}
                          <span className="ml-1 rtl:ml-0 rtl:mr-1">{t(`admin.dashboard.${system.status}`)}</span>
                        </span>
                      </div>

                      {/* Content Indicators */}
                      <div className="absolute bottom-2 left-2 z-10 flex space-x-1 rtl:space-x-reverse">
                        {system.video_url && (
                          <Tooltip content={language === 'ar' ? 'يحتوي على فيديو توضيحي' : 'Contains demo video'}>
                            <div className="bg-red-500/90 backdrop-blur-sm rounded-full p-1.5 shadow-lg">
                              <Video className="w-3 h-3 text-white" />
                            </div>
                          </Tooltip>
                        )}
                        {system.gallery_images && system.gallery_images.length > 0 && (
                          <Tooltip content={language === 'ar' ? `${system.gallery_images.length} صور في المعرض` : `${system.gallery_images.length} images in gallery`}>
                            <div className="bg-blue-500/90 backdrop-blur-sm rounded-full p-1.5 shadow-lg relative">
                              <ImageIcon className="w-3 h-3 text-white" />
                              <span className="absolute -top-1 -right-1 bg-white text-blue-500 text-xs rounded-full w-4 h-4 flex items-center justify-center font-bold text-[10px]">
                                {system.gallery_images.length}
                              </span>
                            </div>
                          </Tooltip>
                        )}
                      </div>
                    </div>

                    {/* Content */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-2 line-clamp-1">
                        {getSystemText(system, 'name')}
                      </h3>
                      <p className="text-gray-300 text-sm mb-3 line-clamp-2">
                        {getSystemText(system, 'description')}
                      </p>

                      <div className="flex items-center justify-between mb-3">
                        <div className="flex flex-col">
                          <span className="text-secondary font-bold text-lg">${system.price}</span>
                          <span className="text-xs text-gray-400">
                            {language === 'ar' ? 'سعر منفرد' : 'Standalone Price'}
                          </span>
                        </div>
                        <div className="flex flex-col items-end space-y-1">
                          <span className="text-xs text-gray-400 bg-accent/20 px-2 py-1 rounded">
                            {language === 'ar' ?
                              (system.category === 'general' ? 'عام' :
                               system.category === 'combat' ? 'قتال' :
                               system.category === 'economy' ? 'اقتصاد' :
                               system.category === 'social' ? 'اجتماعي' :
                               system.category === 'utility' ? 'أدوات' :
                               system.category === 'admin' ? 'إدارة' : system.category) :
                              system.category.charAt(0).toUpperCase() + system.category.slice(1)
                            }
                          </span>
                          {/* Premium Integration Badge */}
                          <span className="text-xs bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-300 px-2 py-1 rounded border border-purple-500/30">
                            <Star className="w-3 h-3 inline mr-1" />
                            {language === 'ar' ? 'متاح للمميزة' : 'Premium Ready'}
                          </span>
                        </div>
                      </div>

                      {/* Features */}
                      <div className="space-y-1 mb-4">
                        {(() => {
                          const features = getSystemArray(system, 'features');
                          return features.slice(0, 2).map((feature, index) => (
                            <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                              <Star className="w-3 h-3 text-accent fill-current" />
                              <span className="text-xs text-gray-400 line-clamp-1">{feature}</span>
                            </div>
                          ));
                        })()}
                        {(() => {
                          const features = getSystemArray(system, 'features');
                          return features.length > 2 && (
                            <span className="text-xs text-accent">
                              +{features.length - 2} {language === 'ar' ? 'المزيد' : 'more'}
                            </span>
                          );
                        })()}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="space-y-2">
                      <div className="flex space-x-2 rtl:space-x-reverse">
                        <Button variant="outline" size="sm" onClick={() => handleEdit(system)} className="flex-1">
                          <Edit className="w-4 h-4 mr-1" />
                          {t('admin.dashboard.edit')}
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleDelete(system)}>
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                      <div className="flex space-x-2 rtl:space-x-reverse">
                        <Button
                          variant="secondary"
                          size="sm"
                          className="flex-1 text-xs"
                          onClick={() => handlePreview(system)}
                        >
                          <Eye className="w-3 h-3 mr-1" />
                          {language === 'ar' ? 'معاينة' : 'Preview'}
                        </Button>
                        <Button variant="secondary" size="sm" className="flex-1 text-xs">
                          <Star className="w-3 h-3 mr-1" />
                          {language === 'ar' ? 'إضافة للمميزة' : 'Add to Premium'}
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  // List View
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    {/* Image */}
                    <div className="w-16 h-16 bg-gradient-to-br from-primary to-background rounded-lg overflow-hidden flex-shrink-0">
                      {system.image_url ? (
                        <img
                          src={system.image_url}
                          alt={getSystemText(system, 'name', language)}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <ImageIcon className="w-6 h-6 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-lg font-semibold text-white truncate">
                          {getSystemText(system, 'name')}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(system.status)} flex items-center shadow-sm`}>
                          {getStatusIcon(system.status)}
                          <span className="ml-1 rtl:ml-0 rtl:mr-1">{t(`admin.dashboard.${system.status}`)}</span>
                        </span>
                      </div>

                      <p className="text-gray-300 text-sm mb-2 line-clamp-1">
                        {getSystemText(system, 'description', language)}
                      </p>

                      <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm">
                        <span className="text-secondary font-bold">${system.price}</span>
                        <span className="text-gray-400">{system.category}</span>
                        <span className="text-gray-400">
                          {(() => {
                            const features = getSystemArray(system, 'features');
                            return features.length;
                          })()} {language === 'ar' ? 'ميزة' : 'features'}
                        </span>

                        {/* Content Indicators */}
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          {system.video_url && (
                            <Tooltip content={language === 'ar' ? 'يحتوي على فيديو توضيحي' : 'Contains demo video'}>
                              <div className="bg-red-500/20 text-red-400 rounded-full p-1">
                                <Video className="w-3 h-3" />
                              </div>
                            </Tooltip>
                          )}
                          {system.gallery_images && system.gallery_images.length > 0 && (
                            <Tooltip content={language === 'ar' ? `${system.gallery_images.length} صور في المعرض` : `${system.gallery_images.length} images in gallery`}>
                              <div className="bg-blue-500/20 text-blue-400 rounded-full p-1 relative">
                                <ImageIcon className="w-3 h-3" />
                                <span className="absolute -top-1 -right-1 bg-blue-400 text-white text-xs rounded-full w-3 h-3 flex items-center justify-center font-bold text-[8px]">
                                  {system.gallery_images.length}
                                </span>
                              </div>
                            </Tooltip>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2 rtl:space-x-reverse flex-shrink-0">
                      <Button variant="outline" size="sm" onClick={() => handlePreview(system)}>
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleEdit(system)}>
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleDelete(system)}>
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </Card.Body>
            </Card>
          ))}
        </div>

          {/* Pagination */}
          {sortedSystems.length > 0 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={sortedSystems.length}
              itemsPerPage={itemsPerPage}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
              itemsPerPageOptions={[3, 6, 9, 12]}
              className="mt-6"
            />
          )}
        </>
      )}

      {/* Create/Edit Modal - Golden Ratio Design */}
      {showModal && (
        <Modal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          title={editingSystem ? t('admin.dashboard.editSystem') : t('admin.dashboard.addNewSystem')}
          size="xl"
          className="golden-shadow-xl"
        >
          <Modal.Body className="golden-spacing-lg max-h-[70vh] overflow-y-auto modal-text-fix">
            <div className="space-y-10">
              {/* Basic Information */}
              <div className="golden-card bg-gradient-to-br from-blue-500/5 to-blue-600/5 border-blue-500/20">
                <h3 className="golden-text-xl font-bold text-white mb-6 flex items-center">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 rtl:mr-0 rtl:ml-3">
                    <Package className="w-4 h-4 text-white" />
                  </div>
                  {language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'}
                </h3>
                <div className="golden-grid-2 gap-6">
                  <Tooltip content={language === 'ar' ? 'أدخل اسم النظام باللغة العربية' : 'Enter system name in Arabic'}>
                    <Input
                      label={`${t('admin.dashboard.systemName')} (العربية)`}
                      value={formData.name?.ar || ''}
                      onChange={(e) => handleTextChange('ar', 'name', e.target.value)}
                      className="golden-input"
                      required
                    />
                  </Tooltip>
                  <Tooltip content={language === 'ar' ? 'أدخل اسم النظام باللغة الإنجليزية' : 'Enter system name in English'}>
                    <Input
                      label={`${t('admin.dashboard.systemName')} (English)`}
                      value={formData.name?.en || ''}
                      onChange={(e) => handleTextChange('en', 'name', e.target.value)}
                      className="golden-input"
                      required
                    />
                  </Tooltip>
                </div>
              </div>

              {/* Description */}
              <div className="golden-card bg-gradient-to-br from-green-500/5 to-green-600/5 border-green-500/20">
                <h3 className="golden-text-xl font-bold text-white mb-6 flex items-center">
                  <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3 rtl:mr-0 rtl:ml-3">
                    <FileText className="w-4 h-4 text-white" />
                  </div>
                  {t('admin.dashboard.description')}
                </h3>
                <div className="golden-grid-2 gap-6">
                  <div>
                    <label className="block golden-text-sm font-semibold text-white mb-3 tracking-wide">
                      {t('admin.dashboard.description')} (العربية)
                    </label>
                    <textarea
                      value={formData.description?.ar || ''}
                      onChange={(e) => handleTextChange('ar', 'description', e.target.value)}
                      rows={6}
                      className="golden-input w-full resize-none"
                      placeholder="اكتب وصف النظام باللغة العربية..."
                    />
                  </div>
                  <div>
                    <label className="block golden-text-sm font-semibold text-white mb-3 tracking-wide">
                      {t('admin.dashboard.description')} (English)
                    </label>
                    <textarea
                      value={formData.description?.en || ''}
                      onChange={(e) => handleTextChange('en', 'description', e.target.value)}
                      rows={6}
                      className="golden-input w-full resize-none"
                      placeholder="Write system description in English..."
                    />
                  </div>
                </div>
              </div>

              {/* Price and Category */}
              <div className="golden-card bg-gradient-to-br from-purple-500/5 to-purple-600/5 border-purple-500/20">
                <h3 className="golden-text-xl font-bold text-white mb-6 flex items-center">
                  <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 rtl:mr-0 rtl:ml-3">
                    <DollarSign className="w-4 h-4 text-white" />
                  </div>
                  {language === 'ar' ? 'السعر والفئة' : 'Price and Category'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <Tooltip content={language === 'ar' ? 'أدخل سعر النظام بالدولار الأمريكي' : 'Enter system price in USD'}>
                    <Input
                      label={t('admin.dashboard.price')}
                      type="number"
                      value={formData.price || 0}
                      onChange={(e) => setFormData(prev => ({ ...prev, price: Number(e.target.value) }))}
                      leftIcon={<DollarSign />}
                      className="golden-input"
                    />
                  </Tooltip>
                  <div>
                    <label className="block golden-text-sm font-semibold text-white mb-3 tracking-wide">
                      {t('admin.dashboard.category')}
                    </label>
                    <select
                      value={formData.category || 'general'}
                      onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                      className="golden-select w-full"
                    >
                      {categories.map(cat => (
                        <option key={cat} value={cat}>
                          {cat?.charAt(0)?.toUpperCase() + cat?.slice(1)}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block golden-text-sm font-semibold text-white mb-3 tracking-wide">
                      {language === 'ar' ? 'نوع النظام' : 'System Type'}
                    </label>
                    <select
                      value={formData.type || 'regular'}
                      onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as 'regular' | 'plugin' }))}
                      className="golden-select w-full"
                    >
                      <option value="regular">{language === 'ar' ? 'نظام عادي' : 'Regular System'}</option>
                      <option value="plugin">{language === 'ar' ? 'إضافة خاصة' : 'Special Plugin'}</option>
                    </select>
                  </div>
                  <div>
                    <label className="block golden-text-sm font-semibold text-white mb-3 tracking-wide">
                      {t('admin.dashboard.status')}
                    </label>
                    <select
                      value={formData.status || 'active'}
                      onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as 'active' | 'inactive' }))}
                      className="golden-select w-full"
                    >
                      <option value="active">{t('admin.dashboard.active')}</option>
                      <option value="inactive">{t('admin.dashboard.inactive')}</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Media URLs */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label={t('admin.dashboard.imageUrl')}
                  value={formData.image_url || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, image_url: e.target.value }))}
                  leftIcon={<ImageIcon />}
                />
                <Input
                  label={t('admin.dashboard.videoUrl')}
                  value={formData.video_url || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, video_url: e.target.value }))}
                  leftIcon={<Video />}
                />
              </div>

              {/* Gallery Images */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  {language === 'ar' ? 'معرض الصور (حد أقصى 10 صور)' : 'Image Gallery (Max 10 images)'}
                </label>
                <div className="space-y-3">
                  {/* Gallery Images Input */}
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <Input
                      placeholder={language === 'ar' ? 'رابط الصورة' : 'Image URL'}
                      value={newImageUrl}
                      onChange={(e) => setNewImageUrl(e.target.value)}
                      leftIcon={<ImageIcon />}
                      className="flex-1"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          if (newImageUrl && formData.gallery_images && formData.gallery_images.length < 10) {
                            setFormData(prev => ({
                              ...prev,
                              gallery_images: [...(prev.gallery_images || []), newImageUrl]
                            }));
                            setNewImageUrl('');
                          }
                        }
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => {
                        if (newImageUrl && formData.gallery_images && formData.gallery_images.length < 10) {
                          setFormData(prev => ({
                            ...prev,
                            gallery_images: [...(prev.gallery_images || []), newImageUrl]
                          }));
                          setNewImageUrl('');
                        }
                      }}
                      disabled={!newImageUrl || (formData.gallery_images && formData.gallery_images.length >= 10)}
                      className="px-4 py-2 bg-secondary text-primary rounded-lg hover:bg-secondary/80 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {language === 'ar' ? 'إضافة' : 'Add'}
                    </button>
                  </div>

                  {/* Gallery Images Preview */}
                  {formData.gallery_images && formData.gallery_images.length > 0 && (
                    <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-2 p-4 bg-primary/20 rounded-lg">
                      {formData.gallery_images?.map((imageUrl, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={imageUrl}
                            alt={`Gallery ${index + 1}`}
                            className="w-full aspect-square object-cover rounded-lg"
                          />
                          <button
                            type="button"
                            onClick={() => {
                              setFormData(prev => ({
                                ...prev,
                                gallery_images: prev.gallery_images?.filter((_, i) => i !== index) || []
                              }));
                            }}
                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Features */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('admin.dashboard.features')}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">
                      {t('admin.dashboard.features')} (العربية) - {language === 'ar' ? 'سطر واحد لكل ميزة' : 'One per line'}
                    </label>
                    <textarea
                      value={formData.features?.ar?.join('\n') || ''}
                      onChange={(e) => handleArrayChange('ar', 'features', e.target.value)}
                      rows={6}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">
                      {t('admin.dashboard.features')} (English) - One per line
                    </label>
                    <textarea
                      value={formData.features?.en?.join('\n') || ''}
                      onChange={(e) => handleArrayChange('en', 'features', e.target.value)}
                      rows={6}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                    />
                  </div>
                </div>
              </div>

              {/* Technical Specifications */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('admin.dashboard.techSpecs')}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">
                      {t('admin.dashboard.techSpecs')} (العربية) - {language === 'ar' ? 'سطر واحد لكل ميزة' : 'One per line'}
                    </label>
                    <textarea
                      value={formData.tech_specs?.ar?.join('\n') || ''}
                      onChange={(e) => handleArrayChange('ar', 'tech_specs', e.target.value)}
                      rows={6}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">
                      {t('admin.dashboard.techSpecs')} (English) - One per line
                    </label>
                    <textarea
                      value={formData.tech_specs?.en?.join('\n') || ''}
                      onChange={(e) => handleArrayChange('en', 'tech_specs', e.target.value)}
                      rows={6}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                    />
                  </div>
                </div>
              </div>

              {/* Premium Integration Settings */}
              <div className="border-t border-accent/20 pt-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">
                    {language === 'ar' ? 'إعدادات النسخة المميزة' : 'Premium Integration Settings'}
                  </h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowPremiumIntegration(!showPremiumIntegration)}
                  >
                    {showPremiumIntegration ?
                      (language === 'ar' ? 'إخفاء' : 'Hide') :
                      (language === 'ar' ? 'إظهار' : 'Show')
                    }
                  </Button>
                </div>

                {showPremiumIntegration && (
                  <div className="space-y-4 bg-accent/5 p-4 rounded-lg border border-accent/20">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <input
                        type="checkbox"
                        id="canAddToPremium"
                        checked={formData.isPremiumAddon || false}
                        onChange={(e) => {
                          // Update both formData and premiumIntegrationData
                          setFormData(prev => ({
                            ...prev,
                            isPremiumAddon: e.target.checked
                          }));
                          setPremiumIntegrationData(prev => ({
                            ...prev,
                            canAddToPremium: e.target.checked
                          }));
                        }}
                        className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                      />
                      <label htmlFor="canAddToPremium" className="text-white">
                        {language === 'ar' ? 'يمكن إضافة هذا النظام للنسخة المميزة' : 'Can be added to Premium Edition'}
                      </label>
                    </div>

                    {premiumIntegrationData.canAddToPremium && (
                      <>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <Input
                            label={language === 'ar' ? 'السعر الإضافي للنسخة المميزة' : 'Additional Premium Price'}
                            type="number"
                            value={premiumIntegrationData.premiumPrice}
                            onChange={(e) => setPremiumIntegrationData(prev => ({
                              ...prev,
                              premiumPrice: Number(e.target.value)
                            }))}
                            leftIcon={<DollarSign />}
                          />
                          <div className="space-y-2">
                            <div className="flex items-center space-x-3 rtl:space-x-reverse">
                              <input
                                type="checkbox"
                                id="installationIncluded"
                                checked={premiumIntegrationData.installationIncluded}
                                onChange={(e) => setPremiumIntegrationData(prev => ({
                                  ...prev,
                                  installationIncluded: e.target.checked
                                }))}
                                className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                              />
                              <label htmlFor="installationIncluded" className="text-white text-sm">
                                {language === 'ar' ? 'يشمل التنصيب' : 'Installation Included'}
                              </label>
                            </div>
                            <div className="flex items-center space-x-3 rtl:space-x-reverse">
                              <input
                                type="checkbox"
                                id="maintenanceIncluded"
                                checked={premiumIntegrationData.maintenanceIncluded}
                                onChange={(e) => setPremiumIntegrationData(prev => ({
                                  ...prev,
                                  maintenanceIncluded: e.target.checked
                                }))}
                                className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                              />
                              <label htmlFor="maintenanceIncluded" className="text-white text-sm">
                                {language === 'ar' ? 'يشمل الصيانة' : 'Maintenance Included'}
                              </label>
                            </div>
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-white mb-2">
                            {language === 'ar' ? 'وصف إضافي للنسخة المميزة' : 'Additional Premium Description'}
                          </label>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <textarea
                              placeholder={language === 'ar' ? 'الوصف بالعربية...' : 'Description in Arabic...'}
                              value={premiumIntegrationData.premiumDescription.ar}
                              onChange={(e) => setPremiumIntegrationData(prev => ({
                                ...prev,
                                premiumDescription: {
                                  ...prev.premiumDescription,
                                  ar: e.target.value
                                }
                              }))}
                              rows={3}
                              className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                            />
                            <textarea
                              placeholder="Description in English..."
                              value={premiumIntegrationData.premiumDescription.en}
                              onChange={(e) => setPremiumIntegrationData(prev => ({
                                ...prev,
                                premiumDescription: {
                                  ...prev.premiumDescription,
                                  en: e.target.value
                                }
                              }))}
                              rows={3}
                              className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                            />
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer className="golden-spacing-lg border-t border-accent/20 bg-gradient-to-r from-primary/50 to-background/50">
            <div className="golden-flex justify-end">
              <Button
                variant="outline"
                onClick={() => setShowModal(false)}
                className="golden-btn-lg bg-gray-600/20 border-gray-500/30 text-gray-300 hover:bg-gray-500/30 hover:text-white transition-all duration-300"
              >
                <X className="w-4 h-4 mr-2" />
                {t('admin.dashboard.cancel')}
              </Button>
              <Button
                variant="primary"
                onClick={handleSave}
                className="golden-btn-lg bg-gradient-to-r from-secondary to-accent hover:from-secondary/80 hover:to-accent/80 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Save className="w-4 h-4 mr-2" />
                {t('admin.dashboard.save')}
              </Button>
            </div>
          </Modal.Footer>
        </Modal>
      )}

      {/* Preview Modal */}
      {showPreviewModal && previewSystem && (
        <Modal
          isOpen={showPreviewModal}
          onClose={() => setShowPreviewModal(false)}
          title={`${language === 'ar' ? 'معاينة النظام' : 'System Preview'} - ${getSystemText(previewSystem, 'name', language)}`}
          size="xl"
        >
          <Modal.Body className="max-h-[70vh] overflow-y-auto">
            <div className="space-y-6">
              {/* System Image */}
              {previewSystem.image_url && (
                <div className="w-full h-64 bg-gradient-to-br from-primary to-background rounded-lg overflow-hidden">
                  <img
                    src={previewSystem.image_url}
                    alt={getSystemText(previewSystem, 'name', language)}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}

              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    {getSystemText(previewSystem, 'name', language)}
                  </h3>
                  <p className="text-gray-300 mb-4">
                    {getSystemText(previewSystem, 'description', language)}
                  </p>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">{language === 'ar' ? 'السعر:' : 'Price:'}</span>
                      <span className="text-secondary font-bold text-xl">${previewSystem.price}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">{language === 'ar' ? 'الفئة:' : 'Category:'}</span>
                      <span className="text-white">{previewSystem.category}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">{language === 'ar' ? 'الحالة:' : 'Status:'}</span>
                      <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(previewSystem.status)} flex items-center shadow-sm`}>
                        {getStatusIcon(previewSystem.status)}
                        <span className="ml-1 rtl:ml-0 rtl:mr-1">{t(`admin.dashboard.${previewSystem.status}`)}</span>
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-3">
                    {language === 'ar' ? 'الميزات' : 'Features'}
                  </h4>
                  <ul className="space-y-2">
                    {getSystemArray(previewSystem, 'features')?.map((feature: string, index: number) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" />
                        <span className="text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Technical Specifications */}
              {previewSystem.tech_specs[language].length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-white mb-3">
                    {language === 'ar' ? 'المواصفات التقنية' : 'Technical Specifications'}
                  </h4>
                  <div className="bg-primary/30 rounded-lg p-4">
                    <ul className="space-y-2">
                      {getSystemArray(previewSystem, 'tech_specs')?.map((spec: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <Settings className="w-4 h-4 text-accent mt-0.5 mr-2 flex-shrink-0" />
                          <span className="text-gray-300">{spec}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* Image Gallery */}
              {previewSystem.gallery_images && previewSystem.gallery_images.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-white mb-3">
                    {language === 'ar' ? 'معرض الصور' : 'Image Gallery'}
                  </h4>
                  <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-2">
                    {previewSystem.gallery_images?.slice(0, 10)?.map((imageUrl, index) => (
                      <div
                        key={index}
                        className="relative aspect-square rounded-lg overflow-hidden cursor-pointer group hover:scale-105 transition-all duration-300"
                      >
                        <img
                          src={imageUrl}
                          alt={`${getSystemText(previewSystem, 'name', language)} - ${index + 1}`}
                          className="w-full h-full object-cover group-hover:brightness-110 transition-all duration-300"
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
                          <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <Eye className="w-4 h-4 text-white" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Video Preview */}
              {previewSystem.video_url && (
                <div>
                  <h4 className="text-lg font-semibold text-white mb-3">
                    {language === 'ar' ? 'فيديو توضيحي' : 'Demo Video'}
                  </h4>
                  <div className="aspect-video bg-black rounded-lg overflow-hidden">
                    <iframe
                      src={parseYouTubeURL(previewSystem.video_url) || previewSystem.video_url}
                      className="w-full h-full"
                      allowFullScreen
                      title={`${getSystemText(previewSystem, 'name', language)} Demo`}
                    />
                  </div>
                </div>
              )}
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowPreviewModal(false)}>
              {language === 'ar' ? 'إغلاق' : 'Close'}
            </Button>
            <Button variant="primary" onClick={() => {
              setShowPreviewModal(false);
              handleEdit(previewSystem);
            }} className="btn-icon-fix">
              <Edit className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'تعديل' : 'Edit'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}
    </div>
  );
};

export default TechnicalSystemsManager;
