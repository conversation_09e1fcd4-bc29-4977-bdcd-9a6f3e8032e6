const puppeteer = require('puppeteer');

async function testAdminPanel() {
  let browser;
  
  try {
    console.log('👑 Testing Admin Panel...\n');
    
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Set viewport
    await page.setViewport({ width: 1920, height: 1080 });
    
    console.log('🔐 Loading admin login page...');
    await page.goto('http://localhost:5173/admin', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    // Wait for page to load
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if login form exists
    const loginForm = await page.$('form, [data-testid="login-form"], input[type="email"]');
    if (loginForm) {
      console.log('✅ Login form found');
      
      // Fill login form
      console.log('📝 Filling login credentials...');
      
      // Try different selectors for email input
      const emailSelectors = ['input[type="email"]', 'input[name="email"]', '#email'];
      for (const selector of emailSelectors) {
        try {
          await page.type(selector, '<EMAIL>');
          console.log(`   ✅ Email filled using selector: ${selector}`);
          break;
        } catch (e) {
          // Continue to next selector
        }
      }
      
      // Try different selectors for password input
      const passwordSelectors = ['input[type="password"]', 'input[name="password"]', '#password'];
      for (const selector of passwordSelectors) {
        try {
          await page.type(selector, 'admin123');
          console.log(`   ✅ Password filled using selector: ${selector}`);
          break;
        } catch (e) {
          // Continue to next selector
        }
      }
      
      // Submit form
      console.log('🚀 Submitting login form...');
      const submitSelectors = ['button[type="submit"]', 'input[type="submit"]', 'button:contains("Login")', 'button:contains("دخول")'];
      for (const selector of submitSelectors) {
        try {
          await page.click(selector);
          console.log(`   ✅ Form submitted using selector: ${selector}`);
          break;
        } catch (e) {
          // Continue to next selector
        }
      }
      
      // Wait for navigation or dashboard to load
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Check if we're now in the admin dashboard
      const currentUrl = page.url();
      console.log(`   🌐 Current URL: ${currentUrl}`);
      
      if (currentUrl.includes('/admin') && !currentUrl.includes('/login')) {
        console.log('✅ Successfully logged into admin panel');
        
        // Test dashboard elements
        console.log('\n📊 Testing Dashboard Elements...');
        
        // Check for navigation menu
        const navMenu = await page.$('nav, .sidebar, [data-testid="admin-nav"]');
        if (navMenu) {
          console.log('✅ Navigation menu found');
          
          // Count navigation items
          const navItems = await page.$$('nav a, .sidebar a, [data-testid="admin-nav"] a');
          console.log(`   📋 Found ${navItems.length} navigation items`);
        } else {
          console.log('❌ Navigation menu not found');
        }
        
        // Check for dashboard cards/widgets
        const dashboardCards = await page.$$('.card, .widget, [class*="dashboard"]');
        console.log(`   📊 Found ${dashboardCards.length} dashboard cards/widgets`);
        
        // Test Systems Management
        console.log('\n🖥️ Testing Systems Management...');
        
        try {
          // Look for systems management link
          const systemsLink = await page.$('a[href*="systems"], button:contains("Systems"), button:contains("أنظمة")');
          if (systemsLink) {
            await systemsLink.click();
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const systemsTable = await page.$('table, .grid, [data-testid="systems-table"]');
            if (systemsTable) {
              console.log('✅ Systems management page loaded');
              
              const systemRows = await page.$$('table tr, .grid-item, [data-testid="system-row"]');
              console.log(`   📊 Found ${systemRows.length} system entries`);
            } else {
              console.log('⚠️ Systems management page loaded but no table found');
            }
          } else {
            console.log('⚠️ Systems management link not found');
          }
        } catch (e) {
          console.log('⚠️ Error testing systems management:', e.message);
        }
        
        // Test Services Management
        console.log('\n🛠️ Testing Services Management...');
        
        try {
          // Look for services management link
          const servicesLink = await page.$('a[href*="services"], button:contains("Services"), button:contains("خدمات")');
          if (servicesLink) {
            await servicesLink.click();
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const servicesTable = await page.$('table, .grid, [data-testid="services-table"]');
            if (servicesTable) {
              console.log('✅ Services management page loaded');
              
              const serviceRows = await page.$$('table tr, .grid-item, [data-testid="service-row"]');
              console.log(`   📊 Found ${serviceRows.length} service entries`);
            } else {
              console.log('⚠️ Services management page loaded but no table found');
            }
          } else {
            console.log('⚠️ Services management link not found');
          }
        } catch (e) {
          console.log('⚠️ Error testing services management:', e.message);
        }
        
      } else {
        console.log('❌ Login failed or redirected incorrectly');
      }
      
    } else {
      console.log('❌ Login form not found');
    }
    
    console.log('\n🎉 Admin panel testing completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testAdminPanel();
