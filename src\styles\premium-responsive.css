/* Premium Edition Responsive Styles */

/* Mobile First Approach */
.premium-modal {
  @apply fixed inset-0 z-50 bg-black/80 backdrop-blur-sm;
  overflow-y: auto;
  overflow-x: hidden;
}

.premium-modal-content {
  @apply bg-primary rounded-xl border border-secondary/30 w-full min-h-screen;
  margin: 0 auto;
  max-width: 100%;
}

/* Tablet Styles */
@media (min-width: 768px) {
  .premium-modal {
    @apply p-4;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .premium-modal-content {
    @apply max-w-5xl min-h-0 h-auto rounded-2xl;
    width: 95%;
  }
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .premium-modal {
    @apply p-6;
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .premium-modal-content {
    @apply max-w-7xl;
    width: 90%;
  }
}

/* Large Desktop Styles */
@media (min-width: 1280px) {
  .premium-modal-content {
    @apply max-w-[90rem];
    width: 85%;
  }
}

/* Ultra Wide Desktop */
@media (min-width: 1536px) {
  .premium-modal-content {
    @apply max-w-[100rem];
    width: 80%;
  }
}

/* Premium Header Responsive */
.premium-header {
  @apply text-center mb-6;
}

.premium-header h1 {
  @apply text-3xl font-black;
}

@media (min-width: 768px) {
  .premium-header {
    @apply mb-8;
  }
  
  .premium-header h1 {
    @apply text-5xl;
  }
}

@media (min-width: 1024px) {
  .premium-header {
    @apply mb-12;
  }
  
  .premium-header h1 {
    @apply text-6xl;
  }
}

/* Step Navigation Responsive */
.step-navigation {
  @apply flex flex-col space-y-2;
}

.step-navigation .step-item {
  @apply w-full;
}

@media (min-width: 768px) {
  .step-navigation {
    @apply flex-row space-y-0 space-x-2 rtl:space-x-reverse;
  }
  
  .step-navigation .step-item {
    @apply w-auto;
  }
}

/* Media Section Responsive */
.premium-media {
  @apply h-48 mb-6;
}

@media (min-width: 768px) {
  .premium-media {
    @apply h-72 mb-8;
  }
}

@media (min-width: 1024px) {
  .premium-media {
    @apply h-96;
  }
}

/* Features Grid Responsive */
.features-grid {
  @apply grid grid-cols-1 gap-4;
}

@media (min-width: 768px) {
  .features-grid {
    @apply grid-cols-2 gap-6;
  }
}

@media (min-width: 1024px) {
  .features-grid {
    @apply gap-8;
  }
}

/* Gallery Grid Responsive */
.gallery-grid {
  @apply grid grid-cols-2 gap-2;
}

@media (min-width: 640px) {
  .gallery-grid {
    @apply grid-cols-3 gap-3;
  }
}

@media (min-width: 768px) {
  .gallery-grid {
    @apply grid-cols-4 gap-4;
  }
}

@media (min-width: 1024px) {
  .gallery-grid {
    @apply grid-cols-5;
  }
}

/* Systems/Services Grid Responsive */
.systems-grid {
  @apply grid grid-cols-1 gap-4;
}

@media (min-width: 768px) {
  .systems-grid {
    @apply grid-cols-2 gap-6;
  }
}

@media (min-width: 1024px) {
  .systems-grid {
    @apply grid-cols-3;
  }
}

/* Services Grid Responsive */
.services-grid {
  @apply grid grid-cols-1 gap-4;
}

@media (min-width: 768px) {
  .services-grid {
    @apply grid-cols-2 gap-6;
  }
}

/* Summary Section Responsive */
.summary-section {
  @apply space-y-4;
}

@media (min-width: 768px) {
  .summary-section {
    @apply space-y-6;
  }
}

@media (min-width: 1024px) {
  .summary-section {
    @apply space-y-8;
  }
}

/* Payment Methods Grid Responsive */
.payment-grid {
  @apply grid grid-cols-1 gap-3;
}

@media (min-width: 640px) {
  .payment-grid {
    @apply grid-cols-2;
  }
}

@media (min-width: 768px) {
  .payment-grid {
    @apply grid-cols-3 gap-4;
  }
}

/* Benefits Grid Responsive */
.benefits-grid {
  @apply grid grid-cols-1 gap-3;
}

@media (min-width: 768px) {
  .benefits-grid {
    @apply grid-cols-2 gap-4;
  }
}

/* Button Responsive */
.premium-button {
  @apply px-8 py-3 text-lg;
}

@media (min-width: 768px) {
  .premium-button {
    @apply px-12 py-4 text-xl;
  }
}

@media (min-width: 1024px) {
  .premium-button {
    @apply px-16 py-5;
  }
}

/* Text Responsive */
.responsive-text-sm {
  @apply text-sm;
}

.responsive-text-base {
  @apply text-base;
}

.responsive-text-lg {
  @apply text-lg;
}

.responsive-text-xl {
  @apply text-xl;
}

.responsive-text-2xl {
  @apply text-2xl;
}

@media (min-width: 768px) {
  .responsive-text-sm {
    @apply text-base;
  }
  
  .responsive-text-base {
    @apply text-lg;
  }
  
  .responsive-text-lg {
    @apply text-xl;
  }
  
  .responsive-text-xl {
    @apply text-2xl;
  }
  
  .responsive-text-2xl {
    @apply text-3xl;
  }
}

@media (min-width: 1024px) {
  .responsive-text-lg {
    @apply text-2xl;
  }
  
  .responsive-text-xl {
    @apply text-3xl;
  }
  
  .responsive-text-2xl {
    @apply text-4xl;
  }
}

/* Spacing Responsive */
.responsive-spacing {
  @apply p-4;
}

@media (min-width: 768px) {
  .responsive-spacing {
    @apply p-6;
  }
}

@media (min-width: 1024px) {
  .responsive-spacing {
    @apply p-8;
  }
}

/* Modal Video/Image Responsive */
.modal-video {
  @apply w-full h-64;
}

@media (min-width: 768px) {
  .modal-video {
    @apply h-80;
  }
}

@media (min-width: 1024px) {
  .modal-video {
    @apply h-96;
  }
}

@media (min-width: 1280px) {
  .modal-video {
    @apply h-[32rem];
  }
}

/* Touch Optimizations for Mobile */
@media (max-width: 767px) {
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }
  
  .premium-card {
    @apply p-4;
  }
  
  .premium-card h3 {
    @apply text-lg;
  }
  
  .premium-card p {
    @apply text-sm;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .premium-icon {
    @apply transform scale-110;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .premium-animation {
    @apply transition-none;
  }
  
  .premium-animation * {
    @apply animate-none;
  }
}
