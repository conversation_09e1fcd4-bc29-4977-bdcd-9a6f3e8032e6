@echo off
chcp 65001 >nul
title Khanfashariya - تشغيل جميع الخدمات

echo.
echo ========================================
echo    🚀 بدء تشغيل جميع خدمات المشروع
echo ========================================
echo.

:: التحقق من وجود Node.js
echo 🔍 التحقق من Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت! يرجى تثبيت Node.js أولاً
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

:: التحقق من وجود ngrok
echo 🔍 التحقق من ngrok...
ngrok version >nul 2>&1
if errorlevel 1 (
    echo ❌ ngrok غير مثبت! يرجى تثبيت ngrok أولاً
    echo 💡 تثبيت ngrok: npm install -g ngrok
    pause
    exit /b 1
)
echo ✅ ngrok متوفر

:: إيقاف العمليات السابقة
echo.
echo 🛑 إيقاف العمليات السابقة...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im ngrok.exe >nul 2>&1
timeout /t 2 >nul

:: تشغيل الخادم الخلفي
echo.
echo 🔧 تشغيل الخادم الخلفي (Backend)...
start "Backend Server" cmd /k "title Backend-Server && cd /d %~dp0 && npm run start"
timeout /t 3

:: تشغيل الخادم الأمامي
echo 🎨 تشغيل الخادم الأمامي (Frontend)...
start "Frontend Server" cmd /k "title Frontend-Server && cd /d %~dp0 && npm run dev"
timeout /t 5

:: التحقق من تشغيل الخوادم
echo.
echo 🔍 التحقق من الخوادم...
timeout /t 3

:: فحص Backend
curl -s http://localhost:3001/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️ الخادم الخلفي لم يبدأ بعد، انتظار إضافي...
    timeout /t 5
)

:: فحص Frontend
curl -s http://localhost:5173 >nul 2>&1
if errorlevel 1 (
    echo ⚠️ الخادم الأمامي لم يبدأ بعد، انتظار إضافي...
    timeout /t 5
)

:: تشغيل ngrok للخادم الخلفي
echo.
echo 🌐 تشغيل ngrok للخادم الخلفي...
start "Backend ngrok" cmd /k "title Backend-ngrok && ngrok http 3001"
timeout /t 3

:: تشغيل ngrok للخادم الأمامي
echo 🌐 تشغيل ngrok للخادم الأمامي...
start "Frontend ngrok" cmd /k "title Frontend-ngrok && ngrok http 5173 --web-addr=localhost:4041"
timeout /t 5

:: انتظار تشغيل ngrok
echo.
echo ⏳ انتظار تشغيل أنفاق ngrok...
timeout /t 8

:: الحصول على الروابط
echo.
echo 🔗 الحصول على روابط ngrok...

:: محاولة الحصول على رابط Backend
for /f "tokens=*" %%i in ('curl -s http://127.0.0.1:4040/api/tunnels 2^>nul ^| findstr "public_url"') do (
    set backend_response=%%i
)

:: محاولة الحصول على رابط Frontend
for /f "tokens=*" %%i in ('curl -s http://127.0.0.1:4041/api/tunnels 2^>nul ^| findstr "public_url"') do (
    set frontend_response=%%i
)

:: تشغيل سكريبت Node.js للحصول على الروابط بشكل صحيح
echo.
echo 📋 إنشاء ملف الروابط...
node -e "
const https = require('http');

async function getTunnels() {
    try {
        // الحصول على أنفاق Backend
        const backendReq = https.get('http://127.0.0.1:4040/api/tunnels', (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const backendData = JSON.parse(data);
                    const backendUrl = backendData.tunnels[0]?.public_url || 'غير متاح';
                    
                    // الحصول على أنفاق Frontend
                    const frontendReq = https.get('http://127.0.0.1:4041/api/tunnels', (res2) => {
                        let data2 = '';
                        res2.on('data', chunk => data2 += chunk);
                        res2.on('end', () => {
                            try {
                                const frontendData = JSON.parse(data2);
                                const frontendUrl = frontendData.tunnels[0]?.public_url || 'غير متاح';
                                
                                console.log('✅ تم الحصول على الروابط بنجاح!');
                                console.log('🔧 Backend:', backendUrl);
                                console.log('🎨 Frontend:', frontendUrl);
                                
                                // حفظ الروابط في ملف
                                const fs = require('fs');
                                const config = {
                                    timestamp: new Date().toISOString(),
                                    backend: { url: backendUrl, local: 'http://localhost:3001' },
                                    frontend: { url: frontendUrl, local: 'http://localhost:5173' },
                                    ready: true
                                };
                                fs.writeFileSync('current-ngrok-urls.json', JSON.stringify(config, null, 2));
                                
                            } catch (e) {
                                console.log('⚠️ Frontend ngrok غير متاح');
                            }
                        });
                    });
                    frontendReq.on('error', () => {
                        console.log('⚠️ Frontend ngrok غير متاح');
                    });
                    
                } catch (e) {
                    console.log('⚠️ Backend ngrok غير متاح');
                }
            });
        });
        backendReq.on('error', () => {
            console.log('⚠️ Backend ngrok غير متاح');
        });
        
    } catch (error) {
        console.log('❌ خطأ في الحصول على الروابط');
    }
}

setTimeout(getTunnels, 1000);
"

timeout /t 3

echo.
echo ========================================
echo           🎉 تم تشغيل جميع الخدمات!
echo ========================================
echo.
echo 📋 الخدمات المشغلة:
echo   🔧 Backend Server: http://localhost:3001
echo   🎨 Frontend Server: http://localhost:5173
echo   🌐 Backend ngrok: تحقق من النافذة المفتوحة
echo   🌐 Frontend ngrok: تحقق من النافذة المفتوحة
echo.
echo 📁 تحقق من ملف: current-ngrok-urls.json للروابط
echo.
echo ⚠️ لا تغلق هذه النوافذ للحفاظ على الخدمات
echo.
pause
