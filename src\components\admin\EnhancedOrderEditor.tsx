/**
 * Enhanced Order Editor Component
 * Comprehensive order editing interface for admin panel
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotification } from '../../hooks/useNotification';
import {
  X,
  Save,
  Calendar,
  DollarSign,
  Package,
  User,
  Clock,
  AlertTriangle,
  CheckCircle,
  Settings,
  FileText,
  CreditCard,
  Truck,
  Star
} from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Modal from '../ui/Modal';
import { EnhancedOrder } from '../../types/enhanced-orders';
import {
  getStatusText,
  getPriorityText,
  getNextPossibleStatuses,
  getStatusColor,
  getPriorityColor,
  calculateProgressPercentage,
  getEstimatedCompletionTime
} from '../../utils/orderStatusWorkflow';

interface EnhancedOrderEditorProps {
  order: EnhancedOrder;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedOrder: Partial<EnhancedOrder>) => Promise<void>;
}

const EnhancedOrderEditor: React.FC<EnhancedOrderEditorProps> = ({
  order,
  isOpen,
  onClose,
  onSave
}) => {
  const { language } = useTranslation();
  const { showNotification } = useNotification();
  
  const [formData, setFormData] = useState<Partial<EnhancedOrder>>({});
  const [activeTab, setActiveTab] = useState<'details' | 'status' | 'pricing' | 'timeline' | 'notes'>('details');
  const [loading, setSaving] = useState(false);

  useEffect(() => {
    if (order) {
      setFormData({
        status: order.status,
        priority: order.priority,
        payment_status: order.payment_status,
        estimated_completion: order.estimated_completion,
        admin_notes: order.admin_notes,
        customer_requirements: order.customer_requirements,
        support_level: order.support_level,
        maintenance_included: order.maintenance_included,
        installation_included: order.installation_included
      });
    }
  }, [order]);

  const handleSave = async () => {
    setSaving(true);
    try {
      await onSave(formData);
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم حفظ التغييرات بنجاح' : 'Changes saved successfully'
      });
      onClose();
    } catch (error) {
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في حفظ التغييرات' : 'Failed to save changes'
      });
    } finally {
      setSaving(false);
    }
  };

  const tabs = [
    { id: 'details', label: language === 'ar' ? 'التفاصيل' : 'Details', icon: Package },
    { id: 'status', label: language === 'ar' ? 'الحالة' : 'Status', icon: CheckCircle },
    { id: 'pricing', label: language === 'ar' ? 'التسعير' : 'Pricing', icon: DollarSign },
    { id: 'timeline', label: language === 'ar' ? 'الجدولة' : 'Timeline', icon: Calendar },
    { id: 'notes', label: language === 'ar' ? 'الملاحظات' : 'Notes', icon: FileText }
  ];

  const nextPossibleStatuses = getNextPossibleStatuses(order.status);

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl border border-gray-700">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-gradient-to-br from-secondary to-accent rounded-xl flex items-center justify-center">
              <Package className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">
                {language === 'ar' ? 'تحرير الطلب' : 'Edit Order'}
              </h2>
              <p className="text-gray-400 text-sm">
                #{order.order_number || order.id.slice(0, 8)}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-700">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 rtl:space-x-reverse px-6 py-4 text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'text-secondary border-b-2 border-secondary bg-secondary/10'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Content */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {activeTab === 'details' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {language === 'ar' ? 'نوع الطلب' : 'Order Type'}
                  </label>
                  <div className="p-3 bg-gray-800 rounded-lg border border-gray-600">
                    <span className="text-white">{order.order_type}</span>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {language === 'ar' ? 'فئة الطلب' : 'Order Category'}
                  </label>
                  <div className="p-3 bg-gray-800 rounded-lg border border-gray-600">
                    <span className="text-white">{order.order_category || 'standard'}</span>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'مستوى الدعم' : 'Support Level'}
                </label>
                <select
                  value={formData.support_level || order.support_level || 'basic'}
                  onChange={(e) => setFormData(prev => ({ ...prev, support_level: e.target.value as any }))}
                  className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-secondary focus:ring-1 focus:ring-secondary"
                >
                  <option value="basic">{language === 'ar' ? 'أساسي' : 'Basic'}</option>
                  <option value="standard">{language === 'ar' ? 'قياسي' : 'Standard'}</option>
                  <option value="premium">{language === 'ar' ? 'مميز' : 'Premium'}</option>
                  <option value="enterprise">{language === 'ar' ? 'مؤسسي' : 'Enterprise'}</option>
                </select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <input
                    type="checkbox"
                    id="maintenance_included"
                    checked={formData.maintenance_included ?? order.maintenance_included ?? false}
                    onChange={(e) => setFormData(prev => ({ ...prev, maintenance_included: e.target.checked }))}
                    className="w-4 h-4 text-secondary bg-gray-800 border-gray-600 rounded focus:ring-secondary"
                  />
                  <label htmlFor="maintenance_included" className="text-sm text-gray-300">
                    {language === 'ar' ? 'تشمل الصيانة' : 'Maintenance Included'}
                  </label>
                </div>

                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <input
                    type="checkbox"
                    id="installation_included"
                    checked={formData.installation_included ?? order.installation_included ?? false}
                    onChange={(e) => setFormData(prev => ({ ...prev, installation_included: e.target.checked }))}
                    className="w-4 h-4 text-secondary bg-gray-800 border-gray-600 rounded focus:ring-secondary"
                  />
                  <label htmlFor="installation_included" className="text-sm text-gray-300">
                    {language === 'ar' ? 'تشمل التثبيت' : 'Installation Included'}
                  </label>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'status' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {language === 'ar' ? 'حالة الطلب' : 'Order Status'}
                  </label>
                  <select
                    value={formData.status || order.status}
                    onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                    className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-secondary focus:ring-1 focus:ring-secondary"
                  >
                    <option value={order.status}>{getStatusText(order.status, language)}</option>
                    {nextPossibleStatuses.map(status => (
                      <option key={status} value={status}>
                        {getStatusText(status, language)}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {language === 'ar' ? 'الأولوية' : 'Priority'}
                  </label>
                  <select
                    value={formData.priority || order.priority || 'medium'}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as any }))}
                    className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-secondary focus:ring-1 focus:ring-secondary"
                  >
                    <option value="low">{getPriorityText('low', language)}</option>
                    <option value="medium">{getPriorityText('medium', language)}</option>
                    <option value="high">{getPriorityText('high', language)}</option>
                    <option value="urgent">{getPriorityText('urgent', language)}</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'حالة الدفع' : 'Payment Status'}
                </label>
                <select
                  value={formData.payment_status || order.payment_status || 'pending'}
                  onChange={(e) => setFormData(prev => ({ ...prev, payment_status: e.target.value as any }))}
                  className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-secondary focus:ring-1 focus:ring-secondary"
                >
                  <option value="pending">{language === 'ar' ? 'معلق' : 'Pending'}</option>
                  <option value="paid">{language === 'ar' ? 'مدفوع' : 'Paid'}</option>
                  <option value="partial">{language === 'ar' ? 'جزئي' : 'Partial'}</option>
                  <option value="failed">{language === 'ar' ? 'فشل' : 'Failed'}</option>
                  <option value="refunded">{language === 'ar' ? 'مسترد' : 'Refunded'}</option>
                  <option value="disputed">{language === 'ar' ? 'متنازع عليه' : 'Disputed'}</option>
                </select>
              </div>

              <div className="p-4 bg-gray-800 rounded-lg border border-gray-600">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-300">
                    {language === 'ar' ? 'نسبة الإنجاز' : 'Progress'}
                  </span>
                  <span className="text-sm text-white">
                    {calculateProgressPercentage(formData.status || order.status)}%
                  </span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-secondary to-accent h-2 rounded-full transition-all duration-300"
                    style={{ width: `${calculateProgressPercentage(formData.status || order.status)}%` }}
                  ></div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'pricing' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {language === 'ar' ? 'السعر الأساسي' : 'Base Price'}
                  </label>
                  <div className="p-3 bg-gray-800 rounded-lg border border-gray-600">
                    <span className="text-white">${(order.base_price || order.unit_price || 0).toFixed(2)}</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {language === 'ar' ? 'سعر الإضافات' : 'Add-ons Price'}
                  </label>
                  <div className="p-3 bg-gray-800 rounded-lg border border-gray-600">
                    <span className="text-white">${(order.addons_price || 0).toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {language === 'ar' ? 'سعر الاشتراك' : 'Subscription Price'}
                  </label>
                  <div className="p-3 bg-gray-800 rounded-lg border border-gray-600">
                    <span className="text-white">${(order.subscription_price || 0).toFixed(2)}</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {language === 'ar' ? 'سعر الصيانة' : 'Maintenance Price'}
                  </label>
                  <div className="p-3 bg-gray-800 rounded-lg border border-gray-600">
                    <span className="text-white">${(order.maintenance_price || 0).toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-gradient-to-r from-secondary/20 to-accent/20 rounded-lg border border-secondary/30">
                <div className="flex items-center justify-between">
                  <span className="text-lg font-semibold text-white">
                    {language === 'ar' ? 'المجموع النهائي' : 'Final Total'}
                  </span>
                  <span className="text-2xl font-bold text-secondary">
                    ${(order.final_price || order.total_price || 0).toFixed(2)}
                  </span>
                </div>
                {order.discount_amount && order.discount_amount > 0 && (
                  <div className="flex items-center justify-between mt-2 text-sm">
                    <span className="text-gray-300">
                      {language === 'ar' ? 'الخصم' : 'Discount'}
                    </span>
                    <span className="text-green-400">
                      -${order.discount_amount.toFixed(2)}
                    </span>
                  </div>
                )}
              </div>

              {/* Order Details */}
              {order.order_details && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {language === 'ar' ? 'تفاصيل الطلب' : 'Order Details'}
                  </label>
                  <div className="p-3 bg-gray-800 rounded-lg border border-gray-600 max-h-40 overflow-y-auto">
                    <pre className="text-xs text-gray-300 whitespace-pre-wrap">
                      {typeof order.order_details === 'string'
                        ? JSON.stringify(JSON.parse(order.order_details), null, 2)
                        : JSON.stringify(order.order_details, null, 2)
                      }
                    </pre>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'timeline' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'الوقت المقدر للإنجاز' : 'Estimated Completion'}
                </label>
                <input
                  type="datetime-local"
                  value={formData.estimated_completion || order.estimated_completion || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, estimated_completion: e.target.value }))}
                  className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-secondary focus:ring-1 focus:ring-secondary"
                />
              </div>

              <div className="space-y-4">
                <h4 className="text-lg font-semibold text-white">
                  {language === 'ar' ? 'الجدول الزمني' : 'Timeline'}
                </h4>

                <div className="space-y-3">
                  {[
                    { key: 'created_at', label: language === 'ar' ? 'تم الإنشاء' : 'Created', value: order.created_at },
                    { key: 'confirmed_at', label: language === 'ar' ? 'تم التأكيد' : 'Confirmed', value: order.confirmed_at },
                    { key: 'started_at', label: language === 'ar' ? 'بدء العمل' : 'Started', value: order.started_at },
                    { key: 'estimated_completion', label: language === 'ar' ? 'الإنجاز المقدر' : 'Est. Completion', value: order.estimated_completion },
                    { key: 'completed_at', label: language === 'ar' ? 'تم الإنجاز' : 'Completed', value: order.completed_at }
                  ].map((item) => (
                    <div key={item.key} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg border border-gray-600">
                      <span className="text-gray-300">{item.label}</span>
                      <span className="text-white">
                        {item.value ? new Date(item.value).toLocaleString(language === 'ar' ? 'ar-EG' : 'en-US') : (language === 'ar' ? 'غير محدد' : 'Not set')}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'notes' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'ملاحظات الإدارة' : 'Admin Notes'}
                </label>
                <textarea
                  value={formData.admin_notes || order.admin_notes || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, admin_notes: e.target.value }))}
                  rows={4}
                  className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-secondary focus:ring-1 focus:ring-secondary resize-none"
                  placeholder={language === 'ar' ? 'أضف ملاحظات إدارية...' : 'Add admin notes...'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'متطلبات العميل' : 'Customer Requirements'}
                </label>
                <div className="p-3 bg-gray-800 rounded-lg border border-gray-600 max-h-40 overflow-y-auto">
                  <p className="text-white text-sm whitespace-pre-wrap">
                    {order.customer_requirements || (language === 'ar' ? 'لا توجد متطلبات محددة' : 'No specific requirements')}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {language === 'ar' ? 'ملاحظات عربية' : 'Arabic Notes'}
                  </label>
                  <div className="p-3 bg-gray-800 rounded-lg border border-gray-600 max-h-32 overflow-y-auto">
                    <p className="text-white text-sm" dir="rtl">
                      {order.notes_ar || (language === 'ar' ? 'لا توجد ملاحظات' : 'No notes')}
                    </p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {language === 'ar' ? 'ملاحظات إنجليزية' : 'English Notes'}
                  </label>
                  <div className="p-3 bg-gray-800 rounded-lg border border-gray-600 max-h-32 overflow-y-auto">
                    <p className="text-white text-sm">
                      {order.notes_en || (language === 'ar' ? 'لا توجد ملاحظات' : 'No notes')}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-4 rtl:space-x-reverse p-6 border-t border-gray-700">
          <Button variant="outline" onClick={onClose}>
            {language === 'ar' ? 'إلغاء' : 'Cancel'}
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            <Save className="w-4 h-4 mr-2" />
            {loading ? (language === 'ar' ? 'جاري الحفظ...' : 'Saving...') : (language === 'ar' ? 'حفظ' : 'Save')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default EnhancedOrderEditor;
