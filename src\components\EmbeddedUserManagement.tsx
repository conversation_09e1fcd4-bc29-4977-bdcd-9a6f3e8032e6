import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { getAllUsers, updateUserRole } from '../lib/apiServices';
import { User } from '../lib/database';
import { Users, Search, Edit3, Shield, User as UserIcon, Crown } from 'lucide-react';

interface EmbeddedUserManagementProps {
  onClose: () => void;
}

const EmbeddedUserManagement: React.FC<EmbeddedUserManagementProps> = ({ onClose }) => {
  const { language } = useTranslation();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    setLoading(true);
    try {
      const result = await getAllUsers();
      if (result.data) {
        setUsers(result.data);
      } else if (result.error) {
        console.error('Error loading users:', result.error);
      }
    } catch (error) {
      console.error('Error loading users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRoleChange = async (userId: string, newRole: 'user' | 'admin') => {
    try {
      const result = await updateUserRole(userId, newRole);
      if (result.data) {
        loadUsers();
        alert(language === 'ar' ? 'تم تحديث الرتبة بنجاح' : 'Role updated successfully');
      } else if (result.error) {
        alert(language === 'ar' ? `فشل في تحديث الرتبة: ${result.error.message}` : `Failed to update role: ${result.error.message}`);
      }
    } catch (error: any) {
      console.error('Error updating user role:', error);
      alert(language === 'ar' ? 'فشل في تحديث الرتبة' : 'Failed to update role');
    }
  };

  const filteredUsers = users.filter(user =>
    user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-secondary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">{language === 'ar' ? 'جاري التحميل...' : 'Loading...'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Users className="w-8 h-8 text-secondary" />
          <h2 className="text-2xl font-bold text-white">{language === 'ar' ? 'إدارة المستخدمين' : 'User Management'}</h2>
        </div>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder={language === 'ar' ? 'البحث عن المستخدمين...' : 'Search users...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 bg-background border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-secondary focus:outline-none"
          />
        </div>
      </div>

      {/* Users List */}
      <div className="space-y-4">
        {filteredUsers.map((user) => (
          <div key={user.id} className="bg-background border border-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <div className="w-12 h-12 bg-gradient-to-r from-secondary to-accent rounded-full flex items-center justify-center">
                  {user.role === 'admin' ? (
                    <Crown className="w-6 h-6 text-white" />
                  ) : (
                    <UserIcon className="w-6 h-6 text-white" />
                  )}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">{user.full_name}</h3>
                  <p className="text-gray-400">{user.email}</p>
                  <p className="text-sm text-gray-500">@{user.username}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  user.role === 'admin' 
                    ? 'bg-red-500/20 text-red-400 border border-red-500/30' 
                    : 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                }`}>
                  {user.role === 'admin' ? (language === 'ar' ? 'مدير' : 'Admin') : (language === 'ar' ? 'مستخدم' : 'User')}
                </span>
                
                <select
                  value={user.role}
                  onChange={(e) => handleRoleChange(user.id, e.target.value as 'user' | 'admin')}
                  className="bg-primary border border-gray-700 rounded-lg px-3 py-2 text-white focus:border-secondary focus:outline-none"
                >
                  <option value="user">{language === 'ar' ? 'مستخدم' : 'User'}</option>
                  <option value="admin">{language === 'ar' ? 'مدير' : 'Admin'}</option>
                </select>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredUsers.length === 0 && (
        <div className="text-center py-8">
          <Users className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <p className="text-gray-400">{language === 'ar' ? 'لا توجد مستخدمين' : 'No users found'}</p>
        </div>
      )}
    </div>
  );
};

export default EmbeddedUserManagement;