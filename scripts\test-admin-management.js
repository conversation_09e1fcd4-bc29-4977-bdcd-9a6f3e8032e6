const axios = require('axios');

async function testAdminManagement() {
  try {
    console.log('🔐 Logging in as admin...');
    
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.data.tokens.accessToken;
    console.log('✅ Login successful!');
    
    const headers = { Authorization: `Bearer ${token}` };
    
    // Test Systems Management
    console.log('\n🖥️ Testing Systems Management...');
    
    // Get all systems
    const systemsResponse = await axios.get('http://localhost:3001/api/systems/admin', { headers });
    console.log(`✅ Retrieved ${systemsResponse.data.length} systems`);
    
    if (systemsResponse.data.length > 0) {
      const firstSystem = systemsResponse.data[0];
      console.log(`   📝 Sample system: ${firstSystem.name_ar} (${firstSystem.id})`);
      console.log(`   💰 Price: $${firstSystem.price}`);
      console.log(`   📊 Status: ${firstSystem.status}`);
      console.log(`   🏷️ Category: ${firstSystem.category}`);
    }
    
    // Test Technical Services Management
    console.log('\n🛠️ Testing Technical Services Management...');
    
    // Get all technical services
    const servicesResponse = await axios.get('http://localhost:3001/api/services/admin/technical', { headers });
    console.log(`✅ Retrieved ${servicesResponse.data.length} technical services`);
    
    if (servicesResponse.data.length > 0) {
      const firstService = servicesResponse.data[0];
      console.log(`   📝 Sample service: ${firstService.name_ar} (${firstService.id})`);
      console.log(`   💰 Price: $${firstService.price}`);
      console.log(`   📊 Status: ${firstService.status}`);
      console.log(`   🏷️ Category: ${firstService.category}`);
      console.log(`   🔧 Service Type: ${firstService.service_type}`);
    }
    
    // Test Premium Content Management
    console.log('\n👑 Testing Premium Content Management...');
    
    const premiumResponse = await axios.get('http://localhost:3001/api/services/premium', { headers });
    const premiumData = premiumResponse.data.data.premiumContent || [];
    console.log(`✅ Retrieved ${premiumData.length} premium content items`);
    
    if (premiumData.length > 0) {
      const firstPremium = premiumData[0];
      console.log(`   📝 Sample premium: ${firstPremium.title_ar} (${firstPremium.id})`);
      console.log(`   💰 Price: $${firstPremium.price}`);
      console.log(`   📊 Status: ${firstPremium.status}`);
      console.log(`   🏷️ Category: ${firstPremium.category}`);
    }
    
    // Test Orders Management
    console.log('\n📦 Testing Orders Management...');
    
    try {
      const ordersResponse = await axios.get('http://localhost:3001/api/orders', { headers });
      console.log('✅ Orders endpoint accessible');
      console.log('   📊 Response structure:', Object.keys(ordersResponse.data));
    } catch (orderError) {
      console.log('⚠️ Orders endpoint issue:', orderError.response?.status || orderError.message);
    }
    
    // Test Users Management
    console.log('\n👥 Testing Users Management...');
    
    try {
      const usersResponse = await axios.get('http://localhost:3001/api/admin/users', { headers });
      console.log('✅ Users management accessible');
      console.log('   📊 Response structure:', Object.keys(usersResponse.data));
    } catch (userError) {
      console.log('⚠️ Users management issue:', userError.response?.status || userError.message);
    }
    
    console.log('\n🎉 Admin management testing completed!');
    console.log('🌐 Visit http://localhost:5173/admin to access the admin panel');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testAdminManagement();
