/**
 * Fix All Admin Components Script
 * 
 * This script fixes data format issues in all admin components
 * to ensure they send data in the correct format expected by the server.
 */

const fs = require('fs');
const path = require('path');

const ADMIN_COMPONENTS_DIR = path.join(__dirname, '..', 'src', 'components', 'admin');

// Common fixes to apply
const COMMON_FIXES = [
  // Fix data format in handleSave functions
  {
    pattern: /const\s+(\w+Data)\s*=\s*{\s*\.\.\.formData[^}]*}/g,
    replacement: (match, varName) => {
      return `const ${varName} = {
        name_ar: formData.name?.ar || '',
        name_en: formData.name?.en || '',
        description_ar: formData.description?.ar || '',
        description_en: formData.description?.en || '',
        features_ar: formData.features?.ar || [],
        features_en: formData.features?.en || [],
        tech_specs_ar: formData.tech_specs?.ar || [],
        tech_specs_en: formData.tech_specs?.en || [],
        price: formData.price || 0,
        category: formData.category || 'general',
        type: formData.type || 'regular',
        status: formData.status || 'active',
        video_url: formData.video_url || '',
        image_url: formData.image_url || '',
        gallery_images: formData.gallery_images || []
      }`;
    }
  },
  
  // Fix field access in filters
  {
    pattern: /service\.name\[language\]/g,
    replacement: 'getServiceText(service, \'name\', language)'
  },
  {
    pattern: /service\.description\[language\]/g,
    replacement: 'getServiceText(service, \'description\', language)'
  },
  {
    pattern: /system\.name\[language\]/g,
    replacement: 'getSystemText(system, \'name\', language)'
  },
  {
    pattern: /system\.description\[language\]/g,
    replacement: 'getSystemText(system, \'description\', language)'
  },
  
  // Fix direct field access
  {
    pattern: /language === 'ar' \? (\w+)\.name_ar : \1\.name_en/g,
    replacement: 'getSystemText($1, \'name\', language)'
  },
  {
    pattern: /language === 'ar' \? (\w+)\.description_ar : \1\.description_en/g,
    replacement: 'getSystemText($1, \'description\', language)'
  }
];

class AdminComponentsFixer {
  constructor() {
    this.fixedFiles = [];
    this.errors = [];
  }

  async fixAllComponents() {
    console.log('🔧 Starting comprehensive admin components fix...\n');
    
    try {
      const files = await this.getAdminComponentFiles();
      
      for (const file of files) {
        await this.fixComponent(file);
      }
      
      this.printResults();
    } catch (error) {
      console.error('❌ Fix failed:', error.message);
      throw error;
    }
  }

  async getAdminComponentFiles() {
    const files = [];
    
    if (fs.existsSync(ADMIN_COMPONENTS_DIR)) {
      const dirFiles = fs.readdirSync(ADMIN_COMPONENTS_DIR);
      
      for (const file of dirFiles) {
        if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          files.push(path.join(ADMIN_COMPONENTS_DIR, file));
        }
      }
    }
    
    return files;
  }

  async fixComponent(filePath) {
    try {
      const fileName = path.basename(filePath);
      console.log(`🔍 Fixing ${fileName}...`);
      
      if (!fs.existsSync(filePath)) {
        console.log(`⚠️  ${fileName}: File not found, skipping`);
        return;
      }
      
      let content = fs.readFileSync(filePath, 'utf8');
      
      if (content.trim().length === 0) {
        console.log(`⚠️  ${fileName}: Empty file, skipping`);
        return;
      }
      
      let modified = false;
      const originalContent = content;
      
      // Apply common fixes
      for (const fix of COMMON_FIXES) {
        if (typeof fix.replacement === 'function') {
          content = content.replace(fix.pattern, fix.replacement);
        } else {
          content = content.replace(fix.pattern, fix.replacement);
        }
      }
      
      // Specific fixes for different components
      if (fileName.includes('Manager')) {
        content = this.fixManagerComponent(content, fileName);
      }
      
      if (fileName.includes('Premium')) {
        content = this.fixPremiumComponent(content, fileName);
      }
      
      if (content !== originalContent) {
        fs.writeFileSync(filePath, content);
        this.fixedFiles.push(fileName);
        console.log(`✅ ${fileName}: Fixed`);
        modified = true;
      } else {
        console.log(`✨ ${fileName}: No changes needed`);
      }
      
    } catch (error) {
      const fileName = path.basename(filePath);
      this.errors.push({ file: fileName, error: error.message });
      console.log(`❌ ${fileName}: Error - ${error.message}`);
    }
  }

  fixManagerComponent(content, fileName) {
    // Fix handleSave functions to use correct data format
    content = content.replace(
      /const handleSave = async \(\) => {[\s\S]*?try {[\s\S]*?const (\w+Data) = {[\s\S]*?};/g,
      (match) => {
        return match.replace(
          /const (\w+Data) = {[\s\S]*?};/,
          `const $1 = {
        name_ar: formData.name?.ar || '',
        name_en: formData.name?.en || '',
        description_ar: formData.description?.ar || '',
        description_en: formData.description?.en || '',
        features_ar: formData.features?.ar || [],
        features_en: formData.features?.en || [],
        tech_specs_ar: formData.tech_specs?.ar || [],
        tech_specs_en: formData.tech_specs?.en || [],
        price: formData.price || 0,
        category: formData.category || 'general',
        type: formData.type || 'regular',
        status: formData.status || 'active',
        video_url: formData.video_url || '',
        image_url: formData.image_url || '',
        gallery_images: formData.gallery_images || []
      };`
        );
      }
    );
    
    // Fix data loading to normalize data
    content = content.replace(
      /setServices\(servicesData\);/g,
      'setServices(servicesData.map(normalizeServiceData));'
    );
    
    content = content.replace(
      /setSystems\(systemsData\);/g,
      'setSystems(systemsData.map(normalizeSystemData));'
    );
    
    return content;
  }

  fixPremiumComponent(content, fileName) {
    // Fix premium content specific issues
    content = content.replace(
      /title\[language\]/g,
      'getContentText(content, \'title\', language)'
    );
    
    return content;
  }

  printResults() {
    console.log('\n📋 Fix Results:');
    console.log('================');
    
    console.log(`✅ Fixed files: ${this.fixedFiles.length}`);
    if (this.fixedFiles.length > 0) {
      this.fixedFiles.forEach(file => console.log(`   - ${file}`));
    }
    
    console.log(`❌ Errors: ${this.errors.length}`);
    if (this.errors.length > 0) {
      this.errors.forEach(({ file, error }) => console.log(`   - ${file}: ${error}`));
    }
    
    if (this.errors.length === 0) {
      console.log('\n🎉 All admin components have been fixed!');
      console.log('\n💡 What was fixed:');
      console.log('- Data format in handleSave functions');
      console.log('- Field access in filters and displays');
      console.log('- Data normalization in load functions');
      console.log('- Safe field access with helper functions');
      
      console.log('\n🧪 Next steps:');
      console.log('1. Test admin dashboard');
      console.log('2. Try creating/updating systems');
      console.log('3. Try creating/updating technical services');
      console.log('4. Test premium content management');
    } else {
      console.log('\n⚠️  Some components had issues. Manual review may be needed.');
    }
  }
}

// Run the fixer
if (require.main === module) {
  const fixer = new AdminComponentsFixer();
  fixer.fixAllComponents().catch(console.error);
}

module.exports = AdminComponentsFixer;