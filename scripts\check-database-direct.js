#!/usr/bin/env node

const mysql = require('mysql2/promise');

async function checkDatabaseDirect() {
  console.log('🔍 Checking Database Directly...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '123456',
      database: 'khan<PERSON><PERSON>riya_db'
    });

    console.log('✅ Connected to MySQL database\n');

    // Check all tables
    console.log('📋 Checking all tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('Available tables:');
    tables.forEach(table => {
      const tableName = Object.values(table)[0];
      console.log(`  - ${tableName}`);
    });

    // Check system_services table
    console.log('\n🖥️ Checking system_services table...');
    try {
      const [systemServices] = await connection.execute('SELECT * FROM system_services');
      console.log(`Found ${systemServices.length} system services:`);
      systemServices.forEach((service, index) => {
        console.log(`  ${index + 1}. ${service.name_ar} - $${service.price} (${service.status})`);
      });
    } catch (error) {
      console.log('❌ Error with system_services:', error.message);
    }

    // Check technical_services table
    console.log('\n🔧 Checking technical_services table...');
    try {
      const [technicalServices] = await connection.execute('SELECT * FROM technical_services');
      console.log(`Found ${technicalServices.length} technical services:`);
      technicalServices.forEach((service, index) => {
        console.log(`  ${index + 1}. ${service.name_ar || service.title_ar} - $${service.price} (${service.status})`);
      });
    } catch (error) {
      console.log('❌ Error with technical_services:', error.message);
    }

    // Check premium_content table
    console.log('\n⭐ Checking premium_content table...');
    try {
      const [premiumContent] = await connection.execute('SELECT * FROM premium_content');
      console.log(`Found ${premiumContent.length} premium content items:`);
      premiumContent.forEach((item, index) => {
        console.log(`  ${index + 1}. ${item.title_ar} - $${item.price} (${item.status})`);
      });
    } catch (error) {
      console.log('❌ Error with premium_content:', error.message);
    }

    // Check premium_packages table
    console.log('\n📦 Checking premium_packages table...');
    try {
      const [premiumPackages] = await connection.execute('SELECT * FROM premium_packages');
      console.log(`Found ${premiumPackages.length} premium packages:`);
      premiumPackages.forEach((pkg, index) => {
        console.log(`  ${index + 1}. ${pkg.name_ar} - $${pkg.price} (${pkg.status})`);
      });
    } catch (error) {
      console.log('❌ Error with premium_packages:', error.message);
    }

    // Check users table
    console.log('\n👥 Checking users table...');
    try {
      const [users] = await connection.execute('SELECT id, email, full_name, role, status FROM users');
      console.log(`Found ${users.length} users:`);
      users.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.full_name} (${user.email}) - ${user.role} (${user.status})`);
      });
    } catch (error) {
      console.log('❌ Error with users:', error.message);
    }

    // Check orders table
    console.log('\n📋 Checking orders table...');
    try {
      const [orders] = await connection.execute('SELECT * FROM orders LIMIT 5');
      console.log(`Found orders (showing first 5):`);
      orders.forEach((order, index) => {
        console.log(`  ${index + 1}. Order #${order.id} - $${order.total_amount} (${order.status})`);
      });
    } catch (error) {
      console.log('❌ Error with orders:', error.message);
    }

    await connection.end();

    console.log('\n📊 Summary:');
    console.log('✅ Database connection: Working');
    console.log('✅ Tables exist: Confirmed');
    console.log('⚠️ Issue: API endpoints may not be querying correctly');

    console.log('\n🔧 Next Steps:');
    console.log('1. Check API endpoint implementations');
    console.log('2. Verify SQL queries in backend code');
    console.log('3. Check for table name mismatches');
    console.log('4. Verify data filtering logic');

  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.log('\n💡 Troubleshooting:');
    console.log('1. Check if MySQL server is running');
    console.log('2. Verify database credentials');
    console.log('3. Ensure database "khanfashariya_db" exists');
    console.log('4. Check firewall settings');
  }
}

checkDatabaseDirect();
