import React from 'react';
import { LucideIcon, Loader2 } from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';

interface UnifiedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success' | 'warning' | 'info';
  size?: 'sm' | 'md' | 'lg';
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  fullWidth?: boolean;
  href?: string;
  target?: string;
  ariaLabel?: string;
  ariaLabelAr?: string;
}

/**
 * Unified Button Component
 * Single source of truth for all buttons in the project
 * No hardcoded text or colors, fully accessible and responsive
 */
const UnifiedButton: React.FC<UnifiedButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  icon: Icon,
  iconPosition = 'left',
  disabled = false,
  loading = false,
  className = '',
  type = 'button',
  fullWidth = false,
  href,
  target,
  ariaLabel,
  ariaLabelAr
}) => {
  const { language } = useTranslation();

  const baseClasses = `
    inline-flex items-center justify-center font-semibold transition-all duration-300 
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900
    disabled:opacity-50 disabled:cursor-not-allowed
    border rounded-lg
    ${fullWidth ? 'w-full' : ''}
    ${loading ? 'cursor-wait' : ''}
  `;

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm min-h-[36px]',
    md: 'px-4 py-2.5 text-base min-h-[44px]',
    lg: 'px-6 py-3 text-lg min-h-[52px]'
  };

  const variantClasses = {
    primary: `
      bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800
      text-white border-blue-500/20 hover:border-blue-400/30
      shadow-lg hover:shadow-xl focus:ring-blue-500/50
      transform hover:-translate-y-0.5 active:translate-y-0
    `,
    secondary: `
      bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800
      text-white border-purple-500/20 hover:border-purple-400/30
      shadow-lg hover:shadow-xl focus:ring-purple-500/50
      transform hover:-translate-y-0.5 active:translate-y-0
    `,
    outline: `
      bg-gray-800/60 hover:bg-gray-700/80 backdrop-blur-sm
      text-gray-100 hover:text-white border-gray-400 hover:border-gray-300
      shadow-md hover:shadow-lg focus:ring-gray-400/50
      transform hover:-translate-y-0.5 active:translate-y-0
    `,
    ghost: `
      bg-transparent hover:bg-gray-700/50
      text-gray-300 hover:text-white border-transparent hover:border-gray-600
      focus:ring-gray-500/50
    `,
    danger: `
      bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800
      text-white border-red-500/20 hover:border-red-400/30
      shadow-lg hover:shadow-xl focus:ring-red-500/50
      transform hover:-translate-y-0.5 active:translate-y-0
    `,
    success: `
      bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800
      text-white border-emerald-500/20 hover:border-emerald-400/30
      shadow-lg hover:shadow-xl focus:ring-emerald-500/50
      transform hover:-translate-y-0.5 active:translate-y-0
    `,
    warning: `
      bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800
      text-black border-yellow-500/20 hover:border-yellow-400/30
      shadow-lg hover:shadow-xl focus:ring-yellow-500/50
      transform hover:-translate-y-0.5 active:translate-y-0
    `,
    info: `
      bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-700 hover:to-blue-700
      text-white border-cyan-500/20 hover:border-cyan-400/30
      shadow-lg hover:shadow-xl focus:ring-cyan-500/50
      transform hover:-translate-y-0.5 active:translate-y-0
    `
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  const isDisabled = disabled || loading;
  const finalAriaLabel = language === 'ar' && ariaLabelAr ? ariaLabelAr : ariaLabel;

  const buttonContent = (
    <>
      {loading && (
        <Loader2 className={`${iconSizes[size]} animate-spin ${children ? 'mr-2 rtl:mr-0 rtl:ml-2' : ''}`} />
      )}
      {!loading && Icon && iconPosition === 'left' && (
        <Icon className={`${iconSizes[size]} ${children ? 'mr-2 rtl:mr-0 rtl:ml-2' : ''} ${language === 'ar' ? 'rtl:rotate-180' : ''}`} />
      )}
      {children && <span>{children}</span>}
      {!loading && Icon && iconPosition === 'right' && (
        <Icon className={`${iconSizes[size]} ${children ? 'ml-2 rtl:ml-0 rtl:mr-2' : ''} ${language === 'ar' ? 'rtl:rotate-180' : ''}`} />
      )}
    </>
  );

  const buttonClasses = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`;

  if (href) {
    return (
      <a
        href={href}
        target={target}
        className={buttonClasses}
        aria-label={finalAriaLabel}
        role="button"
      >
        {buttonContent}
      </a>
    );
  }

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={isDisabled}
      className={buttonClasses}
      aria-label={finalAriaLabel}
      aria-disabled={isDisabled}
    >
      {buttonContent}
    </button>
  );
};

export default UnifiedButton;
