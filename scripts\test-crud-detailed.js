const axios = require('axios');

async function testCRUDDetailed() {
  console.log('🔧 اختبار CRUD مفصل\n');
  
  try {
    // تسجيل الدخول
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.data.tokens.accessToken;
    const headers = { Authorization: `Bearer ${token}` };
    
    console.log('✅ تم تسجيل الدخول');
    
    // اختبار إنشاء نظام
    console.log('\n📋 اختبار إنشاء نظام...');
    const systemData = {
      name_ar: 'نظام اختبار مفصل',
      name_en: 'Detailed Test System',
      description_ar: 'وصف مفصل للنظام',
      description_en: 'Detailed system description',
      price: 99.99,
      category: 'general',
      type: 'regular',
      features_ar: ['ميزة 1'],
      features_en: ['Feature 1'],
      tech_specs_ar: ['مواصفة 1'],
      tech_specs_en: ['Spec 1'],
      status: 'active'
    };
    
    const createSystemResponse = await axios.post('http://localhost:3001/api/admin/systems', systemData, { headers });
    console.log('📄 استجابة إنشاء النظام:', JSON.stringify(createSystemResponse.data, null, 2));
    
    // اختبار إنشاء خدمة
    console.log('\n🛠️ اختبار إنشاء خدمة...');
    const serviceData = {
      name_ar: 'خدمة اختبار مفصلة',
      name_en: 'Detailed Test Service',
      description_ar: 'وصف مفصل للخدمة',
      description_en: 'Detailed service description',
      price: 199.99,
      category: 'general',
      service_type: 'development',
      features_ar: ['ميزة خدمة 1'],
      features_en: ['Service Feature 1'],
      tech_specs_ar: ['مواصفة خدمة 1'],
      tech_specs_en: ['Service Spec 1'],
      is_premium_addon: true,
      premium_price: 299.99,
      subscription_type: 'monthly',
      delivery_time_ar: '7 أيام',
      delivery_time_en: '7 days',
      status: 'active'
    };
    
    const createServiceResponse = await axios.post('http://localhost:3001/api/admin/technical-services', serviceData, { headers });
    console.log('📄 استجابة إنشاء الخدمة:', JSON.stringify(createServiceResponse.data, null, 2));
    
    // اختبار قراءة البيانات
    console.log('\n📊 اختبار قراءة البيانات...');
    const systemsResponse = await axios.get('http://localhost:3001/api/systems/admin', { headers });
    const servicesResponse = await axios.get('http://localhost:3001/api/services/admin/technical', { headers });
    
    console.log('📈 عدد الأنظمة:', systemsResponse.data.length);
    console.log('📈 عدد الخدمات:', servicesResponse.data.length);
    
    if (systemsResponse.data.length > 0) {
      console.log('🔍 النظام الأول:', systemsResponse.data[0].name_ar);
    }
    
    if (servicesResponse.data.length > 0) {
      console.log('🔍 الخدمة الأولى:', servicesResponse.data[0].name_ar);
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error.response?.data || error.message);
  }
}

testCRUDDetailed();
