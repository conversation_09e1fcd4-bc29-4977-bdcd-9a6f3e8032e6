/*
 Navicat Premium Data Transfer

 Source Server         : LOCALHOST_WAMP
 Source Server Type    : MySQL
 Source Server Version : 50728
 Source Host           : localhost:3306
 Source Schema         : khan<PERSON><PERSON>riya_db

 Target Server Type    : MySQL
 Target Server Version : 50728
 File Encoding         : 65001

 Date: 25/07/2025 01:55:04
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for activity_logs
-- ----------------------------
DROP TABLE IF EXISTS `activity_logs`;
CREATE TABLE `activity_logs`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `action` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `entity_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `entity_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `details` json NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of activity_logs
-- ----------------------------

-- ----------------------------
-- Table structure for contact_messages
-- ----------------------------
DROP TABLE IF EXISTS `contact_messages`;
CREATE TABLE `contact_messages`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `subject` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `message_type` enum('general','support','sales','technical','complaint','suggestion') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'general',
  `status` enum('new','read','replied','resolved','closed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'new',
  `priority` enum('low','normal','high','urgent') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal',
  `admin_reply` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `admin_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `replied_at` timestamp NULL DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `idx_email`(`email`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_priority`(`priority`) USING BTREE,
  INDEX `idx_message_type`(`message_type`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of contact_messages
-- ----------------------------

-- ----------------------------
-- Table structure for file_uploads
-- ----------------------------
DROP TABLE IF EXISTS `file_uploads`;
CREATE TABLE `file_uploads`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_size` bigint(20) NOT NULL,
  `mime_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_type` enum('image','video','document','archive','other') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `entity_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `entity_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `is_public` tinyint(1) NULL DEFAULT 0,
  `download_count` int(11) NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_file_type`(`file_type`) USING BTREE,
  INDEX `idx_entity_type`(`entity_type`) USING BTREE,
  INDEX `idx_entity_id`(`entity_id`) USING BTREE,
  INDEX `idx_is_public`(`is_public`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of file_uploads
-- ----------------------------

-- ----------------------------
-- Table structure for inbox_messages
-- ----------------------------
DROP TABLE IF EXISTS `inbox_messages`;
CREATE TABLE `inbox_messages`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `sender_type` enum('admin','system') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'admin',
  `sender_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `subject_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `message_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `message_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `message_type` enum('info','warning','success','error','order_update','system_notification') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'info',
  `priority` enum('low','normal','high','urgent') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal',
  `is_read` tinyint(1) NULL DEFAULT 0,
  `read_at` timestamp NULL DEFAULT NULL,
  `action_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `action_label_ar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `action_label_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of inbox_messages
-- ----------------------------

-- ----------------------------
-- Table structure for orders
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `order_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `order_type` enum('system_service','technical_service','premium_content','premium_package') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_name_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `item_name_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `final_price` decimal(10, 2) NOT NULL,
  `status` enum('pending','completed','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `quantity` int(11) NULL DEFAULT 1,
  `unit_price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `total_price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10, 2) NULL DEFAULT 0.00,
  `currency` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'USD',
  `payment_status` enum('pending','paid','failed','refunded') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending',
  `payment_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `payment_reference` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `notes_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `notes_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `admin_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `delivery_date` date NULL DEFAULT NULL,
  `completion_date` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_number`(`order_number`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of orders
-- ----------------------------

-- ----------------------------
-- Table structure for premium_content
-- ----------------------------
DROP TABLE IF EXISTS `premium_content`;
CREATE TABLE `premium_content`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `title_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `title_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `features_ar` json NULL,
  `features_en` json NULL,
  `video_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `gallery_images` json NULL,
  `included_systems` json NULL,
  `included_services` json NULL,
  `status` enum('active','inactive','draft') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active',
  `featured` tinyint(1) NULL DEFAULT 1,
  `sort_order` int(11) NULL DEFAULT 0,
  `purchase_count` int(11) NULL DEFAULT 0,
  `rating` decimal(3, 2) NULL DEFAULT 0.00,
  `rating_count` int(11) NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `detailed_description_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `detailed_description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `original_price` decimal(10, 2) NULL DEFAULT NULL,
  `discount_percentage` int(11) NULL DEFAULT 0,
  `tech_specs_ar` json NULL,
  `tech_specs_en` json NULL,
  `is_active_edition` tinyint(1) NULL DEFAULT 0 COMMENT 'Only one premium edition can be active at a time',
  `installation_guide_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `installation_guide_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `support_info_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `support_info_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of premium_content
-- ----------------------------
INSERT INTO `premium_content` VALUES ('premium_complete_package', 'الحزمة المميزة الشاملة', 'Complete Premium Package', 'نسخة شاملة تحتوي على جميع الأنظمة ', 'A comprehensive version containing all systems and files', 1999.00, 'complete_package', '[\"ميزات عربية\", \"ميزات عربية ميزات عربية\", \"ميزات عربية ميزات عربية\", \"ميزات عربية ميزات عربية\", \"ميزات عربية\"]', '[\"ميزات انجليزية\", \"ميزات انجليزية ميزات انجليزية\", \"ميزات انجليزية ميزات انجليزية\", \"ميزات انجليزية ميزات انجليزية\", \"ميزات انجليزية\"]', 'https://www.youtube.com/watch?v=8Lv9qRvYu4c', 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT40kAHllkcjmqOpqo6NPOiQX-p-E0tTNLgYA&s', '[]', '[]', '[]', 'active', 1, 0, 0, 0.00, 0, '2025-07-16 21:08:45', '2025-07-24 06:23:07', 'وصف مفصل: نسخة شاملة تحتوي على جميع الأنظمة والملفات', 'Detailed description: A comprehensive version containing all systems and files', 2500.00, 0, '[\"متوافق مع Metin2\", \"قاعدة بيانات MySQL\", \"PHP 7.4+\", \"خادم Linux\"]', '[\"Compatible with Metin2\", \"MySQL Database\", \"PHP 7.4+\", \"Linux Server\"]', 1, 'دليل التنصيب متوفر مع الحزمة', 'Installation guide included with package', 'دعم فني متاح 24/7', '24/7 technical support available');
INSERT INTO `premium_content` VALUES ('premium_advanced_systems', 'حزمة الأنظمة المتقدمة', 'Advanced Systems Package', 'مجموعة من أقوى الأنظمة المتقدمة للخوادم الاحترافية', 'Collection of the most powerful advanced systems for professional servers', 999.00, 'systems_package', '[]', '[]', NULL, NULL, '[]', '[]', '[]', 'active', 0, 2, 0, 0.00, 0, '2025-07-21 01:57:26', '2025-07-24 05:40:24', 'وصف مفصل: مجموعة من أقوى الأنظمة المتقدمة للخوادم الاحترافية', 'Detailed description: Collection of the most powerful advanced systems for professional servers', NULL, 0, '[\"متوافق مع Metin2\", \"قاعدة بيانات MySQL\", \"PHP 7.4+\", \"خادم Linux\"]', '[\"Compatible with Metin2\", \"MySQL Database\", \"PHP 7.4+\", \"Linux Server\"]', 0, 'دليل التنصيب متوفر مع الحزمة', 'Installation guide included with package', 'دعم فني متاح 24/7', '24/7 technical support available');
INSERT INTO `premium_content` VALUES ('premium_technical_services', 'حزمة الخدمات التقنية المميزة', 'Premium Technical Services Package', 'خدمات تقنية شاملة مع دعم مستمر وصيانة دورية', 'Comprehensive technical services with continuous support and regular maintenance', 1499.00, 'services_package', '[]', '[]', NULL, NULL, '[]', '[]', '[]', 'active', 0, 3, 0, 0.00, 0, '2025-07-21 01:57:26', '2025-07-24 05:40:24', 'وصف مفصل: خدمات تقنية شاملة مع دعم مستمر وصيانة دورية', 'Detailed description: Comprehensive technical services with continuous support and regular maintenance', NULL, 0, '[\"متوافق مع Metin2\", \"قاعدة بيانات MySQL\", \"PHP 7.4+\", \"خادم Linux\"]', '[\"Compatible with Metin2\", \"MySQL Database\", \"PHP 7.4+\", \"Linux Server\"]', 0, 'دليل التنصيب متوفر مع الحزمة', 'Installation guide included with package', 'دعم فني متاح 24/7', '24/7 technical support available');

-- ----------------------------
-- Table structure for premium_service_pricing
-- ----------------------------
DROP TABLE IF EXISTS `premium_service_pricing`;
CREATE TABLE `premium_service_pricing`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `service_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `premium_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT 'Special price for premium edition customers',
  `installation_included` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Installation included in premium price',
  `maintenance_included` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Maintenance included in premium price',
  `is_available_for_premium` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Available as premium add-on',
  `subscription_discount_percentage` int(11) NULL DEFAULT 0 COMMENT 'Discount percentage for subscription services',
  `description_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_service_pricing`(`service_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of premium_service_pricing
-- ----------------------------
INSERT INTO `premium_service_pricing` VALUES ('02117573-ef48-4e1f-85bb-12506b2f57af', 'yearlySupport', 300.00, 1, 1, 1, 25, 'سعر مخصص للنسخة المميزة: دعم فني سنوي متقدم', 'Premium edition special price: Advanced Annual Technical Support', '2025-07-24 05:43:43', '2025-07-24 05:43:43');
INSERT INTO `premium_service_pricing` VALUES ('55bfc259-1a62-4592-8509-a52bad65c595', 'freebsd', 150.00, 1, 0, 1, 0, 'سعر مخصص للنسخة المميزة: إعداد خوادم FreeBSD المحسنة', 'Premium edition special price: Optimized FreeBSD Server Setup', '2025-07-24 05:43:43', '2025-07-24 05:43:43');
INSERT INTO `premium_service_pricing` VALUES ('81b7ae84-63d0-4b2c-bfb2-c9a95208662a', 'monthlyMaintenance', 30.00, 1, 1, 1, 25, 'سعر مخصص للنسخة المميزة: صيانة شهرية للخوادم', 'Premium edition special price: Monthly Server Maintenance', '2025-07-24 05:43:43', '2025-07-24 05:43:43');

-- ----------------------------
-- Table structure for premium_system_pricing
-- ----------------------------
DROP TABLE IF EXISTS `premium_system_pricing`;
CREATE TABLE `premium_system_pricing`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `system_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `premium_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT 'Special price for premium edition customers',
  `installation_included` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Installation included in premium price',
  `maintenance_included` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Maintenance included in premium price',
  `is_available_for_premium` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Available as premium add-on',
  `description_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_system_pricing`(`system_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of premium_system_pricing
-- ----------------------------
INSERT INTO `premium_system_pricing` VALUES ('0297a182-c698-497a-8aed-2b15f9d9765e', 'guildWar', 209.00, 1, 1, 1, 'سعر مخصص للنسخة المميزة: نظام حروب الروابط التلقائي', 'Premium edition special price: Automatic Guild War System', '2025-07-24 05:43:43', '2025-07-24 05:43:43');
INSERT INTO `premium_system_pricing` VALUES ('1b2388ef-6cd5-4fbe-8fe5-ea106da912cc', 'quickSwitch', 136.00, 1, 1, 1, 'سعر مخصص للنسخة المميزة: اسم عربي', 'Premium edition special price: Quick Tool Switch System', '2025-07-24 05:43:43', '2025-07-24 06:25:11');
INSERT INTO `premium_system_pricing` VALUES ('6e281b20-b154-467d-970f-c86a56be8068', 'petLevel', 209.00, 1, 1, 1, 'سعر مخصص للنسخة المميزة: نظام الرفيق المتطور', 'Premium edition special price: Advanced Companion System', '2025-07-24 05:43:43', '2025-07-24 05:43:43');
INSERT INTO `premium_system_pricing` VALUES ('d882d5fd-342c-4d6a-95b3-2c10c1f39591', '89e9803e-d3e4-4244-9692-a6b7c51df8df', 1010.00, 1, 1, 1, 'سعر مخصص للنسخة المميزة: جديد', 'Premium edition special price: 414141', '2025-07-24 10:07:33', '2025-07-24 10:07:33');

-- ----------------------------
-- Table structure for settings
-- ----------------------------
DROP TABLE IF EXISTS `settings`;
CREATE TABLE `settings`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `setting_type` enum('string','number','boolean','json') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'string',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `setting_key`(`setting_key`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of settings
-- ----------------------------

-- ----------------------------
-- Table structure for subscriptions
-- ----------------------------
DROP TABLE IF EXISTS `subscriptions`;
CREATE TABLE `subscriptions`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `package_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `subscription_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('active','expired','cancelled','suspended') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active',
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `auto_renew` tinyint(1) NULL DEFAULT 0,
  `downloads_used` int(11) NULL DEFAULT 0,
  `max_downloads` int(11) NULL DEFAULT -1,
  `last_activity` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `subscription_number`(`subscription_number`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `package_id`(`package_id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of subscriptions
-- ----------------------------

-- ----------------------------
-- Table structure for system_services
-- ----------------------------
DROP TABLE IF EXISTS `system_services`;
CREATE TABLE `system_services`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `type` enum('regular','plugin') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'regular',
  `is_premium_addon` tinyint(1) NULL DEFAULT 0,
  `features_ar` json NULL,
  `features_en` json NULL,
  `tech_specs_ar` json NULL,
  `tech_specs_en` json NULL,
  `video_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `gallery_images` json NULL,
  `download_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `file_size` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `requirements_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `requirements_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `installation_guide_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `installation_guide_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `featured` tinyint(1) NULL DEFAULT 0,
  `sort_order` int(11) NULL DEFAULT 0,
  `download_count` int(11) NULL DEFAULT 0,
  `rating` decimal(3, 2) NULL DEFAULT 0.00,
  `rating_count` int(11) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of system_services
-- ----------------------------
INSERT INTO `system_services` VALUES ('guildWar', 'نظام حروب الروابط التلقائي', 'Automatic Guild War System', 'نظام حروب الروابط التلقائي مع ذكاء اصطناعي متطور لإدارة المعارك والتحكم في الاستراتيجيات', 'Automatic Guild War system with advanced AI for battle management and strategic control', 299.00, 'combat', 'active', '2025-07-16 21:08:45', '2025-07-22 03:35:59', 'regular', 1, '[\"نظام حروب الروابط التلقائي\", \"واجهة سهلة الاستخدام\", \"إحصائيات مفصلة\", \"نظام مكافآت متقدم\", \"دعم متعدد الخوادم\"]', '[\"Automatic guild war system\", \"Easy-to-use interface\", \"Detailed statistics\", \"Advanced reward system\", \"Multi-server support\"]', '[\"متوافق مع Metin2\", \"قاعدة بيانات MySQL\", \"PHP 7.4 أو أحدث\", \"خادم Linux مُوصى به\"]', '[\"Compatible with Metin2\", \"MySQL database\", \"PHP 7.4 or newer\", \"Linux server recommended\"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg', '[]', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0.00, 0);
INSERT INTO `system_services` VALUES ('petLevel', 'نظام الرفيق المتطور', 'Advanced Companion System', 'نظام رفيق متطور مع تقنيات التطوير الذاتي والذكاء التكيفي', 'Advanced companion system with self-development technologies and adaptive intelligence', 299.00, 'gameplay', 'active', '2025-07-16 21:08:45', '2025-07-24 10:11:06', 'regular', 1, '[\"نظام رفيق متطور\", \"مستويات متعددة للرفيق\", \"مهارات خاصة\", \"نظام تطوير تلقائي\", \"واجهة تحكم متقدمة\"]', '[\"Advanced pet system\", \"Multiple pet levels\", \"Special skills\", \"Automatic development system\", \"Advanced control interface\"]', '[\"متوافق مع Metin2\", \"قاعدة بيانات MySQL\", \"C++ للخادم\", \"Python للأدوات\"]', '[\"Compatible with Metin2\", \"MySQL database\", \"C++ for server\", \"Python for tools\"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'https://images.pexels.com/photos/163036/mario-luigi-yoschi-figures-163036.jpeg', '[]', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0.00, 0);
INSERT INTO `system_services` VALUES ('testInactive', 'نظام اختبار غير نشط', 'Inactive Test System', 'هذا نظام للاختبار وهو غير نشط حالياً', 'This is a test system that is currently inactive', 199.00, 'testing', 'active', '2025-07-22 03:42:26', '2025-07-24 10:11:14', 'plugin', 1, '[\"ميزة اختبار 1\", \"ميزة اختبار 2\", \"ميزة اختبار 3\"]', '[\"Test feature 1\", \"Test feature 2\", \"Test feature 3\"]', '[\"متطلب تقني 1\", \"متطلب تقني 2\"]', '[\"Technical requirement 1\", \"Technical requirement 2\"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg', '[]', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0.00, 0);
INSERT INTO `system_services` VALUES ('89e9803e-d3e4-4244-9692-a6b7c51df8df', 'جديد', '414141', '101010', '10101010', 1010.00, 'general', 'active', '2025-07-24 10:07:12', '2025-07-24 10:07:12', 'regular', 1, '[\"4141\"]', '[\"414141\"]', '[\"414141\"]', '[\"414141\"]', '', '', '[]', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0.00, 0);
INSERT INTO `system_services` VALUES ('quickSwitch', 'اسم عربي', 'Quick Tool Switch System', 'وصف عربي وصف عربيوصف عربيوصف عربيوصف عربيوصف عربيوصف عربيوصف عربيوصف عربيوصف عربيوصف عربيوصف عربي', 'Instant tool switching system with fast response technologies and adaptive memory', 199.00, 'utility', 'active', '2025-07-20 00:14:58', '2025-07-24 10:08:37', 'plugin', 1, '[\"واجهة سهلة الاستخدام\", \"أداء عالي\", \"دعم متعدد اللغات\", \"تحديثات مجانية\"]', '[\"User-friendly interface\", \"High performance\", \"Multi-language support\", \"Free updates\"]', '[\"مواصفات تقنية عربي\", \"مواصفات تقنية عربي\", \"مواصفات تقنية عربي\", \"مواصفات تقنية عربي\"]', '[\"مواصفات تقنية انجليزي\", \"مواصفات تقنية انجليزي\", \"مواصفات تقنية انجليزي\", \"مواصفات تقنية انجليزي\"]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg', '[\"https://cdn.akamai.steamstatic.com/steamcommunity/public/images/clans/28685211/17ec25d3979f027e63447454c4856f9e863fcc9e.png\", \"https://cdn.akamai.steamstatic.com/steamcommunity/public/images/clans/28685211/17ec25d3979f027e63447454c4856f9e863fcc9e.png\", \"https://cdn.akamai.steamstatic.com/steamcommunity/public/images/clans/28685211/17ec25d3979f027e63447454c4856f9e863fcc9e.png\", \"https://cdn.akamai.steamstatic.com/steamcommunity/public/images/clans/28685211/17ec25d3979f027e63447454c4856f9e863fcc9e.png\", \"https://cdn.akamai.steamstatic.com/steamcommunity/public/images/clans/28685211/17ec25d3979f027e63447454c4856f9e863fcc9e.png\", \"https://cdn.akamai.steamstatic.com/steamcommunity/public/images/clans/28685211/17ec25d3979f027e63447454c4856f9e863fcc9e.png\", \"https://cdn.akamai.steamstatic.com/steamcommunity/public/images/clans/28685211/17ec25d3979f027e63447454c4856f9e863fcc9e.png\", \"https://cdn.akamai.steamstatic.com/steamcommunity/public/images/clans/28685211/17ec25d3979f027e63447454c4856f9e863fcc9e.png\", \"https://cdn.akamai.steamstatic.com/steamcommunity/public/images/clans/28685211/17ec25d3979f027e63447454c4856f9e863fcc9e.png\", \"https://cdn.akamai.steamstatic.com/steamcommunity/public/images/clans/28685211/17ec25d3979f027e63447454c4856f9e863fcc9e.png\"]', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0.00, 0);

-- ----------------------------
-- Table structure for technical_services
-- ----------------------------
DROP TABLE IF EXISTS `technical_services`;
CREATE TABLE `technical_services`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00,
  `category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'service',
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `service_type` enum('development','consultation','support','customization') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'development',
  `is_premium_addon` tinyint(1) NULL DEFAULT 0,
  `premium_price` decimal(10, 2) NULL DEFAULT 0.00,
  `subscription_type` enum('none','monthly','yearly') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'none',
  `features_ar` json NULL,
  `features_en` json NULL,
  `tech_specs_ar` json NULL,
  `tech_specs_en` json NULL,
  `delivery_time_ar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `delivery_time_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `video_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `gallery_images` json NULL,
  `requirements_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `requirements_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `process_steps_ar` json NULL,
  `process_steps_en` json NULL,
  `featured` tinyint(1) NULL DEFAULT 0,
  `sort_order` int(11) NULL DEFAULT 0,
  `order_count` int(11) NULL DEFAULT 0,
  `rating` decimal(3, 2) NULL DEFAULT 0.00,
  `rating_count` int(11) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of technical_services
-- ----------------------------
INSERT INTO `technical_services` VALUES ('freebsd', 'إعداد خوادم FreeBSD المحسنة', 'Optimized FreeBSD Server Setup', 'إعداد وتحصين خوادم FreeBSD للحماية القصوى', 'Setup and hardening of FreeBSD servers for maximum protection', 250.00, 'infrastructure', 'service', 'active', '2025-07-16 21:08:45', '2025-07-22 03:35:59', 'development', 1, 0.00, 'none', '[\"إعداد نظام FreeBSD محسن للأداء\", \"تكوين جدار حماية متقدم\", \"تحسين إعدادات الشبكة\", \"تثبيت أدوات المراقبة\", \"إعداد النسخ الاحتياطي التلقائي\"]', '[\"Optimized FreeBSD system setup\", \"Advanced firewall configuration\", \"Network settings optimization\", \"Monitoring tools installation\", \"Automatic backup setup\"]', '[\"دعم FreeBSD 13.x أو أحدث\", \"ذاكرة وصول عشوائي 4GB كحد أدنى\", \"مساحة تخزين 50GB\", \"اتصال إنترنت مستقر\"]', '[\"FreeBSD 13.x or newer support\", \"Minimum 4GB RAM\", \"50GB storage space\", \"Stable internet connection\"]', '3-5 أيام عمل', '3-5 business days', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'https://images.pexels.com/photos/1181675/pexels-photo-1181675.jpeg', '[]', NULL, NULL, NULL, NULL, 0, 0, 0, 0.00, 0);
INSERT INTO `technical_services` VALUES ('monthlyMaintenance', 'صيانة شهرية للخوادم', 'Monthly Server Maintenance', 'خدمة صيانة شهرية شاملة للخوادم مع مراقبة مستمرة وتحديثات أمنية', 'Comprehensive monthly server maintenance with continuous monitoring and security updates', 50.00, 'maintenance', 'service', 'active', '2025-07-16 21:08:45', '2025-07-22 03:35:59', 'development', 1, 30.00, 'monthly', '[\"مراقبة الخادم على مدار الساعة\", \"تحديثات أمنية شهرية\", \"تنظيف ملفات النظام\", \"فحص الأداء والتحسين\", \"تقارير شهرية مفصلة\"]', '[\"24/7 server monitoring\", \"Monthly security updates\", \"System file cleanup\", \"Performance check and optimization\", \"Detailed monthly reports\"]', '[\"متوافق مع جميع أنظمة Linux\", \"وصول SSH مطلوب\", \"صلاحيات root أو sudo\", \"اتصال إنترنت مستمر\"]', '[\"Compatible with all Linux systems\", \"SSH access required\", \"Root or sudo privileges\", \"Continuous internet connection\"]', 'خدمة مستمرة', 'Continuous service', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'https://images.pexels.com/photos/1181677/pexels-photo-1181677.jpeg', '[]', NULL, NULL, NULL, NULL, 0, 0, 0, 0.00, 0);
INSERT INTO `technical_services` VALUES ('yearlySupport', 'دعم فني سنوي متقدم', 'Advanced Annual Technical Support', 'دعم فني متقدم على مدار السنة مع أولوية في الاستجابة وحلول مخصصة', 'Advanced technical support throughout the year with priority response and custom solutions', 500.00, 'support', 'regular', 'inactive', '2025-07-16 21:08:45', '2025-07-24 10:20:16', 'development', 1, 300.00, 'yearly', '[\"دعم فني متقدم على مدار السنة\", \"أولوية في الاستجابة\", \"حلول مخصصة للمشاكل\", \"استشارات تقنية مجانية\", \"دعم عبر الهاتف والبريد\"]', '[\"Advanced technical support all year\", \"Priority response\", \"Custom problem solutions\", \"Free technical consultations\", \"Phone and email support\"]', '[\"تغطية جميع الخدمات المشتراة\", \"وقت استجابة أقل من 4 ساعات\", \"دعم باللغتين العربية والإنجليزية\", \"تقارير دورية عن الحالة\"]', '[\"Coverage for all purchased services\", \"Response time under 4 hours\", \"Support in Arabic and English\", \"Regular status reports\"]', 'فوري', 'Immediate', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'https://images.pexels.com/photos/1181679/pexels-photo-1181679.jpeg', '[]', NULL, NULL, NULL, NULL, 0, 0, 0, 0.00, 0);
INSERT INTO `technical_services` VALUES ('7844f8d6-4491-4879-a069-ab2147722c78', 'اسم', 'اسم', 'وصف', 'وصف', 10.00, 'database', 'service', 'active', '2025-07-25 00:01:37', '2025-07-25 00:01:37', 'development', 0, 0.00, 'none', '[\"ميزة\"]', '[]', '[]', '[]', '', '', 'https://www.youtube.com/watch?v=b0s0f5g-lz4', 'https://www.shutterstock.com/image-photo/hands-typing-on-laptop-programming-600nw-2480023489.jpg', '[]', NULL, NULL, NULL, NULL, 0, 0, 0, 0.00, 0);

-- ----------------------------
-- Table structure for token_blacklist
-- ----------------------------
DROP TABLE IF EXISTS `token_blacklist`;
CREATE TABLE `token_blacklist`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `token_jti` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int(11) NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_token_jti`(`token_jti`(50)) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_expires_at`(`expires_at`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of token_blacklist
-- ----------------------------

-- ----------------------------
-- Table structure for user_services
-- ----------------------------
DROP TABLE IF EXISTS `user_services`;
CREATE TABLE `user_services`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `service_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `service_type` enum('system_service','technical_service','premium_content') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `order_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `status` enum('active','expired','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active',
  `purchase_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `expiry_date` timestamp NULL DEFAULT NULL,
  `download_count` int(11) NULL DEFAULT 0,
  `max_downloads` int(11) NULL DEFAULT -1,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_service_id`(`service_id`) USING BTREE,
  INDEX `idx_service_type`(`service_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_purchase_date`(`purchase_date`) USING BTREE,
  INDEX `idx_expiry_date`(`expiry_date`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_services
-- ----------------------------

-- ----------------------------
-- Table structure for user_sessions
-- ----------------------------
DROP TABLE IF EXISTS `user_sessions`;
CREATE TABLE `user_sessions`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `refresh_token` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `device_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `is_active` tinyint(1) NULL DEFAULT 1,
  `expires_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_sessions
-- ----------------------------

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `full_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` enum('user','admin') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'user',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('active','inactive','suspended') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `email_verified` tinyint(1) NULL DEFAULT 0,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `avatar_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `login_count` int(11) NULL DEFAULT 0,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `two_factor_secret` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `two_factor_recovery_codes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `profile_photo_path` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `email`(`email`) USING BTREE,
  UNIQUE INDEX `username`(`username`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES ('admin-user-id-2024', '<EMAIL>', 'admin', 'Updated Admin User', 'admin', '$2a$12$h2.zCQX.NG.Bez1JVX212eAbfzIA.M4Jubq0EoMwSLNB4nswlYyPC', 'active', '2025-07-17 02:00:11', '2025-07-24 23:59:38', 0, '+1234567890', NULL, '2025-07-24 23:59:38', 161, '$2b$10$wrk7/6o9ZnaymEkza.G1GehGX5MGdL6WDHlF.IVsKptunJuvSx.ii', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `users` VALUES ('6aa68bf0-675a-41a8-bd96-3804f8179393', '<EMAIL>', 'usersql', 'تجربة سكل', 'user', '$2a$12$6CQyRqCCTyEl8fwcqxHNRO2PMZm7yC.2g8ILCtMKtEVyui2O5oAnq', 'active', '2025-07-19 00:14:10', '2025-07-19 00:14:10', 0, NULL, NULL, NULL, 0, '', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `users` VALUES ('admin-user-id-2025', '<EMAIL>', 'admin1', 'Updated admin', 'user', '$2a$12$6CQyRqCCTyEl8fwcqxHNRO2PMZm7yC.2g8ILCtMKtEVyui2O5oAnq', 'active', '2025-07-17 23:09:29', '2025-07-19 01:12:27', 0, NULL, NULL, '2025-07-18 23:22:46', 1, '', '2025-07-17 23:09:54', NULL, NULL, NULL, NULL);
INSERT INTO `users` VALUES ('admin-user-id-2026', '<EMAIL>', 'testuser', 'مستخدم تجريبي', 'user', '$2a$12$6CQyRqCCTyEl8fwcqxHNRO2PMZm7yC.2g8ILCtMKtEVyui2O5oAnq', 'active', '2025-07-18 20:26:58', '2025-07-24 10:54:42', 0, NULL, NULL, '2025-07-24 10:54:42', 4, '', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `users` VALUES ('c6a26921-e6a9-42fe-a819-d45d1785eaf3', '<EMAIL>', 'newuser', 'New User', 'user', '$2a$12$y.8yQcvs4JC9HVg7RPrvyOXvsX9ogbxZ4hdhwxe8myxQ6jYL5lhUq', 'active', '2025-07-22 01:32:37', '2025-07-22 01:32:37', 0, NULL, NULL, NULL, 0, '', NULL, NULL, NULL, NULL, NULL);

SET FOREIGN_KEY_CHECKS = 1;
