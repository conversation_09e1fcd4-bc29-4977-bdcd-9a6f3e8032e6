/**
 * Loading States Components Tests
 * 
 * Tests for loading state components and hooks
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, act } from '../../../test/utils'
import { 
  LoadingSpinner,
  LoadingWithText,
  ProgressLoading,
  Skeleton,
  CardSkeleton,
  ListSkeleton,
  ContextualLoading,
  FullPageLoading,
  LoadingOverlay,
  useLoadingState
} from '../LoadingStates'
import React from 'react'

describe('LoadingSpinner', () => {
  it('renders with default props', () => {
    render(<LoadingSpinner />)
    
    const spinner = document.querySelector('.animate-spin')
    expect(spinner).toBeInTheDocument()
    expect(spinner).toHaveClass('w-6', 'h-6') // default md size
  })

  it('applies correct size classes', () => {
    const { rerender } = render(<LoadingSpinner size="sm" />)
    expect(document.querySelector('.animate-spin')).toHaveClass('w-4', 'h-4')

    rerender(<LoadingSpinner size="lg" />)
    expect(document.querySelector('.animate-spin')).toHaveClass('w-8', 'h-8')

    rerender(<LoadingSpinner size="xl" />)
    expect(document.querySelector('.animate-spin')).toHaveClass('w-12', 'h-12')
  })

  it('applies color classes correctly', () => {
    const { rerender } = render(<LoadingSpinner color="primary" />)
    expect(document.querySelector('.animate-spin')).toHaveClass('text-interactive-primary')

    rerender(<LoadingSpinner color="white" />)
    expect(document.querySelector('.animate-spin')).toHaveClass('text-white')
  })

  it('has accessibility label', () => {
    render(<LoadingSpinner />)
    
    expect(document.querySelector('[aria-label="Loading"]')).toBeInTheDocument()
  })
})

describe('LoadingWithText', () => {
  it('renders with default text', () => {
    render(<LoadingWithText />)
    
    expect(screen.getByText('جاري التحميل...')).toBeInTheDocument()
    expect(screen.getByText('Loading...')).toBeInTheDocument()
  })

  it('renders with custom text', () => {
    render(
      <LoadingWithText 
        text="جاري حفظ البيانات..." 
        textEn="Saving data..." 
      />
    )
    
    expect(screen.getByText('جاري حفظ البيانات...')).toBeInTheDocument()
    expect(screen.getByText('Saving data...')).toBeInTheDocument()
  })

  it('supports horizontal and vertical directions', () => {
    const { rerender } = render(<LoadingWithText direction="horizontal" />)
    expect(document.querySelector('.flex-row')).toBeInTheDocument()

    rerender(<LoadingWithText direction="vertical" />)
    expect(document.querySelector('.flex-col')).toBeInTheDocument()
  })

  it('includes loading spinner', () => {
    render(<LoadingWithText />)
    
    expect(document.querySelector('.animate-spin')).toBeInTheDocument()
  })
})

describe('ProgressLoading', () => {
  it('renders progress bar with correct width', () => {
    render(<ProgressLoading progress={75} />)
    
    const progressBar = document.querySelector('.bg-interactive-primary')
    expect(progressBar).toHaveStyle({ width: '75%' })
  })

  it('shows percentage when enabled', () => {
    render(<ProgressLoading progress={42} showPercentage={true} />)
    
    expect(screen.getByText('42%')).toBeInTheDocument()
  })

  it('hides percentage when disabled', () => {
    render(<ProgressLoading progress={42} showPercentage={false} />)
    
    expect(screen.queryByText('42%')).not.toBeInTheDocument()
  })

  it('clamps progress values', () => {
    const { rerender } = render(<ProgressLoading progress={150} />)
    expect(document.querySelector('.bg-interactive-primary')).toHaveStyle({ width: '100%' })

    rerender(<ProgressLoading progress={-10} />)
    expect(document.querySelector('.bg-interactive-primary')).toHaveStyle({ width: '0%' })
  })

  it('renders custom text', () => {
    render(
      <ProgressLoading 
        progress={50} 
        text="جاري الرفع..." 
        textEn="Uploading..." 
      />
    )
    
    expect(screen.getByText('جاري الرفع...')).toBeInTheDocument()
    expect(screen.getByText('Uploading...')).toBeInTheDocument()
  })
})

describe('Skeleton', () => {
  it('renders with default props', () => {
    render(<Skeleton />)
    
    const skeleton = document.querySelector('.animate-pulse')
    expect(skeleton).toBeInTheDocument()
    expect(skeleton).toHaveClass('rounded-md') // default rectangular variant
  })

  it('applies variant classes correctly', () => {
    const { rerender } = render(<Skeleton variant="text" />)
    expect(document.querySelector('.animate-pulse')).toHaveClass('rounded')

    rerender(<Skeleton variant="circular" />)
    expect(document.querySelector('.animate-pulse')).toHaveClass('rounded-full')
  })

  it('applies custom dimensions', () => {
    render(<Skeleton width={200} height={100} />)
    
    const skeleton = document.querySelector('.animate-pulse')
    expect(skeleton).toHaveStyle({ width: '200px', height: '100px' })
  })

  it('supports string dimensions', () => {
    render(<Skeleton width="50%" height="2rem" />)
    
    const skeleton = document.querySelector('.animate-pulse')
    expect(skeleton).toHaveStyle({ width: '50%', height: '2rem' })
  })

  it('has accessibility label', () => {
    render(<Skeleton />)
    
    expect(document.querySelector('[aria-label="Loading content"]')).toBeInTheDocument()
  })
})

describe('CardSkeleton', () => {
  it('renders card structure', () => {
    render(<CardSkeleton />)
    
    // Check for card container
    expect(document.querySelector('.bg-background-card')).toBeInTheDocument()
    
    // Check for multiple skeleton elements
    const skeletons = document.querySelectorAll('.animate-pulse')
    expect(skeletons.length).toBeGreaterThan(1)
  })

  it('includes circular avatar skeleton', () => {
    render(<CardSkeleton />)
    
    expect(document.querySelector('.rounded-full')).toBeInTheDocument()
  })
})

describe('ListSkeleton', () => {
  it('renders default number of items', () => {
    render(<ListSkeleton />)
    
    const items = document.querySelectorAll('.bg-background-card')
    expect(items).toHaveLength(5) // default items count
  })

  it('renders custom number of items', () => {
    render(<ListSkeleton items={3} />)
    
    const items = document.querySelectorAll('.bg-background-card')
    expect(items).toHaveLength(3)
  })

  it('includes circular avatars in each item', () => {
    render(<ListSkeleton items={2} />)
    
    const avatars = document.querySelectorAll('.rounded-full')
    expect(avatars).toHaveLength(2)
  })
})

describe('ContextualLoading', () => {
  it('renders search loading', () => {
    render(<ContextualLoading type="search" />)
    
    expect(screen.getByText('جاري البحث...')).toBeInTheDocument()
    expect(screen.getByText('Searching...')).toBeInTheDocument()
  })

  it('renders download loading', () => {
    render(<ContextualLoading type="download" />)
    
    expect(screen.getByText('جاري التحميل...')).toBeInTheDocument()
    expect(screen.getByText('Downloading...')).toBeInTheDocument()
  })

  it('renders upload loading', () => {
    render(<ContextualLoading type="upload" />)
    
    expect(screen.getByText('جاري الرفع...')).toBeInTheDocument()
    expect(screen.getByText('Uploading...')).toBeInTheDocument()
  })

  it('renders custom text', () => {
    render(
      <ContextualLoading 
        type="processing" 
        text="معالجة مخصصة..." 
        textEn="Custom processing..." 
      />
    )
    
    expect(screen.getByText('معالجة مخصصة...')).toBeInTheDocument()
    expect(screen.getByText('Custom processing...')).toBeInTheDocument()
  })
})

describe('FullPageLoading', () => {
  it('renders full page overlay', () => {
    render(<FullPageLoading />)
    
    expect(document.querySelector('.fixed.inset-0')).toBeInTheDocument()
  })

  it('shows logo when enabled', () => {
    render(<FullPageLoading showLogo={true} />)
    
    expect(document.querySelector('.bg-interactive-primary.rounded-lg')).toBeInTheDocument()
  })

  it('hides logo when disabled', () => {
    render(<FullPageLoading showLogo={false} />)
    
    expect(document.querySelector('.bg-interactive-primary.rounded-lg')).not.toBeInTheDocument()
  })

  it('renders custom text', () => {
    render(
      <FullPageLoading 
        text="جاري تحميل التطبيق..." 
        textEn="Loading application..." 
      />
    )
    
    expect(screen.getByText('جاري تحميل التطبيق...')).toBeInTheDocument()
    expect(screen.getByText('Loading application...')).toBeInTheDocument()
  })
})

describe('LoadingOverlay', () => {
  it('renders children when not loading', () => {
    render(
      <LoadingOverlay isLoading={false}>
        <div>Content</div>
      </LoadingOverlay>
    )
    
    expect(screen.getByText('Content')).toBeInTheDocument()
    expect(document.querySelector('.absolute.inset-0')).not.toBeInTheDocument()
  })

  it('shows overlay when loading', () => {
    render(
      <LoadingOverlay isLoading={true}>
        <div>Content</div>
      </LoadingOverlay>
    )
    
    expect(screen.getByText('Content')).toBeInTheDocument()
    expect(document.querySelector('.absolute.inset-0')).toBeInTheDocument()
  })

  it('applies blur when enabled', () => {
    render(
      <LoadingOverlay isLoading={true} blur={true}>
        <div>Content</div>
      </LoadingOverlay>
    )
    
    expect(document.querySelector('.backdrop-blur-sm')).toBeInTheDocument()
  })

  it('renders custom loading text', () => {
    render(
      <LoadingOverlay 
        isLoading={true} 
        text="جاري الحفظ..." 
        textEn="Saving..." 
      >
        <div>Content</div>
      </LoadingOverlay>
    )
    
    expect(screen.getByText('جاري الحفظ...')).toBeInTheDocument()
    expect(screen.getByText('Saving...')).toBeInTheDocument()
  })
})

describe('useLoadingState', () => {
  it('initializes with default state', () => {
    const TestComponent = () => {
      const { isLoading, error } = useLoadingState()
      return (
        <div>
          <span>Loading: {isLoading.toString()}</span>
          <span>Error: {error || 'none'}</span>
        </div>
      )
    }
    
    render(<TestComponent />)
    
    expect(screen.getByText('Loading: false')).toBeInTheDocument()
    expect(screen.getByText('Error: none')).toBeInTheDocument()
  })

  it('initializes with custom state', () => {
    const TestComponent = () => {
      const { isLoading } = useLoadingState(true)
      return <span>Loading: {isLoading.toString()}</span>
    }
    
    render(<TestComponent />)
    
    expect(screen.getByText('Loading: true')).toBeInTheDocument()
  })

  it('handles start and stop loading', () => {
    const TestComponent = () => {
      const { isLoading, startLoading, stopLoading } = useLoadingState()
      
      return (
        <div>
          <span>Loading: {isLoading.toString()}</span>
          <button onClick={startLoading}>Start</button>
          <button onClick={stopLoading}>Stop</button>
        </div>
      )
    }
    
    render(<TestComponent />)
    
    expect(screen.getByText('Loading: false')).toBeInTheDocument()
    
    fireEvent.click(screen.getByText('Start'))
    expect(screen.getByText('Loading: true')).toBeInTheDocument()
    
    fireEvent.click(screen.getByText('Stop'))
    expect(screen.getByText('Loading: false')).toBeInTheDocument()
  })

  it('handles error state', () => {
    const TestComponent = () => {
      const { isLoading, error, setLoadingError } = useLoadingState()
      
      return (
        <div>
          <span>Loading: {isLoading.toString()}</span>
          <span>Error: {error || 'none'}</span>
          <button onClick={() => setLoadingError('Test error')}>Set Error</button>
        </div>
      )
    }
    
    render(<TestComponent />)
    
    fireEvent.click(screen.getByText('Set Error'))
    
    expect(screen.getByText('Loading: false')).toBeInTheDocument()
    expect(screen.getByText('Error: Test error')).toBeInTheDocument()
  })

  it('handles reset', () => {
    const TestComponent = () => {
      const { isLoading, error, startLoading, setLoadingError, reset } = useLoadingState()
      
      return (
        <div>
          <span>Loading: {isLoading.toString()}</span>
          <span>Error: {error || 'none'}</span>
          <button onClick={startLoading}>Start</button>
          <button onClick={() => setLoadingError('Test error')}>Set Error</button>
          <button onClick={reset}>Reset</button>
        </div>
      )
    }
    
    render(<TestComponent />)
    
    // Set loading and error
    fireEvent.click(screen.getByText('Start'))
    fireEvent.click(screen.getByText('Set Error'))
    
    expect(screen.getByText('Error: Test error')).toBeInTheDocument()
    
    // Reset
    fireEvent.click(screen.getByText('Reset'))
    
    expect(screen.getByText('Loading: false')).toBeInTheDocument()
    expect(screen.getByText('Error: none')).toBeInTheDocument()
  })
})
