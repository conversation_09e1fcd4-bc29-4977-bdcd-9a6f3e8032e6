import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotification } from '../../hooks/useNotification';
import {
  getSystemServices,
  getAdminSystemServices,
  getTechnicalServices,
  getPremiumContent,
  createPremiumContent,
  updatePremiumContent,
  deletePremiumContent,
} from '../../lib/apiServices';
import {
  SystemService,
  TechnicalService,
  TranslatedText,
  getPremiumEditions,
  createPremiumEdition,
  updatePremiumEdition,
  deletePremiumEdition,
  activatePremiumEdition,
} from '../../lib/database';
import { 
  Crown, 
  Plus, 
  Edit, 
  Trash2,
  Eye,
  Package,
  Star,
  Image as ImageIcon,
  Play,
  DollarSign,
  CheckCircle,
  XCircle,
  Search,
  Filter,
  Download,
  Upload,
  Settings,
  ArrowLeft,
  Save,
  X,
  Percent,
  Tag,
  Calendar,
  Users,
  TrendingUp,
  BarChart3,
  Server,
  Monitor,
  Database,
  Shield,
  Wrench,
  Code,
  Layers,
  Zap,
  Globe,
  HardDrive
} from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import GoldenFilterGrid from '../ui/GoldenFilterGrid';
import BackButton from '../ui/BackButton';
import { useButtonActions } from '../../utils/buttonActions';
import { PREMIUM_CONTENT_FILTERS } from '../../constants/filterOptions';
import Modal from '../ui/Modal';
import Card from '../ui/Card';

interface AdvancedPremiumManagerProps {
  onBack?: () => void;
}

interface PremiumEdition {
  id: string;
  name: TranslatedText;
  description: TranslatedText;
  price: number;
  original_price?: number;
  discount_percentage?: number;
  features: TranslatedText[];
  includedSystems: string[]; // System IDs
  includedServices: string[]; // Service IDs
  images: string[];
  video_url?: string;
  gallery_images?: string[];
  patches_updates: TranslatedText; // خانة التصليحات والباتشات
  analytics: {
    downloads: number;
    active_users: number;
    rating: number;
    reviews_count: number;
  };
  is_available: boolean;
  is_active: boolean; // Only one premium edition can be active at a time
  category: 'pvp' | 'pve' | 'roleplay' | 'economy' | 'custom';
  created_at: string;
  updated_at: string;
}

interface PremiumStats {
  totalEditions: number;
  activeEditions: number;
  totalRevenue: number;
  averagePrice: number;
  popularCategory: string;
  totalSales: number;
}

/**
 * Advanced Premium Edition Manager with comprehensive features
 */
const AdvancedPremiumManager: React.FC<AdvancedPremiumManagerProps> = ({ onBack }) => {
  const { t, language } = useTranslation();
  const { showNotification } = useNotification();
  const buttonActions = useButtonActions();

  const [premiumEditions, setPremiumEditions] = useState<PremiumEdition[]>([]);
  const [availableSystems, setAvailableSystems] = useState<SystemService[]>([]);
  const [availableServices, setAvailableServices] = useState<TechnicalService[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingEdition, setEditingEdition] = useState<PremiumEdition | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeTab, setActiveTab] = useState<'overview' | 'systems' | 'services' | 'media' | 'pricing'>('overview');

  // Unified filter state
  const [filterValues, setFilterValues] = useState<Record<string, string>>({
    type: 'all',
    status: 'all',
    category: 'all',
    priceRange: 'all',
    sortBy: 'created'
  });

  // Premium Edition Form State
  const [formData, setFormData] = useState<Partial<PremiumEdition>>({
    name: { ar: '', en: '' },
    description: { ar: '', en: '' },
    price: 0,
    original_price: 0,
    discount_percentage: 0,
    features: { ar: [], en: [] },
    includedSystems: [],
    includedServices: [],
    images: [],
    video_url: '',
    gallery_images: [],
    patches_updates: { ar: '', en: '' },
    analytics: {
      downloads: 0,
      active_users: 0,
      rating: 0,
      reviews_count: 0
    },
    is_available: true,
    is_active: false,
    category: 'pvp'
  });

  const [premiumStats, setPremiumStats] = useState<PremiumStats>({
    totalEditions: 0,
    activeEditions: 0,
    totalRevenue: 0,
    averagePrice: 0,
    popularCategory: 'pvp',
    totalSales: 0
  });

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    calculateStats();
  }, [premiumEditions]);

  const loadData = async () => {
    setLoading(true);
    try {
      // Load available systems and services
      const systemsResult = await getAdminSystemServices(); // Use admin endpoint to get all systems
      const servicesResult = await getTechnicalServices();
      const editionsResult = getPremiumEditions(); // Keep this as localStorage for now

      // Load available systems and services that can be added to Premium Edition
      if (systemsResult.data) {
        // Only show systems that are active and can be added to Premium Edition
        const premiumEligibleSystems = systemsResult.data.filter(s =>
          s.status === 'active' && s.isPremiumAddon === true
        );
        setAvailableSystems(premiumEligibleSystems);
      } else if (systemsResult.error) {
        console.error('Error loading systems:', systemsResult.error);
      }

      if (servicesResult.data) {
        // Only show services that are active and eligible for Premium Edition
        const premiumEligibleServices = servicesResult.data.filter(s =>
          s.status === 'active' && s.isPremiumAddon === true
        );
        setAvailableServices(premiumEligibleServices);
      } else if (servicesResult.error) {
        console.error('Error loading services:', servicesResult.error);
      }

      if (editionsResult.data) setPremiumEditions(editionsResult.data);

      // If no premium editions exist, show empty state
      if (!editionsResult.data || editionsResult.data.length === 0) {
        console.log('No premium editions found in database');
      }
    } catch (error) {
      console.error('Error loading data:', error);
      showNotification({
        type: 'error',
        message: t('notifications.loadError')
      });
    }
    setLoading(false);
  };

  const calculateStats = () => {
    const stats: PremiumStats = {
      totalEditions: premiumEditions.length,
      activeEditions: premiumEditions.filter(e => e.is_available).length,
      totalRevenue: premiumEditions.filter(e => e.is_available).reduce((sum, e) => sum + (e.price || 0), 0),
      averagePrice: premiumEditions.length > 0 ?
        Math.round(premiumEditions.reduce((sum, e) => sum + (e.price || 0), 0) / premiumEditions.length) : 0,
      popularCategory: 'pvp', // This would be calculated from actual sales data
      totalSales: 0 // This would come from actual sales data
    };
    setPremiumStats(stats);
  };

  const calculateDynamicPrice = (edition: Partial<PremiumEdition>) => {
    let totalPrice = edition.price || 0;
    
    // Add system prices
    if (edition.includedSystems) {
      edition.includedSystems.forEach(systemId => {
        const system = availableSystems.find(s => s.id === systemId);
        if (system) totalPrice += system.price;
      });
    }
    
    // Add service prices
    if (edition.includedServices) {
      edition.includedServices.forEach(serviceId => {
        const service = availableServices.find(s => s.id === serviceId);
        if (service) totalPrice += service.price;
      });
    }
    
    // Add premium multipliers
    if (edition.supportLevel === 'enterprise') totalPrice *= 1.5;
    else if (edition.supportLevel === 'premium') totalPrice *= 1.2;
    
    if (edition.maintenanceType === 'weekly') totalPrice *= 1.3;
    
    return Math.round(totalPrice);
  };

  const handleCreate = () => {
    setEditingEdition(null);
    setFormData({
      name: { ar: '', en: '' },
      description: { ar: '', en: '' },
      price: 0,
      original_price: 0,
      discount_percentage: 0,
      features: { ar: [], en: [] },
      includedSystems: [],
      includedServices: [],
      images: [],
      video_url: '',
      gallery_images: [],
      patches_updates: { ar: '', en: '' },
      analytics: {
        downloads: 0,
        active_users: 0,
        rating: 0,
        reviews_count: 0
      },
      is_available: true,
      category: 'pvp'
    });
    setActiveTab('overview');
    setShowModal(true);
  };

  const handleEdit = (edition: PremiumEdition) => {
    setEditingEdition(edition);
    setFormData(edition);
    setActiveTab('overview');
    setShowModal(true);
  };

  const handlePreview = (edition: PremiumEdition) => {
    // Open preview in a new modal or navigate to preview page
    showNotification({
      type: 'info',
      message: language === 'ar' ? `معاينة ${edition.name[language]}` : `Previewing ${edition.name[language]}`
    });
    // You can implement a full preview modal here similar to Technical Services
  };

  const handleSave = async () => {
    try {
      const editionData = {
        name_ar: formData.name?.ar || '',
        name_en: formData.name?.en || '',
        description_ar: formData.description?.ar || '',
        description_en: formData.description?.en || '',
        features_ar: formData.features?.ar || [],
        features_en: formData.features?.en || [],
        tech_specs_ar: formData.tech_specs?.ar || [],
        tech_specs_en: formData.tech_specs?.en || [],
        price: formData.price || 0,
        category: formData.category || 'general',
        type: formData.type || 'regular',
        status: formData.status || 'active',
        video_url: formData.video_url || '',
        image_url: formData.image_url || '',
        gallery_images: formData.gallery_images || []
      };

      if (editingEdition) {
        // Update existing edition
        const result = updatePremiumEdition(editingEdition.id, editionData);
        if (result.data) {
          setPremiumEditions(prev =>
            prev.map(e => e.id === editingEdition.id ? result.data : e)
          );
          showNotification({
            type: 'success',
            message: language === 'ar' ? 'تم تحديث النسخة المميزة بنجاح' : 'Premium edition updated successfully'
          });
        }
      } else {
        // Create new edition
        const result = createPremiumEdition(editionData);
        if (result.data) {
          setPremiumEditions(prev => [...prev, result.data]);
          showNotification({
            type: 'success',
            message: language === 'ar' ? 'تم إنشاء النسخة المميزة بنجاح' : 'Premium edition created successfully'
          });
        }
      }

      setShowModal(false);
      setEditingEdition(null);
      // Reset form data
      setFormData({
        name: { ar: '', en: '' },
        description: { ar: '', en: '' },
        price: 0,
        original_price: 0,
        discount_percentage: 0,
        features: { ar: [], en: [] },
        includedSystems: [],
        includedServices: [],
        images: [],
        video_url: '',
        gallery_images: [],
        patches_updates: { ar: '', en: '' },
        analytics: {
          downloads: 0,
          active_users: 0,
          rating: 0,
          reviews_count: 0
        },
        is_available: true,
        category: 'pvp'
      });
    } catch (error) {
      console.error('Error saving edition:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في حفظ النسخة المميزة' : 'Failed to save premium edition'
      });
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilterValues(prev => ({ ...prev, [key]: value }));
  };

  const handleActivate = async (edition: PremiumEdition) => {
    // Show confirmation dialog with enhanced styling
    const confirmed = window.confirm(
      language === 'ar'
        ? `هل تريد تنشيط النسخة المميزة "${edition.name[language]}"؟\n\n⚠️ تحذير: سيتم إيقاف جميع النسخ الأخرى تلقائياً.\n\n✅ النسخة المنشطة ستظهر في الصفحة الرئيسية للموقع.`
        : `Do you want to activate the Premium Edition "${edition.name[language]}"?\n\n⚠️ Warning: All other editions will be automatically deactivated.\n\n✅ The activated edition will appear on the main website.`
    );

    if (!confirmed) return;

    try {
      // Use the optimized database function
      const result = activatePremiumEdition(edition.id);

      if (result.success) {
        // Reload premium editions to get updated data
        loadPremiumEditions();

        showNotification({
          type: 'success',
          message: language === 'ar'
            ? `✅ تم تنشيط النسخة المميزة "${edition.name[language]}" بنجاح!`
            : `✅ Premium Edition "${edition.name[language]}" activated successfully!`
        });
      } else {
        throw new Error(result.error || 'Activation failed');
      }
    } catch (error) {
      console.error('Error activating premium edition:', error);
      showNotification({
        type: 'error',
        message: language === 'ar'
          ? '❌ حدث خطأ أثناء تنشيط النسخة المميزة. يرجى المحاولة مرة أخرى.'
          : '❌ Error activating premium edition. Please try again.'
      });
    }
  };

  const handleDelete = (edition: PremiumEdition) => {
    showNotification({
      type: 'confirm',
      message: language === 'ar' ? 'هل أنت متأكد من حذف هذه النسخة المميزة؟' : 'Are you sure you want to delete this premium edition?',
      onConfirm: () => {
        const result = deletePremiumEdition(edition.id);
        if (result.success) {
          setPremiumEditions(prev => prev.filter(e => e.id !== edition.id));
          showNotification({
            type: 'success',
            message: language === 'ar' ? 'تم حذف النسخة المميزة بنجاح' : 'Premium edition deleted successfully'
          });
        } else {
          showNotification({
            type: 'error',
            message: language === 'ar' ? 'فشل في حذف النسخة المميزة' : 'Failed to delete premium edition'
          });
        }
      }
    });
  };

  const filteredEditions = premiumEditions.filter(edition => {
    const matchesSearch = edition.name[language].toLowerCase().includes(searchTerm.toLowerCase()) ||
                         edition.description[language].toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterValues.category === 'all' || edition.category === filterValues.category;
    const matchesStatus = filterValues.status === 'all' ||
                         (filterValues.status === 'active' && edition.is_available) ||
                         (filterValues.status === 'inactive' && !edition.is_available);

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const categories = ['pvp', 'pve', 'roleplay', 'economy', 'custom'];
  const supportLevels = ['basic', 'premium', 'enterprise'];
  const maintenanceTypes = ['weekly', 'monthly', 'custom'];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-secondary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Enhanced Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-6 space-y-4 lg:space-y-0">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          {onBack && (
            <BackButton onClick={onBack} variant="back" size="md" />
          )}
          <div>
            <h1 className="text-2xl font-bold text-white flex items-center">
              <Crown className="w-8 h-8 mr-3 text-yellow-400" />
              {language === 'ar' ? 'إدارة النسخ المميزة' : 'Premium Editions Manager'}
            </h1>
            <p className="text-gray-400 text-sm mt-1">
              {language === 'ar' ? 'إنشاء وإدارة النسخ المميزة مع الأنظمة والخدمات المدمجة' : 'Create and manage premium editions with integrated systems and services'}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Button variant="outline" onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')} size="sm">
            <Package className="w-4 h-4 mr-2" />
            {viewMode === 'grid' ? 'List' : 'Grid'}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            {language === 'ar' ? 'تصدير' : 'Export'}
          </Button>
          <Button variant="primary" onClick={handleCreate}>
            <Plus className="w-4 h-4 mr-2" />
            {language === 'ar' ? 'إضافة نسخة مميزة' : 'Add Premium Edition'}
          </Button>
        </div>
      </div>

      {/* Enhanced Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
        <Card className="bg-gradient-to-br from-yellow-500/10 to-yellow-600/5 border-yellow-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-yellow-300 text-sm font-medium">{language === 'ar' ? 'إجمالي النسخ' : 'Total Editions'}</p>
                <p className="text-2xl font-bold text-white">{premiumStats.totalEditions}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'جميع النسخ' : 'All Editions'}
                </p>
              </div>
              <Crown className="w-8 h-8 text-yellow-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/10 to-green-600/5 border-green-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-300 text-sm font-medium">{language === 'ar' ? 'النسخ النشطة' : 'Active Editions'}</p>
                <p className="text-2xl font-bold text-white">{premiumStats.activeEditions}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'متاحة للبيع' : 'Available for Sale'}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-purple-500/10 to-purple-600/5 border-purple-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-300 text-sm font-medium">{language === 'ar' ? 'متوسط السعر' : 'Average Price'}</p>
                <p className="text-2xl font-bold text-white">${premiumStats.averagePrice}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'للنسخة الواحدة' : 'Per Edition'}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-purple-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-blue-500/10 to-blue-600/5 border-blue-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-300 text-sm font-medium">{language === 'ar' ? 'إجمالي المبيعات' : 'Total Sales'}</p>
                <p className="text-2xl font-bold text-white">{premiumStats.totalSales}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'نسخ مباعة' : 'Editions Sold'}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-blue-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-accent/10 to-accent/5 border-accent/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-accent text-sm font-medium">{language === 'ar' ? 'إجمالي الإيرادات' : 'Total Revenue'}</p>
                <p className="text-2xl font-bold text-white">${premiumStats.totalRevenue}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'من المبيعات' : 'From Sales'}
                </p>
              </div>
              <BarChart3 className="w-8 h-8 text-accent" />
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-gradient-to-br from-orange-500/10 to-orange-600/5 border-orange-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-300 text-sm font-medium">{language === 'ar' ? 'الفئة الأكثر شعبية' : 'Popular Category'}</p>
                <p className="text-lg font-bold text-white">{premiumStats.popularCategory.toUpperCase()}</p>
                <p className="text-xs text-gray-400 mt-1">
                  {language === 'ar' ? 'الأكثر مبيعاً' : 'Best Selling'}
                </p>
              </div>
              <Star className="w-8 h-8 text-orange-400" />
            </div>
          </Card.Body>
        </Card>
      </div>

      {/* Enhanced Filters and Search - Repositioned Below Statistics */}
      <div className="mb-6">
        <GoldenFilterGrid
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          searchPlaceholder="Search premium editions (name, description, category)..."
          searchPlaceholderAr="البحث في النسخ المميزة (الاسم، الوصف، الفئة)..."
          filters={PREMIUM_CONTENT_FILTERS}
          filterValues={filterValues}
          onFilterChange={handleFilterChange}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          resultCount={filteredEditions.length}
          onExport={() => buttonActions.exportData('premium-editions', filteredEditions)}
          onImport={() => buttonActions.importData('premium-editions')}
          onAdvancedSettings={() => buttonActions.openAdvancedSettings('premium-content')}
          compact={true}
          position="horizontal"
          className="enhanced-premium-content-filter"
        />
      </div>

      {/* Premium Editions Grid/List */}
      {filteredEditions.length === 0 ? (
        <Card>
          <Card.Body>
            <div className="text-center py-12">
              <Crown className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                {language === 'ar' ? 'لا توجد نسخ مميزة' : 'No Premium Editions Found'}
              </h3>
              <p className="text-gray-400 mb-6">
                {language === 'ar' ? 'ابدأ بإنشاء نسخة مميزة جديدة' : 'Start by creating a new premium edition'}
              </p>
              <Button variant="primary" onClick={handleCreate}>
                <Plus className="w-4 h-4 mr-2" />
                {language === 'ar' ? 'إضافة نسخة مميزة' : 'Add Premium Edition'}
              </Button>
            </div>
          </Card.Body>
        </Card>
      ) : (
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
          {filteredEditions.map((edition) => {
            const dynamicPrice = calculateDynamicPrice(edition);

            return (
              <Card key={edition.id} className="hover:border-secondary/50 transition-all duration-300 overflow-hidden">
                <Card.Body className="p-0">
                  {viewMode === 'grid' ? (
                    // Grid View
                    <div className="space-y-0">
                      {/* Header with Crown */}
                      <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 p-4 border-b border-accent/20">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <Crown className="w-6 h-6 text-yellow-400" />
                            <span className="text-sm font-medium text-yellow-300">
                              {language === 'ar' ? 'نسخة مميزة' : 'Premium Edition'}
                            </span>
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs border ${
                            edition.status === 'active' ? 'bg-green-500/20 text-green-400 border-green-500/30' :
                            edition.status === 'inactive' ? 'bg-red-500/20 text-red-400 border-red-500/30' :
                            'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
                          }`}>
                            {language === 'ar' ?
                              (edition.status === 'active' ? 'نشط' :
                               edition.status === 'inactive' ? 'غير نشط' : 'مسودة') :
                              edition.status.charAt(0).toUpperCase() + edition.status.slice(1)
                            }
                          </span>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="p-4 space-y-4">
                        <div>
                          <h3 className="text-lg font-semibold text-white mb-2 line-clamp-1">
                            {edition.name[language]}
                          </h3>
                          <p className="text-gray-300 text-sm mb-3 line-clamp-2">
                            {edition.description[language]}
                          </p>
                        </div>

                        {/* Pricing */}
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex flex-col">
                            <span className="text-secondary font-bold text-xl">${dynamicPrice}</span>
                            {dynamicPrice !== edition.basePrice && (
                              <span className="text-xs text-gray-400 line-through">${edition.basePrice}</span>
                            )}
                          </div>
                          <div className="flex flex-col items-end space-y-1">
                            <span className="text-xs text-gray-400 bg-accent/20 px-2 py-1 rounded">
                              {language === 'ar' ?
                                (edition.category === 'pvp' ? 'قتال اللاعبين' :
                                 edition.category === 'pve' ? 'قتال الوحوش' :
                                 edition.category === 'roleplay' ? 'لعب الأدوار' :
                                 edition.category === 'economy' ? 'اقتصادية' :
                                 edition.category === 'custom' ? 'مخصصة' : edition.category) :
                                edition.category.toUpperCase()
                              }
                            </span>
                          </div>
                        </div>

                        {/* Server Specs */}
                        <div className="bg-primary/30 p-3 rounded-lg">
                          <h4 className="text-sm font-medium text-white mb-2 flex items-center">
                            <Server className="w-4 h-4 mr-2" />
                            {language === 'ar' ? 'مواصفات السيرفر' : 'Server Specs'}
                          </h4>
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div className="flex items-center space-x-1 rtl:space-x-reverse">
                              <Package className="w-3 h-3 text-gray-400" />
                              <span className="text-gray-300">{edition.category}</span>
                            </div>
                            <div className="flex items-center space-x-1 rtl:space-x-reverse">
                              <Star className="w-3 h-3 text-gray-400" />
                              <span className="text-gray-300">{edition.analytics?.rating || 0}/5</span>
                            </div>
                            <div className="flex items-center space-x-1 rtl:space-x-reverse">
                              <Users className="w-3 h-3 text-gray-400" />
                              <span className="text-gray-300">{edition.analytics?.active_users || 0}</span>
                            </div>
                            <div className="flex items-center space-x-1 rtl:space-x-reverse">
                              <Download className="w-3 h-3 text-gray-400" />
                              <span className="text-gray-300">{edition.analytics?.downloads || 0}</span>
                            </div>
                          </div>
                        </div>

                        {/* Included Systems & Services */}
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-400">{language === 'ar' ? 'الأنظمة المدمجة' : 'Included Systems'}</span>
                            <span className="text-accent font-medium">{(edition.includedSystems || []).length}</span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-400">{language === 'ar' ? 'الخدمات المدمجة' : 'Included Services'}</span>
                            <span className="text-secondary font-medium">{(edition.includedServices || []).length}</span>
                          </div>
                        </div>

                        {/* Features Preview */}
                        <div>
                          <h4 className="text-sm font-medium text-white mb-2">
                            {language === 'ar' ? 'الميزات الرئيسية' : 'Key Features'}
                          </h4>
                          <div className="space-y-1">
                            {edition.features[language].slice(0, 2).map((feature, index) => (
                              <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                                <CheckCircle className="w-3 h-3 text-green-400 flex-shrink-0" />
                                <span className="text-xs text-gray-300 line-clamp-1">{feature}</span>
                              </div>
                            ))}
                            {(edition.features?.[language] || []).length > 2 && (
                              <span className="text-xs text-accent">
                                +{(edition.features?.[language] || []).length - 2} {language === 'ar' ? 'المزيد' : 'more'}
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="space-y-2 pt-2 border-t border-accent/20">
                          <div className="flex space-x-2 rtl:space-x-reverse">
                            <Button variant="outline" size="sm" onClick={() => handleEdit(edition)} className="flex-1 btn-icon-fix card-action-btn">
                              <Edit className="w-4 h-4 mr-1 enhanced-icon" />
                              {language === 'ar' ? 'تعديل' : 'Edit'}
                            </Button>
                            <Button variant="outline" size="sm" onClick={() => handleDelete(edition)} className="btn-icon-fix card-action-btn">
                              <Trash2 className="w-4 h-4 enhanced-icon" />
                            </Button>
                          </div>
                          <div className="flex space-x-2 rtl:space-x-reverse">
                            <Button
                              variant="secondary"
                              size="sm"
                              className="flex-1 text-xs btn-icon-fix card-action-btn"
                              onClick={() => handlePreview(edition)}
                            >
                              <Eye className="w-3 h-3 mr-1 enhanced-icon" />
                              {language === 'ar' ? 'معاينة' : 'Preview'}
                            </Button>
                            <Button variant="secondary" size="sm" className="flex-1 text-xs">
                              <Package className="w-3 h-3 mr-1" />
                              {language === 'ar' ? 'نسخ' : 'Clone'}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    // List View
                    <div className="flex items-center space-x-4 rtl:space-x-reverse p-4">
                      {/* Edition Icon */}
                      <div className="w-16 h-16 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Crown className="w-8 h-8 text-yellow-400" />
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <h3 className="text-lg font-semibold text-white truncate">{language === 'ar' ? (edition.name_ar || '') : (edition.name_en || '')}</h3>
                            {/* Active Badge */}
                            {edition.is_active && (
                              <span className="px-2 py-1 rounded-full text-xs bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-400 border border-yellow-500/30 flex items-center space-x-1 rtl:space-x-reverse animate-pulse">
                                <Crown className="w-3 h-3" />
                                <span className="font-medium">
                                  {language === 'ar' ? 'نشط' : 'ACTIVE'}
                                </span>
                              </span>
                            )}
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs border ${
                            edition.is_available ? 'bg-green-500/20 text-green-400 border-green-500/30' :
                            'bg-red-500/20 text-red-400 border-red-500/30'
                          } flex items-center`}>
                            {language === 'ar' ?
                              (edition.is_available ? 'متاح' : 'غير متاح') :
                              (edition.is_available ? 'Available' : 'Unavailable')
                            }
                          </span>
                        </div>

                        <p className="text-gray-300 text-sm mb-2 line-clamp-1">{edition.description[language]}</p>

                        <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm">
                          <span className="text-secondary font-bold">${dynamicPrice}</span>
                          <span className="text-gray-400">{edition.category}</span>
                          <span className="text-accent">{(edition.includedSystems || []).length} {language === 'ar' ? 'أنظمة' : 'systems'}</span>
                          <span className="text-blue-400">{(edition.includedServices || []).length} {language === 'ar' ? 'خدمات' : 'services'}</span>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex space-x-2 rtl:space-x-reverse flex-shrink-0">
                        {/* Activation Button with Enhanced Design */}
                        <Button
                          variant={edition.is_active ? "primary" : "outline"}
                          size="sm"
                          onClick={() => handleActivate(edition)}
                          className={`relative ${
                            edition.is_active
                              ? "bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-400 border-green-500/30 shadow-lg shadow-green-500/20"
                              : "hover:bg-secondary/10 hover:border-secondary/50"
                          }`}
                          title={edition.is_active
                            ? (language === 'ar' ? 'النسخة نشطة حالياً' : 'Currently Active Edition')
                            : (language === 'ar' ? 'انقر للتنشيط' : 'Click to Activate')
                          }
                        >
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            {edition.is_active ? (
                              <>
                                <CheckCircle className="w-4 h-4" />
                                <span className="text-xs font-medium">
                                  {language === 'ar' ? 'نشط' : 'Active'}
                                </span>
                              </>
                            ) : (
                              <>
                                <Crown className="w-4 h-4" />
                                <span className="text-xs">
                                  {language === 'ar' ? 'تنشيط' : 'Activate'}
                                </span>
                              </>
                            )}
                          </div>

                          {/* Active Badge */}
                          {edition.is_active && (
                            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background animate-pulse"></div>
                          )}
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleEdit(edition)}>
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleDelete(edition)}>
                          <Trash2 className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePreview(edition)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </Card.Body>
              </Card>
            );
          })}
        </div>
      )}

      {/* Premium Edition Modal */}
      {showModal && (
        <Modal
          isOpen={showModal}
          onClose={() => {
            setShowModal(false);
            setEditingEdition(null);
          }}
          title={editingEdition ?
            (language === 'ar' ? 'تعديل النسخة المميزة' : 'Edit Premium Edition') :
            (language === 'ar' ? 'إضافة نسخة مميزة جديدة' : 'Add New Premium Edition')
          }
          size="xl"
          className="max-h-[90vh] overflow-y-auto"
        >
          <div className="space-y-6 max-h-[70vh] overflow-y-auto">
            {/* Tab Navigation */}
            <div className="flex space-x-1 rtl:space-x-reverse bg-primary/30 rounded-lg p-1 sticky top-0 z-10">
              {[
                { id: 'overview', label: language === 'ar' ? 'نظرة عامة' : 'Overview', icon: Crown },
                { id: 'systems', label: language === 'ar' ? 'الأنظمة' : 'Systems', icon: Server },
                { id: 'services', label: language === 'ar' ? 'الخدمات' : 'Services', icon: Settings },
                { id: 'media', label: language === 'ar' ? 'الوسائط' : 'Media', icon: Play },
                { id: 'pricing', label: language === 'ar' ? 'التسعير' : 'Pricing', icon: DollarSign }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex-1 flex items-center justify-center space-x-2 rtl:space-x-reverse px-4 py-2 rounded-md transition-colors ${
                    activeTab === tab.id
                      ? 'bg-secondary text-white'
                      : 'text-gray-400 hover:text-white hover:bg-primary/50'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  <span className="text-sm font-medium">{tab.label}</span>
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div className="min-h-[400px]">
              {activeTab === 'overview' && (
                <div className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <Input
                      label={language === 'ar' ? 'الاسم (عربي)' : 'Name (Arabic)'}
                      value={formData.name?.ar || ''}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        name: { ...prev.name, ar: e.target.value }
                      }))}
                      placeholder={language === 'ar' ? 'أدخل اسم النسخة المميزة' : 'Enter premium edition name'}
                    />
                    <Input
                      label={language === 'ar' ? 'الاسم (إنجليزي)' : 'Name (English)'}
                      value={formData.name?.en || ''}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        name: { ...prev.name, en: e.target.value }
                      }))}
                      placeholder="Enter premium edition name"
                    />
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        {language === 'ar' ? 'الوصف (عربي)' : 'Description (Arabic)'}
                      </label>
                      <textarea
                        value={formData.description?.ar || ''}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          description: { ...prev.description, ar: e.target.value }
                        }))}
                        className="w-full px-3 py-2 bg-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-secondary focus:outline-none"
                        rows={3}
                        placeholder={language === 'ar' ? 'أدخل وصف النسخة المميزة' : 'Enter premium edition description'}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        {language === 'ar' ? 'الوصف (إنجليزي)' : 'Description (English)'}
                      </label>
                      <textarea
                        value={formData.description?.en || ''}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          description: { ...prev.description, en: e.target.value }
                        }))}
                        className="w-full px-3 py-2 bg-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-secondary focus:outline-none"
                        rows={3}
                        placeholder="Enter premium edition description"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        {language === 'ar' ? 'الفئة' : 'Category'}
                      </label>
                      <select
                        value={formData.category || 'pvp'}
                        onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value as any }))}
                        className="w-full px-3 py-2 bg-primary border border-gray-600 rounded-lg text-white focus:border-secondary focus:outline-none"
                      >
                        <option value="pvp">PvP</option>
                        <option value="pve">PvE</option>
                        <option value="roleplay">Roleplay</option>
                        <option value="economy">Economy</option>
                        <option value="custom">Custom</option>
                      </select>
                    </div>
                    <div className="flex items-center space-x-4 rtl:space-x-reverse">
                      <label className="flex items-center space-x-2 rtl:space-x-reverse">
                        <input
                          type="checkbox"
                          checked={formData.is_available || false}
                          onChange={(e) => setFormData(prev => ({ ...prev, is_available: e.target.checked }))}
                          className="rounded border-gray-600 bg-primary text-secondary focus:ring-secondary"
                        />
                        <span className="text-gray-300">{language === 'ar' ? 'متاح للبيع' : 'Available for sale'}</span>
                      </label>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {language === 'ar' ? 'الميزات' : 'Features'}
                    </label>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs text-gray-400 mb-1">{language === 'ar' ? 'عربي' : 'Arabic'}</label>
                        <div className="space-y-2">
                          {(formData.features?.ar || []).map((feature, index) => (
                            <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                              <Input
                                value={feature}
                                onChange={(e) => {
                                  const newFeatures = [...(formData.features?.ar || [])];
                                  newFeatures[index] = e.target.value;
                                  setFormData(prev => ({
                                    ...prev,
                                    features: { ...prev.features, ar: newFeatures }
                                  }));
                                }}
                                placeholder={language === 'ar' ? 'أدخل ميزة' : 'Enter feature'}
                                className="flex-1"
                              />
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  const newFeatures = (formData.features?.ar || []).filter((_, i) => i !== index);
                                  setFormData(prev => ({
                                    ...prev,
                                    features: { ...prev.features, ar: newFeatures }
                                  }));
                                }}
                              >
                                <X className="w-4 h-4" />
                              </Button>
                            </div>
                          ))}
                          <Button
                            variant="outline"
                            onClick={() => {
                              const newFeatures = [...(formData.features?.ar || []), ''];
                              setFormData(prev => ({
                                ...prev,
                                features: { ...prev.features, ar: newFeatures }
                              }));
                            }}
                            className="w-full"
                            size="sm"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            {language === 'ar' ? 'إضافة ميزة' : 'Add Feature'}
                          </Button>
                        </div>
                      </div>
                      <div>
                        <label className="block text-xs text-gray-400 mb-1">{language === 'ar' ? 'إنجليزي' : 'English'}</label>
                        <div className="space-y-2">
                          {(formData.features?.en || []).map((feature, index) => (
                            <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                              <Input
                                value={feature}
                                onChange={(e) => {
                                  const newFeatures = [...(formData.features?.en || [])];
                                  newFeatures[index] = e.target.value;
                                  setFormData(prev => ({
                                    ...prev,
                                    features: { ...prev.features, en: newFeatures }
                                  }));
                                }}
                                placeholder="Enter feature"
                                className="flex-1"
                              />
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  const newFeatures = (formData.features?.en || []).filter((_, i) => i !== index);
                                  setFormData(prev => ({
                                    ...prev,
                                    features: { ...prev.features, en: newFeatures }
                                  }));
                                }}
                              >
                                <X className="w-4 h-4" />
                              </Button>
                            </div>
                          ))}
                          <Button
                            variant="outline"
                            onClick={() => {
                              const newFeatures = [...(formData.features?.en || []), ''];
                              setFormData(prev => ({
                                ...prev,
                                features: { ...prev.features, en: newFeatures }
                              }));
                            }}
                            className="w-full"
                            size="sm"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Add Feature
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'systems' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white">
                    {language === 'ar' ? 'الأنظمة المضمنة' : 'Included Systems'}
                  </h3>
                  <div className="grid md:grid-cols-2 gap-4 max-h-80 overflow-y-auto">
                    {availableSystems.map((system) => (
                      <div
                        key={system.id}
                        className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                          formData.includedSystems?.includes(system.id)
                            ? 'border-secondary bg-secondary/20'
                            : 'border-gray-600 bg-primary/50 hover:border-gray-500'
                        }`}
                        onClick={() => {
                          const currentSystems = formData.includedSystems || [];
                          const newSystems = currentSystems.includes(system.id)
                            ? currentSystems.filter(id => id !== system.id)
                            : [...currentSystems, system.id];
                          setFormData(prev => ({ ...prev, includedSystems: newSystems }));
                        }}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-white">{getSystemText(system, 'name', language)}</h4>
                          <span className="text-secondary font-bold">${system.price}</span>
                        </div>
                        <p className="text-gray-300 text-sm">{getSystemText(system, 'description', language)}</p>
                        <div className="mt-2">
                          <span className={`px-2 py-1 rounded text-xs ${
                            system.type === 'plugin' ? 'bg-purple-500/20 text-purple-400' : 'bg-blue-500/20 text-blue-400'
                          }`}>
                            {system.type === 'plugin' ? (language === 'ar' ? 'إضافة' : 'Plugin') : (language === 'ar' ? 'نظام' : 'System')}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'services' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white">
                    {language === 'ar' ? 'الخدمات المضمنة' : 'Included Services'}
                  </h3>
                  <div className="grid md:grid-cols-2 gap-4 max-h-80 overflow-y-auto">
                    {availableServices.map((service) => (
                      <div
                        key={service.id}
                        className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                          formData.includedServices?.includes(service.id)
                            ? 'border-secondary bg-secondary/20'
                            : 'border-gray-600 bg-primary/50 hover:border-gray-500'
                        }`}
                        onClick={() => {
                          const currentServices = formData.includedServices || [];
                          const newServices = currentServices.includes(service.id)
                            ? currentServices.filter(id => id !== service.id)
                            : [...currentServices, service.id];
                          setFormData(prev => ({ ...prev, includedServices: newServices }));
                        }}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-white">{getServiceText(service, 'name', language)}</h4>
                          <div className="text-right">
                            <span className="text-secondary font-bold">${service.premiumPrice || service.price}</span>
                            {service.premiumPrice !== service.price && (
                              <div className="text-xs text-gray-400 line-through">${service.price}</div>
                            )}
                          </div>
                        </div>
                        <p className="text-gray-300 text-sm">{getServiceText(service, 'description', language)}</p>
                        <div className="mt-2 flex items-center space-x-2 rtl:space-x-reverse">
                          <span className="px-2 py-1 rounded text-xs bg-green-500/20 text-green-400">
                            {language === 'ar' ? 'خدمة مميزة' : 'Premium Service'}
                          </span>
                          {service.subscriptionType !== 'none' && (
                            <span className="px-2 py-1 rounded text-xs bg-orange-500/20 text-orange-400">
                              {service.subscriptionType === 'monthly' ? (language === 'ar' ? 'شهري' : 'Monthly') : (language === 'ar' ? 'سنوي' : 'Yearly')}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'media' && (
                <div className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <Input
                      label={language === 'ar' ? 'رابط الصورة الرئيسية' : 'Main Image URL'}
                      value={formData.images?.[0] || ''}
                      onChange={(e) => {
                        const newImages = [...(formData.images || [])];
                        newImages[0] = e.target.value;
                        setFormData(prev => ({ ...prev, images: newImages }));
                      }}
                      placeholder="https://example.com/image.jpg"
                    />
                    <Input
                      label={language === 'ar' ? 'رابط الفيديو' : 'Video URL'}
                      value={formData.video_url || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, video_url: e.target.value }))}
                      placeholder="https://youtube.com/watch?v=..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {language === 'ar' ? 'معرض الصور' : 'Image Gallery'}
                    </label>
                    <div className="space-y-2">
                      {(formData.gallery_images || []).map((image, index) => (
                        <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                          <Input
                            value={image}
                            onChange={(e) => {
                              const newGallery = [...(formData.gallery_images || [])];
                              newGallery[index] = e.target.value;
                              setFormData(prev => ({ ...prev, gallery_images: newGallery }));
                            }}
                            placeholder="https://example.com/gallery-image.jpg"
                            className="flex-1"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const newGallery = (formData.gallery_images || []).filter((_, i) => i !== index);
                              setFormData(prev => ({ ...prev, gallery_images: newGallery }));
                            }}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        onClick={() => {
                          const newGallery = [...(formData.gallery_images || []), ''];
                          setFormData(prev => ({ ...prev, gallery_images: newGallery }));
                        }}
                        className="w-full"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        {language === 'ar' ? 'إضافة صورة' : 'Add Image'}
                      </Button>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        {language === 'ar' ? 'التصليحات والباتشات (عربي)' : 'Patches & Updates (Arabic)'}
                      </label>
                      <textarea
                        value={formData.patches_updates?.ar || ''}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          patches_updates: { ...prev.patches_updates, ar: e.target.value }
                        }))}
                        className="w-full px-3 py-2 bg-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-secondary focus:outline-none"
                        rows={4}
                        placeholder={language === 'ar' ? 'أدخل معلومات التصليحات والتحديثات' : 'Enter patches and updates information'}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        {language === 'ar' ? 'التصليحات والباتشات (إنجليزي)' : 'Patches & Updates (English)'}
                      </label>
                      <textarea
                        value={formData.patches_updates?.en || ''}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          patches_updates: { ...prev.patches_updates, en: e.target.value }
                        }))}
                        className="w-full px-3 py-2 bg-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-secondary focus:outline-none"
                        rows={4}
                        placeholder="Enter patches and updates information"
                      />
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'pricing' && (
                <div className="space-y-4">
                  <div className="grid md:grid-cols-3 gap-4">
                    <Input
                      label={language === 'ar' ? 'السعر الأساسي' : 'Base Price'}
                      type="number"
                      value={formData.price || 0}
                      onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                      placeholder="299"
                    />
                    <Input
                      label={language === 'ar' ? 'السعر الأصلي' : 'Original Price'}
                      type="number"
                      value={formData.original_price || 0}
                      onChange={(e) => setFormData(prev => ({ ...prev, original_price: parseFloat(e.target.value) || 0 }))}
                      placeholder="399"
                    />
                    <Input
                      label={language === 'ar' ? 'نسبة الخصم %' : 'Discount %'}
                      type="number"
                      value={formData.discount_percentage || 0}
                      onChange={(e) => setFormData(prev => ({ ...prev, discount_percentage: parseFloat(e.target.value) || 0 }))}
                      placeholder="25"
                    />
                  </div>

                  <div className="bg-primary/30 rounded-lg p-4">
                    <h4 className="text-lg font-semibold text-white mb-4">
                      {language === 'ar' ? 'التحليلات' : 'Analytics'}
                    </h4>
                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <Input
                        label={language === 'ar' ? 'عدد التحميلات' : 'Downloads'}
                        type="number"
                        value={formData.analytics?.downloads || 0}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          analytics: { ...prev.analytics, downloads: parseInt(e.target.value) || 0 }
                        }))}
                        placeholder="0"
                      />
                      <Input
                        label={language === 'ar' ? 'المستخدمون النشطون' : 'Active Users'}
                        type="number"
                        value={formData.analytics?.active_users || 0}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          analytics: { ...prev.analytics, active_users: parseInt(e.target.value) || 0 }
                        }))}
                        placeholder="0"
                      />
                      <Input
                        label={language === 'ar' ? 'التقييم' : 'Rating'}
                        type="number"
                        step="0.1"
                        min="0"
                        max="5"
                        value={formData.analytics?.rating || 0}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          analytics: { ...prev.analytics, rating: parseFloat(e.target.value) || 0 }
                        }))}
                        placeholder="4.5"
                      />
                      <Input
                        label={language === 'ar' ? 'عدد المراجعات' : 'Reviews Count'}
                        type="number"
                        value={formData.analytics?.reviews_count || 0}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          analytics: { ...prev.analytics, reviews_count: parseInt(e.target.value) || 0 }
                        }))}
                        placeholder="0"
                      />
                    </div>
                  </div>

                  <div className="bg-secondary/10 rounded-lg p-4">
                    <h4 className="text-lg font-semibold text-white mb-2">
                      {language === 'ar' ? 'ملخص التسعير' : 'Pricing Summary'}
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-300">{language === 'ar' ? 'السعر الأساسي:' : 'Base Price:'}</span>
                        <span className="text-white">${formData.price || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">{language === 'ar' ? 'الأنظمة المضافة:' : 'Added Systems:'}</span>
                        <span className="text-white">{formData.includedSystems?.length || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">{language === 'ar' ? 'الخدمات المضافة:' : 'Added Services:'}</span>
                        <span className="text-white">{formData.includedServices?.length || 0}</span>
                      </div>
                      <div className="flex justify-between border-t border-gray-600 pt-2">
                        <span className="text-secondary font-semibold">{language === 'ar' ? 'السعر الإجمالي:' : 'Total Price:'}</span>
                        <span className="text-secondary font-bold">${calculateDynamicPrice(formData)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 rtl:space-x-reverse pt-4 border-t border-gray-700">
              <Button
                variant="outline"
                onClick={() => {
                  setShowModal(false);
                  setEditingEdition(null);
                }}
              >
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button
                variant="primary"
                onClick={handleSave}
                disabled={!formData.name?.ar || !formData.name?.en}
              >
                <Save className="w-4 h-4 mr-2" />
                {editingEdition ? (language === 'ar' ? 'تحديث' : 'Update') : (language === 'ar' ? 'إنشاء' : 'Create')}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default AdvancedPremiumManager;
