/**
 * Mobile Optimized Components
 * 
 * Collection of components specifically optimized for mobile devices
 * with proper touch targets and responsive design
 */

import React, { ReactNode } from 'react'
import { ChevronLeft, Menu, X } from 'lucide-react'

// Touch-friendly button component
interface TouchButtonProps {
  children: ReactNode
  onClick?: () => void
  variant?: 'primary' | 'secondary' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  fullWidth?: boolean
  disabled?: boolean
  className?: string
}

export const TouchButton: React.FC<TouchButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  disabled = false,
  className = ''
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'
  
  // Ensure minimum touch target size (44px)
  const sizeClasses = {
    sm: 'min-h-[44px] px-4 py-2 text-sm',
    md: 'min-h-[48px] px-6 py-3 text-base',
    lg: 'min-h-[52px] px-8 py-4 text-lg'
  }
  
  const variantClasses = {
    primary: 'bg-interactive-primary hover:bg-interactive-primaryHover text-white focus:ring-interactive-primary',
    secondary: 'bg-interactive-secondary hover:bg-interactive-secondaryHover text-text-primary focus:ring-interactive-secondary',
    ghost: 'bg-transparent hover:bg-background-hover text-text-primary focus:ring-background-border'
  }
  
  const widthClass = fullWidth ? 'w-full' : ''
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${widthClass} ${className}`}
    >
      {children}
    </button>
  )
}

// Mobile navigation component
interface MobileNavProps {
  isOpen: boolean
  onToggle: () => void
  children: ReactNode
}

export const MobileNav: React.FC<MobileNavProps> = ({
  isOpen,
  onToggle,
  children
}) => {
  return (
    <>
      {/* Mobile menu button */}
      <button
        onClick={onToggle}
        className="md:hidden fixed top-4 right-4 z-50 min-h-[44px] min-w-[44px] bg-background-card border border-background-border rounded-md flex items-center justify-center"
        aria-label="Toggle navigation menu"
      >
        {isOpen ? (
          <X className="w-6 h-6 text-text-primary" />
        ) : (
          <Menu className="w-6 h-6 text-text-primary" />
        )}
      </button>

      {/* Mobile menu overlay */}
      {isOpen && (
        <div className="md:hidden fixed inset-0 z-40 bg-background-primary bg-opacity-95 backdrop-blur-sm">
          <div className="flex flex-col h-full pt-16 pb-6 px-4">
            <nav className="flex-1 space-y-2">
              {children}
            </nav>
          </div>
        </div>
      )}
    </>
  )
}

// Mobile-optimized card component
interface MobileCardProps {
  children: ReactNode
  className?: string
  padding?: 'sm' | 'md' | 'lg'
  clickable?: boolean
  onClick?: () => void
}

export const MobileCard: React.FC<MobileCardProps> = ({
  children,
  className = '',
  padding = 'md',
  clickable = false,
  onClick
}) => {
  const paddingClasses = {
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6'
  }
  
  const baseClasses = `bg-background-card border border-background-border rounded-lg ${paddingClasses[padding]}`
  const clickableClasses = clickable ? 'cursor-pointer hover:bg-background-hover transition-colors duration-200 min-h-[44px]' : ''
  
  const Component = clickable ? 'button' : 'div'
  
  return (
    <Component
      onClick={onClick}
      className={`${baseClasses} ${clickableClasses} ${className}`}
    >
      {children}
    </Component>
  )
}

// Mobile-optimized input component
interface MobileInputProps {
  type?: string
  placeholder?: string
  value?: string
  onChange?: (value: string) => void
  label?: string
  error?: string
  required?: boolean
  disabled?: boolean
  className?: string
}

export const MobileInput: React.FC<MobileInputProps> = ({
  type = 'text',
  placeholder,
  value,
  onChange,
  label,
  error,
  required = false,
  disabled = false,
  className = ''
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-text-primary">
          {label}
          {required && <span className="text-status-error ml-1">*</span>}
        </label>
      )}
      
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        disabled={disabled}
        className={`
          w-full min-h-[48px] px-4 py-3 text-base
          bg-background-input border border-background-border rounded-md
          text-text-primary placeholder-text-tertiary
          focus:outline-none focus:ring-2 focus:ring-interactive-primary focus:border-transparent
          disabled:opacity-50 disabled:cursor-not-allowed
          ${error ? 'border-status-error focus:ring-status-error' : ''}
        `}
      />
      
      {error && (
        <p className="text-sm text-status-error">{error}</p>
      )}
    </div>
  )
}

// Mobile-optimized list item
interface MobileListItemProps {
  children: ReactNode
  onClick?: () => void
  showArrow?: boolean
  className?: string
}

export const MobileListItem: React.FC<MobileListItemProps> = ({
  children,
  onClick,
  showArrow = false,
  className = ''
}) => {
  const Component = onClick ? 'button' : 'div'
  
  return (
    <Component
      onClick={onClick}
      className={`
        w-full min-h-[56px] px-4 py-3 flex items-center justify-between
        bg-background-card border-b border-background-border
        ${onClick ? 'hover:bg-background-hover transition-colors duration-200 cursor-pointer' : ''}
        ${className}
      `}
    >
      <div className="flex-1 text-left">
        {children}
      </div>
      
      {showArrow && (
        <ChevronLeft className="w-5 h-5 text-text-tertiary transform rotate-180" />
      )}
    </Component>
  )
}

// Mobile bottom navigation
interface MobileBottomNavProps {
  items: Array<{
    id: string
    label: string
    icon: ReactNode
    active?: boolean
    onClick: () => void
  }>
}

export const MobileBottomNav: React.FC<MobileBottomNavProps> = ({ items }) => {
  return (
    <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-background-card border-t border-background-border z-30">
      <div className="flex">
        {items.map((item) => (
          <button
            key={item.id}
            onClick={item.onClick}
            className={`
              flex-1 min-h-[60px] px-2 py-2 flex flex-col items-center justify-center
              transition-colors duration-200
              ${item.active 
                ? 'text-interactive-primary bg-background-hover' 
                : 'text-text-tertiary hover:text-text-secondary hover:bg-background-hover'
              }
            `}
          >
            <div className="w-6 h-6 mb-1">
              {item.icon}
            </div>
            <span className="text-xs font-medium">{item.label}</span>
          </button>
        ))}
      </div>
    </nav>
  )
}

// Mobile-optimized modal
interface MobileModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  children: ReactNode
  fullScreen?: boolean
}

export const MobileModal: React.FC<MobileModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  fullScreen = false
}) => {
  if (!isOpen) return null
  
  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className={`
        relative h-full flex flex-col
        ${fullScreen 
          ? 'w-full' 
          : 'max-w-lg mx-auto mt-8 mb-8 rounded-t-lg overflow-hidden'
        }
        bg-background-primary
      `}>
        {/* Header */}
        {title && (
          <div className="flex items-center justify-between p-4 border-b border-background-border">
            <h2 className="text-lg font-semibold text-text-primary">{title}</h2>
            <button
              onClick={onClose}
              className="min-h-[44px] min-w-[44px] flex items-center justify-center rounded-md hover:bg-background-hover"
            >
              <X className="w-6 h-6 text-text-secondary" />
            </button>
          </div>
        )}
        
        {/* Content */}
        <div className="flex-1 overflow-auto p-4">
          {children}
        </div>
      </div>
    </div>
  )
}

// Mobile swipe gesture hook
export const useSwipeGesture = (
  onSwipeLeft?: () => void,
  onSwipeRight?: () => void,
  threshold: number = 50
) => {
  const [touchStart, setTouchStart] = React.useState<number | null>(null)
  const [touchEnd, setTouchEnd] = React.useState<number | null>(null)

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return
    
    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > threshold
    const isRightSwipe = distance < -threshold

    if (isLeftSwipe && onSwipeLeft) {
      onSwipeLeft()
    }
    if (isRightSwipe && onSwipeRight) {
      onSwipeRight()
    }
  }

  return {
    onTouchStart,
    onTouchMove,
    onTouchEnd
  }
}
