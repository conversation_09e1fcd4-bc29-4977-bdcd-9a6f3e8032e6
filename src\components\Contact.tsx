import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useNotification } from '../hooks/useNotification';
import { Send, MessageCircle, Phone, Mail, MapPin, Zap, Shield } from 'lucide-react';

const Contact: React.FC = () => {
  const { t } = useTranslation();
  const { showNotification } = useNotification();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // For now, just simulate success since we don't have contact API endpoint
      // TODO: Implement contact API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showNotification({ type: 'success', message: t('notifications.messageSuccess') });
      setFormData({ name: '', email: '', message: '' });
    } catch (error) {
      console.error('Error:', error);
      showNotification({ type: 'error', message: t('notifications.messageError') });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <section id="contact" className="py-20 bg-gradient-to-br from-background to-primary">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center items-center space-x-4 rtl:space-x-reverse mb-6">
            <Shield className="w-8 h-8 text-secondary" />
            <h2 className="text-4xl md:text-5xl font-bold text-white">
              <span className="bg-gradient-to-r from-secondary to-accent bg-clip-text text-transparent">
                {t('contact.title')}
              </span>
            </h2>
            <Zap className="w-8 h-8 text-accent" />
          </div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            {t('contact.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div className="bg-gradient-to-br from-primary/50 to-background/50 backdrop-blur-sm rounded-2xl border border-accent/20 hover:border-secondary/30 transition-all duration-300 p-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-secondary mb-2">
                  {t('contact.name')}
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200 text-white placeholder-gray-400"
                  placeholder={t('contact.name')}
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-secondary mb-2">
                  {t('contact.email')}
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200 text-white placeholder-gray-400"
                  placeholder={t('contact.email')}
                />
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-secondary mb-2">
                  {t('contact.message')}
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                  rows={6}
                  className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200 resize-none text-white placeholder-gray-400"
                  placeholder={t('contact.message')}
                />
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-gradient-to-r from-secondary to-accent hover:from-accent hover:to-secondary text-primary font-bold py-4 px-6 rounded-lg transition-all duration-300 flex items-center justify-center space-x-2 rtl:space-x-reverse shadow-lg hover:shadow-secondary/25 group border-2 border-transparent hover:border-white/20 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                    <span>{t('contact.sending')}</span>
                  </>
                ) : (
                  <>
                    <Send className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
                    <span className="group-hover:scale-105 transition-transform duration-300">{t('contact.send')}</span>
                  </>
                )}
              </button>
            </form>
          </div>

          {/* Contact Info */}
          <div className="space-y-8">
            {/* Quick Contact */}
            <div className="bg-gradient-to-br from-primary/50 to-background/50 backdrop-blur-sm rounded-2xl border border-accent/20 hover:border-secondary/30 transition-all duration-300 p-8">
              <h3 className="text-2xl font-bold text-white mb-6 flex items-center space-x-3 rtl:space-x-reverse">
                <Zap className="w-6 h-6 text-secondary" />
                <span>{t('contact.quickContact')}</span>
              </h3>
              
              <div className="space-y-4">
                <a
                  href="https://wa.me/1234567890"
                  className="flex items-center space-x-4 rtl:space-x-reverse p-4 bg-green-500/10 hover:bg-green-500/20 rounded-lg transition-all duration-300 group border border-green-500/20 hover:border-green-500/40"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200 shadow-lg">
                    <Phone className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-white">{t('contact.whatsapp')}</h4>
                    <p className="text-sm text-gray-400">+1 (234) 567-890</p>
                  </div>
                </a>

                <a
                  href="https://discord.gg/your-server"
                  className="flex items-center space-x-4 rtl:space-x-reverse p-4 bg-indigo-500/10 hover:bg-indigo-500/20 rounded-lg transition-all duration-300 group border border-indigo-500/20 hover:border-indigo-500/40"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200 shadow-lg">
                    <MessageCircle className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-white">{t('contact.discord')}</h4>
                    <p className="text-sm text-gray-400">Join our server</p>
                  </div>
                </a>

                <div className="flex items-center space-x-4 rtl:space-x-reverse p-4 bg-accent/10 rounded-lg border border-accent/20">
                  <div className="w-12 h-12 bg-gradient-to-br from-accent to-secondary rounded-full flex items-center justify-center shadow-lg">
                    <Mail className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-white">Email</h4>
                    <p className="text-sm text-gray-400"><EMAIL></p>
                  </div>
                </div>
              </div>
            </div>

            {/* Working Hours */}
            <div className="bg-gradient-to-br from-primary/50 to-background/50 backdrop-blur-sm rounded-2xl border border-accent/20 hover:border-secondary/30 transition-all duration-300 p-8">
              <h3 className="text-2xl font-bold text-white mb-6 flex items-center space-x-3 rtl:space-x-reverse">
                <Shield className="w-6 h-6 text-accent" />
                <span>{t('contact.workingHours')}</span>
              </h3>
              
              <p className="text-gray-300 mb-4">
                {t('contact.workingHoursDesc')}
              </p>

              <div className="space-y-3">
                <div className="flex justify-between items-center p-2 rounded border border-secondary/10">
                  <span className="text-gray-300">{t('contact.mondayToFriday')}</span>
                </div>
                <div className="flex justify-between items-center p-2 rounded border border-accent/10">
                  <span className="text-gray-300">{t('contact.saturday')}</span>
                </div>
                <div className="flex justify-between items-center p-2 rounded border border-red-500/10">
                  <span className="text-gray-300">{t('contact.sunday')}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;