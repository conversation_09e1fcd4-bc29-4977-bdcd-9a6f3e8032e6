#!/usr/bin/env node

/**
 * <PERSON>ript to help setup TestSprite with ngrok
 * This script will guide you through the process
 */

const { spawn } = require('child_process');
const { exec } = require('child_process');

console.log('🔧 إعداد TestSprite للمشروع');
console.log('================================');

// Check if ngrok is installed
function checkNgrok() {
  return new Promise((resolve) => {
    exec('ngrok version', (error) => {
      if (error) {
        console.log('❌ ngrok غير مثبت');
        console.log('📥 يرجى تثبيت ngrok أولاً:');
        console.log('   npm install -g ngrok');
        console.log('   أو تحميل من: https://ngrok.com/download');
        resolve(false);
      } else {
        console.log('✅ ngrok مثبت ومتاح');
        resolve(true);
      }
    });
  });
}

// Check if server is running
function checkServer() {
  return new Promise((resolve) => {
    exec('curl -s http://localhost:3001/health', (error, stdout) => {
      if (error) {
        console.log('❌ الخادم غير يعمل على المنفذ 3001');
        console.log('🚀 يرجى تشغيل الخادم أولاً:');
        console.log('   npm run dev:full');
        resolve(false);
      } else {
        console.log('✅ الخادم يعمل على المنفذ 3001');
        resolve(true);
      }
    });
  });
}

async function main() {
  console.log('🔍 فحص المتطلبات...');
  
  const ngrokInstalled = await checkNgrok();
  const serverRunning = await checkServer();
  
  if (!ngrokInstalled) {
    console.log('\n📋 خطوات التثبيت:');
    console.log('1. ثبت ngrok: npm install -g ngrok');
    console.log('2. شغل هذا السكريبت مرة أخرى');
    return;
  }
  
  if (!serverRunning) {
    console.log('\n📋 خطوات التشغيل:');
    console.log('1. شغل الخادم: npm run dev:full');
    console.log('2. شغل هذا السكريبت مرة أخرى');
    return;
  }
  
  console.log('\n🌐 تشغيل ngrok...');
  console.log('سيتم إنشاء رابط عام للوصول إلى API');
  
  const ngrokProcess = spawn('ngrok', ['http', '3001'], {
    stdio: 'inherit'
  });
  
  console.log('\n📋 معلومات TestSprite:');
  console.log('- انسخ الرابط الذي يظهر من ngrok');
  console.log('- استخدمه كـ API Endpoint في TestSprite');
  console.log('- مثال: https://abc123.ngrok.io');
  
  ngrokProcess.on('error', (error) => {
    console.error('❌ خطأ في تشغيل ngrok:', error);
  });
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف ngrok...');
    ngrokProcess.kill('SIGINT');
    process.exit(0);
  });
}

main().catch(console.error);