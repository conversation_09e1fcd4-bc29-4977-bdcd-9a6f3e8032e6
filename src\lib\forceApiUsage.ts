/**
 * Force API Usage Module
 * 
 * This module ensures that all components use the API instead of localStorage
 * by overriding localStorage methods and redirecting to API calls.
 */

import { DEBUG_CONFIG } from '../config/dataSource';
import dataAdapter from './dataAdapter';

// Store original localStorage methods
const originalLocalStorage = {
  getItem: localStorage.getItem.bind(localStorage),
  setItem: localStorage.setItem.bind(localStorage),
  removeItem: localStorage.removeItem.bind(localStorage),
  clear: localStorage.clear.bind(localStorage)
};

// Keys that should be intercepted and redirected to API
const API_INTERCEPTED_KEYS = [
  'khanfashariya_db',
  'khanfashariya_systems',
  'khanfashariya_services',
  'khanfashariya_users',
  'khanfashariya_orders',
  'khanfashariya_premium'
];

// Keys that are allowed to use localStorage (auth tokens, settings, etc.)
const ALLOWED_LOCALSTORAGE_KEYS = [
  'khanfashariya_access_token',
  'khanfashariya_refresh_token',
  'khanfashariya_current_user',
  'khanfashariya_language',
  'khanfashariya_theme',
  'khanfashariya_settings'
];

/**
 * Check if a key should be intercepted
 */
function shouldInterceptKey(key: string): boolean {
  return API_INTERCEPTED_KEYS.some(interceptedKey => 
    key.includes(interceptedKey) || key.startsWith(interceptedKey)
  );
}

/**
 * Check if a key is allowed in localStorage
 */
function isAllowedKey(key: string): boolean {
  return ALLOWED_LOCALSTORAGE_KEYS.some(allowedKey => 
    key === allowedKey || key.startsWith(allowedKey)
  );
}

/**
 * Override localStorage.getItem
 */
function interceptGetItem(key: string): string | null {
  if (shouldInterceptKey(key) && !isAllowedKey(key)) {
    if (DEBUG_CONFIG.logApiCalls) {
      console.warn(`🚫 localStorage.getItem('${key}') intercepted - use API instead`);
    }
    
    // Return null to indicate no data in localStorage
    // This forces components to use API calls
    return null;
  }
  
  return originalLocalStorage.getItem(key);
}

/**
 * Override localStorage.setItem
 */
function interceptSetItem(key: string, value: string): void {
  if (shouldInterceptKey(key) && !isAllowedKey(key)) {
    if (DEBUG_CONFIG.logApiCalls) {
      console.warn(`🚫 localStorage.setItem('${key}') intercepted - use API instead`);
    }
    
    // Don't save to localStorage, just log the attempt
    console.warn(`Attempted to save ${key} to localStorage. Use API calls instead.`);
    return;
  }
  
  originalLocalStorage.setItem(key, value);
}

/**
 * Override localStorage.removeItem
 */
function interceptRemoveItem(key: string): void {
  if (shouldInterceptKey(key) && !isAllowedKey(key)) {
    if (DEBUG_CONFIG.logApiCalls) {
      console.warn(`🚫 localStorage.removeItem('${key}') intercepted - use API instead`);
    }
    return;
  }
  
  originalLocalStorage.removeItem(key);
}

/**
 * Override localStorage.clear
 */
function interceptClear(): void {
  if (DEBUG_CONFIG.logApiCalls) {
    console.warn('🚫 localStorage.clear() intercepted - clearing only allowed keys');
  }
  
  // Only clear allowed keys
  const keysToRemove: string[] = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && !isAllowedKey(key)) {
      keysToRemove.push(key);
    }
  }
  
  keysToRemove.forEach(key => originalLocalStorage.removeItem(key));
}

/**
 * Apply localStorage interception
 */
export function enableApiForcing(): void {
  if (DEBUG_CONFIG.logDataSource) {
    console.log('🔒 Enabling API forcing - localStorage access restricted');
  }
  
  // Override localStorage methods
  Object.defineProperty(localStorage, 'getItem', {
    value: interceptGetItem,
    writable: false,
    configurable: true
  });
  
  Object.defineProperty(localStorage, 'setItem', {
    value: interceptSetItem,
    writable: false,
    configurable: true
  });
  
  Object.defineProperty(localStorage, 'removeItem', {
    value: interceptRemoveItem,
    writable: false,
    configurable: true
  });
  
  Object.defineProperty(localStorage, 'clear', {
    value: interceptClear,
    writable: false,
    configurable: true
  });
}

/**
 * Disable localStorage interception (restore original behavior)
 */
export function disableApiForcing(): void {
  if (DEBUG_CONFIG.logDataSource) {
    console.log('🔓 Disabling API forcing - localStorage access restored');
  }
  
  // Restore original localStorage methods
  Object.defineProperty(localStorage, 'getItem', {
    value: originalLocalStorage.getItem,
    writable: true,
    configurable: true
  });
  
  Object.defineProperty(localStorage, 'setItem', {
    value: originalLocalStorage.setItem,
    writable: true,
    configurable: true
  });
  
  Object.defineProperty(localStorage, 'removeItem', {
    value: originalLocalStorage.removeItem,
    writable: true,
    configurable: true
  });
  
  Object.defineProperty(localStorage, 'clear', {
    value: originalLocalStorage.clear,
    writable: true,
    configurable: true
  });
}

/**
 * Clean up localStorage data that should be in API
 */
export function cleanupLocalStorageData(): void {
  if (DEBUG_CONFIG.logDataSource) {
    console.log('🧹 Cleaning up localStorage data that should be in API');
  }
  
  API_INTERCEPTED_KEYS.forEach(key => {
    if (localStorage.getItem(key)) {
      console.log(`Removing ${key} from localStorage`);
      originalLocalStorage.removeItem(key);
    }
  });
}

/**
 * Initialize API forcing
 */
export function initializeApiForcing(): void {
  // Clean up old localStorage data
  cleanupLocalStorageData();
  
  // Enable API forcing
  enableApiForcing();
  
  // Set dataAdapter to use API
  dataAdapter.setDataSource(true);
  
  if (DEBUG_CONFIG.logDataSource) {
    console.log('✅ API forcing initialized successfully');
  }
}

// Auto-initialize if in production or if forced
if (import.meta.env.NODE_ENV === 'production' || import.meta.env.VITE_FORCE_API === 'true') {
  initializeApiForcing();
}
