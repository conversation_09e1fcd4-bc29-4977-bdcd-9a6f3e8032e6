/**
 * Request Monitor Component
 * 
 * This component shows the current status of API requests
 * and helps debug infinite request loops
 */

import React, { useState, useEffect } from 'react';
import { getThrottlerStatus, clearRequestCache } from '../utils/requestThrottle';

interface RequestMonitorProps {
  show?: boolean;
}

const RequestMonitor: React.FC<RequestMonitorProps> = ({ show = false }) => {
  const [status, setStatus] = useState({
    activeRequests: 0,
    queuedRequests: 0,
    cachedKeys: [] as string[]
  });
  const [isVisible, setIsVisible] = useState(show);

  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      setStatus(getThrottlerStatus());
    }, 1000);

    return () => clearInterval(interval);
  }, [isVisible]);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-500 text-white px-3 py-2 rounded-lg text-sm z-50"
        title="Show Request Monitor"
      >
        📊 Monitor
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 z-50 max-w-sm">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-semibold text-gray-800">Request Monitor</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span>Active Requests:</span>
          <span className={`font-mono ${status.activeRequests > 3 ? 'text-red-600' : 'text-green-600'}`}>
            {status.activeRequests}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Queued Requests:</span>
          <span className={`font-mono ${status.queuedRequests > 0 ? 'text-yellow-600' : 'text-gray-600'}`}>
            {status.queuedRequests}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Cached APIs:</span>
          <span className="font-mono text-blue-600">
            {status.cachedKeys.length}
          </span>
        </div>
        
        {status.cachedKeys.length > 0 && (
          <div className="mt-3">
            <div className="text-xs text-gray-600 mb-1">Cached Keys:</div>
            <div className="max-h-20 overflow-y-auto text-xs">
              {status.cachedKeys.map((key, index) => (
                <div key={index} className="text-gray-500 truncate">
                  {key}
                </div>
              ))}
            </div>
          </div>
        )}
        
        <div className="mt-3 pt-2 border-t border-gray-200">
          <button
            onClick={() => clearRequestCache()}
            className="w-full bg-red-500 text-white px-3 py-1 rounded text-xs hover:bg-red-600"
          >
            Clear Cache
          </button>
        </div>
        
        {(status.activeRequests > 5 || status.queuedRequests > 3) && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
            ⚠️ High request activity detected! This might indicate an infinite loop.
          </div>
        )}
      </div>
    </div>
  );
};

export default RequestMonitor;