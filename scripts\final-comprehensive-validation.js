const axios = require('axios');
const mysql = require('mysql2/promise');

/**
 * Final Comprehensive Validation Script
 * 
 * Validates all aspects of the Premium Integration:
 * 1. Premium system creation and management
 * 2. Data consistency across all interfaces
 * 3. Statistics accuracy
 * 4. API functionality
 * 5. Admin panel operations
 */

class FinalValidation {
  constructor() {
    this.connection = null;
    this.adminToken = null;
    this.testResults = {
      premiumCreation: { passed: 0, failed: 0, tests: [] },
      dataConsistency: { passed: 0, failed: 0, tests: [] },
      statistics: { passed: 0, failed: 0, tests: [] },
      apiIntegration: { passed: 0, failed: 0, tests: [] },
      adminOperations: { passed: 0, failed: 0, tests: [] }
    };
  }

  async runFinalValidation() {
    console.log('🎯 FINAL COMPREHENSIVE VALIDATION');
    console.log('=' .repeat(70));
    console.log(`📅 Validation Date: ${new Date().toISOString()}`);
    console.log(`🎯 Objective: Validate complete Premium Integration\n`);

    try {
      await this.connect();
      
      // 1. Premium System Creation Tests
      await this.validatePremiumCreation();
      
      // 2. Data Consistency Tests
      await this.validateDataConsistency();
      
      // 3. Statistics Accuracy Tests
      await this.validateStatistics();
      
      // 4. API Integration Tests
      await this.validateApiIntegration();
      
      // 5. Admin Operations Tests
      await this.validateAdminOperations();
      
      // 6. Generate Final Report
      this.generateFinalValidationReport();
      
    } catch (error) {
      console.error('💥 Validation failed:', error.message);
    } finally {
      await this.cleanup();
    }
  }

  async connect() {
    // Database connection
    this.connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'khanfashariya_db'
    });

    // Admin authentication
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    this.adminToken = loginResponse.data.data.tokens.accessToken;
  }

  async validatePremiumCreation() {
    console.log('👑 Validating Premium System Creation...\n');

    // Test 1: Create Premium System
    await this.test('premiumCreation', 'Create Premium System', async () => {
      const headers = { Authorization: `Bearer ${this.adminToken}` };
      const premiumData = {
        name_ar: 'نظام اختبار التحقق النهائي',
        name_en: 'Final Validation Test System',
        description_ar: 'نظام للتحقق النهائي من وظائف Premium',
        description_en: 'System for final premium functionality validation',
        price: 999.99,
        category: 'validation',
        type: 'plugin',
        is_premium_addon: true,
        features_ar: ['ميزة اختبار 1', 'ميزة اختبار 2'],
        features_en: ['Test feature 1', 'Test feature 2'],
        status: 'active'
      };

      const response = await axios.post('http://localhost:3001/api/admin/systems', premiumData, { headers });
      
      if (response.status !== 200) throw new Error('Creation failed');
      if (!response.data.data.is_premium_addon) throw new Error('Premium flag not set');
      
      this.testSystemId = response.data.data.id;
      return true;
    });

    // Test 2: Verify Premium System in Database
    await this.test('premiumCreation', 'Verify in Database', async () => {
      const [systems] = await this.connection.execute(
        'SELECT id, name_ar, is_premium_addon, price FROM system_services WHERE id = ?',
        [this.testSystemId]
      );

      if (systems.length === 0) throw new Error('System not found in database');
      if (systems[0].is_premium_addon !== 1) throw new Error('Premium flag not saved correctly');
      if (parseFloat(systems[0].price) !== 999.99) throw new Error('Price not saved correctly');
      
      return true;
    });

    // Test 3: Verify Premium System in Public API
    await this.test('premiumCreation', 'Verify in Public API', async () => {
      const response = await axios.get('http://localhost:3001/api/systems');
      const systems = response.data.data.systems;
      const testSystem = systems.find(s => s.id === this.testSystemId);

      if (!testSystem) throw new Error('System not found in public API');
      if (!testSystem.is_premium_addon) throw new Error('Premium flag not visible in public API');
      
      return true;
    });
  }

  async validateDataConsistency() {
    console.log('\n🔄 Validating Data Consistency...\n');

    // Test 1: Database vs Public API Consistency
    await this.test('dataConsistency', 'Database vs Public API', async () => {
      const [dbActive] = await this.connection.execute(
        'SELECT COUNT(*) as count FROM system_services WHERE status = "active"'
      );
      
      const publicResponse = await axios.get('http://localhost:3001/api/systems');
      const publicCount = publicResponse.data.data.systems.length;

      if (dbActive[0].count !== publicCount) {
        throw new Error(`DB active: ${dbActive[0].count}, Public API: ${publicCount}`);
      }
      
      return true;
    });

    // Test 2: Database vs Admin API Consistency
    await this.test('dataConsistency', 'Database vs Admin API', async () => {
      const [dbTotal] = await this.connection.execute(
        'SELECT COUNT(*) as count FROM system_services'
      );
      
      const headers = { Authorization: `Bearer ${this.adminToken}` };
      const adminResponse = await axios.get('http://localhost:3001/api/systems/admin', { headers });
      const adminCount = adminResponse.data.length;

      if (dbTotal[0].count !== adminCount) {
        throw new Error(`DB total: ${dbTotal[0].count}, Admin API: ${adminCount}`);
      }
      
      return true;
    });

    // Test 3: Premium Systems Consistency
    await this.test('dataConsistency', 'Premium Systems Consistency', async () => {
      const [dbPremium] = await this.connection.execute(
        'SELECT COUNT(*) as count FROM system_services WHERE is_premium_addon = 1 AND status = "active"'
      );
      
      const publicResponse = await axios.get('http://localhost:3001/api/systems');
      const publicPremium = publicResponse.data.data.systems.filter(s => s.is_premium_addon).length;

      if (dbPremium[0].count !== publicPremium) {
        throw new Error(`DB premium: ${dbPremium[0].count}, Public premium: ${publicPremium}`);
      }
      
      return true;
    });
  }

  async validateStatistics() {
    console.log('\n📊 Validating Statistics Accuracy...\n');

    // Test 1: System Count Statistics
    await this.test('statistics', 'System Count Statistics', async () => {
      const [stats] = await this.connection.execute(`
        SELECT 
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
          COUNT(CASE WHEN is_premium_addon = 1 THEN 1 END) as premium
        FROM system_services
      `);

      const dbStats = stats[0];
      
      // Verify counts are reasonable
      if (dbStats.total < 5) throw new Error('Too few total systems');
      if (dbStats.active < 3) throw new Error('Too few active systems');
      if (dbStats.premium < 1) throw new Error('No premium systems found');
      
      console.log(`   📊 Total: ${dbStats.total}, Active: ${dbStats.active}, Premium: ${dbStats.premium}`);
      return true;
    });

    // Test 2: Price Statistics
    await this.test('statistics', 'Price Statistics', async () => {
      const [priceStats] = await this.connection.execute(`
        SELECT 
          AVG(price) as avg_price,
          SUM(price) as total_value,
          MIN(price) as min_price,
          MAX(price) as max_price
        FROM system_services 
        WHERE status = 'active' AND price > 0
      `);

      const stats = priceStats[0];
      
      if (stats.avg_price <= 0) throw new Error('Invalid average price');
      if (stats.total_value <= 0) throw new Error('Invalid total value');
      if (stats.max_price <= stats.min_price) throw new Error('Invalid price range');
      
      console.log(`   💰 Avg: $${parseFloat(stats.avg_price).toFixed(2)}, Total: $${parseFloat(stats.total_value).toFixed(2)}`);
      return true;
    });
  }

  async validateApiIntegration() {
    console.log('\n🌐 Validating API Integration...\n');

    // Test 1: Premium Content API
    await this.test('apiIntegration', 'Premium Content API', async () => {
      const response = await axios.get('http://localhost:3001/api/premium');
      
      if (response.status !== 200) throw new Error('Premium API failed');
      if (!response.data.success) throw new Error('Premium API returned error');
      if (!Array.isArray(response.data.data.premiumContent)) throw new Error('Invalid premium content format');
      
      return true;
    });

    // Test 2: Admin Premium API
    await this.test('apiIntegration', 'Admin Premium API', async () => {
      const headers = { Authorization: `Bearer ${this.adminToken}` };
      const response = await axios.get('http://localhost:3001/api/premium/admin', { headers });
      
      if (response.status !== 200) throw new Error('Admin Premium API failed');
      if (!response.data.success) throw new Error('Admin Premium API returned error');
      
      const data = response.data.data;
      if (!Array.isArray(data.premiumContent)) throw new Error('Invalid premium content');
      if (!Array.isArray(data.availableSystems)) throw new Error('Invalid available systems');
      
      console.log(`   📊 Premium: ${data.premiumContent.length}, Systems: ${data.availableSystems.length}`);
      return true;
    });
  }

  async validateAdminOperations() {
    console.log('\n👨‍💼 Validating Admin Operations...\n');

    // Test 1: System Update Operation
    await this.test('adminOperations', 'System Update', async () => {
      const headers = { Authorization: `Bearer ${this.adminToken}` };
      const updateData = {
        name_ar: 'نظام اختبار محدث',
        price: 1299.99
      };

      const response = await axios.put(`http://localhost:3001/api/admin/systems/${this.testSystemId}`, updateData, { headers });
      
      if (response.status !== 200) throw new Error('Update failed');
      if (parseFloat(response.data.data.price) !== 1299.99) throw new Error('Price update failed');
      
      return true;
    });

    // Test 2: System Status Toggle
    await this.test('adminOperations', 'System Status Toggle', async () => {
      const headers = { Authorization: `Bearer ${this.adminToken}` };
      
      // Set to inactive
      await axios.put(`http://localhost:3001/api/admin/systems/${this.testSystemId}`, { status: 'inactive' }, { headers });
      
      // Verify not in public API
      const publicResponse = await axios.get('http://localhost:3001/api/systems');
      const inPublic = publicResponse.data.data.systems.some(s => s.id === this.testSystemId);
      
      if (inPublic) throw new Error('Inactive system still in public API');
      
      // Set back to active
      await axios.put(`http://localhost:3001/api/admin/systems/${this.testSystemId}`, { status: 'active' }, { headers });
      
      return true;
    });
  }

  async test(category, name, testFunction) {
    try {
      console.log(`   🧪 ${name}...`);
      await testFunction();
      this.testResults[category].passed++;
      this.testResults[category].tests.push({ name, status: 'PASSED' });
      console.log(`   ✅ ${name} - PASSED`);
    } catch (error) {
      this.testResults[category].failed++;
      this.testResults[category].tests.push({ name, status: 'FAILED', error: error.message });
      console.log(`   ❌ ${name} - FAILED: ${error.message}`);
    }
  }

  generateFinalValidationReport() {
    console.log('\n' + '=' .repeat(70));
    console.log('🎯 FINAL VALIDATION RESULTS');
    console.log('=' .repeat(70));

    let totalPassed = 0;
    let totalFailed = 0;

    for (const [category, results] of Object.entries(this.testResults)) {
      const categoryPassed = results.passed;
      const categoryFailed = results.failed;
      const categoryTotal = categoryPassed + categoryFailed;
      
      totalPassed += categoryPassed;
      totalFailed += categoryFailed;
      
      console.log(`\n📋 ${category.toUpperCase()}:`);
      console.log(`   ✅ Passed: ${categoryPassed}/${categoryTotal}`);
      console.log(`   ❌ Failed: ${categoryFailed}/${categoryTotal}`);
      console.log(`   📈 Success Rate: ${categoryTotal > 0 ? Math.round((categoryPassed / categoryTotal) * 100) : 0}%`);
    }

    const totalTests = totalPassed + totalFailed;
    const overallSuccessRate = totalTests > 0 ? Math.round((totalPassed / totalTests) * 100) : 0;

    console.log('\n' + '=' .repeat(70));
    console.log('🏆 OVERALL VALIDATION RESULTS:');
    console.log(`   📊 Total Tests: ${totalTests}`);
    console.log(`   ✅ Passed: ${totalPassed}`);
    console.log(`   ❌ Failed: ${totalFailed}`);
    console.log(`   📈 Success Rate: ${overallSuccessRate}%`);

    if (overallSuccessRate >= 95) {
      console.log('\n🎉 EXCELLENT! Premium Integration is FULLY OPERATIONAL!');
      console.log('✅ All critical systems working perfectly');
      console.log('🚀 Ready for production deployment');
    } else if (overallSuccessRate >= 85) {
      console.log('\n✅ GOOD! Most systems working correctly');
      console.log('⚠️ Some minor issues need attention');
    } else {
      console.log('\n⚠️ NEEDS ATTENTION! Several issues found');
      console.log('❌ Review failed tests and fix issues');
    }

    console.log('\n📅 Validation completed at:', new Date().toISOString());
    console.log('=' .repeat(70));
  }

  async cleanup() {
    // Clean up test system
    if (this.testSystemId && this.adminToken) {
      try {
        const headers = { Authorization: `Bearer ${this.adminToken}` };
        await axios.delete(`http://localhost:3001/api/admin/systems/${this.testSystemId}`, { headers });
        console.log('\n🧹 Test system cleaned up');
      } catch (error) {
        console.log('\n⚠️ Cleanup warning:', error.message);
      }
    }

    if (this.connection) {
      await this.connection.end();
    }
  }
}

// Main execution
async function main() {
  const validator = new FinalValidation();
  await validator.runFinalValidation();
}

// Export for use in other scripts
module.exports = FinalValidation;

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
