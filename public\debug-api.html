<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 اختبار API مباشر</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #1976D2;
        }
        pre {
            background: #1e1e1e;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #444;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-online { background: #4CAF50; }
        .status-offline { background: #f44336; }
        .status-loading { background: #ff9800; animation: pulse 1s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار API مباشر - الخنفشارية</h1>
        
        <div class="test-section">
            <h2>📊 حالة الخوادم</h2>
            <div id="server-status">
                <p>Backend Server (3001): <span class="status-indicator status-loading" id="backend-status"></span> <span id="backend-text">جاري الفحص...</span></p>
                <p>Frontend Server (5173): <span class="status-indicator status-loading" id="frontend-status"></span> <span id="frontend-text">جاري الفحص...</span></p>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 اختبار APIs</h2>
            <div class="grid">
                <div>
                    <h3>الأنظمة التقنية</h3>
                    <button onclick="testSystems('direct')">اختبار مباشر (3001)</button>
                    <button onclick="testSystems('proxy')">اختبار Proxy (5173)</button>
                    <div id="systems-result"></div>
                </div>
                
                <div>
                    <h3>الخدمات التقنية</h3>
                    <button onclick="testServices('direct')">اختبار مباشر (3001)</button>
                    <button onclick="testServices('proxy')">اختبار Proxy (5173)</button>
                    <div id="services-result"></div>
                </div>
                
                <div>
                    <h3>المحتوى المميز</h3>
                    <button onclick="testPremium('direct')">اختبار مباشر (3001)</button>
                    <button onclick="testPremium('proxy')">اختبار Proxy (5173)</button>
                    <div id="premium-result"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 معلومات التكوين</h2>
            <div id="config-info">
                <p><strong>Current URL:</strong> <span id="current-url"></span></p>
                <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
                <p><strong>Environment:</strong> <span id="environment"></span></p>
            </div>
        </div>

        <div class="test-section">
            <h2>📝 سجل الأحداث</h2>
            <button onclick="clearLog()">مسح السجل</button>
            <pre id="log-output"></pre>
        </div>
    </div>

    <script>
        let logOutput = '';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            logOutput += `[${timestamp}] ${message}\n`;
            document.getElementById('log-output').innerHTML = logOutput;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function clearLog() {
            logOutput = '';
            document.getElementById('log-output').innerHTML = '';
        }
        
        async function checkServerStatus() {
            // Check backend server
            try {
                const response = await fetch('http://localhost:3001/api/systems', {
                    headers: { 'ngrok-skip-browser-warning': 'true' }
                });
                if (response.ok) {
                    document.getElementById('backend-status').className = 'status-indicator status-online';
                    document.getElementById('backend-text').textContent = 'متصل';
                    log('Backend server (3001) is online', 'success');
                } else {
                    throw new Error(`Status: ${response.status}`);
                }
            } catch (error) {
                document.getElementById('backend-status').className = 'status-indicator status-offline';
                document.getElementById('backend-text').textContent = 'غير متصل';
                log(`Backend server (3001) is offline: ${error.message}`, 'error');
            }
            
            // Check frontend proxy
            try {
                const response = await fetch('/api/systems');
                if (response.ok) {
                    document.getElementById('frontend-status').className = 'status-indicator status-online';
                    document.getElementById('frontend-text').textContent = 'متصل';
                    log('Frontend proxy (5173) is online', 'success');
                } else {
                    throw new Error(`Status: ${response.status}`);
                }
            } catch (error) {
                document.getElementById('frontend-status').className = 'status-indicator status-offline';
                document.getElementById('frontend-text').textContent = 'غير متصل';
                log(`Frontend proxy (5173) is offline: ${error.message}`, 'error');
            }
        }
        
        async function testSystems(type) {
            const resultDiv = document.getElementById('systems-result');
            resultDiv.innerHTML = '<p class="info">جاري الاختبار...</p>';
            
            const url = type === 'direct' ? 'http://localhost:3001/api/systems' : '/api/systems';
            log(`Testing systems API: ${url}`);
            
            try {
                const response = await fetch(url, {
                    headers: { 'ngrok-skip-browser-warning': 'true' }
                });
                const data = await response.json();
                
                if (data.success) {
                    const systems = data.data.systems || data.data || [];
                    resultDiv.innerHTML = `
                        <p class="success">✅ نجح (${type})</p>
                        <p>عدد الأنظمة: ${systems.length}</p>
                        <pre>${JSON.stringify(systems, null, 2)}</pre>
                    `;
                    log(`Systems API (${type}) success: ${systems.length} items`, 'success');
                } else {
                    throw new Error(data.error || 'Unknown error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ فشل (${type}): ${error.message}</p>`;
                log(`Systems API (${type}) failed: ${error.message}`, 'error');
            }
        }
        
        async function testServices(type) {
            const resultDiv = document.getElementById('services-result');
            resultDiv.innerHTML = '<p class="info">جاري الاختبار...</p>';
            
            const url = type === 'direct' ? 'http://localhost:3001/api/services/technical' : '/api/services/technical';
            log(`Testing services API: ${url}`);
            
            try {
                const response = await fetch(url, {
                    headers: { 'ngrok-skip-browser-warning': 'true' }
                });
                const data = await response.json();
                
                if (data.success) {
                    const services = data.data.services || data.data || [];
                    resultDiv.innerHTML = `
                        <p class="success">✅ نجح (${type})</p>
                        <p>عدد الخدمات: ${services.length}</p>
                        <pre>${JSON.stringify(services, null, 2)}</pre>
                    `;
                    log(`Services API (${type}) success: ${services.length} items`, 'success');
                } else {
                    throw new Error(data.error || 'Unknown error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ فشل (${type}): ${error.message}</p>`;
                log(`Services API (${type}) failed: ${error.message}`, 'error');
            }
        }
        
        async function testPremium(type) {
            const resultDiv = document.getElementById('premium-result');
            resultDiv.innerHTML = '<p class="info">جاري الاختبار...</p>';
            
            const url = type === 'direct' ? 'http://localhost:3001/api/services/premium' : '/api/services/premium';
            log(`Testing premium API: ${url}`);
            
            try {
                const response = await fetch(url, {
                    headers: { 'ngrok-skip-browser-warning': 'true' }
                });
                const data = await response.json();
                
                if (data.success) {
                    const premium = data.data.premiumContent || data.data || [];
                    resultDiv.innerHTML = `
                        <p class="success">✅ نجح (${type})</p>
                        <p>عدد المحتوى المميز: ${premium.length}</p>
                        <pre>${JSON.stringify(premium, null, 2)}</pre>
                    `;
                    log(`Premium API (${type}) success: ${premium.length} items`, 'success');
                } else {
                    throw new Error(data.error || 'Unknown error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">❌ فشل (${type}): ${error.message}</p>`;
                log(`Premium API (${type}) failed: ${error.message}`, 'error');
            }
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('user-agent').textContent = navigator.userAgent;
            document.getElementById('environment').textContent = 'Development';
            
            log('Debug page loaded', 'info');
            checkServerStatus();
        });
    </script>
</body>
</html>
