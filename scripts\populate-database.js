#!/usr/bin/env node

/**
 * Database Population Script for Khanfashariya.com
 * 
 * This script populates the MySQL database with sample data
 * to test the API migration from localStorage.
 */

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'khanfashariya_db',
  charset: 'utf8mb4'
};

console.log('🗄️ Connecting to database...');
console.log(`📊 Database: ${dbConfig.database}@${dbConfig.host}:${dbConfig.port}`);

/**
 * Generate UUID
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Hash password
 */
async function hashPassword(password) {
  return await bcrypt.hash(password, 10);
}

/**
 * Populate users table
 */
async function populateUsers(connection) {
  console.log('👥 Populating users...');
  
  // Check if admin user exists
  const [existingAdmin] = await connection.execute(
    'SELECT id FROM users WHERE email = ? OR id = ?',
    ['<EMAIL>', 'admin-user-id-2024']
  );
  
  if (existingAdmin.length === 0) {
    const adminPassword = await hashPassword('admin123');
    await connection.execute(`
      INSERT INTO users (id, email, username, full_name, role, password_hash, status, login_count)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      'admin-user-id-2024',
      '<EMAIL>',
      'admin',
      'Updated Admin User',
      'admin',
      adminPassword,
      'active',
      0
    ]);
    console.log('✅ Admin user created');
  } else {
    // Update existing admin user
    const adminPassword = await hashPassword('admin123');
    await connection.execute(`
      UPDATE users SET password_hash = ?, full_name = ?, updated_at = CURRENT_TIMESTAMP
      WHERE email = ?
    `, [adminPassword, 'Updated Admin User', '<EMAIL>']);
    console.log('✅ Admin user updated');
  }
  
  // Check if test user exists
  const [existingTest] = await connection.execute(
    'SELECT id FROM users WHERE email = ?',
    ['<EMAIL>']
  );
  
  if (existingTest.length === 0) {
    const testPassword = await hashPassword('test123');
    await connection.execute(`
      INSERT INTO users (id, email, username, full_name, role, password_hash, status)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      generateUUID(),
      '<EMAIL>',
      'testuser',
      'مستخدم تجريبي',
      'user',
      testPassword,
      'active'
    ]);
    console.log('✅ Test user created');
  } else {
    console.log('ℹ️ Test user already exists');
  }
}

/**
 * Populate system services
 */
async function populateSystemServices(connection) {
  console.log('🖥️ Populating system services...');
  
  const [existing] = await connection.execute('SELECT COUNT(*) as count FROM system_services');
  if (existing[0].count > 0) {
    console.log('ℹ️ System services already exist');
    return;
  }
  
  const systems = [
    {
      id: generateUUID(),
      name_ar: 'نظام إدارة المحتوى المتقدم',
      name_en: 'Advanced Content Management System',
      description_ar: 'نظام متقدم لإدارة المحتوى مع واجهة حديثة وميزات قوية',
      description_en: 'Advanced content management system with modern interface and powerful features',
      price: 150.00,
      category: 'management',
      type: 'regular',
      features_ar: JSON.stringify(['إدارة سهلة', 'واجهة حديثة', 'أمان عالي']),
      features_en: JSON.stringify(['Easy Management', 'Modern Interface', 'High Security']),
      tech_specs_ar: JSON.stringify(['MySQL 8.0+', 'PHP 8.0+', 'Apache/Nginx']),
      tech_specs_en: JSON.stringify(['MySQL 8.0+', 'PHP 8.0+', 'Apache/Nginx']),
      status: 'active',
      featured: true
    },
    {
      id: generateUUID(),
      name_ar: 'نظام القتال المطور',
      name_en: 'Enhanced Combat System',
      description_ar: 'نظام قتال متطور مع ميزات جديدة ومثيرة',
      description_en: 'Enhanced combat system with new and exciting features',
      price: 200.00,
      category: 'combat',
      type: 'regular',
      features_ar: JSON.stringify(['مهارات جديدة', 'تأثيرات بصرية', 'توازن محسن']),
      features_en: JSON.stringify(['New Skills', 'Visual Effects', 'Improved Balance']),
      tech_specs_ar: JSON.stringify(['C++ 17', 'DirectX 11', 'Windows 10+']),
      tech_specs_en: JSON.stringify(['C++ 17', 'DirectX 11', 'Windows 10+']),
      status: 'active',
      featured: false
    },
    {
      id: generateUUID(),
      name_ar: 'نظام الاقتصاد الذكي',
      name_en: 'Smart Economy System',
      description_ar: 'نظام اقتصادي ذكي يدير الأسعار والتجارة تلقائياً',
      description_en: 'Smart economy system that manages prices and trade automatically',
      price: 300.00,
      category: 'economy',
      type: 'plugin',
      features_ar: JSON.stringify(['تسعير تلقائي', 'إحصائيات متقدمة', 'تحكم كامل']),
      features_en: JSON.stringify(['Auto Pricing', 'Advanced Statistics', 'Full Control']),
      tech_specs_ar: JSON.stringify(['Python 3.9+', 'Redis', 'PostgreSQL']),
      tech_specs_en: JSON.stringify(['Python 3.9+', 'Redis', 'PostgreSQL']),
      status: 'active',
      featured: true
    }
  ];
  
  for (const system of systems) {
    await connection.execute(`
      INSERT INTO system_services (
        id, name_ar, name_en, description_ar, description_en, price, category, type,
        features_ar, features_en, tech_specs_ar, tech_specs_en, status, featured
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      system.id, system.name_ar, system.name_en, system.description_ar, system.description_en,
      system.price, system.category, system.type, system.features_ar, system.features_en,
      system.tech_specs_ar, system.tech_specs_en, system.status, system.featured
    ]);
  }
  
  console.log(`✅ Created ${systems.length} system services`);
}

/**
 * Populate technical services
 */
async function populateTechnicalServices(connection) {
  console.log('🛠️ Populating technical services...');
  
  const [existing] = await connection.execute('SELECT COUNT(*) as count FROM technical_services');
  if (existing[0].count > 0) {
    console.log('ℹ️ Technical services already exist');
    return;
  }
  
  const services = [
    {
      id: generateUUID(),
      name_ar: 'خدمة التطوير المخصص',
      name_en: 'Custom Development Service',
      description_ar: 'خدمة تطوير مخصصة حسب احتياجاتك الخاصة',
      description_en: 'Custom development service tailored to your specific needs',
      price: 500.00,
      category: 'development',
      service_type: 'development',
      features_ar: JSON.stringify(['تطوير مخصص', 'دعم مباشر', 'ضمان جودة']),
      features_en: JSON.stringify(['Custom Development', 'Direct Support', 'Quality Assurance']),
      status: 'active'
    },
    {
      id: generateUUID(),
      name_ar: 'استشارة تقنية',
      name_en: 'Technical Consultation',
      description_ar: 'استشارة تقنية من خبراء متخصصين',
      description_en: 'Technical consultation from specialized experts',
      price: 100.00,
      category: 'consultation',
      service_type: 'consultation',
      features_ar: JSON.stringify(['خبرة عالية', 'حلول مبتكرة', 'متابعة مستمرة']),
      features_en: JSON.stringify(['High Expertise', 'Innovative Solutions', 'Continuous Follow-up']),
      status: 'active'
    }
  ];
  
  for (const service of services) {
    await connection.execute(`
      INSERT INTO technical_services (
        id, name_ar, name_en, description_ar, description_en, price, category, service_type,
        features_ar, features_en, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      service.id, service.name_ar, service.name_en, service.description_ar, service.description_en,
      service.price, service.category, service.service_type, service.features_ar, service.features_en,
      service.status
    ]);
  }
  
  console.log(`✅ Created ${services.length} technical services`);
}

/**
 * Main function
 */
async function populateDatabase() {
  let connection;
  
  try {
    console.log('🔌 Connecting to MySQL...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');
    
    await populateUsers(connection);
    await populateSystemServices(connection);
    await populateTechnicalServices(connection);
    
    console.log('🎉 Database population completed successfully!');
    
  } catch (error) {
    console.error('❌ Error populating database:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run if called directly
if (require.main === module) {
  populateDatabase();
}

module.exports = { populateDatabase };
