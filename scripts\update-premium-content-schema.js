#!/usr/bin/env node

/**
 * Update Premium Content Schema Script
 * 
 * Updates the premium_content table and creates new tables for premium integration
 */

const mysql = require('mysql2/promise');

const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khan<PERSON><PERSON>riya_db',
  charset: 'utf8mb4'
};

async function updatePremiumContentSchema() {
  console.log('🔄 Updating Premium Content Schema');
  console.log('===================================\n');
  
  let connection;
  
  try {
    connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ Connected to MySQL database\n');
    
    // 1. Update premium_content table with new fields
    console.log('1️⃣ Updating premium_content table...');
    
    const premiumContentUpdates = [
      { name: 'detailed_description_ar', definition: 'TEXT NULL' },
      { name: 'detailed_description_en', definition: 'TEXT NULL' },
      { name: 'original_price', definition: 'DECIMAL(10,2) NULL' },
      { name: 'discount_percentage', definition: 'INT DEFAULT 0' },
      { name: 'tech_specs_ar', definition: 'JSON NULL' },
      { name: 'tech_specs_en', definition: 'JSON NULL' },
      { name: 'is_active_edition', definition: 'TINYINT(1) DEFAULT 0 COMMENT "Only one premium edition can be active at a time"' },
      { name: 'installation_guide_ar', definition: 'TEXT NULL' },
      { name: 'installation_guide_en', definition: 'TEXT NULL' },
      { name: 'support_info_ar', definition: 'TEXT NULL' },
      { name: 'support_info_en', definition: 'TEXT NULL' }
    ];
    
    // Check existing columns
    const [existingColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'premium_content'
    `, [DB_CONFIG.database]);
    
    const existingColumnNames = existingColumns.map(col => col.COLUMN_NAME);
    
    for (const column of premiumContentUpdates) {
      if (!existingColumnNames.includes(column.name)) {
        await connection.execute(`ALTER TABLE premium_content ADD COLUMN ${column.name} ${column.definition}`);
        console.log(`   ✅ Added premium_content.${column.name} column`);
      } else {
        console.log(`   ⚠️  Column premium_content.${column.name} already exists`);
      }
    }
    
    // 2. Create premium_system_pricing table
    console.log('\n2️⃣ Creating premium_system_pricing table...');
    
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS premium_system_pricing (
        id VARCHAR(36) PRIMARY KEY,
        system_id VARCHAR(36) NOT NULL,
        premium_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Special price for premium edition customers',
        installation_included TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'Installation included in premium price',
        maintenance_included TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'Maintenance included in premium price',
        is_available_for_premium TINYINT(1) NOT NULL DEFAULT 1 COMMENT 'Available as premium add-on',
        description_ar TEXT NULL,
        description_en TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_system_pricing (system_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('   ✅ premium_system_pricing table created');
    
    // 3. Create premium_service_pricing table
    console.log('\n3️⃣ Creating premium_service_pricing table...');
    
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS premium_service_pricing (
        id VARCHAR(36) PRIMARY KEY,
        service_id VARCHAR(36) NOT NULL,
        premium_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Special price for premium edition customers',
        installation_included TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'Installation included in premium price',
        maintenance_included TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'Maintenance included in premium price',
        is_available_for_premium TINYINT(1) NOT NULL DEFAULT 1 COMMENT 'Available as premium add-on',
        subscription_discount_percentage INT DEFAULT 0 COMMENT 'Discount percentage for subscription services',
        description_ar TEXT NULL,
        description_en TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_service_pricing (service_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('   ✅ premium_service_pricing table created');
    
    // 4. Add unique constraint for is_active_edition (only one active premium edition)
    console.log('\n4️⃣ Adding unique constraint for active edition...');
    
    try {
      await connection.execute(`
        ALTER TABLE premium_content 
        ADD CONSTRAINT unique_active_edition 
        UNIQUE (is_active_edition)
      `);
      console.log('   ✅ Unique constraint for active edition added');
    } catch (error) {
      if (error.code === 'ER_DUP_KEYNAME') {
        console.log('   ⚠️  Unique constraint already exists');
      } else {
        console.log(`   ⚠️  Could not add unique constraint: ${error.message}`);
      }
    }
    
    // 5. Update existing premium content with new structure
    console.log('\n5️⃣ Updating existing premium content...');
    
    await connection.execute(`
      UPDATE premium_content 
      SET 
        detailed_description_ar = CONCAT('وصف مفصل: ', description_ar),
        detailed_description_en = CONCAT('Detailed description: ', description_en),
        tech_specs_ar = JSON_ARRAY('متوافق مع Metin2', 'قاعدة بيانات MySQL', 'PHP 7.4+', 'خادم Linux'),
        tech_specs_en = JSON_ARRAY('Compatible with Metin2', 'MySQL Database', 'PHP 7.4+', 'Linux Server'),
        installation_guide_ar = 'دليل التنصيب متوفر مع الحزمة',
        installation_guide_en = 'Installation guide included with package',
        support_info_ar = 'دعم فني متاح 24/7',
        support_info_en = '24/7 technical support available'
      WHERE detailed_description_ar IS NULL
    `);
    console.log('   ✅ Existing premium content updated');
    
    console.log('\n🎉 Premium Content Schema updated successfully!');
    
  } catch (error) {
    console.error('❌ Update failed:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Generate UUID function
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Run update
updatePremiumContentSchema().catch(error => {
  console.error('Update failed:', error);
  process.exit(1);
});
