#!/usr/bin/env node

/**
 * Fix services tables by adding missing columns
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixServicesTables() {
  let connection;
  
  try {
    console.log('🔧 Fixing services tables...');
    
    // Connect to database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '123456',
      database: 'khanfashariya_db',
      charset: 'utf8mb4'
    });
    
    console.log('✅ Connected to database');
    
    // Fix system_services table
    const [systemColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'khanfashariya_db' 
      AND TABLE_NAME = 'system_services'
    `);
    
    const systemColumnNames = systemColumns.map(col => col.COLUMN_NAME);
    
    const systemColumnsToAdd = [
      { name: 'type', definition: 'ENUM("regular", "plugin") DEFAULT "regular"' },
      { name: 'is_premium_addon', definition: 'BOOLEAN DEFAULT FALSE' },
      { name: 'features_ar', definition: 'JSON' },
      { name: 'features_en', definition: 'JSON' },
      { name: 'tech_specs_ar', definition: 'JSON' },
      { name: 'tech_specs_en', definition: 'JSON' },
      { name: 'video_url', definition: 'VARCHAR(500)' },
      { name: 'image_url', definition: 'VARCHAR(500)' },
      { name: 'gallery_images', definition: 'JSON' },
      { name: 'download_url', definition: 'VARCHAR(500)' },
      { name: 'version', definition: 'VARCHAR(50)' },
      { name: 'file_size', definition: 'VARCHAR(50)' },
      { name: 'requirements_ar', definition: 'TEXT' },
      { name: 'requirements_en', definition: 'TEXT' },
      { name: 'installation_guide_ar', definition: 'TEXT' },
      { name: 'installation_guide_en', definition: 'TEXT' },
      { name: 'featured', definition: 'BOOLEAN DEFAULT FALSE' },
      { name: 'sort_order', definition: 'INT DEFAULT 0' },
      { name: 'download_count', definition: 'INT DEFAULT 0' },
      { name: 'rating', definition: 'DECIMAL(3,2) DEFAULT 0.00' },
      { name: 'rating_count', definition: 'INT DEFAULT 0' }
    ];
    
    for (const column of systemColumnsToAdd) {
      if (!systemColumnNames.includes(column.name)) {
        await connection.execute(`ALTER TABLE system_services ADD COLUMN ${column.name} ${column.definition}`);
        console.log(`✅ Added system_services.${column.name} column`);
      }
    }
    
    // Fix technical_services table
    const [techColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'khanfashariya_db' 
      AND TABLE_NAME = 'technical_services'
    `);
    
    const techColumnNames = techColumns.map(col => col.COLUMN_NAME);
    
    const techColumnsToAdd = [
      { name: 'service_type', definition: 'ENUM("development", "consultation", "support", "customization") DEFAULT "development"' },
      { name: 'features_ar', definition: 'JSON' },
      { name: 'features_en', definition: 'JSON' },
      { name: 'delivery_time_ar', definition: 'VARCHAR(100)' },
      { name: 'delivery_time_en', definition: 'VARCHAR(100)' },
      { name: 'video_url', definition: 'VARCHAR(500)' },
      { name: 'image_url', definition: 'VARCHAR(500)' },
      { name: 'gallery_images', definition: 'JSON' },
      { name: 'requirements_ar', definition: 'TEXT' },
      { name: 'requirements_en', definition: 'TEXT' },
      { name: 'process_steps_ar', definition: 'JSON' },
      { name: 'process_steps_en', definition: 'JSON' },
      { name: 'featured', definition: 'BOOLEAN DEFAULT FALSE' },
      { name: 'sort_order', definition: 'INT DEFAULT 0' },
      { name: 'order_count', definition: 'INT DEFAULT 0' },
      { name: 'rating', definition: 'DECIMAL(3,2) DEFAULT 0.00' },
      { name: 'rating_count', definition: 'INT DEFAULT 0' }
    ];
    
    for (const column of techColumnsToAdd) {
      if (!techColumnNames.includes(column.name)) {
        await connection.execute(`ALTER TABLE technical_services ADD COLUMN ${column.name} ${column.definition}`);
        console.log(`✅ Added technical_services.${column.name} column`);
      }
    }
    
    // Fix orders table
    const [orderColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'khanfashariya_db' 
      AND TABLE_NAME = 'orders'
    `);
    
    const orderColumnNames = orderColumns.map(col => col.COLUMN_NAME);
    
    const orderColumnsToAdd = [
      { name: 'quantity', definition: 'INT DEFAULT 1' },
      { name: 'unit_price', definition: 'DECIMAL(10,2) NOT NULL DEFAULT 0.00' },
      { name: 'total_price', definition: 'DECIMAL(10,2) NOT NULL DEFAULT 0.00' },
      { name: 'discount_amount', definition: 'DECIMAL(10,2) DEFAULT 0.00' },
      { name: 'currency', definition: 'VARCHAR(3) DEFAULT "USD"' },
      { name: 'payment_status', definition: 'ENUM("pending", "paid", "failed", "refunded") DEFAULT "pending"' },
      { name: 'payment_method', definition: 'VARCHAR(50)' },
      { name: 'payment_reference', definition: 'VARCHAR(255)' },
      { name: 'notes_ar', definition: 'TEXT' },
      { name: 'notes_en', definition: 'TEXT' },
      { name: 'admin_notes', definition: 'TEXT' },
      { name: 'delivery_date', definition: 'DATE' },
      { name: 'completion_date', definition: 'TIMESTAMP NULL' }
    ];
    
    for (const column of orderColumnsToAdd) {
      if (!orderColumnNames.includes(column.name)) {
        // Handle special case for order_type enum update
        if (column.name === 'order_type') {
          await connection.execute(`ALTER TABLE orders MODIFY COLUMN order_type ENUM('system_service', 'technical_service', 'premium_content', 'premium_package') NOT NULL`);
          console.log(`✅ Updated orders.order_type enum`);
        } else {
          await connection.execute(`ALTER TABLE orders ADD COLUMN ${column.name} ${column.definition}`);
          console.log(`✅ Added orders.${column.name} column`);
        }
      }
    }
    
    // Update order_type enum
    try {
      await connection.execute(`ALTER TABLE orders MODIFY COLUMN order_type ENUM('system_service', 'technical_service', 'premium_content', 'premium_package') NOT NULL`);
      console.log(`✅ Updated orders.order_type enum`);
    } catch (error) {
      console.log(`⚠️  order_type enum already updated or error: ${error.message}`);
    }
    
    console.log('🎉 Services tables fixed successfully!');
    
  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run fix
fixServicesTables().catch(error => {
  console.error('Fix failed:', error);
  process.exit(1);
});
