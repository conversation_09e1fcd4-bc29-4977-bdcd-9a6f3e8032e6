/**
 * Orders Schema Migration Script
 * Safely migrates existing orders to enhanced schema
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

class OrdersMigration {
  constructor() {
    this.connection = null;
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '123456',
        database: process.env.DB_NAME || 'khanfashariya_db',
        charset: 'utf8mb4'
      });
      console.log('✅ Connected to database');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      throw error;
    }
  }

  async backupExistingOrders() {
    try {
      console.log('📦 Creating backup of existing orders...');
      
      // Create backup table
      await this.connection.execute(`
        CREATE TABLE IF NOT EXISTS orders_backup_${Date.now()} AS 
        SELECT * FROM orders
      `);
      
      console.log('✅ Orders backup created successfully');
    } catch (error) {
      console.error('❌ Backup failed:', error.message);
      throw error;
    }
  }

  async migrateExistingData() {
    try {
      console.log('🔄 Migrating existing order data...');
      
      // Get existing orders
      const [existingOrders] = await this.connection.execute('SELECT * FROM orders');
      console.log(`📊 Found ${existingOrders.length} existing orders to migrate`);

      if (existingOrders.length === 0) {
        console.log('ℹ️ No existing orders to migrate');
        return;
      }

      // Prepare migration data
      const migratedOrders = existingOrders.map(order => ({
        id: order.id,
        user_id: order.user_id,
        order_number: order.order_number || `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        order_type: order.order_type || 'system_service',
        order_category: this.determineOrderCategory(order),
        item_id: order.item_id,
        item_name_ar: order.item_name_ar || 'غير محدد',
        item_name_en: order.item_name_en || 'Not specified',
        quantity: order.quantity || 1,
        unit_price: order.unit_price || 0,
        base_price: order.unit_price || order.final_price || 0,
        addons_price: 0,
        subscription_price: 0,
        maintenance_price: 0,
        total_price: order.total_price || order.final_price || 0,
        discount_amount: order.discount_amount || 0,
        tax_amount: 0,
        final_price: order.final_price || 0,
        currency: order.currency || 'USD',
        status: this.mapStatus(order.status),
        priority: 'medium',
        progress_percentage: this.calculateProgress(order.status),
        payment_status: order.payment_status || 'pending',
        payment_method: order.payment_method,
        payment_reference: order.payment_reference,
        payment_gateway: null,
        subscription_type: 'none',
        subscription_duration: null,
        subscription_start_date: null,
        subscription_end_date: null,
        auto_renewal: 0,
        maintenance_included: 0,
        installation_included: 0,
        support_level: 'basic',
        order_details: this.createOrderDetails(order),
        selected_addons: null,
        customization_options: null,
        created_at: order.created_at,
        updated_at: order.updated_at,
        confirmed_at: null,
        started_at: null,
        estimated_completion: null,
        completed_at: order.completion_date,
        delivery_date: order.delivery_date,
        notes_ar: order.notes_ar,
        notes_en: order.notes_en,
        admin_notes: order.admin_notes,
        customer_requirements: null,
        attached_files: null,
        created_by: null,
        last_modified_by: null
      }));

      // Insert migrated data
      for (const order of migratedOrders) {
        await this.insertMigratedOrder(order);
      }

      console.log('✅ Data migration completed successfully');
    } catch (error) {
      console.error('❌ Data migration failed:', error.message);
      throw error;
    }
  }

  determineOrderCategory(order) {
    if (order.order_type === 'premium_content' || order.order_type === 'premium_package') {
      return 'premium_base';
    }
    return 'standard';
  }

  mapStatus(oldStatus) {
    const statusMap = {
      'pending': 'pending',
      'completed': 'completed',
      'cancelled': 'cancelled',
      'processing': 'in_progress'
    };
    return statusMap[oldStatus] || 'pending';
  }

  calculateProgress(status) {
    const progressMap = {
      'pending': 0,
      'processing': 50,
      'completed': 100,
      'cancelled': 0
    };
    return progressMap[status] || 0;
  }

  createOrderDetails(order) {
    return JSON.stringify({
      legacy_order: true,
      original_structure: {
        order_type: order.order_type,
        item_id: order.item_id,
        quantity: order.quantity
      },
      migration_date: new Date().toISOString()
    });
  }

  async insertMigratedOrder(order) {
    const fields = Object.keys(order).join(', ');
    const placeholders = Object.keys(order).map(() => '?').join(', ');
    const values = Object.values(order);

    await this.connection.execute(
      `INSERT INTO orders_new (${fields}) VALUES (${placeholders})`,
      values
    );
  }

  async applyNewSchema() {
    try {
      console.log('🔧 Applying new orders schema...');
      
      // Read the enhanced schema
      const schemaPath = path.join(__dirname, '..', 'database', 'enhanced-orders-schema.sql');
      const schemaContent = await fs.readFile(schemaPath, 'utf8');
      
      // Replace table name for migration
      const migrationSchema = schemaContent.replace(/CREATE TABLE `orders`/g, 'CREATE TABLE `orders_new`');
      
      // Split and execute statements
      const statements = migrationSchema
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt && !stmt.startsWith('--') && stmt.length > 10);

      for (const statement of statements) {
        if (statement.includes('CREATE TABLE')) {
          await this.connection.execute(statement);
        }
      }
      
      console.log('✅ New schema applied successfully');
    } catch (error) {
      console.error('❌ Schema application failed:', error.message);
      throw error;
    }
  }

  async switchTables() {
    try {
      console.log('🔄 Switching to new orders table...');
      
      await this.connection.execute('RENAME TABLE orders TO orders_old, orders_new TO orders');
      
      console.log('✅ Table switch completed successfully');
    } catch (error) {
      console.error('❌ Table switch failed:', error.message);
      throw error;
    }
  }

  async createIndexes() {
    try {
      console.log('📊 Creating additional indexes for performance...');
      
      const indexes = [
        'CREATE INDEX idx_orders_user_status ON orders (user_id, status)',
        'CREATE INDEX idx_orders_type_category ON orders (order_type, order_category)',
        'CREATE INDEX idx_orders_payment_status ON orders (payment_status, created_at)',
        'CREATE INDEX idx_orders_subscription ON orders (subscription_type, subscription_end_date)',
        'CREATE INDEX idx_orders_completion ON orders (estimated_completion, status)'
      ];

      for (const indexSql of indexes) {
        try {
          await this.connection.execute(indexSql);
        } catch (error) {
          if (!error.message.includes('Duplicate key name')) {
            console.warn(`⚠️ Index creation warning: ${error.message}`);
          }
        }
      }
      
      console.log('✅ Indexes created successfully');
    } catch (error) {
      console.error('❌ Index creation failed:', error.message);
      throw error;
    }
  }

  async validateMigration() {
    try {
      console.log('🔍 Validating migration...');
      
      const [oldCount] = await this.connection.execute('SELECT COUNT(*) as count FROM orders_old');
      const [newCount] = await this.connection.execute('SELECT COUNT(*) as count FROM orders');
      
      console.log(`📊 Old table: ${oldCount[0].count} records`);
      console.log(`📊 New table: ${newCount[0].count} records`);
      
      if (oldCount[0].count === newCount[0].count) {
        console.log('✅ Migration validation successful - record counts match');
      } else {
        console.warn('⚠️ Migration validation warning - record counts do not match');
      }
    } catch (error) {
      console.error('❌ Migration validation failed:', error.message);
      throw error;
    }
  }

  async run() {
    try {
      await this.connect();
      await this.backupExistingOrders();
      await this.applyNewSchema();
      await this.migrateExistingData();
      await this.switchTables();
      await this.createIndexes();
      await this.validateMigration();
      
      console.log('🎉 Orders schema migration completed successfully!');
      console.log('📝 Next steps:');
      console.log('   1. Update application code to use new schema');
      console.log('   2. Test order creation and management');
      console.log('   3. Remove old backup tables when confident');
      
    } catch (error) {
      console.error('💥 Migration failed:', error.message);
      console.log('🔄 Rolling back changes...');
      
      try {
        await this.connection.execute('DROP TABLE IF EXISTS orders_new');
        console.log('✅ Rollback completed');
      } catch (rollbackError) {
        console.error('❌ Rollback failed:', rollbackError.message);
      }
    } finally {
      if (this.connection) {
        await this.connection.end();
      }
    }
  }
}

// Run migration if called directly
if (require.main === module) {
  const migration = new OrdersMigration();
  migration.run();
}

module.exports = OrdersMigration;
