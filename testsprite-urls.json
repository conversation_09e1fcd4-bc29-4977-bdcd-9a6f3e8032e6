{"name": "Khanfashariya - TestSprite Ready", "timestamp": "2025-07-21T23:24:56.131Z", "frontend_url": "https://7bdecd66f690.ngrok-free.app", "backend_api_url": "https://72e29761aabe.ngrok-free.app", "testsprite_settings": {"base_url": "https://72e29761aabe.ngrok-free.app", "headers": {"ngrok-skip-browser-warning": "true", "User-Agent": "TestSprite/1.0", "Content-Type": "application/json", "Accept": "application/json"}}, "endpoints": [{"name": "Health Check", "url": "https://72e29761aabe.ngrok-free.app/health", "method": "GET", "expected_status": 200}, {"name": "<PERSON><PERSON>", "url": "https://72e29761aabe.ngrok-free.app/api/auth/login", "method": "POST", "body": {"email": "<EMAIL>", "password": "admin123"}, "expected_status": 200}, {"name": "Get Systems", "url": "https://72e29761aabe.ngrok-free.app/api/systems", "method": "GET", "expected_status": 200}, {"name": "Get Technical Services", "url": "https://72e29761aabe.ngrok-free.app/api/services/technical", "method": "GET", "expected_status": 200}, {"name": "Get Premium Services", "url": "https://72e29761aabe.ngrok-free.app/api/services/premium", "method": "GET", "expected_status": 200}], "frontend_pages": ["https://duly-enough-mayfly.ngrok-free.app/", "https://duly-enough-mayfly.ngrok-free.app/login", "https://duly-enough-mayfly.ngrok-free.app/register", "https://duly-enough-mayfly.ngrok-free.app/services", "https://duly-enough-mayfly.ngrok-free.app/profile", "https://duly-enough-mayfly.ngrok-free.app/admin"]}