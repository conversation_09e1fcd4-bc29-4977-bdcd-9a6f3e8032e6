#!/usr/bin/env node

const axios = require('axios');

async function testFrontendFix() {
  console.log('🔧 Testing Frontend API Fix...\n');
  
  const tests = [
    {
      name: 'Frontend Proxy - Systems',
      url: 'http://localhost:5173/api/systems',
      description: 'Systems through Vite proxy'
    },
    {
      name: 'Frontend Proxy - Services',
      url: 'http://localhost:5173/api/services/technical',
      description: 'Technical services through Vite proxy'
    },
    {
      name: 'Frontend Proxy - Premium',
      url: 'http://localhost:5173/api/services/premium',
      description: 'Premium content through Vite proxy'
    }
  ];

  let allWorking = true;

  for (const test of tests) {
    console.log(`🔍 Testing: ${test.name}`);
    
    try {
      const response = await axios.get(test.url, {
        headers: {
          'User-Agent': 'FrontendFixTester/1.0'
        },
        timeout: 5000
      });
      
      console.log(`   ✅ Status: ${response.status}`);
      console.log(`   ✅ Success: ${response.data.success}`);
      
      if (response.data.success) {
        const data = response.data.data;
        if (data.systems) {
          const activeSystems = data.systems.filter(s => s.status === 'active');
          console.log(`   📊 Active Systems: ${activeSystems.length}`);
        } else if (data.services) {
          const activeServices = data.services.filter(s => s.status === 'active');
          console.log(`   📊 Active Services: ${activeServices.length}`);
        } else if (data.premiumContent) {
          const activePremium = data.premiumContent.filter(p => p.status === 'active');
          console.log(`   📊 Active Premium: ${activePremium.length}`);
        } else if (Array.isArray(data)) {
          console.log(`   📊 Data Items: ${data.length}`);
        }
      } else {
        allWorking = false;
        console.log(`   ❌ API returned success: false`);
      }
      
    } catch (error) {
      allWorking = false;
      console.log(`   ❌ Error: ${error.message}`);
      if (error.response) {
        console.log(`   ❌ Status: ${error.response.status}`);
      }
    }
    
    console.log('');
  }

  console.log('📊 Summary:');
  if (allWorking) {
    console.log('✅ All frontend proxy endpoints are working!');
    console.log('✅ Frontend components should now display data correctly');
    console.log('\n🎯 Expected results:');
    console.log('- Homepage should show systems and services');
    console.log('- Debug component should show loaded data');
    console.log('- No more "Failed to fetch" errors');
  } else {
    console.log('❌ Some endpoints are still failing');
    console.log('🔧 Additional troubleshooting may be needed');
  }

  console.log('\n🔄 Next steps:');
  console.log('1. Refresh the browser page');
  console.log('2. Check the Debug component in bottom-right corner');
  console.log('3. Verify data appears in homepage sections');
  console.log('4. Check browser console for any remaining errors');
}

testFrontendFix();
