#!/usr/bin/env node

/**
 * Quick Test Script
 * 
 * Tests both login and systems loading to verify everything works
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001';

async function testLogin() {
  console.log('🔐 Testing login...');
  
  try {
    const response = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (response.data.success) {
      console.log('✅ Login successful');
      console.log(`   User: ${response.data.data.user.full_name} (${response.data.data.user.role})`);
      return response.data.data.tokens.accessToken;
    } else {
      console.log('❌ Login failed');
      return null;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return null;
  }
}

async function testSystems() {
  console.log('\n🖥️ Testing systems API...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/api/systems`);
    
    if (response.data.success && response.data.data.systems) {
      const systems = response.data.data.systems;
      console.log(`✅ Systems loaded: ${systems.length} systems`);
      
      const activeSystems = systems.filter(s => s.status === 'active');
      console.log(`   Active systems: ${activeSystems.length}`);
      
      if (activeSystems.length > 0) {
        console.log(`   Sample: ${activeSystems[0].name_en}`);
      }
      
      return true;
    } else {
      console.log('❌ Systems API failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Systems error:', error.message);
    return false;
  }
}

async function testServices() {
  console.log('\n🛠️ Testing services API...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/api/services/technical`);
    
    if (response.data.success && response.data.data.services) {
      const services = response.data.data.services;
      console.log(`✅ Services loaded: ${services.length} services`);
      
      const activeServices = services.filter(s => s.status === 'active');
      console.log(`   Active services: ${activeServices.length}`);
      
      return true;
    } else {
      console.log('❌ Services API failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Services error:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Quick Test - Login & Data Loading');
  console.log('====================================\n');
  
  // Test login
  const token = await testLogin();
  
  // Test systems
  const systemsOk = await testSystems();
  
  // Test services
  const servicesOk = await testServices();
  
  // Summary
  console.log('\n📊 Test Results:');
  console.log('================');
  console.log(`   Login: ${token ? '✅ Working' : '❌ Failed'}`);
  console.log(`   Systems: ${systemsOk ? '✅ Working' : '❌ Failed'}`);
  console.log(`   Services: ${servicesOk ? '✅ Working' : '❌ Failed'}`);
  
  const allPassed = token && systemsOk && servicesOk;
  
  if (allPassed) {
    console.log('\n🎉 All tests passed!');
    console.log('✅ Login works with MySQL users');
    console.log('✅ Systems load from MySQL');
    console.log('✅ Services load from MySQL');
    console.log('\n🌐 Frontend should work at: http://localhost:5173');
  } else {
    console.log('\n⚠️ Some tests failed');
    console.log('🔧 Please check the issues above');
  }
  
  process.exit(allPassed ? 0 : 1);
}

main().catch(error => {
  console.error('Test error:', error);
  process.exit(1);
});
