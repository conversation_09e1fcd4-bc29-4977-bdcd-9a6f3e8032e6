#!/usr/bin/env node

/**
 * Add missing tables to the database
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function addMissingTables() {
  let connection;
  
  try {
    console.log('🔧 Adding missing tables...');
    
    // Connect to database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '123456',
      database: 'khanfashariya_db',
      charset: 'utf8mb4'
    });
    
    console.log('✅ Connected to database');
    
    // Add premium_content table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS premium_content (
        id VARCHAR(36) PRIMARY KEY,
        title_ar TEXT NOT NULL,
        title_en TEXT NOT NULL,
        description_ar TEXT NOT NULL,
        description_en TEXT NOT NULL,
        price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        category VARCHAR(100) NOT NULL,
        features_ar JSON,
        features_en JSON,
        video_url VARCHAR(500),
        image_url VARCHAR(500),
        gallery_images JSON,
        included_systems JSON,
        included_services JSON,
        status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
        featured BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        purchase_count INT DEFAULT 0,
        rating DECIMAL(3,2) DEFAULT 0.00,
        rating_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ premium_content table created');
    
    // Add premium_packages table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS premium_packages (
        id VARCHAR(36) PRIMARY KEY,
        name_ar TEXT NOT NULL,
        name_en TEXT NOT NULL,
        description_ar TEXT NOT NULL,
        description_en TEXT NOT NULL,
        price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        original_price DECIMAL(10,2),
        discount_percentage DECIMAL(5,2) DEFAULT 0.00,
        features_ar JSON,
        features_en JSON,
        included_content JSON,
        duration_months INT DEFAULT 12,
        max_downloads INT DEFAULT -1,
        support_level ENUM('basic', 'premium', 'vip') DEFAULT 'premium',
        status ENUM('active', 'inactive', 'limited_time') DEFAULT 'active',
        featured BOOLEAN DEFAULT FALSE,
        sort_order INT DEFAULT 0,
        purchase_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ premium_packages table created');
    
    // Add subscriptions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS subscriptions (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        package_id VARCHAR(36) NOT NULL,
        subscription_number VARCHAR(50) UNIQUE NOT NULL,
        status ENUM('active', 'expired', 'cancelled', 'suspended') DEFAULT 'active',
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        auto_renew BOOLEAN DEFAULT FALSE,
        downloads_used INT DEFAULT 0,
        max_downloads INT DEFAULT -1,
        last_activity TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (package_id) REFERENCES premium_packages(id) ON DELETE CASCADE
      )
    `);
    console.log('✅ subscriptions table created');
    
    // Add inbox_messages table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS inbox_messages (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        sender_type ENUM('admin', 'system') DEFAULT 'admin',
        sender_id VARCHAR(36),
        subject_ar TEXT NOT NULL,
        subject_en TEXT NOT NULL,
        message_ar TEXT NOT NULL,
        message_en TEXT NOT NULL,
        message_type ENUM('info', 'warning', 'success', 'error', 'order_update', 'system_notification') DEFAULT 'info',
        priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
        is_read BOOLEAN DEFAULT FALSE,
        read_at TIMESTAMP NULL,
        action_url VARCHAR(500),
        action_label_ar VARCHAR(100),
        action_label_en VARCHAR(100),
        expires_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    console.log('✅ inbox_messages table created');
    
    // Add user_sessions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        refresh_token VARCHAR(500) NOT NULL,
        device_info TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    console.log('✅ user_sessions table created');
    
    // Add some sample data
    await connection.execute(`
      INSERT IGNORE INTO premium_content (id, title_ar, title_en, description_ar, description_en, price, category) 
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'premium-content-1',
      'الحزمة الذهبية',
      'Gold Package',
      'حزمة شاملة تتضمن جميع الأنظمة والخدمات',
      'Comprehensive package including all systems and services',
      4999.99,
      'Premium'
    ]);
    console.log('✅ Sample premium content added');
    
    await connection.execute(`
      INSERT IGNORE INTO premium_packages (id, name_ar, name_en, description_ar, description_en, price) 
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      'premium-package-1',
      'باقة البلاتينيوم',
      'Platinum Package',
      'باقة متميزة مع دعم مدى الحياة',
      'Premium package with lifetime support',
      9999.99
    ]);
    console.log('✅ Sample premium package added');
    
    console.log('🎉 Missing tables added successfully!');
    
  } catch (error) {
    console.error('❌ Failed to add tables:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the script
addMissingTables().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
