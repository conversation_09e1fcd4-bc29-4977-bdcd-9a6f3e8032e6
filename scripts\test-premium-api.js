#!/usr/bin/env node

/**
 * Test Premium API Script
 * 
 * Tests the new premium content API endpoints
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function testPremiumAPI() {
  console.log('🧪 Testing Premium Content API');
  console.log('===============================\n');
  
  try {
    // Test 1: Get active premium edition (public endpoint)
    console.log('1️⃣ Testing GET /premium (Active Premium Edition)...');
    
    try {
      const response = await axios.get(`${BASE_URL}/premium`);
      console.log('   ✅ Status:', response.status);
      
      if (response.data.success && response.data.data.premiumContent) {
        const premium = response.data.data.premiumContent;
        console.log('   📦 Premium Edition Found:');
        console.log(`      ID: ${premium.id}`);
        console.log(`      Title (AR): ${premium.title_ar}`);
        console.log(`      Title (EN): ${premium.title_en}`);
        console.log(`      Price: $${premium.price}`);
        console.log(`      Status: ${premium.status}`);
        console.log(`      Active Edition: ${premium.is_active_edition}`);
        console.log(`      Features (AR): ${premium.features_ar?.length || 0} items`);
        console.log(`      Features (EN): ${premium.features_en?.length || 0} items`);
        console.log(`      Included Systems: ${premium.included_systems?.length || 0} items`);
        console.log(`      Included Services: ${premium.included_services?.length || 0} items`);
      } else {
        console.log('   ⚠️  No active premium edition found');
      }
    } catch (error) {
      console.log('   ❌ Error:', error.response?.data?.message || error.message);
    }
    
    // Test 2: Get premium add-ons (systems and services with premium pricing)
    console.log('\n2️⃣ Testing GET /premium/addons (Premium Add-ons)...');
    
    try {
      const response = await axios.get(`${BASE_URL}/premium/addons`);
      console.log('   ✅ Status:', response.status);
      
      if (response.data.success && response.data.data) {
        const { systems, services } = response.data.data;
        console.log('   🔧 Premium Systems Found:', systems?.length || 0);
        
        if (systems && systems.length > 0) {
          systems.slice(0, 3).forEach((system, index) => {
            console.log(`      ${index + 1}. ${system.name_ar} - $${system.premium_price || system.price}`);
            console.log(`         Installation: ${system.installation_included ? 'Yes' : 'No'}`);
            console.log(`         Maintenance: ${system.maintenance_included ? 'Yes' : 'No'}`);
          });
        }
        
        console.log('   🛠️  Premium Services Found:', services?.length || 0);
        
        if (services && services.length > 0) {
          services.slice(0, 3).forEach((service, index) => {
            console.log(`      ${index + 1}. ${service.name_ar} - $${service.premium_price || service.price}`);
            console.log(`         Subscription Discount: ${service.subscription_discount_percentage || 0}%`);
          });
        }
      } else {
        console.log('   ⚠️  No premium add-ons found');
      }
    } catch (error) {
      console.log('   ❌ Error:', error.response?.data?.message || error.message);
    }
    
    // Test 3: Test admin endpoint (will likely fail without auth, but that's expected)
    console.log('\n3️⃣ Testing GET /premium/admin (Admin Endpoint - Expected to fail without auth)...');
    
    try {
      const response = await axios.get(`${BASE_URL}/premium/admin`);
      console.log('   ✅ Status:', response.status);
      console.log('   📊 Admin data received successfully');
    } catch (error) {
      if (error.response?.status === 401 || error.response?.status === 403) {
        console.log('   ✅ Expected authentication error (this is correct behavior)');
      } else {
        console.log('   ❌ Unexpected error:', error.response?.data?.message || error.message);
      }
    }
    
    // Test 4: Test database connection
    console.log('\n4️⃣ Testing Database Connection...');
    
    try {
      const response = await axios.get(`${BASE_URL}/health`);
      console.log('   ✅ Health check passed');
    } catch (error) {
      console.log('   ❌ Health check failed:', error.message);
    }
    
    // Test 5: Test systems API (for integration)
    console.log('\n5️⃣ Testing Systems API Integration...');
    
    try {
      const response = await axios.get(`${BASE_URL}/systems`);
      console.log('   ✅ Systems API Status:', response.status);
      
      if (response.data.success && response.data.data) {
        const systems = response.data.data.systems || response.data.data;
        const premiumSystems = systems.filter(s => s.is_premium_addon);
        console.log(`   🔧 Total Systems: ${systems.length}`);
        console.log(`   👑 Premium-eligible Systems: ${premiumSystems.length}`);
      }
    } catch (error) {
      console.log('   ❌ Systems API Error:', error.response?.data?.message || error.message);
    }
    
    // Test 6: Test services API (for integration)
    console.log('\n6️⃣ Testing Services API Integration...');
    
    try {
      const response = await axios.get(`${BASE_URL}/services/technical`);
      console.log('   ✅ Services API Status:', response.status);
      
      if (response.data.success && response.data.data) {
        const services = response.data.data.services || response.data.data;
        const premiumServices = services.filter(s => s.is_premium_addon);
        console.log(`   🛠️  Total Services: ${services.length}`);
        console.log(`   👑 Premium-eligible Services: ${premiumServices.length}`);
      }
    } catch (error) {
      console.log('   ❌ Services API Error:', error.response?.data?.message || error.message);
    }
    
    console.log('\n🎉 Premium API Testing Complete!');
    console.log('\n📋 Summary:');
    console.log('   - Premium content endpoint: Tested');
    console.log('   - Premium add-ons endpoint: Tested');
    console.log('   - Admin endpoint: Tested (auth required)');
    console.log('   - Database connection: Tested');
    console.log('   - Systems integration: Tested');
    console.log('   - Services integration: Tested');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run tests
testPremiumAPI().catch(error => {
  console.error('Test execution failed:', error);
  process.exit(1);
});
