#!/usr/bin/env node

const axios = require('axios');

async function testAPIParameters() {
  console.log('🧪 Testing API Parameters...\n');
  
  const BASE_URL = 'https://7b93f343ea56.ngrok-free.app';
  const headers = {
    'ngrok-skip-browser-warning': 'true',
    'User-Agent': 'ParameterTester/1.0',
    'Content-Type': 'application/json'
  };

  try {
    // Test 1: Systems API with different status values
    console.log('1️⃣ Testing Systems API with different status values...');
    
    // Test with default (active)
    const systemsDefault = await axios.get(`${BASE_URL}/api/systems`, { headers });
    console.log(`   Default (active): ${systemsDefault.data.data?.systems?.length || 0} systems`);
    
    // Test with all statuses
    const systemsAll = await axios.get(`${BASE_URL}/api/systems?status=all`, { headers });
    console.log(`   All statuses: ${systemsAll.data.data?.systems?.length || 0} systems`);
    
    // Test without status filter
    const systemsNoStatus = await axios.get(`${BASE_URL}/api/systems?status=`, { headers });
    console.log(`   No status filter: ${systemsNoStatus.data.data?.systems?.length || 0} systems`);

    // Test 2: Technical Services API
    console.log('\n2️⃣ Testing Technical Services API...');
    
    const servicesDefault = await axios.get(`${BASE_URL}/api/services/technical`, { headers });
    console.log(`   Default (active): ${servicesDefault.data.data?.length || 0} services`);
    
    const servicesAll = await axios.get(`${BASE_URL}/api/services/technical?status=all`, { headers });
    console.log(`   All statuses: ${servicesAll.data.data?.length || 0} services`);

    // Test 3: Premium Services API
    console.log('\n3️⃣ Testing Premium Services API...');
    
    const premiumDefault = await axios.get(`${BASE_URL}/api/services/premium`, { headers });
    console.log(`   Default (active): ${premiumDefault.data.data?.premiumContent?.length || 0} premium items`);
    
    const premiumAll = await axios.get(`${BASE_URL}/api/services/premium?status=all`, { headers });
    console.log(`   All statuses: ${premiumAll.data.data?.premiumContent?.length || 0} premium items`);

    // Test 4: Admin endpoints (need authentication)
    console.log('\n4️⃣ Testing Admin endpoints...');
    
    // Login first
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    }, { headers });

    if (loginResponse.data.success) {
      const token = loginResponse.data.data.tokens.accessToken;
      const authHeaders = {
        ...headers,
        'Authorization': `Bearer ${token}`
      };

      // Test admin systems endpoint
      const adminSystems = await axios.get(`${BASE_URL}/api/admin/systems`, { headers: authHeaders });
      console.log(`   Admin systems: ${adminSystems.data.data?.length || 0} systems`);

      // Test admin technical services
      const adminServices = await axios.get(`${BASE_URL}/api/admin/technical-services`, { headers: authHeaders });
      console.log(`   Admin technical services: ${adminServices.data.data?.length || 0} services`);
    }

    // Test 5: Detailed response analysis
    console.log('\n5️⃣ Detailed Response Analysis...');
    
    if (systemsDefault.data.success) {
      const systems = systemsDefault.data.data.systems;
      console.log('   Systems details:');
      systems.forEach((system, index) => {
        console.log(`     ${index + 1}. ${system.name_ar} - $${system.price} (${system.status})`);
      });
    }

    if (servicesDefault.data.success) {
      const services = servicesDefault.data.data;
      console.log('   Technical Services details:');
      if (Array.isArray(services) && services.length > 0) {
        services.forEach((service, index) => {
          console.log(`     ${index + 1}. ${service.name_ar} - $${service.price} (${service.status})`);
        });
      } else {
        console.log('     No technical services found or wrong data structure');
        console.log('     Response structure:', typeof services, Array.isArray(services));
      }
    }

    if (premiumDefault.data.success) {
      const premium = premiumDefault.data.data.premiumContent;
      console.log('   Premium Content details:');
      if (Array.isArray(premium) && premium.length > 0) {
        premium.forEach((item, index) => {
          console.log(`     ${index + 1}. ${item.title_ar} - $${item.price} (${item.status})`);
        });
      } else {
        console.log('     No premium content found or wrong data structure');
        console.log('     Response structure:', typeof premium, Array.isArray(premium));
      }
    }

    console.log('\n📊 Summary:');
    console.log('✅ API endpoints are working');
    console.log('✅ Database connection is functional');
    console.log('⚠️ Some endpoints may be filtering data incorrectly');
    
    console.log('\n🔧 Recommendations:');
    console.log('1. Check status filtering logic in backend');
    console.log('2. Verify default status parameter handling');
    console.log('3. Test with different query parameters');
    console.log('4. Check response data structure consistency');

  } catch (error) {
    console.error('❌ Error:', error.message);
    
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testAPIParameters();
