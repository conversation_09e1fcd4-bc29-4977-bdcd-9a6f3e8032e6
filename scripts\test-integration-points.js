/**
 * Critical Integration Points Verification Script
 * Tests database connectivity, schema alignment, data flow, and system interoperability
 */

const axios = require('axios');
const mysql = require('mysql2/promise');

const API_BASE = 'http://localhost:3001/api';
const FRONTEND_BASE = 'http://localhost:5173';
const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khanfashariya_db'
};

let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

function logTest(name, status, details = '') {
  testResults.total++;
  const icon = status === 'PASS' ? '✅' : '❌';
  const message = `${icon} ${name}`;
  
  if (status === 'PASS') {
    testResults.passed++;
    console.log(`${message} ${details}`);
  } else {
    testResults.failed++;
    console.log(`${message} - ${details}`);
    testResults.errors.push({ test: name, error: details });
  }
}

async function testDatabaseConnectivity() {
  console.log('\n🔍 Testing Database Connectivity...');
  
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // Test basic connection
    await connection.execute('SELECT 1');
    logTest('Database Connection', 'PASS', 'MySQL connection successful');
    
    // Test database selection
    await connection.execute('USE khanfashariya_db');
    logTest('Database Selection', 'PASS', 'Database selected successfully');
    
    // Test transaction support
    await connection.beginTransaction();
    await connection.execute('SELECT COUNT(*) FROM users');
    await connection.commit();
    logTest('Transaction Support', 'PASS', 'Transactions working correctly');
    
    await connection.end();
  } catch (error) {
    logTest('Database Connectivity', 'FAIL', error.message);
  }
}

async function testSchemaAlignment() {
  console.log('\n🔍 Testing Schema Alignment...');
  
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // Check required tables exist
    const requiredTables = [
      'users', 'system_services', 'technical_services', 'premium_content',
      'orders', 'user_sessions', 'activity_logs', 'inbox_messages'
    ];
    
    const [tables] = await connection.execute('SHOW TABLES');
    const existingTables = tables.map(row => Object.values(row)[0]);
    
    for (const table of requiredTables) {
      if (existingTables.includes(table)) {
        logTest(`Table: ${table}`, 'PASS', 'Table exists');
      } else {
        logTest(`Table: ${table}`, 'FAIL', 'Table missing');
      }
    }
    
    // Check critical columns in users table
    const [userColumns] = await connection.execute('DESCRIBE users');
    const userColumnNames = userColumns.map(col => col.Field);
    
    const requiredUserColumns = ['id', 'email', 'username', 'full_name', 'password_hash', 'role'];
    for (const column of requiredUserColumns) {
      if (userColumnNames.includes(column)) {
        logTest(`Users.${column}`, 'PASS', 'Column exists');
      } else {
        logTest(`Users.${column}`, 'FAIL', 'Column missing');
      }
    }
    
    // Check orders table structure
    const [orderColumns] = await connection.execute('DESCRIBE orders');
    const orderColumnNames = orderColumns.map(col => col.Field);
    
    const requiredOrderColumns = ['id', 'user_id', 'order_type', 'status', 'payment_status'];
    for (const column of requiredOrderColumns) {
      if (orderColumnNames.includes(column)) {
        logTest(`Orders.${column}`, 'PASS', 'Column exists');
      } else {
        logTest(`Orders.${column}`, 'FAIL', 'Column missing');
      }
    }
    
    await connection.end();
  } catch (error) {
    logTest('Schema Alignment', 'FAIL', error.message);
  }
}

async function testDataFlow() {
  console.log('\n🔍 Testing Data Flow...');
  
  try {
    // Test API to Database flow
    const systemsResponse = await axios.get(`${API_BASE}/systems`);
    
    if (systemsResponse.data.success) {
      const apiSystems = systemsResponse.data.data.systems || [];
      
      // Check database directly
      const connection = await mysql.createConnection(DB_CONFIG);
      const [dbSystems] = await connection.execute('SELECT * FROM system_services WHERE status = "active"');
      
      if (apiSystems.length === dbSystems.length) {
        logTest('API-Database Sync', 'PASS', `${apiSystems.length} systems match between API and DB`);
      } else {
        logTest('API-Database Sync', 'FAIL', `API: ${apiSystems.length}, DB: ${dbSystems.length}`);
      }
      
      await connection.end();
    } else {
      logTest('API-Database Flow', 'FAIL', 'API request failed');
    }
    
    // Test Frontend to API flow
    const frontendResponse = await axios.get(`${FRONTEND_BASE}/api/systems`);
    
    if (frontendResponse.data.success) {
      logTest('Frontend-API Flow', 'PASS', 'Frontend proxy working correctly');
    } else {
      logTest('Frontend-API Flow', 'FAIL', 'Frontend proxy failed');
    }
    
  } catch (error) {
    logTest('Data Flow', 'FAIL', error.message);
  }
}

async function testSystemInteroperability() {
  console.log('\n🔍 Testing System Interoperability...');
  
  try {
    // Test user authentication flow
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: '123456'
    });
    
    if (loginResponse.data.success) {
      const token = loginResponse.data.data.tokens.accessToken;
      logTest('Authentication Flow', 'PASS', 'Login successful');
      
      // Test authenticated API access
      const profileResponse = await axios.get(`${API_BASE}/users/profile`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (profileResponse.data.success) {
        logTest('Authenticated API Access', 'PASS', 'Profile access successful');
      } else {
        logTest('Authenticated API Access', 'FAIL', 'Profile access failed');
      }
      
      // Test order creation flow
      const systemsResponse = await axios.get(`${API_BASE}/systems`);
      if (systemsResponse.data.success && systemsResponse.data.data.systems.length > 0) {
        const testSystem = systemsResponse.data.data.systems[0];
        
        const orderResponse = await axios.post(`${API_BASE}/orders`, {
          order_type: 'system_service',
          item_id: testSystem.id,
          quantity: 1,
          notes_ar: 'اختبار التكامل',
          notes_en: 'Integration test'
        }, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        if (orderResponse.data.success) {
          logTest('Order Creation Flow', 'PASS', 'Order created successfully');
          
          // Verify order in database
          const connection = await mysql.createConnection(DB_CONFIG);
          const [orders] = await connection.execute(
            'SELECT * FROM orders WHERE id = ?',
            [orderResponse.data.data.order.id]
          );
          
          if (orders.length > 0) {
            logTest('Order Database Persistence', 'PASS', 'Order saved to database');
          } else {
            logTest('Order Database Persistence', 'FAIL', 'Order not found in database');
          }
          
          await connection.end();
        } else {
          logTest('Order Creation Flow', 'FAIL', 'Order creation failed');
        }
      }
    } else {
      logTest('Authentication Flow', 'FAIL', 'Login failed');
    }
    
  } catch (error) {
    logTest('System Interoperability', 'FAIL', error.message);
  }
}

async function testBilingualSupport() {
  console.log('\n🔍 Testing Bilingual Support...');
  
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // Check Arabic content in database
    const [arabicSystems] = await connection.execute(
      'SELECT name_ar, description_ar FROM system_services WHERE name_ar IS NOT NULL LIMIT 1'
    );
    
    if (arabicSystems.length > 0 && arabicSystems[0].name_ar) {
      logTest('Arabic Content Storage', 'PASS', 'Arabic content found in database');
    } else {
      logTest('Arabic Content Storage', 'FAIL', 'No Arabic content found');
    }
    
    // Check English content
    const [englishSystems] = await connection.execute(
      'SELECT name_en, description_en FROM system_services WHERE name_en IS NOT NULL LIMIT 1'
    );
    
    if (englishSystems.length > 0 && englishSystems[0].name_en) {
      logTest('English Content Storage', 'PASS', 'English content found in database');
    } else {
      logTest('English Content Storage', 'FAIL', 'No English content found');
    }
    
    // Test API bilingual response
    const systemsResponse = await axios.get(`${API_BASE}/systems`);
    if (systemsResponse.data.success) {
      const systems = systemsResponse.data.data.systems || [];
      if (systems.length > 0 && systems[0].name_ar && systems[0].name_en) {
        logTest('API Bilingual Response', 'PASS', 'Both Arabic and English content in API');
      } else {
        logTest('API Bilingual Response', 'FAIL', 'Missing bilingual content in API');
      }
    }
    
    await connection.end();
  } catch (error) {
    logTest('Bilingual Support', 'FAIL', error.message);
  }
}

async function testPerformanceMetrics() {
  console.log('\n🔍 Testing Performance Metrics...');
  
  try {
    // Test API response times
    const startTime = Date.now();
    await axios.get(`${API_BASE}/systems`);
    const apiResponseTime = Date.now() - startTime;
    
    if (apiResponseTime < 1000) {
      logTest('API Response Time', 'PASS', `${apiResponseTime}ms (< 1000ms)`);
    } else {
      logTest('API Response Time', 'FAIL', `${apiResponseTime}ms (> 1000ms)`);
    }
    
    // Test database query performance
    const connection = await mysql.createConnection(DB_CONFIG);
    const dbStartTime = Date.now();
    await connection.execute('SELECT COUNT(*) FROM users');
    const dbResponseTime = Date.now() - dbStartTime;
    
    if (dbResponseTime < 100) {
      logTest('Database Query Time', 'PASS', `${dbResponseTime}ms (< 100ms)`);
    } else {
      logTest('Database Query Time', 'FAIL', `${dbResponseTime}ms (> 100ms)`);
    }
    
    await connection.end();
  } catch (error) {
    logTest('Performance Metrics', 'FAIL', error.message);
  }
}

async function runIntegrationPointsTest() {
  console.log('🚀 Starting Critical Integration Points Verification');
  console.log('=' * 60);
  
  const startTime = Date.now();
  
  // Run all integration tests
  await testDatabaseConnectivity();
  await testSchemaAlignment();
  await testDataFlow();
  await testSystemInteroperability();
  await testBilingualSupport();
  await testPerformanceMetrics();
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  // Print summary
  console.log('\n' + '=' * 60);
  console.log('📊 INTEGRATION POINTS VERIFICATION SUMMARY');
  console.log('=' * 60);
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  console.log(`Duration: ${duration} seconds`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.test}: ${error.error}`);
    });
  }
  
  // Integration assessment
  const successRate = (testResults.passed / testResults.total) * 100;
  console.log('\n🔗 INTEGRATION ASSESSMENT:');
  
  if (successRate >= 95) {
    console.log('🟢 EXCELLENT - All critical integration points verified');
  } else if (successRate >= 85) {
    console.log('🟡 GOOD - Most integration points working, minor issues to address');
  } else {
    console.log('🔴 NEEDS WORK - Critical integration issues need resolution');
  }
  
  console.log('\n🎉 Integration points verification completed!');
}

// Run the test
runIntegrationPointsTest().catch(console.error);
