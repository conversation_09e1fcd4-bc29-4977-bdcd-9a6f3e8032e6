{"name": "Khanfashariya API Tests via TestSprite", "baseUrl": "https://0aee7f80ed24.ngrok-free.app", "headers": {"Content-Type": "application/json", "User-Agent": "TestSprite/1.0", "Accept": "application/json"}, "endpoints": [{"path": "/health", "method": "GET", "description": "Health check endpoint", "testCases": [{"name": "Health Check", "expectedStatus": 200}]}, {"path": "/api/auth/login", "method": "POST", "description": "User authentication", "testCases": [{"name": "<PERSON><PERSON>", "body": {"email": "<EMAIL>", "password": "admin123"}, "expectedStatus": 200, "expectedFields": ["success", "data"]}, {"name": "Invalid Credentials", "body": {"email": "<EMAIL>", "password": "wrongpass"}, "expectedStatus": 401}, {"name": "Missing Fields", "body": {}, "expectedStatus": 400}]}, {"path": "/api/auth/register", "method": "POST", "description": "User registration", "testCases": [{"name": "Valid Registration", "body": {"email": "testsprite_{{timestamp}}@test.com", "username": "testsprite_{{timestamp}}", "full_name": "TestSprite User", "password": "testpass123"}, "expectedStatus": [201, 400, 409]}]}, {"path": "/api/systems", "method": "GET", "description": "Technical systems", "testCases": [{"name": "Get Systems", "expectedStatus": 200, "expectedFields": ["success", "data"]}]}, {"path": "/api/services/technical", "method": "GET", "description": "Technical services", "testCases": [{"name": "Get Technical Services", "expectedStatus": 200, "expectedFields": ["success", "data"]}]}, {"path": "/api/services/premium", "method": "GET", "description": "Premium services", "testCases": [{"name": "Get Premium Services", "expectedStatus": 200, "expectedFields": ["success", "data"]}]}], "timestamp": "2025-07-22T01:05:20.822Z", "status": "updated"}