const axios = require('axios');

async function addRichSystemData() {
  console.log('🎨 إضافة بيانات غنية للأنظمة التقنية\n');
  
  try {
    // تسجيل الدخول كإداري
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.data.tokens.accessToken;
    const headers = { Authorization: `Bearer ${token}` };
    
    console.log('✅ تم تسجيل الدخول كإداري');
    
    // جلب الأنظمة الحالية
    const systemsResponse = await axios.get('http://localhost:3001/api/systems/admin', { headers });
    const systems = systemsResponse.data;
    
    console.log(`📊 عدد الأنظمة الحالية: ${systems.length}`);
    
    // بيانات غنية للتحديث
    const richDataSamples = [
      {
        image_url: 'https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg',
        video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        gallery_images: [
          'https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg',
          'https://images.pexels.com/photos/442150/pexels-photo-442150.jpeg',
          'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg'
        ],
        features_ar: ['واجهة سهلة الاستخدام', 'أداء عالي', 'دعم متعدد اللغات', 'تحديثات مجانية'],
        features_en: ['User-friendly interface', 'High performance', 'Multi-language support', 'Free updates'],
        tech_specs_ar: ['متوافق مع جميع الإصدارات', 'يتطلب 2GB RAM', 'مساحة 500MB', 'دعم MySQL'],
        tech_specs_en: ['Compatible with all versions', 'Requires 2GB RAM', '500MB storage', 'MySQL support'],
        is_premium_addon: true
      },
      {
        image_url: 'https://images.pexels.com/photos/442150/pexels-photo-442150.jpeg',
        gallery_images: [
          'https://images.pexels.com/photos/442150/pexels-photo-442150.jpeg',
          'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg'
        ],
        features_ar: ['نظام قتال متطور', 'ذكاء اصطناعي', 'رسوم متحركة سلسة'],
        features_en: ['Advanced combat system', 'AI intelligence', 'Smooth animations'],
        tech_specs_ar: ['يدعم 1000 لاعب', 'معالجة سريعة', 'ذاكرة محسنة'],
        tech_specs_en: ['Supports 1000 players', 'Fast processing', 'Optimized memory'],
        is_premium_addon: false
      },
      {
        image_url: 'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg',
        video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        gallery_images: [
          'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg',
          'https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg',
          'https://images.pexels.com/photos/442150/pexels-photo-442150.jpeg',
          'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg'
        ],
        features_ar: ['أحداث تلقائية', 'مكافآت يومية', 'تحديات أسبوعية', 'نظام نقاط'],
        features_en: ['Automatic events', 'Daily rewards', 'Weekly challenges', 'Points system'],
        tech_specs_ar: ['جدولة ذكية', 'إدارة الموارد', 'تسجيل شامل', 'واجهة إدارية'],
        tech_specs_en: ['Smart scheduling', 'Resource management', 'Comprehensive logging', 'Admin interface'],
        is_premium_addon: true
      }
    ];
    
    // تحديث الأنظمة الثلاثة الأولى بالبيانات الغنية
    for (let i = 0; i < Math.min(3, systems.length); i++) {
      const system = systems[i];
      const richData = richDataSamples[i];
      
      console.log(`\n🔄 تحديث النظام ${i + 1}: ${system.name_ar}`);
      
      try {
        const updateResponse = await axios.put(`http://localhost:3001/api/admin/systems/${system.id}`, richData, { headers });
        
        if (updateResponse.status === 200) {
          console.log(`   ✅ تم تحديث النظام بنجاح`);
          console.log(`   🖼️ صور المعرض: ${richData.gallery_images.length}`);
          console.log(`   ✨ الميزات: ${richData.features_ar.length}`);
          console.log(`   🔧 المواصفات: ${richData.tech_specs_ar.length}`);
          console.log(`   👑 مميز: ${richData.is_premium_addon ? 'نعم' : 'لا'}`);
        }
      } catch (updateError) {
        console.error(`   ❌ فشل في تحديث النظام: ${updateError.response?.data?.message || updateError.message}`);
      }
    }
    
    // إنشاء نظام جديد مميز
    console.log('\n🆕 إنشاء نظام جديد مميز...');
    const newPremiumSystem = {
      name_ar: 'نظام الحماية المتقدم',
      name_en: 'Advanced Protection System',
      description_ar: 'نظام حماية شامل ومتطور لحماية الخادم من جميع أنواع الهجمات والاختراقات',
      description_en: 'Comprehensive and advanced protection system to protect the server from all types of attacks and intrusions',
      price: 299.99,
      category: 'security',
      type: 'plugin',
      is_premium_addon: true,
      features_ar: [
        'حماية من DDoS',
        'كشف الاختراقات',
        'تشفير البيانات',
        'مراقبة في الوقت الفعلي',
        'تقارير أمنية شاملة',
        'نسخ احتياطية تلقائية'
      ],
      features_en: [
        'DDoS Protection',
        'Intrusion Detection',
        'Data Encryption',
        'Real-time Monitoring',
        'Comprehensive Security Reports',
        'Automatic Backups'
      ],
      tech_specs_ar: [
        'يدعم جميع إصدارات الخادم',
        'استهلاك ذاكرة منخفض',
        'معالجة متوازية',
        'واجهة ويب للإدارة',
        'API للتكامل',
        'دعم قواعد البيانات المتعددة'
      ],
      tech_specs_en: [
        'Supports all server versions',
        'Low memory consumption',
        'Parallel processing',
        'Web management interface',
        'Integration API',
        'Multi-database support'
      ],
      video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      image_url: 'https://images.pexels.com/photos/60504/security-protection-anti-virus-software-60504.jpeg',
      gallery_images: [
        'https://images.pexels.com/photos/60504/security-protection-anti-virus-software-60504.jpeg',
        'https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg',
        'https://images.pexels.com/photos/442150/pexels-photo-442150.jpeg',
        'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg',
        'https://images.pexels.com/photos/60504/security-protection-anti-virus-software-60504.jpeg'
      ],
      status: 'active'
    };
    
    try {
      const createResponse = await axios.post('http://localhost:3001/api/admin/systems', newPremiumSystem, { headers });
      console.log('   ✅ تم إنشاء النظام المميز بنجاح');
      console.log('   🆔 ID:', createResponse.data.data.id);
    } catch (createError) {
      console.error('   ❌ فشل في إنشاء النظام المميز:', createError.response?.data?.message || createError.message);
    }
    
    // التحقق من النتائج
    console.log('\n📊 التحقق من النتائج...');
    const updatedSystemsResponse = await axios.get('http://localhost:3001/api/systems');
    const updatedSystems = updatedSystemsResponse.data.data.systems;
    
    const systemsWithImages = updatedSystems.filter(s => s.image_url);
    const systemsWithGallery = updatedSystems.filter(s => s.gallery_images && s.gallery_images.length > 0);
    const systemsWithFeatures = updatedSystems.filter(s => s.features_ar && s.features_ar.length > 0);
    const premiumSystems = updatedSystems.filter(s => s.is_premium_addon);
    const systemsWithVideo = updatedSystems.filter(s => s.video_url);
    
    console.log(`   🖼️ أنظمة بصور رئيسية: ${systemsWithImages.length}/${updatedSystems.length}`);
    console.log(`   🖼️ أنظمة بمعرض صور: ${systemsWithGallery.length}/${updatedSystems.length}`);
    console.log(`   ✨ أنظمة بميزات: ${systemsWithFeatures.length}/${updatedSystems.length}`);
    console.log(`   👑 أنظمة مميزة: ${premiumSystems.length}/${updatedSystems.length}`);
    console.log(`   🎥 أنظمة بفيديو: ${systemsWithVideo.length}/${updatedSystems.length}`);
    
    console.log('\n🎉 تم إضافة البيانات الغنية بنجاح!');
    console.log('🌐 يمكنك الآن زيارة الصفحة الرئيسية لرؤية التحسينات');
    
  } catch (error) {
    console.error('❌ خطأ:', error.response?.data || error.message);
  }
}

addRichSystemData();
