/**
 * Test Admin Fixes
 * Tests the new admin functionality for messaging and user management
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';
const TEST_USER_CREDS = { email: '<EMAIL>', password: '123456' };
const ADMIN_CREDS = { email: '<EMAIL>', password: 'admin123' };

async function testAdminFixes() {
  console.log('🔧 Testing Admin Fixes...');
  
  try {
    // 1. Admin Login
    const adminLogin = await axios.post(`${API_BASE}/auth/login`, ADMIN_CREDS);
    
    if (!adminLogin.data.success) {
      console.log('❌ Admin login failed');
      return;
    }
    
    const adminAuth = {
      token: adminLogin.data.data.tokens.accessToken,
      headers: { Authorization: `Bearer ${adminLogin.data.data.tokens.accessToken}` }
    };
    
    console.log('✅ Admin login successful');
    
    // 2. Get users list
    const usersResponse = await axios.get(`${API_BASE}/admin/users`, { headers: adminAuth.headers });
    
    if (!usersResponse.data.success || !usersResponse.data.data.users.length) {
      console.log('❌ Failed to get users list');
      return;
    }
    
    const testUser = usersResponse.data.data.users.find(u => u.email === '<EMAIL>');
    
    if (!testUser) {
      console.log('❌ Test user not found');
      return;
    }
    
    console.log(`✅ Found test user: ${testUser.email} (ID: ${testUser.id})`);
    
    // 3. Test sending message to user
    console.log('\n💬 Testing Admin Message Sending...');
    
    const messageData = {
      subject_ar: 'رسالة تجريبية من المدير',
      subject_en: 'Test message from admin',
      message_ar: 'هذه رسالة تجريبية لاختبار النظام الجديد',
      message_en: 'This is a test message to check the new system',
      message_type: 'admin_message',
      priority: 'normal'
    };
    
    const sendMessageResponse = await axios.post(
      `${API_BASE}/admin/users/${testUser.id}/messages`, 
      messageData, 
      { headers: adminAuth.headers }
    );
    
    if (sendMessageResponse.data.success) {
      console.log('✅ Message sent successfully to user');
    } else {
      console.log('❌ Failed to send message to user');
    }
    
    // 4. Test user role update
    console.log('\n👤 Testing User Management...');
    
    const updateUserData = {
      role: testUser.role, // Keep same role
      status: 'active',
      full_name: testUser.full_name + ' (Updated)'
    };
    
    const updateUserResponse = await axios.put(
      `${API_BASE}/admin/users/${testUser.id}`, 
      updateUserData, 
      { headers: adminAuth.headers }
    );
    
    if (updateUserResponse.data.success) {
      console.log('✅ User updated successfully');
      console.log(`Updated name: ${updateUserResponse.data.data.user.full_name}`);
    } else {
      console.log('❌ Failed to update user');
    }
    
    // 5. Test order management
    console.log('\n📦 Testing Order Management...');
    
    const ordersResponse = await axios.get(`${API_BASE}/admin/orders`, { headers: adminAuth.headers });
    
    if (ordersResponse.data.success && ordersResponse.data.data.orders.length > 0) {
      const testOrder = ordersResponse.data.data.orders[0];
      console.log(`Found test order: ${testOrder.order_number} (Status: ${testOrder.status})`);
      
      // Update order status
      const updateOrderData = {
        status: 'confirmed',
        admin_notes: 'Order confirmed by admin test',
        priority: 'high'
      };
      
      const updateOrderResponse = await axios.put(
        `${API_BASE}/admin/orders/${testOrder.id}`, 
        updateOrderData, 
        { headers: adminAuth.headers }
      );
      
      if (updateOrderResponse.data.success) {
        console.log('✅ Order updated successfully');
        console.log(`New status: ${updateOrderResponse.data.data.order.status}`);
      } else {
        console.log('❌ Failed to update order');
      }
    } else {
      console.log('❌ No orders found to test');
    }
    
    // 6. Verify user received the message
    console.log('\n📬 Testing User Message Reception...');
    
    const userLogin = await axios.post(`${API_BASE}/auth/login`, TEST_USER_CREDS);
    
    if (userLogin.data.success) {
      const userAuth = {
        headers: { Authorization: `Bearer ${userLogin.data.data.tokens.accessToken}` }
      };
      
      const userMessagesResponse = await axios.get(
        `${API_BASE}/users/${testUser.id}/messages`, 
        { headers: userAuth.headers }
      );
      
      if (userMessagesResponse.data.success) {
        const messages = userMessagesResponse.data.data.messages || [];
        const adminMessage = messages.find(m => m.subject_en === 'Test message from admin');
        
        if (adminMessage) {
          console.log('✅ User received admin message successfully');
        } else {
          console.log('❌ User did not receive admin message');
        }
        
        console.log(`Total user messages: ${messages.length}`);
      }
    }
    
    console.log('\n🎉 Admin fixes testing completed!');
    console.log('✅ Admin can now send messages to users');
    console.log('✅ Admin can update user information');
    console.log('✅ Admin can update order status');
    console.log('✅ Users receive notifications when orders are updated');
    
  } catch (error) {
    console.log('❌ Error during testing:', error.response?.data || error.message);
  }
}

testAdminFixes();
