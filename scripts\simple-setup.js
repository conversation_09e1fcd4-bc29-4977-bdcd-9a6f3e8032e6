#!/usr/bin/env node

/**
 * Simple Database Setup Script
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  charset: 'utf8mb4'
};

async function setupDatabase() {
  let connection;
  
  try {
    console.log('🚀 Starting Simple Database Setup...');
    
    // Connect to MySQL
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to MySQL');
    
    // Create database
    await connection.execute(`CREATE DATABASE IF NOT EXISTS khanfashariya_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log('✅ Database created');
    
    // Close connection and reconnect with database
    await connection.end();

    const dbConfigWithDB = {
      ...dbConfig,
      database: 'khanfashariya_db'
    };

    connection = await mysql.createConnection(dbConfigWithDB);
    console.log('✅ Connected to database');
    
    // Create users table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(36) PRIMARY KEY,
        email VARCHAR(191) UNIQUE NOT NULL,
        username VARCHAR(50) UNIQUE NOT NULL,
        full_name VARCHAR(191) NOT NULL,
        role ENUM('user', 'admin') DEFAULT 'user',
        password_hash VARCHAR(255) NOT NULL,
        status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Users table created');
    
    // Create system_services table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS system_services (
        id VARCHAR(36) PRIMARY KEY,
        name_ar TEXT NOT NULL,
        name_en TEXT NOT NULL,
        description_ar TEXT NOT NULL,
        description_en TEXT NOT NULL,
        price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        category VARCHAR(100) NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ System services table created');
    
    // Create technical_services table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS technical_services (
        id VARCHAR(36) PRIMARY KEY,
        name_ar TEXT NOT NULL,
        name_en TEXT NOT NULL,
        description_ar TEXT NOT NULL,
        description_en TEXT NOT NULL,
        price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        category VARCHAR(100) NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Technical services table created');
    
    // Create orders table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS orders (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        order_number VARCHAR(50) UNIQUE NOT NULL,
        order_type ENUM('system_service', 'technical_service') NOT NULL,
        item_id VARCHAR(36) NOT NULL,
        item_name_ar TEXT NOT NULL,
        item_name_en TEXT NOT NULL,
        final_price DECIMAL(10,2) NOT NULL,
        status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    console.log('✅ Orders table created');
    
    // Create activity_logs table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS activity_logs (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36),
        action VARCHAR(100) NOT NULL,
        entity_type VARCHAR(50),
        entity_id VARCHAR(36),
        details JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
      )
    `);
    console.log('✅ Activity logs table created');
    
    // Create settings table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS settings (
        id VARCHAR(36) PRIMARY KEY,
        setting_key VARCHAR(50) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Settings table created');
    
    // Insert admin user
    await connection.execute(`
      INSERT IGNORE INTO users (id, email, username, full_name, role, password_hash, status) 
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'admin-user-id-2024',
      '<EMAIL>',
      'admin',
      'مدير النظام',
      'admin',
      '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
      'active'
    ]);
    console.log('✅ Admin user created');
    
    // Insert sample system service
    await connection.execute(`
      INSERT IGNORE INTO system_services (id, name_ar, name_en, description_ar, description_en, price, category) 
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'sys-service-1',
      'نظام إدارة المخازن',
      'Warehouse Management System',
      'نظام متقدم لإدارة المخازن والمستودعات',
      'Advanced warehouse and inventory management system',
      299.99,
      'Management'
    ]);
    console.log('✅ Sample system service created');
    
    // Insert sample technical service
    await connection.execute(`
      INSERT IGNORE INTO technical_services (id, name_ar, name_en, description_ar, description_en, price, category) 
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      'tech-service-1',
      'تطوير موقع مخصص',
      'Custom Website Development',
      'تطوير مواقع ويب مخصصة حسب احتياجاتك',
      'Custom website development tailored to your needs',
      1999.99,
      'Development'
    ]);
    console.log('✅ Sample technical service created');
    
    console.log('🎉 Database setup completed successfully!');
    console.log('📋 Summary:');
    console.log('   ✅ Database: khanfashariya_db');
    console.log('   ✅ Tables: 6 tables created');
    console.log('   ✅ Admin user: <EMAIL> / admin123');
    console.log('   ✅ Sample data: 2 services added');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run setup
setupDatabase().catch(error => {
  console.error('Setup failed:', error);
  process.exit(1);
});
