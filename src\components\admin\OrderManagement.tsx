import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotification } from '../../hooks/useNotification';
import {
  getAllUsers,
  getAllOrders,
  updateOrderStatus,
  deleteOrder
} from '../../lib/apiServices';
import {
  UserService,
  User,
} from '../../lib/database';
import OrderCard from './OrderCard';
import OrderStatistics from './OrderStatistics';
import OrderFilters from './OrderFilters';
import EnhancedOrderEditor from './EnhancedOrderEditor';
import OrderDebugger from './OrderDebugger';
import Pagination from '../ui/Pagination';
import {
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  User as UserIcon,
  Calendar,
  Package,
  ArrowLeft,
  RefreshCw,
  MoreVertical,
  FileText,
  Mail,
  Phone,
  Bug
} from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Modal from '../ui/Modal';
import Card from '../ui/Card';
import GoldenFilterGrid from '../ui/GoldenFilterGrid';
import BackButton from '../ui/BackButton';
import { useButtonActions } from '../../utils/buttonActions';
import GoldenButton from '../ui/GoldenButton';
import { ORDER_MANAGEMENT_FILTERS } from '../../constants/filterOptions';

interface OrderManagementProps {
  onBack?: () => void;
}

/**
 * Enhanced Order Management System with advanced features
 */
const OrderManagement: React.FC<OrderManagementProps> = ({ onBack }) => {
  const { t, language } = useTranslation();
  const { showNotification } = useNotification();
  const buttonActions = useButtonActions();
  
  const [orders, setOrders] = useState<UserService[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<UserService | null>(null);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [orderToDelete, setOrderToDelete] = useState<UserService | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [showDebugger, setShowDebugger] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [showEnhancedEditor, setShowEnhancedEditor] = useState(false);
  const [messageOrder, setMessageOrder] = useState<UserService | null>(null);
  const [messageText, setMessageText] = useState('');

  // Unified filter state
  const [filterValues, setFilterValues] = useState<Record<string, string>>({
    status: 'all',
    dateRange: 'all',
    priceRange: 'all',
    sortBy: 'created'
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [ordersResult, usersResult] = await Promise.all([
        getAllOrders(),
        getAllUsers()
      ]);
      
      if (ordersResult.data) {
        // Convert database orders to UserService format for compatibility
        const convertedOrders: UserService[] = ordersResult.data.map((dbOrder: any) => ({
          id: dbOrder.id,
          user_id: dbOrder.user_id,
          service_name: dbOrder.item_name_en || 'Unknown Service',
          service_type: dbOrder.order_type || 'system_service',
          status: dbOrder.status || 'pending',
          purchase_date: dbOrder.created_at ? new Date(dbOrder.created_at).toISOString() : new Date().toISOString(),
          completion_date: dbOrder.completion_date ? new Date(dbOrder.completion_date).toISOString() :
                          (dbOrder.updated_at ? new Date(dbOrder.updated_at).toISOString() : undefined),
          price: parseFloat(dbOrder.final_price?.toString() || '0') || 0,
          notes: dbOrder.notes_en || '',
          type: 'standard',
          priority: 'medium',
          details: JSON.stringify({
            order_number: dbOrder.order_number,
            payment_status: dbOrder.payment_status,
            payment_method: dbOrder.payment_method,
            quantity: parseInt(dbOrder.quantity?.toString() || '1') || 1,
            unit_price: parseFloat(dbOrder.unit_price?.toString() || '0') || 0,
            total_price: parseFloat(dbOrder.total_price?.toString() || '0') || 0,
            final_price: parseFloat(dbOrder.final_price?.toString() || '0') || 0,
            discount_amount: parseFloat(dbOrder.discount_amount?.toString() || '0') || 0,
            admin_notes: dbOrder.admin_notes
          }),
          subscriptionId: null,
          start_date: dbOrder.created_at ? new Date(dbOrder.created_at).toISOString() : new Date().toISOString(),
          estimated_completion: dbOrder.delivery_date
        }));
        setOrders(convertedOrders);
      }
      if (usersResult.data) setUsers(usersResult.data);
    } catch (error) {
      console.error('Error loading data:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في تحميل البيانات' : 'Failed to load data'
      });
    }
    setLoading(false);
  };

  const handleStatusUpdate = async (orderId: string, newStatus: string) => {
    try {
      const result = await updateOrderStatus(orderId, newStatus);
      if (result.error) {
        throw new Error(result.error.message || 'Failed to update order status');
      }
      await loadData();
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم تحديث حالة الطلب بنجاح' : 'Order status updated successfully'
      });
    } catch (error) {
      console.error('Error updating status:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في تحديث حالة الطلب' : 'Failed to update order status'
      });
    }
  };

  const handleDeleteOrder = async (order: UserService) => {
    setOrderToDelete(order);
    setShowDeleteConfirm(true);
  };

  const handleSaveEnhancedOrder = async (updatedOrder: Partial<any>) => {
    if (!selectedOrder) return;

    try {
      // Call API to update order
      const result = await updateOrderStatus(selectedOrder.id, updatedOrder.status || selectedOrder.status, {
        admin_notes: updatedOrder.admin_notes,
        priority: updatedOrder.priority,
        estimated_completion: updatedOrder.estimated_completion,
        payment_status: updatedOrder.payment_status,
        support_level: updatedOrder.support_level,
        maintenance_included: updatedOrder.maintenance_included,
        installation_included: updatedOrder.installation_included
      });

      if (result.error) {
        throw new Error(result.error.message || 'Failed to update order');
      }

      await loadData();
      setShowEnhancedEditor(false);
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم تحديث الطلب بنجاح' : 'Order updated successfully'
      });
    } catch (error) {
      console.error('Error updating order:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في تحديث الطلب' : 'Failed to update order'
      });
    }
  };

  const handleSendMessage = (order: UserService) => {
    setMessageOrder(order);
    setShowMessageModal(true);
  };

  const handleMessageSubmit = async () => {
    if (!messageOrder || !messageText.trim()) return;

    try {
      const user = getUserById(messageOrder.user_id);
      if (user?.email) {
        // Here you would integrate with your email service
        // For now, we'll simulate the email sending
        await new Promise(resolve => setTimeout(resolve, 1000));

        showNotification({
          type: 'success',
          message: language === 'ar' ? 'تم إرسال الرسالة بنجاح' : 'Message sent successfully'
        });

        setShowMessageModal(false);
        setMessageText('');
        setMessageOrder(null);
      }
    } catch (error) {
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في إرسال الرسالة' : 'Failed to send message'
      });
    }
  };

  const confirmDelete = async () => {
    if (!orderToDelete) return;
    
    try {
      // Cancel the order instead of deleting it
      const result = await deleteOrder(orderToDelete.id);
      if (result.error) {
        throw new Error(result.error.message || 'Failed to cancel order');
      }
      await loadData();
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم إلغاء الطلب بنجاح' : 'Order cancelled successfully'
      });
    } catch (error) {
      console.error('Error deleting order:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في إلغاء الطلب' : 'Failed to cancel order'
      });
    }
    setShowDeleteConfirm(false);
    setOrderToDelete(null);
  };

  const getUserById = (userId: string) => {
    return users.find(user => user.id === userId);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'pending': return <Clock className="w-4 h-4 text-yellow-400" />;
      case 'completed': return <CheckCircle className="w-4 h-4 text-blue-400" />;
      case 'cancelled': return <XCircle className="w-4 h-4 text-red-400" />;
      default: return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'pending': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'completed': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'cancelled': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        // If date is invalid, return current date
        return new Date().toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US', {
          calendar: 'gregory',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });
      }
      return date.toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US', {
        calendar: 'gregory',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return new Date().toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US', {
        calendar: 'gregory',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    }
  };

  const formatDateTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        // If date is invalid, return current date
        return new Date().toLocaleString(language === 'ar' ? 'ar-EG' : 'en-US', {
          calendar: 'gregory',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
      return date.toLocaleString(language === 'ar' ? 'ar-EG' : 'en-US', {
        calendar: 'gregory',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting datetime:', error);
      return new Date().toLocaleString(language === 'ar' ? 'ar-EG' : 'en-US', {
        calendar: 'gregory',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return new Date().toLocaleTimeString(language === 'ar' ? 'ar-EG' : 'en-US', {
          calendar: 'gregory',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
      return date.toLocaleTimeString(language === 'ar' ? 'ar-EG' : 'en-US', {
        calendar: 'gregory',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting time:', error);
      return new Date().toLocaleTimeString(language === 'ar' ? 'ar-EG' : 'en-US', {
        calendar: 'gregory',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const filterOrdersByDate = (orders: UserService[]) => {
    if (filterValues.dateRange === 'all') return orders;

    const now = new Date();
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const startOfWeek = new Date(startOfDay.getTime() - (startOfDay.getDay() * 24 * 60 * 60 * 1000));
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    return orders.filter(order => {
      try {
        const orderDate = new Date(order.purchase_date);
        if (isNaN(orderDate.getTime())) {
          // If order date is invalid, include it in results
          return true;
        }

        switch (filterValues.dateRange) {
          case 'today': return orderDate >= startOfDay;
          case 'week': return orderDate >= startOfWeek;
          case 'month': return orderDate >= startOfMonth;
          default: return true;
        }
      } catch (error) {
        console.error('Error filtering by date:', error);
        return true; // Include orders with invalid dates
      }
    });
  };

  // Handle filter changes
  const handleFilterChange = (key: string, value: string) => {
    setFilterValues(prev => ({ ...prev, [key]: value }));
  };

  const filteredOrders = filterOrdersByDate(orders.filter(order => {
    const user = getUserById(order.user_id);
    const matchesSearch = searchTerm === '' ||
      order.service_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user?.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user?.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterValues.status === 'all' || order.status === filterValues.status;

    // Price range filter
    const matchesPriceRange = filterValues.priceRange === 'all' || (() => {
      const price = order.price;
      switch (filterValues.priceRange) {
        case '0-50': return price >= 0 && price <= 50;
        case '50-100': return price > 50 && price <= 100;
        case '100-200': return price > 100 && price <= 200;
        case '200-500': return price > 200 && price <= 500;
        case '500+': return price > 500;
        default: return true;
      }
    })();

    return matchesSearch && matchesStatus && matchesPriceRange;
  }));

  // Sort filtered orders
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    switch (filterValues.sortBy) {
      case 'price':
        return a.price - b.price;
      case 'name':
        return a.service_name.localeCompare(b.service_name);
      case 'updated':
        return new Date(b.updated_at || '').getTime() - new Date(a.updated_at || '').getTime();
      case 'created':
      default:
        return new Date(b.purchase_date).getTime() - new Date(a.purchase_date).getTime();
    }
  });

  // Pagination logic
  const totalPages = Math.ceil(sortedOrders.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedOrders = sortedOrders.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterValues]);

  // Calculate comprehensive order statistics
  const orderStats = {
    total: orders.length,
    pending: orders.filter(o => o.status === 'pending').length,
    active: orders.filter(o => o.status === 'active').length,
    completed: orders.filter(o => o.status === 'completed').length,
    cancelled: orders.filter(o => o.status === 'cancelled').length,
    // Calculate revenue from completed orders only (paid orders)
    totalRevenue: orders
      .filter(o => o.status === 'completed')
      .reduce((sum, o) => {
        // Get final price from database fields or details
        let finalPrice = 0;
        try {
          const details = JSON.parse(o.details || '{}');
          finalPrice = parseFloat(details.final_price?.toString() || details.total_price?.toString() || details.unit_price?.toString() || '0') || 0;
        } catch (e) {
          // Fallback to direct price field
          finalPrice = parseFloat(o.price?.toString() || '0') || 0;
        }
        return sum + finalPrice;
      }, 0),
    // Additional statistics
    pendingRevenue: orders
      .filter(o => o.status === 'pending')
      .reduce((sum, o) => {
        let finalPrice = 0;
        try {
          const details = JSON.parse(o.details || '{}');
          finalPrice = parseFloat(details.final_price?.toString() || details.total_price?.toString() || details.unit_price?.toString() || '0') || 0;
        } catch (e) {
          finalPrice = parseFloat(o.price?.toString() || '0') || 0;
        }
        return sum + finalPrice;
      }, 0),
    averageOrderValue: orders.length > 0 ?
      orders
        .filter(o => o.status === 'completed')
        .reduce((sum, o) => {
          let finalPrice = 0;
          try {
            const details = JSON.parse(o.details || '{}');
            finalPrice = parseFloat(details.final_price?.toString() || details.total_price?.toString() || details.unit_price?.toString() || '0') || 0;
          } catch (e) {
            finalPrice = parseFloat(o.price?.toString() || '0') || 0;
          }
          return sum + finalPrice;
        }, 0) / Math.max(orders.filter(o => o.status === 'completed').length, 1)
      : 0
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-secondary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Enhanced Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-6 space-y-4 lg:space-y-0">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          {onBack && (
            <BackButton onClick={onBack} variant="back" size="md" />
          )}
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('admin.dashboard.orders')}
            </h1>
            <p className="text-gray-400 text-sm mt-1">
              {language === 'ar' ? 'إدارة وتتبع جميع الطلبات' : 'Manage and track all orders'}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Button variant="outline" onClick={loadData} size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            {language === 'ar' ? 'تصدير' : 'Export'}
          </Button>
          <Button variant="outline" size="sm">
            <FileText className="w-4 h-4 mr-2" />
            {language === 'ar' ? 'تقرير' : 'Report'}
          </Button>
          <Button
            variant={showDebugger ? "primary" : "outline"}
            size="sm"
            onClick={() => setShowDebugger(!showDebugger)}
          >
            <Bug className="w-4 h-4 mr-2" />
            {language === 'ar' ? 'مصحح الأخطاء' : 'Debug'}
          </Button>
        </div>
      </div>

      {/* Enhanced Statistics */}
      <OrderStatistics orders={orders} />

      {/* Order Debugger */}
      {showDebugger && (
        <div className="mb-6">
          <OrderDebugger />
        </div>
      )}

      {/* Enhanced Filters and Search */}
      <OrderFilters
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        filterStatus={filterValues.status}
        setFilterStatus={(status) => handleFilterChange('status', status)}
        sortBy={filterValues.sortBy}
        setSortBy={(sort) => handleFilterChange('sortBy', sort)}
        viewMode={viewMode}
        setViewMode={setViewMode}
        onRefresh={loadData}
        onExport={() => buttonActions.exportData('orders', sortedOrders)}
        totalOrders={orders.length}
        filteredOrders={sortedOrders.length}
      />

      {/* Orders Grid/List */}
      {sortedOrders.length === 0 ? (
        <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/40 border border-gray-700/50 rounded-xl p-12 text-center">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">
            {language === 'ar' ? 'لا توجد طلبات' : 'No Orders Found'}
          </h3>
          <p className="text-gray-400">
            {language === 'ar' ? 'لا توجد طلبات تطابق معايير البحث' : 'No orders match your search criteria'}
          </p>
        </div>
      ) : (
        <>
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4' : 'space-y-3'}>
            {paginatedOrders.map((order) => {
            const user = getUserById(order.user_id);
            return (
              <OrderCard
                key={order.id}
                order={order}
                user={user}
                viewMode={viewMode}
                onView={(order) => {
                  setSelectedOrder(order);
                  setShowOrderModal(true);
                }}
                onEdit={(order) => {
                  setSelectedOrder(order);
                  setShowEnhancedEditor(true);
                }}
                onDelete={handleDeleteOrder}
                onStatusUpdate={handleStatusUpdate}
              />
            );
          })}
          </div>

          {/* Pagination */}
          {sortedOrders.length > 0 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={sortedOrders.length}
              itemsPerPage={itemsPerPage}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
              itemsPerPageOptions={[10, 20, 30, 50]}
              className="mt-6"
            />
          )}
        </>
      )}

      {/* Order Details Modal */}
      {showOrderModal && selectedOrder && (
        <Modal
          isOpen={showOrderModal}
          onClose={() => setShowOrderModal(false)}
          title={language === 'ar' ? 'تفاصيل الطلب' : 'Order Details'}
          size="lg"
        >
          <Modal.Body className="modal-text-fix">
            <div className="space-y-6">
              {/* Order Information */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'معلومات الطلب' : 'Order Information'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm text-dark-theme-secondary">{language === 'ar' ? 'اسم الخدمة' : 'Service Name'}</label>
                      <p className="text-white font-semibold">{selectedOrder.service_name}</p>
                    </div>
                    <div>
                      <label className="text-sm text-dark-theme-secondary">{language === 'ar' ? 'السعر' : 'Price'}</label>
                      <p className="text-secondary font-bold text-lg">${selectedOrder.price}</p>
                    </div>
                    <div>
                      <label className="text-sm text-dark-theme-secondary">{language === 'ar' ? 'الحالة' : 'Status'}</label>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        {getStatusIcon(selectedOrder.status)}
                        <span className="text-white">{t(`admin.dashboard.${selectedOrder.status}`)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm text-dark-theme-secondary">{language === 'ar' ? 'تاريخ الطلب' : 'Order Date'}</label>
                      <p className="text-white">{formatDateTime(selectedOrder.purchase_date)}</p>
                    </div>
                    <div>
                      <label className="text-sm text-dark-theme-secondary">{language === 'ar' ? 'نوع الخدمة' : 'Service Type'}</label>
                      <p className="text-white">{selectedOrder.service_type}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Customer Information */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'معلومات العميل' : 'Customer Information'}
                </h3>
                {(() => {
                  const user = getUserById(selectedOrder.user_id);
                  return user ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm text-dark-theme-secondary">{language === 'ar' ? 'اسم المستخدم' : 'Username'}</label>
                          <p className="text-white">{user.username}</p>
                        </div>
                        <div>
                          <label className="text-sm text-dark-theme-secondary">{language === 'ar' ? 'الاسم الكامل' : 'Full Name'}</label>
                          <p className="text-white">{user.full_name}</p>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm text-dark-theme-secondary">{language === 'ar' ? 'البريد الإلكتروني' : 'Email'}</label>
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <Mail className="w-4 h-4 text-dark-theme-secondary" />
                            <p className="text-white">{user.email}</p>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm text-dark-theme-secondary">{language === 'ar' ? 'الرتبة' : 'Role'}</label>
                          <p className="text-white capitalize">{user.role}</p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-400">{language === 'ar' ? 'معلومات المستخدم غير متاحة' : 'User information not available'}</p>
                  );
                })()}
              </div>

              {/* Order Notes */}
              {selectedOrder.notes && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">
                    {language === 'ar' ? 'ملاحظات الطلب' : 'Order Notes'}
                  </h3>
                  <div className="bg-primary/50 border border-accent/30 rounded-lg p-4">
                    <p className="text-gray-300">{selectedOrder.notes}</p>
                  </div>
                </div>
              )}

              {/* Status Update */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'تحديث الحالة' : 'Update Status'}
                </h3>
                <div className="flex space-x-2 rtl:space-x-reverse">
                  {['pending', 'active', 'completed', 'cancelled'].map(status => (
                    <Button
                      key={status}
                      variant={selectedOrder.status === status ? 'primary' : 'outline'}
                      size="sm"
                      onClick={() => handleStatusUpdate(selectedOrder.id, status)}
                      disabled={selectedOrder.status === status}
                    >
                      {getStatusIcon(status)}
                      <span className="ml-1">{t(`admin.dashboard.${status}`)}</span>
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowOrderModal(false)}>
              {language === 'ar' ? 'إغلاق' : 'Close'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && orderToDelete && (
        <Modal
          isOpen={showDeleteConfirm}
          onClose={() => setShowDeleteConfirm(false)}
          title={language === 'ar' ? 'تأكيد الحذف' : 'Confirm Deletion'}
        >
          <Modal.Body className="modal-text-fix">
            <div className="text-center py-6">
              <XCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">
                {language === 'ar' ? 'هل أنت متأكد؟' : 'Are you sure?'}
              </h3>
              <p className="text-dark-theme-secondary mb-4">
                {language === 'ar'
                  ? `سيتم إلغاء الطلب "${orderToDelete.service_name}" نهائياً`
                  : `This will permanently cancel the order "${orderToDelete.service_name}"`
                }
              </p>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowDeleteConfirm(false)}>
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button variant="danger" onClick={confirmDelete}>
              <Trash2 className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'حذف الطلب' : 'Delete Order'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}

      {/* Message Modal */}
      {showMessageModal && messageOrder && (
        <Modal
          isOpen={showMessageModal}
          onClose={() => setShowMessageModal(false)}
          title={language === 'ar' ? 'إرسال رسالة للعميل' : 'Send Message to Customer'}
          size="md"
        >
          <Modal.Body className="modal-text-fix">
            <div className="space-y-4">
              <div className="bg-primary/30 p-4 rounded-lg">
                <h4 className="text-white font-medium mb-2">
                  {language === 'ar' ? 'معلومات الطلب' : 'Order Information'}
                </h4>
                <p className="text-dark-theme-secondary text-sm">
                  {language === 'ar' ? 'الخدمة:' : 'Service:'} {messageOrder.service_name}
                </p>
                <p className="text-dark-theme-secondary text-sm">
                  {language === 'ar' ? 'العميل:' : 'Customer:'} {getUserById(messageOrder.user_id)?.username}
                </p>
                <p className="text-dark-theme-secondary text-sm">
                  {language === 'ar' ? 'البريد الإلكتروني:' : 'Email:'} {getUserById(messageOrder.user_id)?.email}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-dark-theme-secondary mb-2">
                  {language === 'ar' ? 'الرسالة' : 'Message'}
                </label>
                <textarea
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  rows={6}
                  className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none focus:border-secondary focus:ring-1 focus:ring-secondary"
                  placeholder={language === 'ar' ? 'اكتب رسالتك هنا...' : 'Type your message here...'}
                />
              </div>

              <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
                <p className="text-yellow-400 text-sm">
                  {language === 'ar'
                    ? 'سيتم إرسال هذه الرسالة إلى البريد الإلكتروني للعميل'
                    : 'This message will be sent to the customer\'s email address'
                  }
                </p>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowMessageModal(false)}>
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button
              variant="primary"
              onClick={handleMessageSubmit}
              disabled={!messageText.trim()}
            >
              <Mail className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'إرسال الرسالة' : 'Send Message'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}

      {/* Enhanced Order Editor */}
      {showEnhancedEditor && selectedOrder && (
        <EnhancedOrderEditor
          order={selectedOrder as any}
          isOpen={showEnhancedEditor}
          onClose={() => setShowEnhancedEditor(false)}
          onSave={handleSaveEnhancedOrder}
        />
      )}
    </div>
  );
};

export default OrderManagement;
