/**
 * Request Throttling Utility
 * 
 * This utility prevents too many API requests from being made simultaneously
 */

interface RequestCache {
  [key: string]: {
    timestamp: number;
    promise: Promise<any>;
  };
}

class RequestThrottler {
  private cache: RequestCache = {};
  private readonly THROTTLE_TIME = 5000; // 5 seconds for better caching
  private readonly MAX_CONCURRENT = 5; // Max 5 concurrent requests
  private activeRequests = 0;
  private requestQueue: Array<() => void> = [];

  /**
   * Throttle API requests to prevent overwhelming the server
   */
  async throttleRequest<T>(
    key: string,
    requestFn: () => Promise<T>,
    cacheTime: number = this.THROTTLE_TIME
  ): Promise<T> {
    const now = Date.now();
    
    // Check if we have a cached result
    if (this.cache[key] && (now - this.cache[key].timestamp) < cacheTime) {
      if (import.meta.env.DEV) {
        console.log(`🔄 Using cached result for: ${key}`);
      }
      return this.cache[key].promise;
    }
    
    // Wait if too many concurrent requests
    if (this.activeRequests >= this.MAX_CONCURRENT) {
      if (import.meta.env.DEV) {
        console.log(`⏳ Queuing request: ${key}`);
      }
      await this.waitForSlot();
    }

    // Create new request
    this.activeRequests++;
    if (import.meta.env.DEV) {
      console.log(`📤 Making request: ${key} (${this.activeRequests}/${this.MAX_CONCURRENT})`);
    }
    
    const promise = requestFn()
      .finally(() => {
        this.activeRequests--;
        this.processQueue();
      });
    
    // Cache the request
    this.cache[key] = {
      timestamp: now,
      promise
    };
    
    return promise;
  }
  
  private waitForSlot(): Promise<void> {
    return new Promise((resolve) => {
      this.requestQueue.push(resolve);
    });
  }
  
  private processQueue(): void {
    if (this.requestQueue.length > 0 && this.activeRequests < this.MAX_CONCURRENT) {
      const next = this.requestQueue.shift();
      if (next) next();
    }
  }
  
  /**
   * Clear cache for specific key or all cache
   */
  clearCache(key?: string): void {
    if (key) {
      delete this.cache[key];
      console.log(`🗑️ Cleared cache for: ${key}`);
    } else {
      this.cache = {};
      console.log('🗑️ Cleared all cache');
    }
  }
  
  /**
   * Get current status
   */
  getStatus(): {
    activeRequests: number;
    queuedRequests: number;
    cachedKeys: string[];
  } {
    return {
      activeRequests: this.activeRequests,
      queuedRequests: this.requestQueue.length,
      cachedKeys: Object.keys(this.cache)
    };
  }
}

// Create singleton instance
export const requestThrottler = new RequestThrottler();

/**
 * Throttled API call wrapper
 */
export async function throttledApiCall<T>(
  key: string,
  apiCall: () => Promise<T>,
  cacheTime?: number
): Promise<T> {
  return requestThrottler.throttleRequest(key, apiCall, cacheTime);
}

/**
 * Clear request cache
 */
export function clearRequestCache(key?: string): void {
  requestThrottler.clearCache(key);
}

/**
 * Get throttler status
 */
export function getThrottlerStatus() {
  return requestThrottler.getStatus();
}