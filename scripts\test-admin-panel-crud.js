/**
 * Admin Panel CRUD Operations Testing Script
 * Tests all admin management sections and MySQL operations
 */

const axios = require('axios');
const mysql = require('mysql2/promise');

const API_BASE = 'http://localhost:3001/api';
const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khanfashariya_db'
};

// Admin credentials
const ADMIN_CREDS = { email: '<EMAIL>', password: 'admin123' };

let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

function logTest(name, status, details = '') {
  testResults.total++;
  const icon = status === 'PASS' ? '✅' : '❌';
  const message = `${icon} ${name}`;
  
  if (status === 'PASS') {
    testResults.passed++;
    console.log(`${message} ${details}`);
  } else {
    testResults.failed++;
    console.log(`${message} - ${details}`);
    testResults.errors.push({ test: name, error: details });
  }
}

async function getAdminAuth() {
  try {
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, ADMIN_CREDS);
    if (loginResponse.data.success && loginResponse.data.data.tokens) {
      return {
        success: true,
        token: loginResponse.data.data.tokens.accessToken,
        headers: { Authorization: `Bearer ${loginResponse.data.data.tokens.accessToken}` }
      };
    }
  } catch (error) {
    console.error('Admin authentication failed:', error.message);
  }
  return { success: false };
}

async function testAdminDashboard(authData) {
  console.log('\n🔍 Testing Admin Dashboard...');
  
  if (!authData.success) {
    logTest('Admin Dashboard Prerequisites', 'FAIL', 'No admin authentication');
    return;
  }
  
  try {
    const dashboardResponse = await axios.get(`${API_BASE}/admin/dashboard`, { headers: authData.headers });
    
    if (dashboardResponse.data.success) {
      const stats = dashboardResponse.data.data.stats;
      logTest('Admin Dashboard Access', 'PASS', 'Dashboard loaded successfully');
      logTest('Dashboard Statistics', 'PASS', `Users: ${stats.users.total_users}, Orders: ${stats.orders.total_orders}`);
      logTest('Dashboard Data Integrity', 'PASS', `Revenue: $${stats.revenue.total_revenue || 0}`);
    } else {
      logTest('Admin Dashboard Access', 'FAIL', 'Dashboard access failed');
    }
  } catch (error) {
    logTest('Admin Dashboard Access', 'FAIL', error.message);
  }
}

async function testSystemsManagement(authData) {
  console.log('\n🔍 Testing Systems Management CRUD...');
  
  if (!authData.success) {
    logTest('Systems Management Prerequisites', 'FAIL', 'No admin authentication');
    return;
  }
  
  try {
    // Test READ - Get all systems
    const getSystemsResponse = await axios.get(`${API_BASE}/admin/systems`, { headers: authData.headers });
    
    if (getSystemsResponse.data.success) {
      const systems = getSystemsResponse.data.data.systems || [];
      logTest('Systems READ Operation', 'PASS', `Retrieved ${systems.length} systems`);
      
      if (systems.length > 0) {
        const testSystem = systems[0];
        logTest('Systems Data Quality', 'PASS', `System: ${testSystem.name_ar} - $${testSystem.price}`);
        
        // Test UPDATE - Update system
        const updateData = {
          name_ar: testSystem.name_ar + ' (محدث)',
          name_en: testSystem.name_en + ' (Updated)',
          price: parseFloat(testSystem.price) + 10
        };
        
        const updateResponse = await axios.put(`${API_BASE}/admin/systems/${testSystem.id}`, updateData, { headers: authData.headers });
        
        if (updateResponse.data.success) {
          logTest('Systems UPDATE Operation', 'PASS', 'System updated successfully');
          
          // Verify update in database
          const connection = await mysql.createConnection(DB_CONFIG);
          const [rows] = await connection.execute('SELECT name_ar, price FROM system_services WHERE id = ?', [testSystem.id]);
          
          if (rows.length > 0 && rows[0].name_ar.includes('محدث')) {
            logTest('Systems UPDATE Verification', 'PASS', 'Database reflects changes');
          } else {
            logTest('Systems UPDATE Verification', 'FAIL', 'Database not updated');
          }
          
          await connection.end();
        } else {
          logTest('Systems UPDATE Operation', 'FAIL', 'System update failed');
        }
      }
    } else {
      logTest('Systems READ Operation', 'FAIL', 'Failed to retrieve systems');
    }
    
    // Test CREATE - Create new system
    const newSystemData = {
      name_ar: 'نظام تجريبي للاختبار',
      name_en: 'Test System for Testing',
      description_ar: 'وصف النظام التجريبي',
      description_en: 'Test system description',
      price: 99.99,
      category: 'testing',
      status: 'active'
    };
    
    const createResponse = await axios.post(`${API_BASE}/admin/systems`, newSystemData, { headers: authData.headers });
    
    if (createResponse.data.success) {
      logTest('Systems CREATE Operation', 'PASS', 'New system created successfully');

      const newSystemId = createResponse.data.data?.system?.id || createResponse.data.data?.id;

      if (newSystemId) {
        // Test DELETE - Delete the test system
        try {
          const deleteResponse = await axios.delete(`${API_BASE}/admin/systems/${newSystemId}`, { headers: authData.headers });

          if (deleteResponse.data.success) {
            logTest('Systems DELETE Operation', 'PASS', 'Test system deleted successfully');
          } else {
            logTest('Systems DELETE Operation', 'FAIL', 'Failed to delete test system');
          }
        } catch (deleteError) {
          logTest('Systems DELETE Operation', 'FAIL', `Delete error: ${deleteError.message}`);
        }
      } else {
        logTest('Systems DELETE Operation', 'FAIL', 'No system ID returned from create');
      }
    } else {
      logTest('Systems CREATE Operation', 'FAIL', 'Failed to create new system');
    }
    
  } catch (error) {
    logTest('Systems Management CRUD', 'FAIL', error.message);
  }
}

async function testServicesManagement(authData) {
  console.log('\n🔍 Testing Technical Services Management CRUD...');
  
  if (!authData.success) {
    logTest('Services Management Prerequisites', 'FAIL', 'No admin authentication');
    return;
  }
  
  try {
    // Test READ - Get all services
    const getServicesResponse = await axios.get(`${API_BASE}/admin/technical-services`, { headers: authData.headers });
    
    if (getServicesResponse.data.success) {
      const services = getServicesResponse.data.data || [];
      logTest('Services READ Operation', 'PASS', `Retrieved ${services.length} services`);
    } else {
      logTest('Services READ Operation', 'FAIL', 'Failed to retrieve services');
    }
    
    // Test CREATE - Create new service
    const newServiceData = {
      name_ar: 'خدمة تجريبية للاختبار',
      name_en: 'Test Service for Testing',
      description_ar: 'وصف الخدمة التجريبية',
      description_en: 'Test service description',
      price: 49.99,
      category: 'testing',
      status: 'active'
    };
    
    const createResponse = await axios.post(`${API_BASE}/admin/technical-services`, newServiceData, { headers: authData.headers });
    
    if (createResponse.data.success) {
      logTest('Services CREATE Operation', 'PASS', 'New service created successfully');

      const newServiceId = createResponse.data.data?.service?.id || createResponse.data.data?.id;

      if (newServiceId) {
        // Test DELETE - Delete the test service
        try {
          const deleteResponse = await axios.delete(`${API_BASE}/admin/technical-services/${newServiceId}`, { headers: authData.headers });

          if (deleteResponse.data.success) {
            logTest('Services DELETE Operation', 'PASS', 'Test service deleted successfully');
          } else {
            logTest('Services DELETE Operation', 'FAIL', 'Failed to delete test service');
          }
        } catch (deleteError) {
          logTest('Services DELETE Operation', 'FAIL', `Delete error: ${deleteError.message}`);
        }
      } else {
        logTest('Services DELETE Operation', 'FAIL', 'No service ID returned from create');
      }
    } else {
      logTest('Services CREATE Operation', 'FAIL', 'Failed to create new service');
    }
    
  } catch (error) {
    logTest('Services Management CRUD', 'FAIL', error.message);
  }
}

async function testOrdersManagement(authData) {
  console.log('\n🔍 Testing Orders Management...');
  
  if (!authData.success) {
    logTest('Orders Management Prerequisites', 'FAIL', 'No admin authentication');
    return;
  }
  
  try {
    // Test READ - Get all orders
    const getOrdersResponse = await axios.get(`${API_BASE}/admin/orders`, { headers: authData.headers });
    
    if (getOrdersResponse.data.success) {
      const orders = getOrdersResponse.data.data.orders || [];
      logTest('Orders READ Operation', 'PASS', `Retrieved ${orders.length} orders`);
      
      if (orders.length > 0) {
        const testOrder = orders[0];
        logTest('Orders Data Quality', 'PASS', `Order: ${testOrder.order_number} - $${testOrder.final_price}`);
        
        // Test UPDATE - Update order status
        const updateData = {
          status: 'confirmed',
          admin_notes: 'Order confirmed by admin test'
        };
        
        const updateResponse = await axios.put(`${API_BASE}/admin/orders/${testOrder.id}`, updateData, { headers: authData.headers });
        
        if (updateResponse.data.success) {
          logTest('Orders UPDATE Operation', 'PASS', 'Order status updated successfully');
        } else {
          logTest('Orders UPDATE Operation', 'FAIL', 'Failed to update order status');
        }
      }
    } else {
      logTest('Orders READ Operation', 'FAIL', 'Failed to retrieve orders');
    }
  } catch (error) {
    logTest('Orders Management', 'FAIL', error.message);
  }
}

async function testUserManagement(authData) {
  console.log('\n🔍 Testing User Management...');
  
  if (!authData.success) {
    logTest('User Management Prerequisites', 'FAIL', 'No admin authentication');
    return;
  }
  
  try {
    // Test READ - Get all users
    const getUsersResponse = await axios.get(`${API_BASE}/admin/users`, { headers: authData.headers });
    
    if (getUsersResponse.data.success) {
      const users = getUsersResponse.data.data.users || [];
      logTest('Users READ Operation', 'PASS', `Retrieved ${users.length} users`);
      
      // Find a regular user to test role management
      const regularUser = users.find(u => u.role === 'user');
      if (regularUser) {
        logTest('User Data Quality', 'PASS', `User: ${regularUser.email} - Role: ${regularUser.role}`);
      }
    } else {
      logTest('Users READ Operation', 'FAIL', 'Failed to retrieve users');
    }
  } catch (error) {
    logTest('User Management', 'FAIL', error.message);
  }
}

async function testPremiumContentManagement(authData) {
  console.log('\n🔍 Testing Premium Content Management...');
  
  if (!authData.success) {
    logTest('Premium Management Prerequisites', 'FAIL', 'No admin authentication');
    return;
  }
  
  try {
    // Test READ - Get all premium content
    const getPremiumResponse = await axios.get(`${API_BASE}/premium/admin`, { headers: authData.headers });
    
    if (getPremiumResponse.data.success) {
      const premium = getPremiumResponse.data.data || [];
      logTest('Premium READ Operation', 'PASS', `Retrieved ${premium.length} premium items`);
    } else {
      logTest('Premium READ Operation', 'FAIL', 'Failed to retrieve premium content');
    }
  } catch (error) {
    logTest('Premium Content Management', 'FAIL', error.message);
  }
}

async function runAdminPanelCRUDTest() {
  console.log('🚀 Starting Admin Panel CRUD Operations Testing');
  console.log('=' * 60);
  
  const startTime = Date.now();
  
  // Get admin authentication
  const authData = await getAdminAuth();
  
  if (!authData.success) {
    console.log('❌ Failed to authenticate as admin. Cannot proceed with tests.');
    return;
  }
  
  logTest('Admin Authentication', 'PASS', 'Admin login successful');
  
  // Run all admin panel tests
  await testAdminDashboard(authData);
  await testSystemsManagement(authData);
  await testServicesManagement(authData);
  await testOrdersManagement(authData);
  await testUserManagement(authData);
  await testPremiumContentManagement(authData);
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  // Print summary
  console.log('\n' + '=' * 60);
  console.log('📊 ADMIN PANEL CRUD OPERATIONS TEST SUMMARY');
  console.log('=' * 60);
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  console.log(`Duration: ${duration} seconds`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.test}: ${error.error}`);
    });
  }
  
  // Admin panel assessment
  const successRate = (testResults.passed / testResults.total) * 100;
  console.log('\n🛠️ ADMIN PANEL ASSESSMENT:');
  
  if (successRate >= 90) {
    console.log('🟢 EXCELLENT - Admin panel fully functional for business management');
  } else if (successRate >= 75) {
    console.log('🟡 GOOD - Admin panel mostly working, minor issues to address');
  } else {
    console.log('🔴 NEEDS WORK - Critical admin panel issues need resolution');
  }
  
  console.log('\n🎉 Admin panel CRUD operations testing completed!');
}

// Run the test
runAdminPanelCRUDTest().catch(console.error);
