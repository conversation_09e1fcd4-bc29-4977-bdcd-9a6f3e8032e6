import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotification } from '../../hooks/useNotification';
import { 
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  CreditCard,
  Package,
  MessageSquare,
  User,
  Calendar,
  DollarSign,
  CheckCircle,
  Clock,
  AlertCircle,
  Star,
  Eye,
  Download,
  Send,
  X,
  ArrowRight,
  ArrowLeft,
  Filter,
  Search,
  Settings,
  Bell,
  FileText,
  Percent,
  Tag
} from 'lucide-react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Modal from '../ui/Modal';
import Card from '../ui/Card';

interface CartItem {
  id: string;
  type: 'system' | 'service' | 'premium';
  name: { ar: string; en: string };
  description: { ar: string; en: string };
  price: number;
  originalPrice?: number;
  quantity: number;
  image?: string;
  category: string;
  customizations?: {
    [key: string]: any;
  };
  addedAt: string;
}

interface Order {
  id: string;
  userId: string;
  items: CartItem[];
  totalAmount: number;
  discountAmount: number;
  finalAmount: number;
  status: 'pending' | 'processing' | 'completed' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: string;
  notes: string;
  adminNotes: string;
  messages: OrderMessage[];
  createdAt: string;
  updatedAt: string;
  estimatedDelivery?: string;
}

interface OrderMessage {
  id: string;
  senderId: string;
  senderType: 'user' | 'admin';
  message: string;
  timestamp: string;
  attachments?: string[];
  isRead: boolean;
}

interface ShoppingCartManagerProps {
  userId?: string;
  isAdmin?: boolean;
}

/**
 * Comprehensive Shopping Cart and Order Management System
 */
const ShoppingCartManager: React.FC<ShoppingCartManagerProps> = ({ userId, isAdmin = false }) => {
  const { t, language } = useTranslation();
  const { showNotification } = useNotification();
  
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showCart, setShowCart] = useState(false);
  const [showCheckout, setShowCheckout] = useState(false);
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'cart' | 'orders' | 'messages'>('cart');

  // Checkout form state
  const [checkoutData, setCheckoutData] = useState({
    paymentMethod: 'paypal',
    notes: '',
    couponCode: '',
    billingInfo: {
      name: '',
      email: '',
      phone: '',
      address: ''
    }
  });

  // Message form state
  const [messageData, setMessageData] = useState({
    message: '',
    attachments: [] as string[]
  });

  // Filters and search
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'status'>('date');

  useEffect(() => {
    loadCartData();
    if (userId) {
      loadUserOrders();
    }
  }, [userId]);

  const loadCartData = () => {
    // Load cart from localStorage or API
    const savedCart = localStorage.getItem('shopping_cart');
    if (savedCart) {
      try {
        setCartItems(JSON.parse(savedCart));
      } catch (error) {
        console.error('Error loading cart:', error);
      }
    }
  };

  const loadUserOrders = async () => {
    setLoading(true);
    try {
      // Mock orders data
      const mockOrders: Order[] = [
        {
          id: '1',
          userId: userId || '1',
          items: [
            {
              id: '1',
              type: 'system',
              name: { ar: 'نظام القتال المتطور', en: 'Advanced Combat System' },
              description: { ar: 'نظام قتال محسن مع ميزات متقدمة', en: 'Enhanced combat system with advanced features' },
              price: 150,
              quantity: 1,
              category: 'combat',
              addedAt: new Date().toISOString()
            }
          ],
          totalAmount: 150,
          discountAmount: 0,
          finalAmount: 150,
          status: 'processing',
          paymentStatus: 'paid',
          paymentMethod: 'PayPal',
          notes: 'Please install on FreeBSD server',
          adminNotes: 'Installation scheduled for tomorrow',
          messages: [
            {
              id: '1',
              senderId: userId || '1',
              senderType: 'user',
              message: 'When will the installation be completed?',
              timestamp: new Date().toISOString(),
              isRead: true
            },
            {
              id: '2',
              senderId: 'admin',
              senderType: 'admin',
              message: 'Installation will be completed within 24 hours. We will notify you once it\'s ready.',
              timestamp: new Date().toISOString(),
              isRead: false
            }
          ],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          estimatedDelivery: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        }
      ];
      setOrders(mockOrders);
    } catch (error) {
      console.error('Error loading orders:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في تحميل الطلبات' : 'Failed to load orders'
      });
    }
    setLoading(false);
  };

  const addToCart = (item: Omit<CartItem, 'quantity' | 'addedAt'>) => {
    const existingItem = cartItems.find(cartItem => cartItem.id === item.id && cartItem.type === item.type);
    
    if (existingItem) {
      updateQuantity(existingItem.id, existingItem.quantity + 1);
    } else {
      const newItem: CartItem = {
        ...item,
        quantity: 1,
        addedAt: new Date().toISOString()
      };
      const updatedCart = [...cartItems, newItem];
      setCartItems(updatedCart);
      localStorage.setItem('shopping_cart', JSON.stringify(updatedCart));
      
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم إضافة العنصر إلى السلة' : 'Item added to cart'
      });
    }
  };

  const updateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(itemId);
      return;
    }
    
    const updatedCart = cartItems.map(item =>
      item.id === itemId ? { ...item, quantity: newQuantity } : item
    );
    setCartItems(updatedCart);
    localStorage.setItem('shopping_cart', JSON.stringify(updatedCart));
  };

  const removeFromCart = (itemId: string) => {
    const updatedCart = cartItems.filter(item => item.id !== itemId);
    setCartItems(updatedCart);
    localStorage.setItem('shopping_cart', JSON.stringify(updatedCart));
    
    showNotification({
      type: 'success',
      message: language === 'ar' ? 'تم حذف العنصر من السلة' : 'Item removed from cart'
    });
  };

  const calculateTotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const calculateDiscount = () => {
    // Apply discount logic based on coupon code or bulk purchase
    const total = calculateTotal();
    if (checkoutData.couponCode === 'SAVE10') {
      return total * 0.1; // 10% discount
    }
    if (cartItems.length >= 3) {
      return total * 0.05; // 5% bulk discount
    }
    return 0;
  };

  const processCheckout = async () => {
    setLoading(true);
    try {
      const total = calculateTotal();
      const discount = calculateDiscount();
      const finalAmount = total - discount;

      const newOrder: Order = {
        id: Date.now().toString(),
        userId: userId || 'guest',
        items: [...cartItems],
        totalAmount: total,
        discountAmount: discount,
        finalAmount: finalAmount,
        status: 'pending',
        paymentStatus: 'pending',
        paymentMethod: checkoutData.paymentMethod,
        notes: checkoutData.notes,
        adminNotes: '',
        messages: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      newOrder.paymentStatus = 'paid';
      newOrder.status = 'processing';
      
      setOrders(prev => [newOrder, ...prev]);
      setCartItems([]);
      localStorage.removeItem('shopping_cart');
      setShowCheckout(false);
      setShowCart(false);
      
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم إنشاء الطلب بنجاح' : 'Order created successfully'
      });
    } catch (error) {
      console.error('Error processing checkout:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في معالجة الطلب' : 'Failed to process order'
      });
    }
    setLoading(false);
  };

  const sendMessage = async (orderId: string) => {
    if (!messageData.message.trim()) return;

    try {
      const newMessage: OrderMessage = {
        id: Date.now().toString(),
        senderId: userId || 'user',
        senderType: isAdmin ? 'admin' : 'user',
        message: messageData.message,
        timestamp: new Date().toISOString(),
        attachments: messageData.attachments,
        isRead: false
      };

      setOrders(prev => prev.map(order => 
        order.id === orderId 
          ? { ...order, messages: [...order.messages, newMessage] }
          : order
      ));

      setMessageData({ message: '', attachments: [] });
      setShowMessageModal(false);
      
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم إرسال الرسالة' : 'Message sent'
      });
    } catch (error) {
      console.error('Error sending message:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في إرسال الرسالة' : 'Failed to send message'
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4 text-yellow-400" />;
      case 'processing': return <Package className="w-4 h-4 text-blue-400" />;
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'cancelled': return <X className="w-4 h-4 text-red-400" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'processing': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'completed': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'cancelled': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.id.includes(searchTerm) ||
                         order.items.some(item => 
                           item.name[language].toLowerCase().includes(searchTerm.toLowerCase())
                         );
    const matchesStatus = filterStatus === 'all' || order.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Cart Icon and Counter */}
      <div className="fixed top-4 right-4 z-50">
        <Button
          variant="primary"
          onClick={() => setShowCart(true)}
          className="relative"
        >
          <ShoppingCart className="w-5 h-5" />
          {cartItems.length > 0 && (
            <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {cartItems.length}
            </span>
          )}
        </Button>
      </div>

      {/* Shopping Cart Modal */}
      {showCart && (
        <Modal
          isOpen={showCart}
          onClose={() => setShowCart(false)}
          title={language === 'ar' ? 'سلة التسوق' : 'Shopping Cart'}
          size="lg"
        >
          <Modal.Body>
            {cartItems.length === 0 ? (
              <div className="text-center py-12">
                <ShoppingCart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">
                  {language === 'ar' ? 'السلة فارغة' : 'Cart is Empty'}
                </h3>
                <p className="text-gray-400">
                  {language === 'ar' ? 'ابدأ بإضافة بعض العناصر' : 'Start adding some items'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {cartItems.map((item) => (
                  <div key={`${item.type}-${item.id}`} className="flex items-center space-x-4 rtl:space-x-reverse p-4 bg-primary/30 rounded-lg">
                    <div className="w-16 h-16 bg-gradient-to-br from-secondary to-accent rounded-lg flex items-center justify-center">
                      {item.type === 'system' && <Settings className="w-8 h-8 text-white" />}
                      {item.type === 'service' && <Package className="w-8 h-8 text-white" />}
                      {item.type === 'premium' && <Star className="w-8 h-8 text-white" />}
                    </div>

                    <div className="flex-1">
                      <h4 className="text-white font-medium">{language === 'ar' ? (item.name_ar || '') : (item.name_en || '')}</h4>
                      <p className="text-gray-400 text-sm line-clamp-1">{language === 'ar' ? (item.description_ar || '') : (item.description_en || '')}</p>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse mt-2">
                        <span className="text-secondary font-bold">${item.price}</span>
                        {item.originalPrice && item.originalPrice > item.price && (
                          <span className="text-gray-400 line-through text-sm">${item.originalPrice}</span>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => updateQuantity(item.id, item.quantity - 1)}
                      >
                        <Minus className="w-4 h-4" />
                      </Button>
                      <span className="text-white font-medium w-8 text-center">{item.quantity}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => updateQuantity(item.id, item.quantity + 1)}
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeFromCart(item.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}

                {/* Cart Summary */}
                <div className="border-t border-accent/20 pt-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-gray-300">
                      <span>{language === 'ar' ? 'المجموع الفرعي' : 'Subtotal'}</span>
                      <span>${calculateTotal()}</span>
                    </div>
                    {calculateDiscount() > 0 && (
                      <div className="flex justify-between text-green-400">
                        <span>{language === 'ar' ? 'الخصم' : 'Discount'}</span>
                        <span>-${calculateDiscount()}</span>
                      </div>
                    )}
                    <div className="flex justify-between text-white font-bold text-lg border-t border-accent/20 pt-2">
                      <span>{language === 'ar' ? 'المجموع' : 'Total'}</span>
                      <span>${calculateTotal() - calculateDiscount()}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </Modal.Body>

          {cartItems.length > 0 && (
            <Modal.Footer>
              <Button variant="outline" onClick={() => setShowCart(false)}>
                {language === 'ar' ? 'متابعة التسوق' : 'Continue Shopping'}
              </Button>
              <Button variant="primary" onClick={() => setShowCheckout(true)}>
                <CreditCard className="w-4 h-4 mr-2" />
                {language === 'ar' ? 'إتمام الطلب' : 'Checkout'}
              </Button>
            </Modal.Footer>
          )}
        </Modal>
      )}

      {/* Checkout Modal */}
      {showCheckout && (
        <Modal
          isOpen={showCheckout}
          onClose={() => setShowCheckout(false)}
          title={language === 'ar' ? 'إتمام الطلب' : 'Checkout'}
          size="xl"
        >
          <Modal.Body>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Order Summary */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'ملخص الطلب' : 'Order Summary'}
                </h3>
                <div className="space-y-3">
                  {cartItems.map((item) => (
                    <div key={`${item.type}-${item.id}`} className="flex justify-between items-center p-3 bg-primary/30 rounded-lg">
                      <div>
                        <p className="text-white font-medium">{item.name[language]}</p>
                        <p className="text-gray-400 text-sm">
                          {language === 'ar' ? 'الكمية' : 'Qty'}: {item.quantity}
                        </p>
                      </div>
                      <span className="text-secondary font-bold">${item.price * item.quantity}</span>
                    </div>
                  ))}

                  <div className="border-t border-accent/20 pt-3 space-y-2">
                    <div className="flex justify-between text-gray-300">
                      <span>{language === 'ar' ? 'المجموع الفرعي' : 'Subtotal'}</span>
                      <span>${calculateTotal()}</span>
                    </div>
                    {calculateDiscount() > 0 && (
                      <div className="flex justify-between text-green-400">
                        <span>{language === 'ar' ? 'الخصم' : 'Discount'}</span>
                        <span>-${calculateDiscount()}</span>
                      </div>
                    )}
                    <div className="flex justify-between text-white font-bold text-lg">
                      <span>{language === 'ar' ? 'المجموع النهائي' : 'Final Total'}</span>
                      <span>${calculateTotal() - calculateDiscount()}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Checkout Form */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {language === 'ar' ? 'معلومات الدفع' : 'Payment Information'}
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {language === 'ar' ? 'طريقة الدفع' : 'Payment Method'}
                    </label>
                    <select
                      value={checkoutData.paymentMethod}
                      onChange={(e) => setCheckoutData(prev => ({ ...prev, paymentMethod: e.target.value }))}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                    >
                      <option value="paypal">PayPal</option>
                      <option value="stripe">Stripe</option>
                      <option value="bank_transfer">{language === 'ar' ? 'تحويل بنكي' : 'Bank Transfer'}</option>
                    </select>
                  </div>

                  <Input
                    label={language === 'ar' ? 'كود الخصم' : 'Coupon Code'}
                    value={checkoutData.couponCode}
                    onChange={(e) => setCheckoutData(prev => ({ ...prev, couponCode: e.target.value }))}
                    placeholder={language === 'ar' ? 'أدخل كود الخصم' : 'Enter coupon code'}
                    leftIcon={<Tag />}
                  />

                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {language === 'ar' ? 'ملاحظات إضافية' : 'Additional Notes'}
                    </label>
                    <textarea
                      value={checkoutData.notes}
                      onChange={(e) => setCheckoutData(prev => ({ ...prev, notes: e.target.value }))}
                      rows={3}
                      className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                      placeholder={language === 'ar' ? 'أي ملاحظات خاصة...' : 'Any special notes...'}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label={language === 'ar' ? 'الاسم الكامل' : 'Full Name'}
                      value={checkoutData.billingInfo.name}
                      onChange={(e) => setCheckoutData(prev => ({
                        ...prev,
                        billingInfo: { ...prev.billingInfo, name: e.target.value }
                      }))}
                      leftIcon={<User />}
                    />
                    <Input
                      label={language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                      type="email"
                      value={checkoutData.billingInfo.email}
                      onChange={(e) => setCheckoutData(prev => ({
                        ...prev,
                        billingInfo: { ...prev.billingInfo, email: e.target.value }
                      }))}
                    />
                  </div>
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowCheckout(false)}>
              {language === 'ar' ? 'رجوع' : 'Back'}
            </Button>
            <Button variant="primary" onClick={processCheckout} disabled={loading}>
              {loading ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              ) : (
                <CreditCard className="w-4 h-4 mr-2" />
              )}
              {language === 'ar' ? 'تأكيد الطلب' : 'Confirm Order'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}
    </div>
  );
};

export default ShoppingCartManager;
