import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuth } from '../hooks/useAuth';
import { 
  getAllServices,
  updateServiceStatus,
  UserService,
  getAllUsers,
  User
} from '../lib/database';
import { 
  ShoppingCart, 
  Search, 
  X,
  Eye,
  Edit3,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle
} from 'lucide-react';

interface AdvancedOrderManagementProps {
  onClose: () => void;
}

const AdvancedOrderManagement: React.FC<AdvancedOrderManagementProps> = ({ onClose }) => {
  const { language } = useTranslation();
  const [orders, setOrders] = useState<UserService[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | UserService['status']>('all');
  const [selectedOrder, setSelectedOrder] = useState<UserService | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  React.useEffect(() => {
    document.body.classList.add('modal-open');
    return () => {
      document.body.classList.remove('modal-open');
    };
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const ordersResult = getAllServices();
      const usersResult = getAllUsers();
      
      if (ordersResult.data) setOrders(ordersResult.data);
      if (usersResult.data) setUsers(usersResult.data);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getUserById = (userId: string) => {
    return users.find(user => user.id === userId);
  };

  const handleStatusChange = async (orderId: string, status: UserService['status']) => {
    try {
      const result = updateServiceStatus(orderId, status);
      if (result.data) {
        setOrders(orders.map(order => 
          order.id === orderId ? { ...order, status } : order
        ));
      }
    } catch (error) {
      console.error('Error updating order status:', error);
    }
  };

  const getStatusIcon = (status: UserService['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'in_progress':
        return <Clock className="w-5 h-5 text-blue-500" />;
      case 'pending':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: UserService['status']) => {
    const statusMap = {
      'pending': language === 'ar' ? 'في الانتظار' : 'Pending',
      'confirmed': language === 'ar' ? 'مؤكد' : 'Confirmed',
      'in_progress': language === 'ar' ? 'قيد التنفيذ' : 'In Progress',
      'testing': language === 'ar' ? 'قيد الاختبار' : 'Testing',
      'completed': language === 'ar' ? 'مكتمل' : 'Completed',
      'cancelled': language === 'ar' ? 'ملغي' : 'Cancelled',
      'refunded': language === 'ar' ? 'مسترد' : 'Refunded',
      'on_hold': language === 'ar' ? 'معلق' : 'On Hold'
    };
    return statusMap[status] || status;
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.service_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         getUserById(order.user_id)?.full_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="modal-backdrop flex items-center justify-center p-4"
         onClick={(e) => e.target === e.currentTarget && onClose()}>
      <div className="bg-primary rounded-lg max-w-7xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <ShoppingCart className="w-8 h-8 text-secondary" />
            <h2 className="text-2xl font-bold text-white">
              {language === 'ar' ? 'إدارة الطلبات المتقدمة' : 'Advanced Order Management'}
            </h2>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-white">
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {/* Search and Filters */}
          <div className="mb-6 flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder={language === 'ar' ? 'البحث في الطلبات...' : 'Search orders...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-background border border-gray-600 rounded-lg pl-10 rtl:pr-10 rtl:pl-4 pr-4 py-2 text-white focus:border-secondary focus:outline-none"
                />
              </div>
            </div>
            
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none"
            >
              <option value="all">{language === 'ar' ? 'جميع الحالات' : 'All Statuses'}</option>
              <option value="pending">{language === 'ar' ? 'في الانتظار' : 'Pending'}</option>
              <option value="confirmed">{language === 'ar' ? 'مؤكد' : 'Confirmed'}</option>
              <option value="in_progress">{language === 'ar' ? 'قيد التنفيذ' : 'In Progress'}</option>
              <option value="testing">{language === 'ar' ? 'قيد الاختبار' : 'Testing'}</option>
              <option value="completed">{language === 'ar' ? 'مكتمل' : 'Completed'}</option>
              <option value="cancelled">{language === 'ar' ? 'ملغي' : 'Cancelled'}</option>
              <option value="refunded">{language === 'ar' ? 'مسترد' : 'Refunded'}</option>
              <option value="on_hold">{language === 'ar' ? 'معلق' : 'On Hold'}</option>
            </select>
          </div>

          {/* Orders Table */}
          <div className="bg-background rounded-lg border border-gray-700 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-800">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      {language === 'ar' ? 'الطلب' : 'Order'}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      {language === 'ar' ? 'العميل' : 'Customer'}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      {language === 'ar' ? 'الحالة' : 'Status'}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      {language === 'ar' ? 'السعر' : 'Price'}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      {language === 'ar' ? 'التاريخ' : 'Date'}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      {language === 'ar' ? 'الإجراءات' : 'Actions'}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {filteredOrders.map((order) => {
                    const user = getUserById(order.user_id);
                    return (
                      <tr key={order.id} className="hover:bg-gray-800/50">
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-white">{order.service_name}</div>
                            <div className="text-sm text-gray-400">{order.service_type}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-white">{user?.full_name || 'Unknown'}</div>
                          <div className="text-sm text-gray-400">{user?.email}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            {getStatusIcon(order.status)}
                            <span className="text-sm text-white">{getStatusText(order.status)}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <span className="text-sm font-medium text-secondary">${order.price}</span>
                        </td>
                        <td className="px-6 py-4">
                          <span className="text-sm text-gray-300">
                            {new Date(order.purchase_date).toLocaleDateString()}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <button
                              onClick={() => setSelectedOrder(order)}
                              className="text-blue-400 hover:text-blue-300"
                              title={language === 'ar' ? 'عرض التفاصيل' : 'View Details'}
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                            <select
                              value={order.status}
                              onChange={(e) => handleStatusChange(order.id, e.target.value as UserService['status'])}
                              className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-xs text-white"
                            >
                              <option value="pending">{language === 'ar' ? 'في الانتظار' : 'Pending'}</option>
                              <option value="confirmed">{language === 'ar' ? 'مؤكد' : 'Confirmed'}</option>
                              <option value="in_progress">{language === 'ar' ? 'قيد التنفيذ' : 'In Progress'}</option>
                              <option value="testing">{language === 'ar' ? 'قيد الاختبار' : 'Testing'}</option>
                              <option value="completed">{language === 'ar' ? 'مكتمل' : 'Completed'}</option>
                              <option value="cancelled">{language === 'ar' ? 'ملغي' : 'Cancelled'}</option>
                              <option value="refunded">{language === 'ar' ? 'مسترد' : 'Refunded'}</option>
                              <option value="on_hold">{language === 'ar' ? 'معلق' : 'On Hold'}</option>
                            </select>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* Order Details Modal */}
          {selectedOrder && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
              <div className="bg-primary rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
                <div className="flex items-center justify-between p-6 border-b border-gray-700">
                  <h3 className="text-xl font-bold text-white">
                    {language === 'ar' ? 'تفاصيل الطلب' : 'Order Details'}
                  </h3>
                  <button onClick={() => setSelectedOrder(null)} className="text-gray-400 hover:text-white">
                    <X className="w-6 h-6" />
                  </button>
                </div>
                
                <div className="p-6 space-y-4">
                  <div>
                    <label className="text-sm text-gray-400">{language === 'ar' ? 'اسم الخدمة' : 'Service Name'}</label>
                    <p className="text-white font-medium">{selectedOrder.service_name}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm text-gray-400">{language === 'ar' ? 'نوع الخدمة' : 'Service Type'}</label>
                    <p className="text-white">{selectedOrder.service_type}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm text-gray-400">{language === 'ar' ? 'السعر' : 'Price'}</label>
                    <p className="text-secondary font-bold">${selectedOrder.price}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm text-gray-400">{language === 'ar' ? 'تاريخ الطلب' : 'Order Date'}</label>
                    <p className="text-white">{new Date(selectedOrder.purchase_date).toLocaleString()}</p>
                  </div>
                  
                  {selectedOrder.notes && (
                    <div>
                      <label className="text-sm text-gray-400">{language === 'ar' ? 'الملاحظات' : 'Notes'}</label>
                      <div className="bg-background rounded-lg p-3 border border-gray-700">
                        <p className="text-white">{selectedOrder.notes}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdvancedOrderManagement;