import React, { useEffect, useState } from 'react';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';
import { useNotifications } from '../../store/simpleStore';

interface ToastProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  onClose: (id: string) => void;
}

/**
 * Simple Toast component
 */
const Toast: React.FC<ToastProps> = ({
  id,
  type,
  title,
  message,
  duration = 5000,
  onClose
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  // Animation states
  useEffect(() => {
    // Enter animation
    const enterTimer = setTimeout(() => setIsVisible(true), 10);
    
    // Auto-close timer
    let exitTimer: NodeJS.Timeout;
    if (duration > 0) {
      exitTimer = setTimeout(() => {
        handleClose();
      }, duration);
    }

    return () => {
      clearTimeout(enterTimer);
      if (exitTimer) clearTimeout(exitTimer);
    };
  }, [duration]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      onClose(id);
    }, 300);
  };

  // Icon mapping
  const icons = {
    success: CheckCircle,
    error: AlertCircle,
    warning: AlertTriangle,
    info: Info
  };

  // Color mapping
  const colorClasses = {
    success: {
      bg: 'bg-green-500/10 border-green-500/30',
      icon: 'text-green-500',
      title: 'text-green-400',
      text: 'text-green-300'
    },
    error: {
      bg: 'bg-red-500/10 border-red-500/30',
      icon: 'text-red-500',
      title: 'text-red-400',
      text: 'text-red-300'
    },
    warning: {
      bg: 'bg-yellow-500/10 border-yellow-500/30',
      icon: 'text-yellow-500',
      title: 'text-yellow-400',
      text: 'text-yellow-300'
    },
    info: {
      bg: 'bg-blue-500/10 border-blue-500/30',
      icon: 'text-blue-500',
      title: 'text-blue-400',
      text: 'text-blue-300'
    }
  };

  const Icon = icons[type];
  const colors = colorClasses[type];

  const isRTL = document.documentElement.dir === 'rtl';

  return (
    <div
      className={`
        relative max-w-sm w-full bg-gray-800 backdrop-blur-sm border rounded-lg shadow-lg p-4 mb-3
        transition-all duration-300 ease-in-out transform
        ${colors.bg}
        ${isVisible && !isExiting
          ? 'translate-x-0 opacity-100 scale-100'
          : `${isRTL ? 'translate-x-full' : '-translate-x-full'} opacity-0 scale-95`
        }
      `}
      role="alert"
      aria-live="polite"
      dir={isRTL ? 'rtl' : 'ltr'}
    >
      <div className={`flex items-start ${isRTL ? 'space-x-reverse' : ''} space-x-3`}>
        {/* Icon */}
        <div className={`flex-shrink-0 ${colors.icon}`}>
          <Icon className="w-5 h-5" />
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0" style={{ direction: 'inherit' }}>
          <h4 className={`text-sm font-medium ${colors.title}`} style={{ textAlign: isRTL ? 'right' : 'left' }}>
            {title}
          </h4>
          <p className={`text-sm ${colors.text} mt-1`} style={{ textAlign: isRTL ? 'right' : 'left' }}>
            {message}
          </p>
        </div>

        {/* Close button */}
        <button
          onClick={handleClose}
          className="flex-shrink-0 text-gray-400 hover:text-gray-200 transition-colors"
          aria-label="Close notification"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

/**
 * Simple Toast Container component
 */
const SimpleToastContainer: React.FC = () => {
  const { notifications, removeNotification } = useNotifications();
  const isRTL = document.documentElement.dir === 'rtl';

  if (notifications.length === 0) return null;

  return (
    <div
      className={`fixed top-4 z-50 flex flex-col ${isRTL ? 'left-4' : 'right-4'}`}
      aria-live="polite"
      aria-label="Notifications"
    >
      {notifications.map((notification) => (
        <Toast
          key={notification.id}
          id={notification.id}
          type={notification.type}
          title={notification.title}
          message={notification.message}
          duration={notification.duration}
          onClose={removeNotification}
        />
      ))}
    </div>
  );
};

export default SimpleToastContainer;
