const axios = require('axios');

async function testAdminPanel() {
  try {
    console.log('👑 Testing Admin Panel Functionality...\n');
    
    // Login as admin
    console.log('🔐 Logging in as admin...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.data.tokens.accessToken;
    const headers = { Authorization: `Bearer ${token}` };
    console.log('✅ Admin login successful');
    
    // Test admin systems endpoint
    console.log('\n🖥️ Testing Admin Systems Management...');
    const adminSystemsResponse = await axios.get('http://localhost:3001/api/systems/admin', { headers });
    console.log(`   ✅ Admin systems endpoint: ${adminSystemsResponse.data.length} systems`);
    
    if (adminSystemsResponse.data.length > 0) {
      const firstSystem = adminSystemsResponse.data[0];
      console.log('   📊 First system has fields:', Object.keys(firstSystem));
      console.log('   🔧 tech_specs_ar:', Array.isArray(firstSystem.tech_specs_ar) ? `Array[${firstSystem.tech_specs_ar.length}]` : 'Not Array');
      console.log('   🎯 features_ar:', Array.isArray(firstSystem.features_ar) ? `Array[${firstSystem.features_ar.length}]` : 'Not Array');
    }
    
    // Test admin services endpoint
    console.log('\n🛠️ Testing Admin Services Management...');
    const adminServicesResponse = await axios.get('http://localhost:3001/api/services/admin/technical', { headers });
    console.log(`   ✅ Admin services endpoint: ${adminServicesResponse.data.length} services`);
    
    if (adminServicesResponse.data.length > 0) {
      const firstService = adminServicesResponse.data[0];
      console.log('   📊 First service has fields:', Object.keys(firstService));
      console.log('   🔧 tech_specs_ar:', Array.isArray(firstService.tech_specs_ar) ? `Array[${firstService.tech_specs_ar.length}]` : 'Not Array');
      console.log('   💎 is_premium_addon:', firstService.is_premium_addon);
      console.log('   💰 premium_price:', firstService.premium_price);
      console.log('   📅 subscription_type:', firstService.subscription_type);
    }
    
    // Test dashboard endpoint
    console.log('\n📊 Testing Admin Dashboard...');
    const dashboardResponse = await axios.get('http://localhost:3001/api/admin/dashboard', { headers });
    console.log('   ✅ Dashboard endpoint working');
    console.log('   📈 Dashboard data keys:', Object.keys(dashboardResponse.data));
    
    // Test users management
    console.log('\n👥 Testing Users Management...');
    try {
      const usersResponse = await axios.get('http://localhost:3001/api/admin/users', { headers });
      console.log('   ✅ Users management working');
      console.log('   👤 Users data structure:', Object.keys(usersResponse.data));
    } catch (userError) {
      console.log('   ⚠️ Users management issue:', userError.response?.status || userError.message);
    }
    
    // Test orders management
    console.log('\n📦 Testing Orders Management...');
    try {
      const ordersResponse = await axios.get('http://localhost:3001/api/orders', { headers });
      console.log('   ✅ Orders management working');
      console.log('   📋 Orders data structure:', Object.keys(ordersResponse.data));
    } catch (orderError) {
      console.log('   ⚠️ Orders management issue:', orderError.response?.status || orderError.message);
    }
    
    console.log('\n🎉 Admin panel functionality test completed!');
    
  } catch (error) {
    console.error('❌ Admin panel test failed:', error.response?.data || error.message);
  }
}

testAdminPanel();
