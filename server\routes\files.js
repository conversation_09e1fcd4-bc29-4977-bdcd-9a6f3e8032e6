/**
 * File Management Routes
 * 
 * Handles file operations:
 * - File uploads (images, videos, documents)
 * - File downloads and serving
 * - File management and organization
 * - Image processing and optimization
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { executeQuery, generateUUID } = require('../config/database');
const { verifyToken, requireAdmin, optionalAuth } = require('../middleware/auth');
const { 
  asyncHandler, 
  validationError, 
  notFoundError,
  fileUploadError 
} = require('../middleware/errorHandler');
const { logUserAction } = require('../middleware/logger');

const router = express.Router();

// Ensure upload directories exist
const uploadDir = process.env.UPLOAD_DIR || 'uploads';
const subdirs = ['images', 'videos', 'documents', 'temp'];

async function ensureUploadDirs() {
  try {
    await fs.mkdir(uploadDir, { recursive: true });
    for (const subdir of subdirs) {
      await fs.mkdir(path.join(uploadDir, subdir), { recursive: true });
    }
  } catch (error) {
    console.error('Error creating upload directories:', error);
  }
}

ensureUploadDirs();

// File type detection
function getFileType(mimetype) {
  if (mimetype.startsWith('image/')) return 'image';
  if (mimetype.startsWith('video/')) return 'video';
  if (mimetype.includes('pdf') || mimetype.includes('document') || mimetype.includes('text')) return 'document';
  if (mimetype.includes('zip') || mimetype.includes('rar') || mimetype.includes('tar')) return 'archive';
  return 'other';
}

// Get file subdirectory based on type
function getFileSubdir(fileType) {
  switch (fileType) {
    case 'image': return 'images';
    case 'video': return 'videos';
    case 'document': return 'documents';
    default: return 'temp';
  }
}

// Multer configuration for different file types
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const fileType = getFileType(file.mimetype);
    const subdir = getFileSubdir(fileType);
    cb(null, path.join(uploadDir, subdir));
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const baseName = path.basename(file.originalname, ext).replace(/[^a-zA-Z0-9]/g, '_');
    cb(null, `${baseName}_${uniqueSuffix}${ext}`);
  }
});

const fileFilter = (req, file, cb) => {
  const allowedTypes = process.env.ALLOWED_FILE_TYPES 
    ? process.env.ALLOWED_FILE_TYPES.split(',')
    : ['jpg', 'jpeg', 'png', 'gif', 'mp4', 'avi', 'mov', 'pdf', 'doc', 'docx', 'zip', 'rar'];
  
  const ext = path.extname(file.originalname).toLowerCase().slice(1);
  
  if (allowedTypes.includes(ext)) {
    cb(null, true);
  } else {
    cb(new Error(`File type .${ext} is not allowed. Allowed types: ${allowedTypes.join(', ')}`), false);
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB default
    files: 5 // Maximum 5 files per request
  },
  fileFilter: fileFilter
});

/**
 * @route   POST /api/files/upload
 * @desc    Upload single file
 * @access  Private
 */
router.post('/upload', verifyToken, upload.single('file'), asyncHandler(async (req, res) => {
  if (!req.file) {
    throw fileUploadError('No file uploaded');
  }
  
  const { entity_type, entity_id, is_public = false } = req.body;
  const file = req.file;
  const fileType = getFileType(file.mimetype);
  const fileId = generateUUID();
  
  // Save file record to database
  await executeQuery(`
    INSERT INTO file_uploads (
      id, user_id, original_name, file_name, file_path, file_size, 
      mime_type, file_type, entity_type, entity_id, is_public, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
  `, [
    fileId,
    req.user.id,
    file.originalname,
    file.filename,
    file.path,
    file.size,
    file.mimetype,
    fileType,
    entity_type || null,
    entity_id || null,
    is_public === 'true'
  ]);
  
  // Log file upload
  await logUserAction('file_uploaded', 'file', fileId, {
    originalName: file.originalname,
    fileName: file.filename,
    fileSize: file.size,
    fileType,
    entityType: entity_type,
    entityId: entity_id
  }, req);
  
  // Generate file URL
  const fileUrl = `/uploads/${getFileSubdir(fileType)}/${file.filename}`;
  
  res.json({
    success: true,
    message: 'File uploaded successfully',
    data: {
      file: {
        id: fileId,
        originalName: file.originalname,
        fileName: file.filename,
        fileSize: file.size,
        fileType,
        mimeType: file.mimetype,
        url: fileUrl,
        isPublic: is_public === 'true'
      }
    }
  });
}));

/**
 * @route   POST /api/files/upload-multiple
 * @desc    Upload multiple files
 * @access  Private
 */
router.post('/upload-multiple', verifyToken, upload.array('files', 5), asyncHandler(async (req, res) => {
  if (!req.files || req.files.length === 0) {
    throw fileUploadError('No files uploaded');
  }
  
  const { entity_type, entity_id, is_public = false } = req.body;
  const uploadedFiles = [];
  
  // Process each file
  for (const file of req.files) {
    const fileType = getFileType(file.mimetype);
    const fileId = generateUUID();
    
    // Save file record to database
    await executeQuery(`
      INSERT INTO file_uploads (
        id, user_id, original_name, file_name, file_path, file_size, 
        mime_type, file_type, entity_type, entity_id, is_public, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      fileId,
      req.user.id,
      file.originalname,
      file.filename,
      file.path,
      file.size,
      file.mimetype,
      fileType,
      entity_type || null,
      entity_id || null,
      is_public === 'true'
    ]);
    
    // Generate file URL
    const fileUrl = `/uploads/${getFileSubdir(fileType)}/${file.filename}`;
    
    uploadedFiles.push({
      id: fileId,
      originalName: file.originalname,
      fileName: file.filename,
      fileSize: file.size,
      fileType,
      mimeType: file.mimetype,
      url: fileUrl,
      isPublic: is_public === 'true'
    });
  }
  
  // Log multiple file upload
  await logUserAction('multiple_files_uploaded', 'file', 'multiple', {
    fileCount: req.files.length,
    totalSize: req.files.reduce((sum, file) => sum + file.size, 0),
    entityType: entity_type,
    entityId: entity_id
  }, req);
  
  res.json({
    success: true,
    message: `${req.files.length} files uploaded successfully`,
    data: {
      files: uploadedFiles
    }
  });
}));

/**
 * @route   GET /api/files
 * @desc    Get user's uploaded files
 * @access  Private
 */
router.get('/', verifyToken, asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    file_type, 
    entity_type,
    search 
  } = req.query;
  
  const offset = (page - 1) * limit;
  const userId = req.user.role === 'admin' ? null : req.user.id;
  
  // Build query conditions
  let whereConditions = [];
  let queryParams = [];
  
  if (userId) {
    whereConditions.push('user_id = ?');
    queryParams.push(userId);
  }
  
  if (file_type) {
    whereConditions.push('file_type = ?');
    queryParams.push(file_type);
  }
  
  if (entity_type) {
    whereConditions.push('entity_type = ?');
    queryParams.push(entity_type);
  }
  
  if (search) {
    whereConditions.push('(original_name LIKE ? OR file_name LIKE ?)');
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm);
  }
  
  const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
  
  // Get files
  const { rows: files } = await executeQuery(`
    SELECT 
      f.*,
      u.username
    FROM file_uploads f
    LEFT JOIN users u ON f.user_id = u.id
    ${whereClause}
    ORDER BY f.created_at DESC
    LIMIT ? OFFSET ?
  `, [...queryParams, parseInt(limit), offset]);
  
  // Add file URLs
  files.forEach(file => {
    const subdir = getFileSubdir(file.file_type);
    file.url = `/uploads/${subdir}/${file.file_name}`;
  });
  
  // Get total count
  const { rows: countResult } = await executeQuery(`
    SELECT COUNT(*) as total
    FROM file_uploads f
    ${whereClause}
  `, queryParams);
  
  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.json({
    success: true,
    data: {
      files,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @route   GET /api/files/:id
 * @desc    Get file details
 * @access  Private
 */
router.get('/:id', verifyToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // Get file details
  const { rows: files } = await executeQuery(`
    SELECT 
      f.*,
      u.username
    FROM file_uploads f
    LEFT JOIN users u ON f.user_id = u.id
    WHERE f.id = ?
  `, [id]);
  
  if (files.length === 0) {
    throw notFoundError('File not found');
  }
  
  const file = files[0];
  
  // Check access permissions
  if (req.user.role !== 'admin' && file.user_id !== req.user.id && !file.is_public) {
    throw forbiddenError('Access denied');
  }
  
  // Add file URL
  const subdir = getFileSubdir(file.file_type);
  file.url = `/uploads/${subdir}/${file.file_name}`;
  
  res.json({
    success: true,
    data: {
      file
    }
  });
}));

/**
 * @route   DELETE /api/files/:id
 * @desc    Delete file
 * @access  Private (owner or admin)
 */
router.delete('/:id', verifyToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // Get file details
  const { rows: files } = await executeQuery(
    'SELECT * FROM file_uploads WHERE id = ?',
    [id]
  );
  
  if (files.length === 0) {
    throw notFoundError('File not found');
  }
  
  const file = files[0];
  
  // Check permissions
  if (req.user.role !== 'admin' && file.user_id !== req.user.id) {
    throw forbiddenError('Access denied');
  }
  
  try {
    // Delete physical file
    await fs.unlink(file.file_path);
  } catch (error) {
    console.warn('Failed to delete physical file:', error.message);
  }
  
  // Delete database record
  await executeQuery('DELETE FROM file_uploads WHERE id = ?', [id]);
  
  // Log file deletion
  await logUserAction('file_deleted', 'file', id, {
    originalName: file.original_name,
    fileName: file.file_name,
    fileSize: file.file_size
  }, req);
  
  res.json({
    success: true,
    message: 'File deleted successfully'
  });
}));

/**
 * @route   GET /api/files/download/:id
 * @desc    Download file
 * @access  Private
 */
router.get('/download/:id', verifyToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // Get file details
  const { rows: files } = await executeQuery(
    'SELECT * FROM file_uploads WHERE id = ?',
    [id]
  );
  
  if (files.length === 0) {
    throw notFoundError('File not found');
  }
  
  const file = files[0];
  
  // Check access permissions
  if (req.user.role !== 'admin' && file.user_id !== req.user.id && !file.is_public) {
    throw forbiddenError('Access denied');
  }
  
  // Check if file exists
  try {
    await fs.access(file.file_path);
  } catch (error) {
    throw notFoundError('Physical file not found');
  }
  
  // Update download count
  await executeQuery(
    'UPDATE file_uploads SET download_count = download_count + 1 WHERE id = ?',
    [id]
  );
  
  // Log download
  await logUserAction('file_downloaded', 'file', id, {
    originalName: file.original_name,
    fileName: file.file_name
  }, req);
  
  // Send file
  res.download(file.file_path, file.original_name);
}));

module.exports = router;
