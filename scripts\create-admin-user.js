/**
 * Create Admin User Script
 * 
 * This script creates an admin user for testing admin endpoints
 */

const bcrypt = require('bcrypt');
const { executeQuery, generateUUID } = require('../server/config/database');
require('dotenv').config();

async function createAdminUser() {
  console.log('👤 Creating admin user...\n');
  
  try {
    const adminData = {
      id: generateUUID(),
      email: '<EMAIL>',
      password: 'admin123456',
      name_ar: 'المدير العام',
      name_en: 'System Administrator',
      role: 'admin',
      status: 'active'
    };
    
    // Check if admin already exists
    console.log('1️⃣ Checking for existing admin user...');
    const { rows: existingUsers } = await executeQuery(
      'SELECT id, email, role FROM users WHERE email = ? OR role = ?',
      [adminData.email, 'admin']
    );
    
    if (existingUsers.length > 0) {
      console.log('✅ Admin user already exists:');
      existingUsers.forEach(user => {
        console.log(`   📧 ${user.email} (${user.role})`);
      });
      
      // Update password for existing admin
      console.log('\n2️⃣ Updating admin password...');
      const hashedPassword = await bcrypt.hash(adminData.password, 10);
      
      await executeQuery(
        'UPDATE users SET password = ?, updated_at = NOW() WHERE role = ?',
        [hashedPassword, 'admin']
      );
      
      console.log('✅ Admin password updated');
      console.log(`📧 Email: ${adminData.email}`);
      console.log(`🔑 Password: ${adminData.password}`);
      
      return;
    }
    
    // Create new admin user
    console.log('2️⃣ Creating new admin user...');
    const hashedPassword = await bcrypt.hash(adminData.password, 10);
    
    await executeQuery(`
      INSERT INTO users (
        id, email, password, name_ar, name_en, role, status, 
        email_verified, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      adminData.id,
      adminData.email,
      hashedPassword,
      adminData.name_ar,
      adminData.name_en,
      adminData.role,
      adminData.status,
      true
    ]);
    
    console.log('✅ Admin user created successfully!');
    console.log(`📧 Email: ${adminData.email}`);
    console.log(`🔑 Password: ${adminData.password}`);
    console.log(`👤 Name: ${adminData.name_en}`);
    console.log(`🎭 Role: ${adminData.role}`);
    
    // Verify creation
    console.log('\n3️⃣ Verifying admin user...');
    const { rows: newUser } = await executeQuery(
      'SELECT id, email, name_en, role, status, created_at FROM users WHERE email = ?',
      [adminData.email]
    );
    
    if (newUser.length > 0) {
      console.log('✅ Verification successful:');
      console.log('   ID:', newUser[0].id);
      console.log('   Email:', newUser[0].email);
      console.log('   Name:', newUser[0].name_en);
      console.log('   Role:', newUser[0].role);
      console.log('   Status:', newUser[0].status);
      console.log('   Created:', newUser[0].created_at);
    }
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  createAdminUser()
    .then(() => {
      console.log('\n🎉 Admin user setup complete!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Setup failed:', error.message);
      process.exit(1);
    });
}

module.exports = { createAdminUser };