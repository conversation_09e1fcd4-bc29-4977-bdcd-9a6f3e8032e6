@echo off
echo Setting up Khanfashariya Development Environment...

echo Step 1: Installing dependencies...
npm install

echo Step 2: Setting up database...
mysql -u root -p123456 -e "CREATE DATABASE IF NOT EXISTS khanfashariya_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -u root -p123456 -e "CREATE USER IF NOT EXISTS 'kfs_user'@'localhost' IDENTIFIED BY 'kfs_2024_secure';"
mysql -u root -p123456 -e "GRANT ALL PRIVILEGES ON khanfashariya_db.* TO 'kfs_user'@'localhost';"
mysql -u root -p123456 -e "FLUSH PRIVILEGES;"

echo Step 3: Creating tables and migrating data...
node scripts/setup-database.js
node scripts/migrate-data.js

echo Step 4: Starting development servers...
npm run dev:full

echo Setup complete! Visit http://localhost:5173
pause