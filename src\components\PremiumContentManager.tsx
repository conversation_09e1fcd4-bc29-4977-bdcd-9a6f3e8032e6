import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useNotification } from '../hooks/useNotification';
import {
  getPremiumContent,
  createPremiumContent,
  updatePremiumContent,
  deletePremiumContent,
} from '../lib/apiServices';
import {
  PremiumPackage
} from '../lib/database';
import { Crown, Star, X, Plus, Edit3, Save, Trash2, Package } from 'lucide-react';

interface PremiumContentManagerProps {
  onClose: () => void;
}

const PremiumContentManager: React.FC<PremiumContentManagerProps> = ({ onClose }) => {
  const { t, language } = useTranslation();
  const { showNotification } = useNotification();
  const [packages, setPackages] = useState<PremiumPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingPackage, setEditingPackage] = useState<Partial<PremiumPackage> | null>(null);

  useEffect(() => {
    document.body.classList.add('modal-open');
    loadPackages();
    return () => {
      document.body.classList.remove('modal-open');
    };
  }, []);

  const loadPackages = async () => {
    setLoading(true);
    try {
      const { data, error } = await getPremiumContent();
      if (error) {
        console.error("Error loading premium content:", error);
        showNotification({ type: 'error', message: 'Failed to load premium content.' });
      } else {
        setPackages(data || []);
      }
    } catch (error) {
      console.error("Error loading premium content:", error);
      showNotification({ type: 'error', message: 'Failed to load premium content.' });
    } finally {
      setLoading(false);
    }
  };

  const handleAddPackage = () => {
    setEditingPackage({
      name: '',
      description: '',
      price: 0,
      original_price: 0,
      discount_percentage: 0,
      features: [],
      systems: [],
      status: 'active'
    });
    setShowForm(true);
  };

  const handleEditPackage = (pkg: PremiumPackage) => {
    setEditingPackage(pkg);
    setShowForm(true);
  };

  const handleSavePackage = async () => {
    if (!editingPackage) return;

    try {
      if (editingPackage.id) {
        // Update existing package
        await updatePremiumContent(editingPackage.id, editingPackage);
      } else {
        // Create new package
        await createPremiumContent(editingPackage as Omit<PremiumPackage, 'id' | 'created_at' | 'updated_at'>);
      }
      setShowForm(false);
      setEditingPackage(null);
      await loadPackages();
      showNotification({ type: 'success', message: 'Premium content saved successfully.' });
    } catch (error) {
      console.error("Error saving premium content:", error);
      showNotification({ type: 'error', message: 'Failed to save premium content.' });
    }
  };

  const handleDeletePackage = (id: string) => {
    showNotification({
      type: 'confirm',
      message: t('notifications.deletePackageConfirm'),
      onConfirm: async () => {
        try {
          await deletePremiumContent(id);
          await loadPackages();
          showNotification({ type: 'success', message: 'Premium content deleted successfully.' });
        } catch (error) {
          console.error("Error deleting premium content:", error);
          showNotification({ type: 'error', message: 'Failed to delete premium content.' });
        }
      }
    });
  };

  if (loading) {
    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="w-16 h-16 border-4 border-secondary border-t-transparent rounded-full animate-spin"></div>
        </div>
    );
  }

  return (
    <div className="modal-backdrop flex items-center justify-center p-4"
         onClick={(e) => e.target === e.currentTarget && onClose()}>
      <div className="bg-primary rounded-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <Crown className="w-8 h-8 text-yellow-400" />
              <h2 className="text-2xl font-bold text-white">
                {language === 'ar' ? 'إدارة حزم البريميوم' : 'Premium Package Manager'}
              </h2>
            </div>
            <button onClick={onClose} className="text-gray-400 hover:text-white">
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Add Package Button */}
          <div className="mb-6">
            <button
              onClick={handleAddPackage}
              className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-300"
            >
              <Plus className="w-5 h-5" />
              <span>{language === 'ar' ? 'إضافة حزمة جديدة' : 'Add New Package'}</span>
            </button>
          </div>

          {/* Packages Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {packages.map((pkg) => (
              <div key={pkg.id} className="bg-background rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">{pkg.name}</h3>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <button
                      onClick={() => handleEditPackage(pkg)}
                      className="text-yellow-400 hover:text-yellow-300"
                    >
                      <Edit3 className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeletePackage(pkg.id)}
                      className="text-red-400 hover:text-red-300"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <p className="text-gray-300 mb-4">{pkg.description}</p>
                
                <div className="mb-4">
                  <span className="text-2xl font-bold text-yellow-400">${pkg.price}</span>
                  {pkg.original_price && pkg.original_price > pkg.price && (
                     <span className="text-gray-400 line-through text-sm ml-2">${pkg.original_price}</span>
                  )}
                </div>

                <div className="space-y-2">
                  {pkg.features.slice(0, 3).map((feature, index) => (
                    <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Star className="w-4 h-4 text-yellow-400" />
                      <span className="text-sm text-gray-300">{feature}</span>
                    </div>
                  ))}
                  {pkg.features.length > 3 && (
                    <p className="text-sm text-gray-400">
                      {language === 'ar' ? `+${pkg.features.length - 3} ميزة أخرى` : `+${pkg.features.length - 3} more features`}
                    </p>
                  )}
                </div>

                <div className="mt-4 pt-4 border-t border-gray-700">
                  <span className={`px-2 py-1 rounded text-xs ${
                    pkg.status === 'active'
                      ? 'bg-green-500/20 text-green-400' 
                      : 'bg-red-500/20 text-red-400'
                  }`}>
                    {pkg.status === 'active'
                      ? (language === 'ar' ? 'نشط' : 'Active')
                      : (language === 'ar' ? 'غير نشط' : 'Inactive')
                    }
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Package Form Modal */}
          {showForm && editingPackage && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
              <div className="bg-primary rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold text-white">
                      {editingPackage.id 
                        ? (language === 'ar' ? 'تحرير الحزمة' : 'Edit Package')
                        : (language === 'ar' ? 'إضافة حزمة جديدة' : 'Add New Package')
                      }
                    </h3>
                    <button
                      onClick={() => setShowForm(false)}
                      className="text-gray-400 hover:text-white"
                    >
                      <X className="w-6 h-6" />
                    </button>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-white font-medium mb-2">
                        {language === 'ar' ? 'اسم الحزمة' : 'Package Name'}
                      </label>
                      <input
                        type="text"
                        value={editingPackage.name}
                        onChange={(e) => setEditingPackage({...editingPackage, name: e.target.value})}
                        className="w-full bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none"
                      />
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        {language === 'ar' ? 'الوصف' : 'Description'}
                      </label>
                      <textarea
                        value={editingPackage.description}
                        onChange={(e) => setEditingPackage({...editingPackage, description: e.target.value})}
                        rows={3}
                        className="w-full bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none resize-none"
                      />
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        {language === 'ar' ? 'السعر ($)' : 'Price ($)'}
                      </label>
                      <input
                        type="number"
                        value={editingPackage.price}
                        onChange={(e) => setEditingPackage({...editingPackage, price: Number(e.target.value)})}
                        className="w-full bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none"
                      />
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        {language === 'ar' ? 'الميزات (واحدة في كل سطر)' : 'Features (one per line)'}
                      </label>
                      <textarea
                        value={editingPackage.features?.join('\n')}
                        onChange={(e) => setEditingPackage({...editingPackage, features: e.target.value.split('\n').filter(f => f.trim())})}
                        rows={5}
                        className="w-full bg-background border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-secondary focus:outline-none resize-none"
                      />
                    </div>

                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <input
                        type="checkbox"
                        id="isActive"
                        checked={editingPackage.status === 'active'}
                        onChange={(e) => setEditingPackage({...editingPackage, status: e.target.checked ? 'active' : 'inactive'})}
                        className="text-secondary focus:ring-secondary"
                      />
                      <label htmlFor="isActive" className="text-white">
                        {language === 'ar' ? 'نشط' : 'Active'}
                      </label>
                    </div>
                  </div>

                  <div className="flex items-center justify-end space-x-4 rtl:space-x-reverse mt-6 pt-6 border-t border-gray-700">
                    <button
                      onClick={() => setShowForm(false)}
                      className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-all duration-300"
                    >
                      {language === 'ar' ? 'إلغاء' : 'Cancel'}
                    </button>
                    <button
                      onClick={handleSavePackage}
                      className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-300"
                    >
                      <Save className="w-4 h-4" />
                      <span>{language === 'ar' ? 'حفظ' : 'Save'}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PremiumContentManager;