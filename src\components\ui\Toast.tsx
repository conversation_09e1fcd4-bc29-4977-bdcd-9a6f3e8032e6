import React, { useEffect, useState } from 'react';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';
import { useNotifications } from '../../store/useAppStore';
import { useTranslation } from '../../hooks/useTranslation';

interface ToastProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actionUrl?: string;
  actionLabel?: string;
  onClose: (id: string) => void;
}

/**
 * Individual Toast component
 */
const Toast: React.FC<ToastProps> = ({
  id,
  type,
  title,
  message,
  duration = 5000,
  actionUrl,
  actionLabel,
  onClose
}) => {
  const { language } = useTranslation();
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  const isRTL = language === 'ar';

  // Animation states
  useEffect(() => {
    // Enter animation
    const enterTimer = setTimeout(() => setIsVisible(true), 10);
    
    // Auto-close timer
    let exitTimer: NodeJS.Timeout;
    if (duration > 0) {
      exitTimer = setTimeout(() => {
        handleClose();
      }, duration);
    }

    return () => {
      clearTimeout(enterTimer);
      if (exitTimer) clearTimeout(exitTimer);
    };
  }, [duration]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      onClose(id);
    }, 300);
  };

  const handleAction = () => {
    if (actionUrl) {
      window.open(actionUrl, '_blank');
    }
    handleClose();
  };

  // Icon mapping
  const icons = {
    success: CheckCircle,
    error: AlertCircle,
    warning: AlertTriangle,
    info: Info
  };

  // Color mapping
  const colorClasses = {
    success: {
      bg: 'bg-success-500/10 border-success-500/30',
      icon: 'text-success-500',
      title: 'text-success-400',
      text: 'text-success-300'
    },
    error: {
      bg: 'bg-error-500/10 border-error-500/30',
      icon: 'text-error-500',
      title: 'text-error-400',
      text: 'text-error-300'
    },
    warning: {
      bg: 'bg-warning-500/10 border-warning-500/30',
      icon: 'text-warning-500',
      title: 'text-warning-400',
      text: 'text-warning-300'
    },
    info: {
      bg: 'bg-info-500/10 border-info-500/30',
      icon: 'text-info-500',
      title: 'text-info-400',
      text: 'text-info-300'
    }
  };

  const Icon = icons[type];
  const colors = colorClasses[type];

  return (
    <div
      className={`
        relative max-w-sm w-full bg-primary-500/95 backdrop-blur-sm border rounded-lg shadow-lg p-4 mb-3
        transition-all duration-300 ease-in-out transform
        ${colors.bg}
        ${isVisible && !isExiting 
          ? 'translate-x-0 opacity-100 scale-100' 
          : isRTL 
            ? 'translate-x-full opacity-0 scale-95'
            : '-translate-x-full opacity-0 scale-95'
        }
      `}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-start space-x-3 rtl:space-x-reverse">
        {/* Icon */}
        <div className={`flex-shrink-0 ${colors.icon}`}>
          <Icon className="w-5 h-5" />
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <h4 className={`text-sm font-medium ${colors.title} ${isRTL ? 'text-right' : 'text-left'}`}>
            {title}
          </h4>
          <p className={`text-sm ${colors.text} mt-1 ${isRTL ? 'text-right' : 'text-left'}`}>
            {message}
          </p>

          {/* Action button */}
          {actionUrl && actionLabel && (
            <button
              onClick={handleAction}
              className={`text-sm font-medium ${colors.icon} hover:underline mt-2 ${isRTL ? 'text-right' : 'text-left'}`}
            >
              {actionLabel}
            </button>
          )}
        </div>

        {/* Close button */}
        <button
          onClick={handleClose}
          className="flex-shrink-0 text-text-tertiary hover:text-text-secondary transition-colors"
          aria-label="Close notification"
        >
          <X className="w-4 h-4" />
        </button>
      </div>

      {/* Progress bar for auto-close */}
      {duration > 0 && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200/20 rounded-b-lg overflow-hidden">
          <div
            className={`h-full ${colors.icon.replace('text-', 'bg-')} transition-all ease-linear`}
            style={{
              width: '100%',
              animation: `shrink ${duration}ms linear forwards`
            }}
          />
        </div>
      )}

      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </div>
  );
};

/**
 * Toast Container component that manages all toasts
 */
const ToastContainer: React.FC = () => {
  const { notifications, removeNotification } = useNotifications();
  const { language } = useTranslation();

  const isRTL = language === 'ar';

  // Only show toast notifications (not persistent ones)
  const toastNotifications = notifications.filter(n => n.duration !== 0);

  if (toastNotifications.length === 0) return null;

  return (
    <div
      className={`fixed top-4 z-50 flex flex-col ${
        isRTL ? 'left-4' : 'right-4'
      }`}
      aria-live="polite"
      aria-label="Notifications"
    >
      {toastNotifications.map((notification) => (
        <Toast
          key={notification.id}
          id={notification.id}
          type={notification.type}
          title={notification.title}
          message={notification.message}
          duration={notification.duration}
          actionUrl={notification.actionUrl}
          actionLabel={notification.actionLabel}
          onClose={removeNotification}
        />
      ))}
    </div>
  );
};

export default ToastContainer;
