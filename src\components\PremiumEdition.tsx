import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuth } from '../hooks/useAuth';
import { useNotification } from '../hooks/useNotification';
import {
  getActivePremiumEdition,
  getPremiumAddons,
  createOrder
} from '../lib/apiServices';
import { PremiumContent, SystemService, TechnicalService } from '../lib/database';

import {
  Crown,
  X,
  Shield,
  Zap,
  Code,
  Database,
  Server,
  Package,
  Play,
  Star,
  DollarSign,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Settings,
  RefreshCw,
  Eye,
  Image as ImageIcon
} from 'lucide-react';

import VideoModal from './ui/VideoModal';
import ImageGalleryModal from './ui/ImageGalleryModal';
import '../styles/premium-responsive.css';

interface PremiumEditionProps {
  onClose: () => void;
}

const PremiumEdition: React.FC<PremiumEditionProps> = ({ onClose }) => {
  const { t, language } = useTranslation();
  const { userProfile, isAuthenticated } = useAuth();
  const { showNotification } = useNotification();
  // UI State
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);
  const [showImageGallery, setShowImageGallery] = useState(false);
  const [galleryStartIndex, setGalleryStartIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState<'overview' | 'systems' | 'services' | 'summary'>('overview');

  // Data State
  const [premiumEdition, setPremiumEdition] = useState<PremiumContent | null>(null);
  const [availableSystems, setAvailableSystems] = useState<SystemService[]>([]);
  const [availableServices, setAvailableServices] = useState<TechnicalService[]>([]);
  const [selectedSystems, setSelectedSystems] = useState<string[]>([]);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);

  // Pricing State
  const [totalPrice, setTotalPrice] = useState(0);
  const [systemsPrice, setSystemsPrice] = useState(0);
  const [servicesPrice, setServicesPrice] = useState(0);

  useEffect(() => {
    loadPremiumData();
  }, []);

  useEffect(() => {
    if (premiumEdition) {
      calculateTotalPrice();
    }
  }, [premiumEdition, selectedSystems, selectedServices, availableSystems, availableServices]);

  const loadPremiumData = async () => {
    try {
      setLoading(true);

      // Load active premium edition
      const { data: activePremium, error: premiumError } = await getActivePremiumEdition();
      if (premiumError) {
        console.error('Error loading premium edition:', premiumError);
        showNotification({
          type: 'error',
          message: language === 'ar' ? 'خطأ في تحميل النسخة المميزة' : 'Error loading premium edition'
        });
        return;
      }

      if (activePremium) {
        setPremiumEdition(activePremium);
        if (import.meta.env.DEV) {
          console.log('Premium Edition loaded:', activePremium);
        }
      } else {
        showNotification({
          type: 'warning',
          message: language === 'ar' ? 'لا توجد نسخة مميزة نشطة حالياً' : 'No active premium edition available'
        });
        return;
      }

      // Load available systems and services with premium pricing
      const { data: addons, error: addonsError } = await getPremiumAddons();
      if (addonsError) {
        console.error('Error loading premium add-ons:', addonsError);
        showNotification({
          type: 'error',
          message: language === 'ar' ? 'خطأ في تحميل الإضافات' : 'Error loading add-ons'
        });
        return;
      }

      if (addons) {
        setAvailableSystems(addons.systems || []);
        setAvailableServices(addons.services || []);
        if (import.meta.env.DEV) {
          console.log('Systems loaded:', addons.systems);
          console.log('Services loaded:', addons.services);
        }
      }

    } catch (error) {
      console.error('Error loading premium data:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'خطأ في تحميل البيانات' : 'Error loading data'
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateTotalPrice = () => {
    if (!premiumEdition) return 0;

    let basePrice = Number(premiumEdition.price) || 0;
    let systemsTotal = 0;
    let servicesTotal = 0;

    // Calculate systems price
    selectedSystems.forEach(systemId => {
      const system = availableSystems.find(s => s.id === systemId);
      if (system) {
        const systemPrice = Number((system as any).premium_price || system.price) || 0;
        systemsTotal += systemPrice;
      }
    });

    // Calculate services price
    selectedServices.forEach(serviceId => {
      const service = availableServices.find(s => s.id === serviceId);
      if (service) {
        const servicePrice = Number((service as any).premium_price || service.price) || 0;
        servicesTotal += servicePrice;
      }
    });

    const newSystemsPrice = systemsTotal;
    const newServicesPrice = servicesTotal;
    const newTotalPrice = basePrice + systemsTotal + servicesTotal;

    if (import.meta.env.DEV) {
      console.log('Price Calculation:', {
        basePrice,
        systemsTotal,
        servicesTotal,
        newTotalPrice,
        selectedSystems,
        selectedServices
      });
    }

    // Only update if values have changed to prevent infinite loops
    if (newSystemsPrice !== systemsPrice) {
      setSystemsPrice(newSystemsPrice);
    }
    if (newServicesPrice !== servicesPrice) {
      setServicesPrice(newServicesPrice);
    }
    if (newTotalPrice !== totalPrice) {
      setTotalPrice(newTotalPrice);
    }

    return newTotalPrice;
  };

  // Gallery functions
  const openGallery = (startIndex: number = 0) => {
    setGalleryStartIndex(startIndex);
    setShowImageGallery(true);
  };

  const closeGalleryModal = () => {
    setShowImageGallery(false);
    setGalleryStartIndex(0);
  };

  const closeImageModal = () => {
    setSelectedImage(null);
  };

  const closeVideoModal = () => {
    setSelectedVideo(null);
  };

  const premiumFeatures = [
    {
      icon: <Crown className="w-6 h-6" />,
      title: language === 'ar' ? 'أنظمة حصرية متقدمة' : 'Exclusive Advanced Systems',
      description: language === 'ar' ? 'مجموعة من الأنظمة الحصرية المطورة خصيصاً للنسخة المميزة' : 'Collection of exclusive systems developed specifically for premium edition'
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: language === 'ar' ? 'حماية قصوى' : 'Maximum Protection',
      description: language === 'ar' ? 'أنظمة حماية متطورة ضد الاختراق والتلاعب' : 'Advanced protection systems against hacking and manipulation'
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: language === 'ar' ? 'أداء فائق' : 'Superior Performance',
      description: language === 'ar' ? 'محسنة للأداء العالي والاستقرار المطلق' : 'Optimized for high performance and absolute stability'
    },
    {
      icon: <Code className="w-6 h-6" />,
      title: language === 'ar' ? 'كود مصدري كامل' : 'Complete Source Code',
      description: language === 'ar' ? 'الوصول الكامل للكود المصدري مع التوثيق الشامل' : 'Full access to source code with comprehensive documentation'
    },
    {
      icon: <Database className="w-6 h-6" />,
      title: language === 'ar' ? 'قاعدة بيانات محسنة' : 'Optimized Database',
      description: language === 'ar' ? 'قاعدة بيانات محسنة للسرعة والأمان' : 'Database optimized for speed and security'
    },
    {
      icon: <Server className="w-6 h-6" />,
      title: language === 'ar' ? 'دعم خوادم متعددة' : 'Multi-Server Support',
      description: language === 'ar' ? 'دعم كامل للخوادم المتعددة والتوزيع المتقدم' : 'Full support for multiple servers and advanced distribution'
    }
  ];

  // New handler functions for the configurator
  const handleSystemToggle = (systemId: string) => {
    setSelectedSystems(prev => {
      const newSystems = prev.includes(systemId)
        ? prev.filter(id => id !== systemId)
        : [...prev, systemId];

      // Force recalculation after state update
      setTimeout(() => calculateTotalPrice(), 0);
      return newSystems;
    });
  };

  const handleServiceToggle = (serviceId: string) => {
    setSelectedServices(prev => {
      const newServices = prev.includes(serviceId)
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId];

      // Force recalculation after state update
      setTimeout(() => calculateTotalPrice(), 0);
      return newServices;
    });
  };



  const handleOrderNow = async () => {
    if (!isAuthenticated || !userProfile) {
      showNotification({ type: 'error', message: t('notifications.loginRequired') });
      return;
    }

    if (!premiumEdition) {
      showNotification({ type: 'error', message: 'Premium Edition not available' });
      return;
    }

    try {
      // Calculate pricing breakdown
      const basePrice = Number(premiumEdition.price) || 0;
      const systemsTotal = selectedSystems.reduce((sum, systemId) => {
        const system = availableSystems.find(s => s.id === systemId);
        return sum + (Number((system as any)?.premium_price || system?.price) || 0);
      }, 0);
      const servicesTotal = selectedServices.reduce((sum, serviceId) => {
        const service = availableServices.find(s => s.id === serviceId);
        return sum + (Number((service as any)?.premium_price || service?.price) || 0);
      }, 0);

      const totalPrice = basePrice + systemsTotal + servicesTotal;

      // Create comprehensive order details
      const orderDetails = {
        is_premium_edition: true,
        base_package: {
          id: premiumEdition.id,
          name_ar: premiumEdition.title_ar,
          name_en: premiumEdition.title_en,
          price: basePrice,
          features: premiumEdition.features_ar || []
        },
        selected_systems: selectedSystems.map(systemId => {
          const system = availableSystems.find(s => s.id === systemId);
          if (!system) return null;
          return {
            id: system.id,
            name_ar: system.name_ar,
            name_en: system.name_en,
            price: Number(system.price) || 0,
            premium_price: Number((system as any).premium_price) || Number(system.price) || 0,
            installation_included: (system as any).installation_included || false,
            maintenance_included: (system as any).maintenance_included || false
          };
        }).filter(Boolean),
        selected_services: selectedServices.map(serviceId => {
          const service = availableServices.find(s => s.id === serviceId);
          if (!service) return null;
          return {
            id: service.id,
            name_ar: service.name_ar,
            name_en: service.name_en,
            price: Number(service.price) || 0,
            premium_price: Number((service as any).premium_price) || Number(service.price) || 0,
            subscription_type: (service as any).subscription_type || 'none',
            installation_included: (service as any).installation_included || false,
            maintenance_included: (service as any).maintenance_included || false
          };
        }).filter(Boolean),
        pricing_breakdown: {
          base_price: basePrice,
          systems_total: systemsTotal,
          services_total: servicesTotal,
          total_price: totalPrice
        },
        configured_at: new Date().toISOString()
      };

      // Create enhanced order request
      const result = await createOrder({
        order_type: 'premium_package',
        item_id: premiumEdition.id,
        quantity: 1,
        notes_ar: `الإصدار المميز مع ${selectedSystems.length} أنظمة و ${selectedServices.length} خدمات تقنية - السعر الإجمالي: $${totalPrice}`,
        notes_en: `Premium Edition with ${selectedSystems.length} systems and ${selectedServices.length} technical services - Total: $${totalPrice}`,
        // Pass order details as a string for now (will be enhanced in API)
        customer_requirements: JSON.stringify(orderDetails)
      });

      if (result.data) {
        showNotification({
          type: 'success',
          message: language === 'ar' ? 'تم إرسال طلب النسخة المميزة بنجاح!' : 'Premium Edition order submitted successfully!'
        });
        onClose();
      } else {
        showNotification({ type: 'error', message: t('notifications.purchaseError') });
      }
    } catch (error) {
      console.error('Error creating Premium Edition order:', error);
      showNotification({ type: 'error', message: t('notifications.purchaseError') });
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-secondary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">{language === 'ar' ? 'جاري التحميل...' : 'Loading...'}</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 overflow-y-auto"
      style={{ scrollBehavior: 'smooth' }}
      onWheel={(e) => {
        // Enable mouse wheel scrolling
        const container = e.currentTarget;
        container.scrollTop += e.deltaY;
      }}
    >
      <div className="min-h-screen py-8">
        <div className="max-w-7xl mx-auto bg-primary rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
          {/* Header */}
          <div className="relative bg-gradient-to-br from-gray-900 via-black to-gray-900 p-12 text-center overflow-hidden border-b-4 border-yellow-500">
            <button
              onClick={onClose}
              className="absolute top-6 right-6 z-50 w-12 h-12 bg-gradient-to-br from-red-600 to-red-800 hover:from-red-500 hover:to-red-700 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-2xl border-2 border-red-400/50"
              title={language === 'ar' ? 'إغلاق' : 'Close'}
            >
              <X className="w-6 h-6 text-white drop-shadow-lg" />
            </button>
            
            {/* Ultra Luxury Background Pattern */}
            <div className="absolute inset-0 opacity-30">
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(255,215,0,0.6),transparent_50%)]"></div>
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_75%_75%,rgba(255,140,0,0.4),transparent_50%)]"></div>
              <div className="absolute inset-0 bg-[linear-gradient(45deg,rgba(255,215,0,0.1)_25%,transparent_25%,transparent_75%,rgba(255,215,0,0.1)_75%)] bg-[length:30px_30px] animate-pulse"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-yellow-500/20 to-transparent animate-pulse delay-1000"></div>
            </div>

            {/* Golden Border Effects */}
            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-yellow-400 to-transparent"></div>
            <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-amber-400 to-transparent"></div>

            {/* Floating Crown Icons - Clear and Sharp */}
            <div className="absolute top-8 left-8 animate-bounce">
              <Crown className="w-8 h-8 text-yellow-400 opacity-60" style={{filter: 'none'}} />
            </div>
            <div className="absolute top-12 right-12 animate-bounce delay-300">
              <Crown className="w-6 h-6 text-amber-300 opacity-40" style={{filter: 'none'}} />
            </div>
            <div className="absolute bottom-8 left-12 animate-bounce delay-700">
              <Crown className="w-7 h-7 text-orange-400 opacity-50" style={{filter: 'none'}} />
            </div>

            {/* Ultra Luxurious Central Crown */}
            <div className="relative mb-8">
              {/* Multiple Glow Layers */}
              <div className="absolute inset-0 w-24 h-24 bg-gradient-to-r from-yellow-400 via-amber-300 to-yellow-400 rounded-full blur-3xl opacity-70 animate-pulse"></div>
              <div className="absolute inset-0 w-20 h-20 bg-gradient-to-r from-amber-200 via-yellow-300 to-amber-200 rounded-full blur-xl opacity-50 animate-pulse delay-300"></div>

              {/* Main Crown Container */}
              <div className="relative w-24 h-24 mx-auto">
                {/* Outer Ring with Diamonds */}
                <div className="absolute inset-0 w-24 h-24 border-4 border-yellow-400 rounded-full bg-gradient-to-br from-yellow-300 via-amber-400 to-yellow-500 shadow-2xl">
                  {/* Diamond Sparkles on Ring */}
                  <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full animate-ping"></div>
                  <div className="absolute top-1/2 -right-1 transform -translate-y-1/2 w-1.5 h-1.5 bg-yellow-200 rounded-full animate-ping delay-500"></div>
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full animate-ping delay-1000"></div>
                  <div className="absolute top-1/2 -left-1 transform -translate-y-1/2 w-1.5 h-1.5 bg-yellow-200 rounded-full animate-ping delay-700"></div>
                </div>

                {/* Inner Crown - Clear and Sharp */}
                <div className="absolute inset-2 w-20 h-20 bg-gradient-to-br from-black via-gray-800 to-black rounded-full flex items-center justify-center border-2 border-yellow-300 shadow-inner">
                  <Crown className="w-12 h-12 text-yellow-400" style={{filter: 'none', backdropFilter: 'none'}} />
                </div>

                {/* Floating Gems */}
                <div className="absolute -top-2 -right-2 w-3 h-3 bg-gradient-to-br from-blue-300 to-blue-500 rounded-full animate-bounce shadow-lg"></div>
                <div className="absolute -bottom-2 -left-2 w-2.5 h-2.5 bg-gradient-to-br from-red-300 to-red-500 rounded-full animate-bounce delay-300 shadow-lg"></div>
                <div className="absolute top-0 -left-3 w-2 h-2 bg-gradient-to-br from-green-300 to-green-500 rounded-full animate-bounce delay-700 shadow-lg"></div>
              </div>
            </div>

            <div className="text-center relative">
              {/* Sparkling Diamond Effects */}
              <div className="absolute -top-4 left-1/4 w-2 h-2 bg-white rounded-full animate-ping opacity-75"></div>
              <div className="absolute -top-2 right-1/3 w-1 h-1 bg-yellow-300 rounded-full animate-ping delay-300"></div>
              <div className="absolute top-8 left-1/6 w-1.5 h-1.5 bg-amber-200 rounded-full animate-ping delay-700"></div>
              <div className="absolute top-12 right-1/4 w-1 h-1 bg-white rounded-full animate-ping delay-1000"></div>

              <h1 className="text-5xl md:text-7xl font-black mb-6 relative">
                {/* Golden Glow Background */}
                <span className="absolute inset-0 bg-gradient-to-r from-yellow-400 via-amber-300 to-yellow-400 bg-clip-text text-transparent blur-lg opacity-60 animate-pulse">
                  {language === 'ar' ? '👑 النسخة المميزة الملكية 👑' : '👑 ROYAL PREMIUM EDITION 👑'}
                </span>
                {/* Main Text - Elegant Beige/Gold */}
                <span className="relative bg-gradient-to-r from-amber-200 via-yellow-300 to-amber-200 bg-clip-text text-transparent drop-shadow-2xl font-bold tracking-wider">
                  {language === 'ar' ? '👑 النسخة المميزة الملكية 👑' : '👑 ROYAL PREMIUM EDITION 👑'}
                </span>
                {/* Outline Effect */}
                <span className="absolute inset-0 text-yellow-400 opacity-20 font-bold tracking-wider" style={{WebkitTextStroke: '2px #fbbf24'}}>
                  {language === 'ar' ? '👑 النسخة المميزة الملكية 👑' : '👑 ROYAL PREMIUM EDITION 👑'}
                </span>
              </h1>

              {/* Luxury Separator with Diamonds */}
              <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mb-6">
                <div className="w-16 h-0.5 bg-gradient-to-r from-transparent via-yellow-500 to-black/50"></div>
                <div className="relative">
                  <div className="w-4 h-4 bg-gradient-to-br from-yellow-300 to-amber-500 rotate-45 shadow-xl"></div>
                  <div className="absolute inset-0 w-4 h-4 bg-white/30 rotate-45 animate-pulse"></div>
                </div>
                <Star className="w-8 h-8 text-yellow-500 animate-pulse drop-shadow-lg" fill="currentColor" />
                <span className="text-2xl font-black bg-gradient-to-r from-amber-300 via-yellow-400 to-amber-300 bg-clip-text text-transparent tracking-wider drop-shadow-lg">
                  {language === 'ar' ? 'الإصدار الفاخر الحصري' : 'EXCLUSIVE LUXURY EDITION'}
                </span>
                <Star className="w-8 h-8 text-yellow-500 animate-pulse drop-shadow-lg" fill="currentColor" />
                <div className="relative">
                  <div className="w-4 h-4 bg-gradient-to-br from-yellow-300 to-amber-500 rotate-45 shadow-xl"></div>
                  <div className="absolute inset-0 w-4 h-4 bg-white/30 rotate-45 animate-pulse"></div>
                </div>
                <div className="w-16 h-0.5 bg-gradient-to-r from-black/50 via-yellow-500 to-transparent"></div>
              </div>

              {/* Majestic Description */}
              <p className="text-amber-200 font-bold text-xl drop-shadow-lg mb-4 tracking-wide">
                {language === 'ar' ? 'تجربة لا مثيل لها من الفخامة والتميز الملكي' : 'An unparalleled experience of royal luxury and distinction'}
              </p>

              {/* Royal Ornaments */}
              <div className="flex justify-center items-center space-x-6 rtl:space-x-reverse">
                <div className="w-8 h-8 border-2 border-yellow-500 rotate-45 bg-gradient-to-br from-yellow-200 to-amber-400 shadow-lg"></div>
                <div className="text-yellow-600 font-bold text-lg">✦ ◆ ✦</div>
                <div className="w-8 h-8 border-2 border-yellow-500 rotate-45 bg-gradient-to-br from-yellow-200 to-amber-400 shadow-lg"></div>
              </div>
            </div>
          </div>

          {/* Interactive Configurator */}
          <div className="p-6">
            {/* Step Navigation */}
            <div className="flex justify-center mb-8">
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                {[
                  { id: 'overview', label: language === 'ar' ? 'نظرة عامة' : 'Overview' },
                  { id: 'systems', label: language === 'ar' ? 'الأنظمة' : 'Systems' },
                  { id: 'services', label: language === 'ar' ? 'الخدمات' : 'Services' },
                  { id: 'summary', label: language === 'ar' ? 'الملخص' : 'Summary' }
                ].map((step, index) => (
                  <div key={step.id} className="flex items-center">
                    <button
                      onClick={() => setCurrentStep(step.id as any)}
                      className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                        currentStep === step.id
                          ? 'bg-secondary text-primary'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      {step.label}
                    </button>
                    {index < 3 && (
                      <div className="w-8 h-0.5 bg-gray-600 mx-2"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Step Content */}
            {currentStep === 'overview' && (
              <div className="space-y-8">
                {/* Premium Edition Base Package */}
                {premiumEdition && (
                  <div className="bg-gradient-to-r from-secondary/20 to-accent/20 p-8 rounded-xl border border-secondary/30">
                    <div className="text-center">
                      <h3 className="text-3xl font-bold text-white mb-4">{language === 'ar' ? premiumEdition.title_ar : premiumEdition.title_en}</h3>
                      <p className="text-xl text-gray-300 mb-6">{language === 'ar' ? premiumEdition.description_ar : premiumEdition.description_en}</p>
                      <div className="text-4xl font-bold text-secondary mb-4">
                        ${Number(premiumEdition.price || 0).toLocaleString()}
                        {premiumEdition.original_price && premiumEdition.original_price > premiumEdition.price && (
                          <span className="text-lg text-gray-400 line-through ml-2">${Number(premiumEdition.original_price || 0).toLocaleString()}</span>
                        )}
                      </div>
                      <p className="text-gray-400">
                        {language === 'ar' ? 'السعر الأساسي للنسخة المميزة' : 'Base Premium Edition Price'}
                      </p>
                    </div>

                    {/* Media Section - Video or Image */}
                    {(premiumEdition.video_url || premiumEdition.image_url) && (
                      <div className="relative h-64 md:h-80 overflow-hidden rounded-xl mb-6">
                        {premiumEdition.video_url ? (
                          <div
                            className="relative w-full h-full bg-black/50 flex items-center justify-center group cursor-pointer overflow-hidden"
                            onClick={() => premiumEdition.video_url && setSelectedVideo(premiumEdition.video_url)}
                          >
                            {/* Video Thumbnail */}
                            {(() => {
                              const getYouTubeThumbnail = (url: string) => {
                                const videoId = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)?.[1];
                                return videoId ? `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg` : null;
                              };
                              const thumbnailUrl = getYouTubeThumbnail(premiumEdition.video_url);

                              return thumbnailUrl ? (
                                <img
                                  src={thumbnailUrl}
                                  alt="Video Thumbnail"
                                  className="absolute inset-0 w-full h-full object-cover"
                                  onError={(e) => {
                                    (e.target as HTMLImageElement).style.display = 'none';
                                  }}
                                />
                              ) : null;
                            })()}

                            <div className="absolute inset-0 bg-gradient-to-br from-black/30 via-transparent to-black/50"></div>
                            <div className="relative z-10 w-20 h-20 bg-red-600/90 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-2xl">
                              <Play className="w-10 h-10 text-white ml-1" fill="currentColor" />
                            </div>
                            <div className="absolute bottom-4 left-4 bg-black/70 px-3 py-1 rounded-full">
                              <span className="text-white text-sm flex items-center space-x-2 rtl:space-x-reverse">
                                <Play className="w-4 h-4" />
                                <span>{language === 'ar' ? 'مشاهدة الفيديو' : 'Watch Video'}</span>
                              </span>
                            </div>
                          </div>
                        ) : premiumEdition.image_url && (
                          <div
                            className="relative w-full h-full cursor-pointer group"
                            onClick={() => premiumEdition.image_url && setSelectedImage(premiumEdition.image_url)}
                          >
                            <img
                              src={premiumEdition.image_url}
                              alt={language === 'ar' ? premiumEdition.title_ar : premiumEdition.title_en}
                              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-primary/60 to-transparent"></div>
                            <div className="absolute bottom-4 left-4 bg-black/70 px-3 py-1 rounded-full">
                              <span className="text-white text-sm flex items-center space-x-2 rtl:space-x-reverse">
                                <ImageIcon className="w-4 h-4" />
                                <span>{language === 'ar' ? 'عرض الصورة' : 'View Image'}</span>
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Image Gallery */}
                    {premiumEdition.gallery_images && premiumEdition.gallery_images.length > 0 && (
                      <div className="mb-6">
                        <h4 className="text-xl font-bold text-secondary mb-4 text-center">
                          {language === 'ar' ? 'معرض الصور' : 'Image Gallery'}
                        </h4>
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                          {premiumEdition.gallery_images?.slice(0, 10)?.map((imageUrl, index) => (
                            <div
                              key={index}
                              className="relative aspect-square rounded-lg overflow-hidden cursor-pointer group hover:scale-105 transition-all duration-300 hover:shadow-lg hover:shadow-secondary/30"
                              onClick={() => setSelectedImage(imageUrl)}
                            >
                              <img
                                src={imageUrl}
                                alt={`${language === 'ar' ? premiumEdition.title_ar : premiumEdition.title_en} - ${index + 1}`}
                                className="w-full h-full object-cover group-hover:brightness-110 transition-all duration-300"
                              />
                              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                              <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                {index + 1}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Premium Features */}
                {premiumEdition && (premiumEdition.features_ar || premiumEdition.features_en) && (
                  <div>
                    <h3 className="text-2xl font-bold text-white mb-6 text-center">
                      {language === 'ar' ? 'مميزات النسخة المميزة' : 'Premium Features'}
                    </h3>
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {(language === 'ar' ? premiumEdition.features_ar : premiumEdition.features_en)?.map((feature: string, index: number) => (
                        <div key={index} className="bg-background rounded-lg p-4 border border-gray-700 hover:border-secondary/50 transition-colors">
                          <div className="flex items-center space-x-3 rtl:space-x-reverse">
                            <CheckCircle className="w-5 h-5 text-secondary flex-shrink-0" />
                            <span className="text-white">{feature}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Included Systems */}
                {premiumEdition && premiumEdition.included_systems && premiumEdition.included_systems.length > 0 && (
                  <div>
                    <h3 className="text-2xl font-bold text-white mb-6 text-center">
                      {language === 'ar' ? 'الأنظمة المضمنة' : 'Included Systems'}
                    </h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      {premiumEdition.included_systems?.map((systemId: string) => {
                        const system = availableSystems.find(s => s.id === systemId);
                        if (!system) return null;
                        return (
                          <div key={systemId} className="bg-background rounded-lg p-4 border border-blue-500/30">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-semibold text-white">{system.name?.[language] || 'System'}</h4>
                              <span className="text-blue-400 font-bold">${system.price.toLocaleString()}</span>
                            </div>
                            <p className="text-gray-300 text-sm">{system.description?.[language] || 'Description'}</p>
                            <div className="mt-2">
                              <span className={`px-2 py-1 rounded text-xs ${
                                system.type === 'plugin' ? 'bg-purple-500/20 text-purple-400' : 'bg-blue-500/20 text-blue-400'
                              }`}>
                                {system.type === 'plugin' ? (language === 'ar' ? 'إضافة' : 'Plugin') : (language === 'ar' ? 'نظام' : 'System')}
                              </span>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Included Services */}
                {premiumEdition && premiumEdition.included_services && premiumEdition.included_services.length > 0 && (
                  <div>
                    <h3 className="text-2xl font-bold text-white mb-6 text-center">
                      {language === 'ar' ? 'الخدمات المضمنة' : 'Included Services'}
                    </h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      {premiumEdition.included_services?.map((serviceId: string) => {
                        const service = availableServices.find(s => s.id === serviceId);
                        if (!service) return null;
                        return (
                          <div key={serviceId} className="bg-background rounded-lg p-4 border border-green-500/30">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-semibold text-white">{service.name?.[language] || 'Service'}</h4>
                              <div className="text-right">
                                <span className="text-green-400 font-bold">${(service.premiumPrice || service.price).toLocaleString()}</span>
                                {service.premiumPrice !== service.price && (
                                  <div className="text-xs text-gray-400 line-through">${service.price.toLocaleString()}</div>
                                )}
                              </div>
                            </div>
                            <p className="text-gray-300 text-sm">{service.description?.[language] || 'Description'}</p>
                            <div className="mt-2 flex items-center space-x-2 rtl:space-x-reverse">
                              <span className="px-2 py-1 rounded text-xs bg-green-500/20 text-green-400">
                                {language === 'ar' ? 'خدمة مميزة' : 'Premium Service'}
                              </span>
                              {service.subscriptionType !== 'none' && (
                                <span className="px-2 py-1 rounded text-xs bg-orange-500/20 text-orange-400">
                                  {service.subscriptionType === 'monthly' ? (language === 'ar' ? 'شهري' : 'Monthly') : (language === 'ar' ? 'سنوي' : 'Yearly')}
                                </span>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Systems Selection Step */}
            {currentStep === 'systems' && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-white mb-4">
                    {language === 'ar' ? 'اختر الأنظمة الإضافية' : 'Choose Additional Systems'}
                  </h3>
                  <p className="text-gray-400">
                    {language === 'ar' ? 'أضف أنظمة متقدمة لتخصيص نسختك المميزة' : 'Add advanced systems to customize your Premium Edition'}
                  </p>
                </div>

                {availableSystems.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-400">
                      {language === 'ar' ? 'لا توجد أنظمة متاحة حالياً' : 'No systems available at the moment'}
                    </p>
                  </div>
                ) : (
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {availableSystems.map((system) => {
                      if (!system || !system.name) return null;
                    const isSelected = selectedSystems.includes(system.id);
                    const isPlugin = system.type === 'plugin';

                    return (
                      <div
                        key={system.id}
                        className={`bg-background rounded-lg border p-6 cursor-pointer transition-all ${
                          isSelected
                            ? 'border-secondary bg-secondary/10'
                            : 'border-gray-700 hover:border-gray-600'
                        }`}
                        onClick={() => handleSystemToggle(system.id)}
                        title={`${system.name?.[language] || 'System'} - $${system.price.toLocaleString()} - ${system.description?.[language] || 'Description'}`}
                      >
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center space-x-3 rtl:space-x-reverse">
                            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                              isPlugin
                                ? 'bg-gradient-to-br from-purple-500 to-pink-500'
                                : 'bg-gradient-to-br from-blue-500 to-cyan-500'
                            }`}>
                              {isPlugin ? <Zap className="w-6 h-6 text-white" /> : <Shield className="w-6 h-6 text-white" />}
                            </div>
                            <div>
                              <h4 className="text-lg font-semibold text-white">{system.name?.[language] || 'System'}</h4>
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                isPlugin
                                  ? 'bg-purple-500/20 text-purple-400'
                                  : 'bg-blue-500/20 text-blue-400'
                              }`}>
                                {isPlugin
                                  ? (language === 'ar' ? 'إضافة خاصة' : 'Special Plugin')
                                  : (language === 'ar' ? 'نظام عادي' : 'Regular System')
                                }
                              </span>
                            </div>
                          </div>
                          <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                            isSelected
                              ? 'border-secondary bg-secondary'
                              : 'border-gray-400'
                          }`}>
                            {isSelected && <CheckCircle className="w-4 h-4 text-primary" />}
                          </div>
                        </div>

                        <p className="text-gray-300 text-sm mb-4 line-clamp-2">{system.description?.[language] || 'Description'}</p>

                        <div className="text-right rtl:text-left">
                          <span className="text-xl font-bold text-secondary">+${system.price.toLocaleString()}</span>
                        </div>
                      </div>
                    );
                    })}
                  </div>
                )}
              </div>
            )}

            {/* Services Selection Step */}
            {currentStep === 'services' && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-white mb-4">
                    {language === 'ar' ? 'اختر الخدمات الإضافية' : 'Choose Additional Services'}
                  </h3>
                  <p className="text-gray-400">
                    {language === 'ar' ? 'أضف خدمات تقنية متخصصة مع أسعار مميزة' : 'Add specialized technical services with premium pricing'}
                  </p>
                </div>

                {availableServices.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-400">
                      {language === 'ar' ? 'لا توجد خدمات متاحة حالياً' : 'No services available at the moment'}
                    </p>
                  </div>
                ) : (
                  <div className="grid md:grid-cols-2 gap-6">
                    {availableServices.map((service) => {
                      if (!service || !service.name) return null;
                    const isSelected = selectedServices.includes(service.id);
                    const isSubscription = service.subscriptionType !== 'none';

                    return (
                      <div
                        key={service.id}
                        className={`bg-background rounded-lg border p-6 cursor-pointer transition-all ${
                          isSelected
                            ? 'border-secondary bg-secondary/10'
                            : 'border-gray-700 hover:border-gray-600'
                        }`}
                        onClick={() => handleServiceToggle(service.id)}
                        title={`${service.name?.[language] || 'Service'} - $${service.premiumPrice.toLocaleString()} - ${service.description?.[language] || 'Description'}`}
                      >
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center space-x-3 rtl:space-x-reverse">
                            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                              isSubscription
                                ? 'bg-gradient-to-br from-green-500 to-emerald-500'
                                : 'bg-gradient-to-br from-orange-500 to-red-500'
                            }`}>
                              {isSubscription ? <RefreshCw className="w-6 h-6 text-white" /> : <Settings className="w-6 h-6 text-white" />}
                            </div>
                            <div className="flex-1">
                              <h4 className="text-lg font-semibold text-white">{service.name?.[language] || 'Service'}</h4>
                              {isSubscription && (
                                <span className="text-xs px-2 py-1 rounded-full bg-green-500/20 text-green-400">
                                  {service.subscriptionType === 'monthly'
                                    ? (language === 'ar' ? 'اشتراك شهري' : 'Monthly Subscription')
                                    : (language === 'ar' ? 'اشتراك سنوي' : 'Yearly Subscription')
                                  }
                                </span>
                              )}
                            </div>
                          </div>
                          <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                            isSelected
                              ? 'border-secondary bg-secondary'
                              : 'border-gray-400'
                          }`}>
                            {isSelected && <CheckCircle className="w-4 h-4 text-primary" />}
                          </div>
                        </div>

                        <p className="text-gray-300 text-sm mb-4">{service.description?.[language] || 'Description'}</p>

                        <div className="flex items-center justify-between">
                          <div>
                            <span className="text-xl font-bold text-secondary">
                              +${service.premiumPrice.toLocaleString()}
                            </span>
                            {service.price > service.premiumPrice && (
                              <span className="text-sm text-gray-400 line-through ml-2">
                                ${service.price.toLocaleString()}
                              </span>
                            )}
                            {isSubscription && (
                              <span className="text-xs text-gray-400 block">
                                /{service.subscriptionType === 'monthly'
                                  ? (language === 'ar' ? 'شهر' : 'month')
                                  : (language === 'ar' ? 'سنة' : 'year')
                                }
                              </span>
                            )}
                          </div>
                          {service.premiumPrice === 0 && (
                            <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                              {language === 'ar' ? 'مجاني' : 'FREE'}
                            </span>
                          )}
                        </div>
                      </div>
                    );
                    })}
                  </div>
                )}
              </div>
            )}

            {/* Summary Step */}
            {currentStep === 'summary' && (
              <div className="space-y-8">
                {/* Enhanced Header with Golden Gradient */}
                <div className="text-center relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/10 via-orange-500/10 to-red-500/10 rounded-2xl blur-xl"></div>
                  <div className="relative bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 rounded-2xl p-8 border border-yellow-500/30">
                    <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mb-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-pulse">
                        <Crown className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-3xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent">
                        {language === 'ar' ? 'ملخص طلبك المخصص' : 'Your Custom Order Summary'}
                      </h3>
                      <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center animate-pulse">
                        <Star className="w-6 h-6 text-white" />
                      </div>
                    </div>
                    <p className="text-gray-300 text-lg">
                      {language === 'ar' ? 'راجع اختياراتك المميزة قبل إتمام الطلب' : 'Review your premium selections before completing the order'}
                    </p>

                    {/* Decorative Elements */}
                    <div className="absolute top-2 left-2 w-4 h-4 bg-yellow-400 rounded-full opacity-60 animate-ping"></div>
                    <div className="absolute top-4 right-4 w-3 h-3 bg-orange-500 rounded-full opacity-60 animate-ping" style={{animationDelay: '0.5s'}}></div>
                    <div className="absolute bottom-2 left-4 w-2 h-2 bg-red-500 rounded-full opacity-60 animate-ping" style={{animationDelay: '1s'}}></div>
                    <div className="absolute bottom-4 right-2 w-3 h-3 bg-yellow-500 rounded-full opacity-60 animate-ping" style={{animationDelay: '1.5s'}}></div>
                  </div>
                </div>

                {/* Enhanced Order Summary */}
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-secondary/10 via-accent/10 to-secondary/10 rounded-2xl blur-lg"></div>
                  <div className="relative bg-gradient-to-br from-background/90 to-primary/90 backdrop-blur-sm rounded-2xl p-8 border border-secondary/30 shadow-2xl">
                    <div className="space-y-6">
                      {/* Base Premium Edition */}
                      {premiumEdition && (
                        <div className="relative">
                          <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-xl blur-sm"></div>
                          <div className="relative bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-xl p-6 border border-yellow-500/30">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                                  <Crown className="w-8 h-8 text-white" />
                                </div>
                                <div>
                                  <h4 className="text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                                    {language === 'ar' ? premiumEdition.title_ar : premiumEdition.title_en}
                                  </h4>
                                  <p className="text-gray-300 text-sm flex items-center space-x-2 rtl:space-x-reverse">
                                    <Star className="w-4 h-4 text-yellow-400" />
                                    <span>{language === 'ar' ? 'النسخة الأساسية المميزة' : 'Premium Base Edition'}</span>
                                  </p>
                                  <p className="text-gray-400 text-xs mt-1">{language === 'ar' ? premiumEdition.description_ar : premiumEdition.description_en}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <span className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                                  ${premiumEdition.price.toLocaleString()}
                                </span>
                                <p className="text-gray-400 text-xs">{language === 'ar' ? 'السعر الأساسي' : 'Base Price'}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Selected Systems */}
                      {selectedSystems.length > 0 && (
                        <div className="relative">
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-xl blur-sm"></div>
                          <div className="relative bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl p-6 border border-blue-500/20">
                            <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
                              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                                <Shield className="w-4 h-4 text-white" />
                              </div>
                              <h5 className="text-lg font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                                {language === 'ar' ? 'الأنظمة المختارة' : 'Selected Systems'}
                              </h5>
                              <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full text-xs">
                                {selectedSystems.length} {language === 'ar' ? 'نظام' : 'Systems'}
                              </span>
                            </div>
                            <div className="space-y-3">
                              {selectedSystems.map(systemId => {
                                const system = availableSystems.find(s => s.id === systemId);
                                if (!system) return null;
                                return (
                                  <div key={systemId} className="bg-background/50 rounded-lg p-4 border border-gray-600/50 hover:border-blue-500/30 transition-colors">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                                        <div className={`w-10 h-10 rounded-xl flex items-center justify-center shadow-lg ${
                                          system.type === 'plugin'
                                            ? 'bg-gradient-to-r from-purple-500 to-pink-500'
                                            : 'bg-gradient-to-r from-blue-500 to-cyan-500'
                                        }`}>
                                          {system.type === 'plugin' ? <Zap className="w-5 h-5 text-white" /> : <Shield className="w-5 h-5 text-white" />}
                                        </div>
                                        <div>
                                          <span className="text-white font-medium">{system.name?.[language] || 'System'}</span>
                                          <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
                                            <span className={`text-xs px-2 py-1 rounded-full ${
                                              system.type === 'plugin'
                                                ? 'bg-purple-500/20 text-purple-400 border border-purple-500/30'
                                                : 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                                            }`}>
                                              {system.type === 'plugin'
                                                ? (language === 'ar' ? 'إضافة خاصة' : 'Special Plugin')
                                                : (language === 'ar' ? 'نظام أساسي' : 'Core System')
                                              }
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                      <div className="text-right">
                                        <span className="text-xl font-bold text-secondary">+${system.price.toLocaleString()}</span>
                                        <p className="text-gray-400 text-xs">{language === 'ar' ? 'إضافة' : 'Add-on'}</p>
                                      </div>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Selected Services */}
                      {selectedServices.length > 0 && (
                        <div className="relative">
                          <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-orange-500/5 rounded-xl blur-sm"></div>
                          <div className="relative bg-gradient-to-r from-green-500/10 to-orange-500/10 rounded-xl p-6 border border-green-500/20">
                            <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
                              <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-orange-500 rounded-full flex items-center justify-center">
                                <Settings className="w-4 h-4 text-white" />
                              </div>
                              <h5 className="text-lg font-bold bg-gradient-to-r from-green-400 to-orange-400 bg-clip-text text-transparent">
                                {language === 'ar' ? 'الخدمات المختارة' : 'Selected Services'}
                              </h5>
                              <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs">
                                {selectedServices.length} {language === 'ar' ? 'خدمة' : 'Services'}
                              </span>
                            </div>
                            <div className="space-y-3">
                              {selectedServices.map(serviceId => {
                                const service = availableServices.find(s => s.id === serviceId);
                                if (!service) return null;
                                const isSubscription = service.subscriptionType !== 'none';
                                return (
                                  <div key={serviceId} className="bg-background/50 rounded-lg p-4 border border-gray-600/50 hover:border-green-500/30 transition-colors">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                                        <div className={`w-10 h-10 rounded-xl flex items-center justify-center shadow-lg ${
                                          isSubscription
                                            ? 'bg-gradient-to-r from-green-500 to-emerald-500'
                                            : 'bg-gradient-to-r from-orange-500 to-red-500'
                                        }`}>
                                          {isSubscription ? <RefreshCw className="w-5 h-5 text-white" /> : <Settings className="w-5 h-5 text-white" />}
                                        </div>
                                        <div>
                                          <span className="text-white font-medium">{service.name?.[language] || 'Service'}</span>
                                          <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
                                            {isSubscription && (
                                              <span className="text-xs px-2 py-1 rounded-full bg-green-500/20 text-green-400 border border-green-500/30">
                                                {service.subscriptionType === 'monthly'
                                                  ? (language === 'ar' ? 'اشتراك شهري' : 'Monthly Subscription')
                                                  : (language === 'ar' ? 'اشتراك سنوي' : 'Yearly Subscription')
                                                }
                                              </span>
                                            )}
                                            {service.premiumPrice === 0 && (
                                              <span className="text-xs px-2 py-1 rounded-full bg-green-500 text-white">
                                                {language === 'ar' ? 'مجاني' : 'FREE'}
                                              </span>
                                            )}
                                          </div>
                                        </div>
                                      </div>
                                      <div className="text-right">
                                        <span className="text-xl font-bold text-secondary">
                                          +${service.premiumPrice.toLocaleString()}
                                          {isSubscription && (
                                            <span className="text-sm text-gray-400">
                                              /{service.subscriptionType === 'monthly'
                                                ? (language === 'ar' ? 'شهر' : 'mo')
                                                : (language === 'ar' ? 'سنة' : 'yr')
                                              }
                                            </span>
                                          )}
                                        </span>
                                        <p className="text-gray-400 text-xs">
                                          {isSubscription
                                            ? (language === 'ar' ? 'اشتراك' : 'Subscription')
                                            : (language === 'ar' ? 'خدمة' : 'Service')
                                          }
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        </div>
                      )}

                    {/* Total Price */}
                    <div className="pt-6 border-t border-gray-600">
                      <div className="flex items-center justify-between">
                        <span className="text-xl font-bold text-white">
                          {language === 'ar' ? 'المجموع الكلي:' : 'Total Price:'}
                        </span>
                        <span className="text-3xl font-bold text-secondary">
                          ${Number(totalPrice || 0).toLocaleString()}
                        </span>
                      </div>
                      {(selectedServices.some(id => {
                        const service = availableServices.find(s => s.id === id);
                        return service && service.subscriptionType !== 'none';
                      })) && (
                        <p className="text-gray-400 text-sm mt-2">
                          {language === 'ar' ? '* يشمل رسوم الاشتراك الشهرية/السنوية' : '* Includes monthly/yearly subscription fees'}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Order Now Button */}
                <div className="text-center">
                  <button
                    onClick={handleOrderNow}
                    disabled={loading}
                    className="relative group bg-gradient-to-r from-yellow-500 via-amber-500 to-yellow-500 hover:from-yellow-400 hover:via-amber-400 hover:to-yellow-400 text-black font-black text-xl px-16 py-5 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-3xl border-2 border-yellow-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-amber-400/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                    <div className="relative flex items-center justify-center space-x-3 rtl:space-x-reverse">
                      <Crown className="w-6 h-6" style={{filter: 'none'}} />
                      <span>{loading ? (language === 'ar' ? 'جاري المعالجة...' : 'Processing...') : (language === 'ar' ? '🚀 اطلب النسخة المميزة الآن' : '🚀 Order Premium Edition Now')}</span>
                      <Crown className="w-6 h-6" style={{filter: 'none'}} />
                    </div>
                  </button>
                </div>
              </div>
              </div>
            )}

            {/* Step Navigation Controls */}
            <div className="flex justify-between items-center p-6 border-t border-gray-700">
              <button
                onClick={() => {
                  const steps = ['overview', 'systems', 'services', 'summary'];
                  const currentIndex = steps.indexOf(currentStep);
                  if (currentIndex > 0) {
                    setCurrentStep(steps[currentIndex - 1] as any);
                  }
                }}
                disabled={currentStep === 'overview'}
                className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                  currentStep === 'overview'
                    ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                    : 'bg-gray-600 text-white hover:bg-gray-500'
                }`}
              >
                {language === 'ar' ? 'السابق' : 'Previous'}
              </button>

              <div className="text-center">
                <div className="text-2xl font-bold text-secondary">
                  ${Number(totalPrice || 0).toLocaleString()}
                </div>
                <div className="text-sm text-gray-400">
                  {language === 'ar' ? 'السعر الإجمالي' : 'Total Price'}
                </div>
              </div>

              <button
                onClick={() => {
                  const steps = ['overview', 'systems', 'services', 'summary'];
                  const currentIndex = steps.indexOf(currentStep);
                  if (currentIndex < steps.length - 1) {
                    setCurrentStep(steps[currentIndex + 1] as any);
                  }
                }}
                disabled={currentStep === 'summary'}
                className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                  currentStep === 'summary'
                    ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                    : 'bg-secondary text-primary hover:bg-secondary/90'
                }`}
              >
                {language === 'ar' ? 'التالي' : 'Next'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black/90 z-60 flex items-center justify-center p-4" onClick={() => setSelectedImage(null)}>
          <div className="relative max-w-4xl max-h-full">
            <img src={selectedImage} alt="Preview" className="max-w-full max-h-full object-contain" />
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute top-4 right-4 w-10 h-10 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center text-white"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>
      )}

      {/* Video Modal */}
      {selectedVideo && (
        <div className="fixed inset-0 bg-black/90 z-60 flex items-center justify-center p-4" onClick={() => setSelectedVideo(null)}>
          <div className="relative max-w-4xl max-h-full">
            <iframe
              src={selectedVideo}
              className="w-full h-96"
              style={{ border: 'none' }}
              allowFullScreen
            ></iframe>
            <button
              onClick={() => setSelectedVideo(null)}
              className="absolute top-4 right-4 w-10 h-10 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center text-white"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>
      )}

      {/* Enhanced Video Modal */}
      <VideoModal
        isOpen={!!selectedVideo}
        onClose={closeVideoModal}
        videoUrl={selectedVideo || ''}
        title={premiumEdition ? (language === 'ar' ? premiumEdition.title_ar : premiumEdition.title_en) : ''}
      />

      {/* Enhanced Image Modal */}
      <ImageGalleryModal
        isOpen={!!selectedImage && !showImageGallery}
        onClose={closeImageModal}
        images={selectedImage ? [selectedImage] : []}
        initialIndex={0}
        title={premiumEdition ? (language === 'ar' ? premiumEdition.title_ar : premiumEdition.title_en) : ''}
      />

      {/* Enhanced Gallery Modal */}
      <ImageGalleryModal
        isOpen={showImageGallery}
        onClose={closeGalleryModal}
        images={premiumEdition?.gallery_images || []}
        initialIndex={galleryStartIndex}
        title={premiumEdition ? (language === 'ar' ? `معرض ${premiumEdition.title_ar}` : `${premiumEdition.title_en} Gallery`) : ''}
      />
    </div>
  );
};

export default PremiumEdition;