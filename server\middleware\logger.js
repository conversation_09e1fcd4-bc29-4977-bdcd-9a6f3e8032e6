/**
 * Request Logging Middleware
 * 
 * Provides comprehensive request logging with:
 * - Request/response timing
 * - User activity tracking
 * - Security event logging
 * - Performance monitoring
 * - Database activity logs
 */

const { executeQuery, generateUUID } = require('../config/database');

/**
 * Generate unique request ID
 */
function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Get client IP address
 */
function getClientIP(req) {
  return req.ip || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
         req.headers['x-forwarded-for']?.split(',')[0] ||
         req.headers['x-real-ip'] ||
         'unknown';
}

/**
 * Determine if request should be logged to database
 */
function shouldLogToDatabase(req, res) {
  // Skip logging for certain routes
  const skipRoutes = [
    '/health',
    '/api/status',
    '/favicon.ico'
  ];
  
  // Skip static file requests
  if (req.url.startsWith('/uploads/') || req.url.startsWith('/static/')) {
    return false;
  }
  
  // Skip if route is in skip list
  if (skipRoutes.some(route => req.url.startsWith(route))) {
    return false;
  }
  
  // Skip successful GET requests to reduce noise
  if (req.method === 'GET' && res.statusCode < 400) {
    return false;
  }
  
  return true;
}

/**
 * Log activity to database
 */
async function logActivity(req, res, responseTime) {
  try {
    if (!shouldLogToDatabase(req, res)) {
      return;
    }
    
    const activityId = generateUUID();
    const userId = req.user?.id || null;
    const action = `${req.method.toLowerCase()}_${req.route?.path || req.url}`.replace(/[^a-zA-Z0-9_]/g, '_');
    
    const details = {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userAgent: req.get('User-Agent'),
      referer: req.get('Referer'),
      contentLength: res.get('Content-Length'),
      requestSize: req.get('Content-Length')
    };
    
    // Add request body for POST/PUT/PATCH (excluding sensitive data)
    if (['POST', 'PUT', 'PATCH'].includes(req.method) && req.body) {
      const sanitizedBody = { ...req.body };
      
      // Remove sensitive fields
      const sensitiveFields = ['password', 'password_hash', 'token', 'refreshToken', 'secret'];
      sensitiveFields.forEach(field => {
        if (sanitizedBody[field]) {
          sanitizedBody[field] = '[REDACTED]';
        }
      });
      
      details.requestBody = sanitizedBody;
    }
    
    // Add query parameters
    if (Object.keys(req.query).length > 0) {
      details.queryParams = req.query;
    }
    
    await executeQuery(
      `INSERT INTO activity_logs (id, user_id, action, entity_type, details, ip_address, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        activityId,
        userId,
        action,
        'request',
        JSON.stringify(details),
        getClientIP(req),
        new Date()
      ]
    );
    
  } catch (error) {
    console.error('❌ Failed to log activity to database:', error.message);
  }
}

/**
 * Main request logging middleware
 */
function requestLogger(req, res, next) {
  const startTime = Date.now();
  const requestId = generateRequestId();
  const clientIP = getClientIP(req);
  
  // Add request ID to request object
  req.requestId = requestId;
  
  // Log request start
  console.log(`📥 ${req.method} ${req.originalUrl} - ${clientIP} [${requestId}]`);
  
  // Override res.end to capture response
  const originalEnd = res.end;
  res.end = function(chunk, encoding) {
    const responseTime = Date.now() - startTime;
    
    // Log response
    const statusColor = res.statusCode >= 400 ? '🔴' : res.statusCode >= 300 ? '🟡' : '🟢';
    console.log(`📤 ${statusColor} ${res.statusCode} ${req.method} ${req.originalUrl} - ${responseTime}ms [${requestId}]`);
    
    // Log to database asynchronously
    logActivity(req, res, responseTime).catch(error => {
      console.error('Database logging error:', error.message);
    });
    
    // Call original end method
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
}

/**
 * Security event logger
 */
async function logSecurityEvent(eventType, details, req = null, userId = null) {
  try {
    const eventId = generateUUID();
    const clientIP = req ? getClientIP(req) : null;
    
    const eventDetails = {
      eventType,
      timestamp: new Date().toISOString(),
      ...details
    };
    
    if (req) {
      eventDetails.request = {
        method: req.method,
        url: req.originalUrl,
        userAgent: req.get('User-Agent'),
        referer: req.get('Referer')
      };
    }
    
    // Log to console
    console.warn(`🔒 SECURITY EVENT: ${eventType}`);
    console.warn(`   Details: ${JSON.stringify(details)}`);
    console.warn(`   IP: ${clientIP}`);
    console.warn(`   User: ${userId || 'Anonymous'}`);
    
    // Log to database
    await executeQuery(
      `INSERT INTO activity_logs (id, user_id, action, entity_type, details, ip_address, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        eventId,
        userId,
        'security_event',
        'security',
        JSON.stringify(eventDetails),
        clientIP,
        new Date()
      ]
    );
    
  } catch (error) {
    console.error('❌ Failed to log security event:', error.message);
  }
}

/**
 * User action logger
 */
async function logUserAction(action, entityType, entityId, details = {}, req = null) {
  try {
    const actionId = generateUUID();
    const userId = req?.user?.id || null;
    const clientIP = req ? getClientIP(req) : null;
    
    const actionDetails = {
      action,
      entityType,
      entityId,
      timestamp: new Date().toISOString(),
      ...details
    };
    
    if (req) {
      actionDetails.request = {
        method: req.method,
        url: req.originalUrl,
        userAgent: req.get('User-Agent')
      };
    }
    
    // Log to console for important actions
    const importantActions = ['user_login', 'user_logout', 'order_created', 'payment_processed', 'admin_action'];
    if (importantActions.includes(action)) {
      console.log(`👤 USER ACTION: ${action}`);
      console.log(`   User: ${userId}`);
      console.log(`   Entity: ${entityType}/${entityId}`);
      console.log(`   IP: ${clientIP}`);
    }
    
    // Log to database
    await executeQuery(
      `INSERT INTO activity_logs (id, user_id, action, entity_type, entity_id, details, ip_address, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        actionId,
        userId,
        action,
        entityType,
        entityId,
        JSON.stringify(actionDetails),
        clientIP,
        new Date()
      ]
    );
    
  } catch (error) {
    console.error('❌ Failed to log user action:', error.message);
  }
}

/**
 * Performance monitoring middleware
 */
function performanceMonitor(req, res, next) {
  const startTime = process.hrtime.bigint();
  
  // Override res.end to capture performance metrics
  const originalEnd = res.end;
  res.end = function(chunk, encoding) {
    const endTime = process.hrtime.bigint();
    const responseTime = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    
    // Log slow requests
    const slowThreshold = 1000; // 1 second
    if (responseTime > slowThreshold) {
      console.warn(`🐌 SLOW REQUEST: ${req.method} ${req.originalUrl} - ${responseTime.toFixed(2)}ms`);
      
      // Log slow request to database
      logUserAction('slow_request', 'performance', req.requestId, {
        responseTime: `${responseTime.toFixed(2)}ms`,
        threshold: `${slowThreshold}ms`,
        method: req.method,
        url: req.originalUrl
      }, req).catch(console.error);
    }
    
    // Add performance headers
    res.set('X-Response-Time', `${responseTime.toFixed(2)}ms`);
    res.set('X-Request-ID', req.requestId);
    
    // Call original end method
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
}

/**
 * API usage analytics
 */
async function trackAPIUsage(endpoint, method, userId = null, success = true) {
  try {
    // This could be expanded to track API usage statistics
    // For now, we'll just log it as a user action
    await logUserAction('api_usage', 'endpoint', endpoint, {
      method,
      success,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Failed to track API usage:', error.message);
  }
}

/**
 * Clean up old logs
 */
async function cleanupOldLogs(daysToKeep = 30) {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    const { rows } = await executeQuery(
      'DELETE FROM activity_logs WHERE created_at < ?',
      [cutoffDate]
    );
    
    const deletedCount = rows.affectedRows || 0;
    console.log(`🧹 Cleaned up old logs: ${deletedCount} records deleted (older than ${daysToKeep} days)`);
    
    return deletedCount;
    
  } catch (error) {
    console.error('❌ Failed to cleanup old logs:', error.message);
    throw error;
  }
}

module.exports = {
  requestLogger,
  performanceMonitor,
  logSecurityEvent,
  logUserAction,
  trackAPIUsage,
  cleanupOldLogs,
  generateRequestId,
  getClientIP
};
