#!/usr/bin/env node

/**
 * Data Migration Script for Khanfashariya.com
 * 
 * This script migrates data from Local Storage format to MySQL database.
 * It handles:
 * - User data migration
 * - System services migration
 * - Technical services migration
 * - Premium content migration
 * - Orders and subscriptions migration
 * - Messages and notifications migration
 * 
 * Usage: node scripts/migrate-data.js [--source=path] [--dry-run]
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'kfs_user',
  password: process.env.DB_PASSWORD || 'kfs_2024_secure',
  database: process.env.DB_NAME || 'khanfashariya_db',
  charset: 'utf8mb4'
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * Generate UUID for database records
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Load complete actual data from database.ts (all 9 systems + 11 services + 1 premium)
 */
function loadCompleteActualData() {
  const now = new Date().toISOString().slice(0, 19).replace('T', ' ');

  return {
    users: [
      {
        id: 'admin-1',
        email: '<EMAIL>',
        username: 'admin',
        full_name: 'مدير النظام',
        role: 'admin',
        password_hash: '$2a$10$8LkdsSeG0tCzKmV7XxMquufSu1AvSd9NNiQYSn4I2cEOZS84NwumO', // admin123
        created_at: now,
        updated_at: now
      },
      {
        id: 'test-user-1',
        email: '<EMAIL>',
        username: 'testuser',
        full_name: 'مستخدم تجريبي',
        role: 'user',
        password_hash: '$2a$10$clYD3lXMflL7iJlLF5eyk.uIMKOH.l7uzZbPbYS8Ers5XDVJvNyKy', // test123
        created_at: now,
        updated_at: now
      }
    ],
    systemServices: [
      {
        id: 'guildWar',
        name: { ar: 'نظام حروب الروابط التلقائي', en: 'Automatic Guild War System' },
        description: { ar: 'نظام حروب الروابط التلقائي مع ذكاء اصطناعي متطور لإدارة المعارك والتحكم في الاستراتيجيات', en: 'Automatic Guild War system with advanced AI for battle management and strategic control' },
        price: 299,
        category: 'combat',
        type: 'regular',
        features: { ar: ['ذكاء اصطناعي متطور', 'إدارة تلقائية', 'تحليل المعارك', 'نظام إنذار متقدم'], en: ['Advanced AI', 'Automatic management', 'Battle analysis', 'Advanced alert system'] },
        image_url: 'https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg?auto=compress&cs=tinysrgb&w=800',
        video_url: 'https://www.youtube.com/watch?v=example1',
        tech_specs: { ar: ['دعم خوادم متعددة', 'واجهة إدارة متقدمة', 'تقارير مفصلة', 'نظام حماية متطور'], en: ['Multi-server support', 'Advanced management interface', 'Detailed reports', 'Advanced protection system'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'duel',
        name: { ar: 'نظام المبارزات التلقائي', en: 'Automatic Duel System' },
        description: { ar: 'نظام مبارزات تقني متطور مع خوارزميات القتال المستقبلية وتحليل الأداء', en: 'Advanced technical duel system with futuristic combat algorithms and performance analysis' },
        price: 249,
        category: 'combat',
        type: 'regular',
        features: { ar: ['خوارزميات قتال', 'نظام ترتيب ذكي', 'مكافآت تلقائية', 'تحليل الأداء'], en: ['Combat algorithms', 'Smart ranking system', 'Automatic rewards', 'Performance analysis'] },
        image_url: 'https://images.pexels.com/photos/442576/pexels-photo-442576.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['نظام مطابقة ذكي', 'إحصائيات مفصلة', 'مكافآت متدرجة', 'حماية من الغش'], en: ['Smart matching system', 'Detailed statistics', 'Progressive rewards', 'Anti-cheat protection'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'contest',
        name: { ar: 'نظام المسابقات التلقائي', en: 'Automatic Contest System' },
        description: { ar: 'نظام مسابقات متطور مع تقنيات المراقبة والتحكم المتقدمة وإدارة الجوائز', en: 'Advanced contest system with monitoring and control technologies and prize management' },
        price: 279,
        category: 'events',
        type: 'regular',
        features: { ar: ['مراقبة متقدمة', 'إدارة المشاركين', 'نظام جوائز ذكي', 'تقارير مفصلة'], en: ['Advanced monitoring', 'Participant management', 'Smart prize system', 'Detailed reports'] },
        image_url: 'https://images.pexels.com/photos/1591056/pexels-photo-1591056.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['أنواع مسابقات متعددة', 'نظام تسجيل تلقائي', 'إدارة الجوائز', 'تقارير في الوقت الفعلي'], en: ['Multiple contest types', 'Automatic registration', 'Prize management', 'Real-time reports'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'wiki',
        name: { ar: 'نظام الويكي التقني', en: 'Technical Wiki System' },
        description: { ar: 'نظام ويكي تقني داخل اللعبة بواجهة عربية مستقبلية ومحرر متطور', en: 'Technical wiki system in-game with futuristic Arabic interface and advanced editor' },
        price: 399,
        category: 'utility',
        type: 'plugin',
        features: { ar: ['واجهة عربية', 'محرر متقدم', 'بحث ذكي', 'تصنيف تلقائي'], en: ['Arabic interface', 'Advanced editor', 'Smart search', 'Automatic categorization'] },
        image_url: 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['محرر WYSIWYG', 'دعم الوسائط المتعددة', 'نظام صلاحيات', 'تاريخ التعديلات'], en: ['WYSIWYG editor', 'Multimedia support', 'Permission system', 'Edit history'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'autoShop',
        name: { ar: 'نظام المتجر الآلي', en: 'Automatic Shop System' },
        description: { ar: 'متجر آلي مع ذكاء اصطناعي لإدارة المخزون والأسعار والمبيعات', en: 'Automatic shop with AI for inventory, pricing and sales management' },
        price: 349,
        category: 'economy',
        type: 'plugin',
        features: { ar: ['ذكاء اصطناعي', 'إدارة مخزون', 'تسعير ديناميكي', 'تحليل مبيعات'], en: ['Artificial intelligence', 'Inventory management', 'Dynamic pricing', 'Sales analysis'] },
        image_url: 'https://images.pexels.com/photos/230544/pexels-photo-230544.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['تحليل السوق', 'تنبيهات المخزون', 'تقارير المبيعات', 'إدارة العملاء'], en: ['Market analysis', 'Inventory alerts', 'Sales reports', 'Customer management'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'petLevel',
        name: { ar: 'نظام الرفيق المتطور', en: 'Advanced Companion System' },
        description: { ar: 'نظام رفيق متطور مع تقنيات التطوير الذاتي والذكاء التكيفي', en: 'Advanced companion system with self-development technologies and adaptive intelligence' },
        price: 299,
        category: 'gameplay',
        type: 'regular',
        features: { ar: ['تطوير ذاتي', 'مهارات متقدمة', 'ذكاء تكيفي', 'نظام ولاء'], en: ['Self-development', 'Advanced skills', 'Adaptive intelligence', 'Loyalty system'] },
        image_url: 'https://images.pexels.com/photos/33053/dog-young-dog-small-dog-maltese.jpg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['تعلم تكيفي', 'مهارات قابلة للتخصيص', 'نظام عواطف', 'تطوير تلقائي'], en: ['Adaptive learning', 'Customizable skills', 'Emotion system', 'Automatic development'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'autoAttack',
        name: { ar: 'نظام الهجوم التلقائي', en: 'Auto Attack System' },
        description: { ar: 'نظام هجوم تلقائي ذكي مع تقنيات التعلم الآلي والتكيف', en: 'Intelligent auto-attack system with machine learning and adaptation technologies' },
        price: 269,
        category: 'combat',
        type: 'regular',
        features: { ar: ['تعلم آلي', 'هجوم ذكي', 'تكيف تلقائي', 'تحسين مستمر'], en: ['Machine learning', 'Smart attack', 'Auto adaptation', 'Continuous improvement'] },
        image_url: 'https://images.pexels.com/photos/1670977/pexels-photo-1670977.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['تحليل الأعداء', 'استراتيجيات متكيفة', 'تحسين الأداء', 'حماية من الاكتشاف'], en: ['Enemy analysis', 'Adaptive strategies', 'Performance optimization', 'Detection protection'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'quickSwitch',
        name: { ar: 'نظام تبديل الأدوات السريع', en: 'Quick Tool Switch System' },
        description: { ar: 'نظام تبديل أدوات فوري مع تقنيات الاستجابة السريعة والذاكرة التكيفية', en: 'Instant tool switching system with fast response technologies and adaptive memory' },
        price: 199,
        category: 'utility',
        type: 'regular',
        features: { ar: ['استجابة فورية', 'تبديل ذكي', 'ذاكرة تكيفية', 'واجهة سهلة'], en: ['Instant response', 'Smart switching', 'Adaptive memory', 'Easy interface'] },
        image_url: 'https://images.pexels.com/photos/414102/pexels-photo-414102.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['تبديل فوري', 'حفظ التفضيلات', 'واجهة قابلة للتخصيص', 'دعم الاختصارات'], en: ['Instant switching', 'Preference saving', 'Customizable interface', 'Shortcut support'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'itemFilter',
        name: { ar: 'نظام فلتر المعدات المتقدم', en: 'Advanced Equipment Filter' },
        description: { ar: 'نظام فلتر معدات متقدم مع خوارزميات التصنيف الذكية والبحث السريع', en: 'Advanced equipment filter system with smart sorting algorithms and quick search' },
        price: 229,
        category: 'utility',
        type: 'regular',
        features: { ar: ['تصنيف ذكي', 'فلترة متقدمة', 'بحث سريع', 'حفظ تفضيلات'], en: ['Smart sorting', 'Advanced filtering', 'Quick search', 'Save preferences'] },
        image_url: 'https://images.pexels.com/photos/669619/pexels-photo-669619.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['فلاتر متعددة', 'بحث متقدم', 'ترتيب تلقائي', 'حفظ عمليات البحث'], en: ['Multiple filters', 'Advanced search', 'Auto-sorting', 'Save searches'] },
        status: 'active',
        created_at: now,
        updated_at: now
      }
    ],
    technicalServices: [
      {
        id: 'crashFix',
        name: { ar: 'إصلاح الأعطال والكراشات', en: 'Bug Fixes and Crash Resolution' },
        description: { ar: 'إصلاح الأعطال الحرجة والثغرات الأمنية باستخدام تقنيات الحماية المتقدمة', en: 'Fixing critical bugs and security vulnerabilities using advanced protection techniques' },
        price: 150,
        category: 'maintenance',
        isPremiumAddon: true,
        premiumPrice: 100,
        subscriptionType: 'none',
        features: { ar: ['إصلاح سريع', 'تحليل الأخطاء', 'حماية متقدمة', 'ضمان الجودة'], en: ['Quick Fixes', 'Error Analysis', 'Advanced Protection', 'Quality Assurance'] },
        image_url: 'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['تشخيص متقدم', 'إصلاح فوري', 'اختبار شامل', 'دعم مستمر'], en: ['Advanced Diagnostics', 'Immediate Fixes', 'Comprehensive Testing', 'Ongoing Support'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'customization',
        name: { ar: 'تعديل وتخصيص أكواد النظام', en: 'System Code Modification' },
        description: { ar: 'تعديل وتخصيص أكواد النظام والأنظمة حسب متطلبات المشروع', en: 'Modification and customization of system codes according to project requirements' },
        price: 200,
        category: 'development',
        isPremiumAddon: true,
        premiumPrice: 150,
        subscriptionType: 'none',
        features: { ar: ['تخصيص كامل', 'برمجة متقدمة', 'تحسين الأداء', 'دعم فني'], en: ['Full Customization', 'Advanced Programming', 'Performance Optimization', 'Technical Support'] },
        image_url: 'https://images.pexels.com/photos/1181677/pexels-photo-1181677.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['كود نظيف', 'توثيق شامل', 'اختبار متقدم', 'صيانة مستمرة'], en: ['Clean Code', 'Comprehensive Documentation', 'Advanced Testing', 'Continuous Maintenance'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'customSystems',
        name: { ar: 'تصميم أنظمة تقنية خاصة', en: 'Custom System Design' },
        description: { ar: 'تصميم وتطوير أنظمة تقنية مخصصة بأحدث التقنيات', en: 'Design and development of custom technical systems with the latest technologies' },
        price: 500,
        category: 'development',
        isPremiumAddon: false,
        premiumPrice: 0,
        subscriptionType: 'none',
        features: { ar: ['تصميم مخصص', 'تقنيات حديثة', 'أداء عالي', 'قابلية التوسع'], en: ['Custom Design', 'Modern Technologies', 'High Performance', 'Scalability'] },
        image_url: 'https://images.pexels.com/photos/1181244/pexels-photo-1181244.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['هندسة متقدمة', 'أمان عالي', 'واجهات حديثة', 'تكامل سلس'], en: ['Advanced Architecture', 'High Security', 'Modern Interfaces', 'Seamless Integration'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'installation',
        name: { ar: 'تنصيب المحتويات والأدوات', en: 'Content and Tool Installation' },
        description: { ar: 'تنصيب المعدات والأدوات الرقمية بسرعة البرق واحترافية عالية', en: 'Installation of digital content and tools with lightning speed and high professionalism' },
        price: 100,
        category: 'installation',
        isPremiumAddon: true,
        premiumPrice: 0,
        subscriptionType: 'none',
        features: { ar: ['تنصيب سريع', 'إعداد شامل', 'اختبار كامل', 'دعم فوري'], en: ['Fast Installation', 'Comprehensive Setup', 'Full Testing', 'Immediate Support'] },
        image_url: 'https://images.pexels.com/photos/1181263/pexels-photo-1181263.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['تنصيب آمن', 'إعداد مُحسن', 'توثيق مفصل', 'دعم تقني'], en: ['Secure Installation', 'Optimized Setup', 'Detailed Documentation', 'Technical Support'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'translation',
        name: { ar: 'ترجمة أنظمة النظام إلى العربية', en: 'System Translation to Arabic' },
        description: { ar: 'ترجمة أنظمة النظام والواجهات إلى العربية مع الحفاظ على الطابع التقني', en: 'Translation of system interfaces to Arabic while maintaining the technical character' },
        price: 120,
        category: 'localization',
        isPremiumAddon: true,
        premiumPrice: 80,
        subscriptionType: 'none',
        features: { ar: ['ترجمة احترافية', 'دعم RTL', 'خطوط عربية', 'واجهات محلية'], en: ['Professional Translation', 'RTL Support', 'Arabic Fonts', 'Localized Interfaces'] },
        image_url: 'https://images.pexels.com/photos/1181406/pexels-photo-1181406.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['ترجمة دقيقة', 'تخطيط RTL', 'خطوط محسنة', 'اختبار شامل'], en: ['Accurate Translation', 'RTL Layout', 'Optimized Fonts', 'Comprehensive Testing'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'questDev',
        name: { ar: 'تطوير المهام والأنظمة التفاعلية', en: 'Quest & Interactive Systems Dev' },
        description: { ar: 'تطوير المهام والأنظمة التفاعلية المتقدمة', en: 'Development of advanced quests and interactive systems' },
        price: 300,
        category: 'development',
        isPremiumAddon: false,
        premiumPrice: 0,
        subscriptionType: 'none',
        features: { ar: ['مهام تفاعلية', 'قصص متطورة', 'مكافآت ذكية', 'تجربة غامرة'], en: ['Interactive Quests', 'Advanced Storylines', 'Smart Rewards', 'Immersive Experience'] },
        image_url: 'https://images.pexels.com/photos/1181354/pexels-photo-1181354.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['محرك مهام', 'نظام مكافآت', 'واجهات تفاعلية', 'تتبع التقدم'], en: ['Quest Engine', 'Reward System', 'Interactive UIs', 'Progress Tracking'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'freebsd',
        name: { ar: 'إعداد خوادم FreeBSD المحسنة', en: 'Optimized FreeBSD Server Setup' },
        description: { ar: 'إعداد وتحصين خوادم FreeBSD للحماية القصوى', en: 'Setup and hardening of FreeBSD servers for maximum protection' },
        price: 250,
        category: 'infrastructure',
        isPremiumAddon: true,
        premiumPrice: 0,
        subscriptionType: 'none',
        features: { ar: ['أمان عالي', 'أداء محسن', 'استقرار عالي', 'مراقبة مستمرة'], en: ['High Security', 'Optimized Performance', 'High Stability', 'Continuous Monitoring'] },
        image_url: 'https://images.pexels.com/photos/1181316/pexels-photo-1181316.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['تحصين متقدم', 'تحسين الأداء', 'مراقبة النظام', 'نسخ احتياطية'], en: ['Advanced Hardening', 'Performance Tuning', 'System Monitoring', 'Backups'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'scripts',
        name: { ar: 'سكربتات الحماية والمراقبة', en: 'Protection & Monitoring Scripts' },
        description: { ar: 'سكربتات حماية ومراقبة متقدمة للأمان الرقمي', en: 'Advanced protection and monitoring scripts for digital security' },
        price: 180,
        category: 'security',
        isPremiumAddon: true,
        premiumPrice: 120,
        subscriptionType: 'none',
        features: { ar: ['حماية متقدمة', 'مراقبة فورية', 'تنبيهات ذكية', 'تقارير مفصلة'], en: ['Advanced Protection', 'Real-time Monitoring', 'Smart Alerts', 'Detailed Reports'] },
        image_url: 'https://images.pexels.com/photos/1181467/pexels-photo-1181467.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['كشف التهديدات', 'حماية تلقائية', 'تحليل السلوك', 'استجابة سريعة'], en: ['Threat Detection', 'Automatic Protection', 'Behavior Analysis', 'Fast Response'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'sourceCode',
        name: { ar: 'أكواد مصدرية محسنة ومضمونة', en: 'Optimized & Guaranteed Source Code' },
        description: { ar: 'أكواد مصدرية محسنة ومضمونة العمل بأعلى معايير الأمان', en: 'Optimized source code guaranteed to work with the highest security standards' },
        price: 400,
        category: 'development',
        isPremiumAddon: false,
        premiumPrice: 0,
        subscriptionType: 'none',
        features: { ar: ['كود محسن', 'أمان عالي', 'أداء ممتاز', 'ضمان الجودة'], en: ['Optimized Code', 'High Security', 'Excellent Performance', 'Quality Assurance'] },
        image_url: 'https://images.pexels.com/photos/1181373/pexels-photo-1181373.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['معايير عالية', 'اختبار شامل', 'توثيق كامل', 'دعم مستمر'], en: ['High Standards', 'Comprehensive Testing', 'Full Documentation', 'Ongoing Support'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'monthlyMaintenance',
        name: { ar: 'صيانة شهرية للخوادم', en: 'Monthly Server Maintenance' },
        description: { ar: 'خدمة صيانة شهرية شاملة للخوادم مع مراقبة مستمرة وتحديثات أمنية', en: 'Comprehensive monthly server maintenance with continuous monitoring and security updates' },
        price: 50,
        category: 'maintenance',
        isPremiumAddon: true,
        premiumPrice: 30,
        subscriptionType: 'monthly',
        features: { ar: ['مراقبة 24/7', 'تحديثات أمنية', 'نسخ احتياطية', 'تقارير شهرية'], en: ['24/7 Monitoring', 'Security Updates', 'Backups', 'Monthly Reports'] },
        image_url: 'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['مراقبة مستمرة', 'تحديثات تلقائية', 'نسخ احتياطية يومية', 'دعم فني'], en: ['Continuous Monitoring', 'Automatic Updates', 'Daily Backups', 'Technical Support'] },
        status: 'active',
        created_at: now,
        updated_at: now
      },
      {
        id: 'yearlySupport',
        name: { ar: 'دعم فني سنوي متقدم', en: 'Advanced Annual Technical Support' },
        description: { ar: 'دعم فني متقدم على مدار السنة مع أولوية في الاستجابة وحلول مخصصة', en: 'Advanced technical support throughout the year with priority response and custom solutions' },
        price: 500,
        category: 'support',
        isPremiumAddon: true,
        premiumPrice: 300,
        subscriptionType: 'yearly',
        features: { ar: ['دعم أولوية', 'استجابة سريعة', 'حلول مخصصة', 'تدريب مجاني'], en: ['Priority Support', 'Fast Response', 'Custom Solutions', 'Free Training'] },
        image_url: 'https://images.pexels.com/photos/1181677/pexels-photo-1181677.jpeg?auto=compress&cs=tinysrgb&w=800',
        tech_specs: { ar: ['استجابة خلال ساعة', 'دعم متعدد القنوات', 'جلسات تدريب', 'تقارير ربع سنوية'], en: ['1-hour Response', 'Multi-channel Support', 'Training Sessions', 'Quarterly Reports'] },
        status: 'active',
        created_at: now,
        updated_at: now
      }
    ],
    premiumContent: [
      {
        id: 'premium_complete_package',
        name: { ar: 'النسخة الشاملة المميزة', en: 'Premium Complete Package' },
        description: { ar: 'نسخة شاملة تحتوي على جميع الأنظمة والملفات', en: 'A comprehensive version containing all systems and files' },
        detailed_description: { ar: 'النسخة الشاملة المميزة تشمل ملفات الكلاينت والسيرفر مع تنصيب اللعبة على نظام FreeBSD محسن للأداء العالي والاستقرار. تحتوي على جميع الأنظمة المتقدمة مع حماية قصوى ضد الاختراق.', en: 'The premium comprehensive version includes client and server files with the game installed on a FreeBSD system optimized for high performance and stability. It contains all advanced systems with maximum protection against hacking.' },
        features: {
          ar: ['ملفات كلاينت كاملة', 'ملفات سيرفر محسنة', 'تنصيب على FreeBSD', 'جميع الأنظمة المتقدمة', 'حماية قصوى', 'دعم فني مدى الحياة'],
          en: ['Full client files', 'Optimized server files', 'Installation on FreeBSD', 'All advanced systems', 'Maximum protection', 'Lifetime technical support']
        },
        technical_specs: {
          ar: ['نظام FreeBSD 13.2', 'MySQL 8.0 محسن', 'Python 3.11 للأنظمة', 'حماية DDoS متقدمة'],
          en: ['FreeBSD 13.2 system', 'Optimized MySQL 8.0', 'Python 3.11 for systems', 'Advanced DDoS protection']
        },
        images: [
          'https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=800',
          'https://images.pexels.com/photos/1181677/pexels-photo-1181677.jpeg?auto=compress&cs=tinysrgb&w=800'
        ],
        videos: [
          'https://www.youtube.com/watch?v=example1',
          'https://www.youtube.com/watch?v=example2'
        ],
        price: 1999,
        original_price: 2999,
        category: 'complete_package',
        status: 'active',
        created_at: now,
        updated_at: now
      }
    ],
    orders: [],
    subscriptions: [],
    inboxMessages: [],
    contactMessages: []
  };
}

/**
 * Migrate users data
 */
async function migrateUsers(connection, users) {
  log('📤 Migrating users...', 'blue');
  
  let migratedCount = 0;
  
  for (const user of users) {
    try {
      // Check if user already exists
      const [existing] = await connection.execute(
        'SELECT id FROM users WHERE email = ? OR username = ?',
        [user.email, user.username]
      );
      
      if (existing.length > 0) {
        log(`   ⚠️  User ${user.username} already exists, skipping...`, 'yellow');
        continue;
      }
      
      // Insert user
      await connection.execute(`
        INSERT INTO users (
          id, email, username, full_name, role, password_hash, 
          status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, 'active', ?, ?)
      `, [
        user.id || generateUUID(),
        user.email,
        user.username,
        user.full_name,
        user.role || 'user',
        user.password_hash,
        user.created_at ? new Date(user.created_at).toISOString().slice(0, 19).replace('T', ' ') : new Date().toISOString().slice(0, 19).replace('T', ' '),
        user.updated_at ? new Date(user.updated_at).toISOString().slice(0, 19).replace('T', ' ') : new Date().toISOString().slice(0, 19).replace('T', ' ')
      ]);
      
      migratedCount++;
      log(`   ✅ Migrated user: ${user.username}`, 'green');
      
    } catch (error) {
      log(`   ❌ Failed to migrate user ${user.username}: ${error.message}`, 'red');
    }
  }
  
  log(`📊 Users migration completed: ${migratedCount} users migrated`, 'cyan');
  return migratedCount;
}

/**
 * Migrate system services data
 */
async function migrateSystemServices(connection, services) {
  log('📤 Migrating system services...', 'blue');
  
  let migratedCount = 0;
  
  for (const service of services) {
    try {
      // Check if service already exists
      const [existing] = await connection.execute(
        'SELECT id FROM system_services WHERE id = ?',
        [service.id]
      );
      
      if (existing.length > 0) {
        log(`   ⚠️  System service ${service.id} already exists, skipping...`, 'yellow');
        continue;
      }
      
      // Insert system service
      await connection.execute(`
        INSERT INTO system_services (
          id, name_ar, name_en, description_ar, description_en, price, category, type,
          features_ar, features_en, tech_specs_ar, tech_specs_en, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        service.id || generateUUID(),
        service.name?.ar || service.name || '',
        service.name?.en || service.name || '',
        service.description?.ar || service.description || '',
        service.description?.en || service.description || '',
        service.price || 0,
        service.category || 'General',
        service.type || 'regular',
        JSON.stringify(service.features?.ar || []),
        JSON.stringify(service.features?.en || []),
        JSON.stringify(service.tech_specs?.ar || []),
        JSON.stringify(service.tech_specs?.en || []),
        service.status || 'active',
        service.created_at ? new Date(service.created_at).toISOString().slice(0, 19).replace('T', ' ') : new Date().toISOString().slice(0, 19).replace('T', ' '),
        service.updated_at ? new Date(service.updated_at).toISOString().slice(0, 19).replace('T', ' ') : new Date().toISOString().slice(0, 19).replace('T', ' ')
      ]);
      
      migratedCount++;
      log(`   ✅ Migrated system service: ${service.name?.en || service.name}`, 'green');
      
    } catch (error) {
      log(`   ❌ Failed to migrate system service ${service.id}: ${error.message}`, 'red');
    }
  }
  
  log(`📊 System services migration completed: ${migratedCount} services migrated`, 'cyan');
  return migratedCount;
}

/**
 * Migrate technical services data
 */
async function migrateTechnicalServices(connection, services) {
  log('📤 Migrating technical services...', 'blue');
  
  let migratedCount = 0;
  
  for (const service of services) {
    try {
      // Check if service already exists
      const [existing] = await connection.execute(
        'SELECT id FROM technical_services WHERE id = ?',
        [service.id]
      );
      
      if (existing.length > 0) {
        log(`   ⚠️  Technical service ${service.id} already exists, skipping...`, 'yellow');
        continue;
      }
      
      // Insert technical service
      await connection.execute(`
        INSERT INTO technical_services (
          id, name_ar, name_en, description_ar, description_en, price, category, service_type,
          features_ar, features_en, delivery_time_ar, delivery_time_en, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        service.id || generateUUID(),
        service.name?.ar || service.name || '',
        service.name?.en || service.name || '',
        service.description?.ar || service.description || '',
        service.description?.en || service.description || '',
        service.price || 0,
        service.category || 'General',
        service.service_type || 'development',
        JSON.stringify(service.features?.ar || []),
        JSON.stringify(service.features?.en || []),
        service.delivery_time?.ar || '',
        service.delivery_time?.en || '',
        service.status || 'active',
        service.created_at ? new Date(service.created_at).toISOString().slice(0, 19).replace('T', ' ') : new Date().toISOString().slice(0, 19).replace('T', ' '),
        service.updated_at ? new Date(service.updated_at).toISOString().slice(0, 19).replace('T', ' ') : new Date().toISOString().slice(0, 19).replace('T', ' ')
      ]);
      
      migratedCount++;
      log(`   ✅ Migrated technical service: ${service.name?.en || service.name}`, 'green');
      
    } catch (error) {
      log(`   ❌ Failed to migrate technical service ${service.id}: ${error.message}`, 'red');
    }
  }
  
  log(`📊 Technical services migration completed: ${migratedCount} services migrated`, 'cyan');
  return migratedCount;
}

/**
 * Migrate premium content data
 */
async function migratePremiumContent(connection, content) {
  log('📤 Migrating premium content...', 'blue');
  
  let migratedCount = 0;
  
  for (const item of content) {
    try {
      // Check if content already exists
      const [existing] = await connection.execute(
        'SELECT id FROM premium_content WHERE id = ?',
        [item.id]
      );
      
      if (existing.length > 0) {
        log(`   ⚠️  Premium content ${item.id} already exists, skipping...`, 'yellow');
        continue;
      }
      
      // Insert premium content
      await connection.execute(`
        INSERT INTO premium_content (
          id, title_ar, title_en, description_ar, description_en, price, category,
          features_ar, features_en, included_systems, included_services, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        item.id || generateUUID(),
        item.title?.ar || item.title || '',
        item.title?.en || item.title || '',
        item.description?.ar || item.description || '',
        item.description?.en || item.description || '',
        item.price || 0,
        item.category || 'Premium',
        JSON.stringify(item.features?.ar || []),
        JSON.stringify(item.features?.en || []),
        JSON.stringify(item.included_systems || []),
        JSON.stringify(item.included_services || []),
        item.status || 'active',
        item.created_at ? new Date(item.created_at).toISOString().slice(0, 19).replace('T', ' ') : new Date().toISOString().slice(0, 19).replace('T', ' '),
        item.updated_at ? new Date(item.updated_at).toISOString().slice(0, 19).replace('T', ' ') : new Date().toISOString().slice(0, 19).replace('T', ' ')
      ]);
      
      migratedCount++;
      log(`   ✅ Migrated premium content: ${item.title?.en || item.title}`, 'green');
      
    } catch (error) {
      log(`   ❌ Failed to migrate premium content ${item.id}: ${error.message}`, 'red');
    }
  }
  
  log(`📊 Premium content migration completed: ${migratedCount} items migrated`, 'cyan');
  return migratedCount;
}

/**
 * Clear existing data from database
 */
async function clearExistingData(connection) {
  log('🗑️  Clearing existing data...', 'yellow');

  try {
    // Clear in correct order to avoid foreign key constraints
    await connection.execute('DELETE FROM premium_content');
    await connection.execute('DELETE FROM technical_services');
    await connection.execute('DELETE FROM system_services');
    // Keep users as they might have important data

    log('✅ Existing data cleared successfully', 'green');
  } catch (error) {
    log(`❌ Error clearing data: ${error.message}`, 'red');
    throw error;
  }
}

/**
 * Main migration function
 */
async function migrateData(sourceData = null, dryRun = false, clearFirst = true) {
  let connection;

  try {
    log('🚀 Starting Khanfashariya Complete Data Migration...', 'bright');

    if (dryRun) {
      log('🔍 DRY RUN MODE - No data will be actually migrated', 'yellow');
    }

    // Load data
    const data = sourceData || loadCompleteActualData();
    
    log('📊 Data summary:', 'cyan');
    log(`   Users: ${data.users?.length || 0}`, 'cyan');
    log(`   System Services: ${data.systemServices?.length || 0}`, 'cyan');
    log(`   Technical Services: ${data.technicalServices?.length || 0}`, 'cyan');
    log(`   Premium Content: ${data.premiumContent?.length || 0}`, 'cyan');
    log(`   Orders: ${data.orders?.length || 0}`, 'cyan');
    log(`   Messages: ${(data.inboxMessages?.length || 0) + (data.contactMessages?.length || 0)}`, 'cyan');
    
    if (dryRun) {
      log('✅ Dry run completed - data structure validated', 'green');
      return;
    }
    
    // Connect to database
    log('🔌 Connecting to MySQL database...', 'blue');
    connection = await mysql.createConnection(dbConfig);
    
    // Test connection
    await connection.execute('SELECT 1');
    log('✅ Database connection established', 'green');

    // Clear existing data if requested
    if (clearFirst) {
      await clearExistingData(connection);
    }

    // Start migration
    const results = {
      users: 0,
      systemServices: 0,
      technicalServices: 0,
      premiumContent: 0,
      orders: 0,
      messages: 0
    };
    
    // Migrate users
    if (data.users && data.users.length > 0) {
      results.users = await migrateUsers(connection, data.users);
    }
    
    // Migrate system services
    if (data.systemServices && data.systemServices.length > 0) {
      results.systemServices = await migrateSystemServices(connection, data.systemServices);
    }
    
    // Migrate technical services
    if (data.technicalServices && data.technicalServices.length > 0) {
      results.technicalServices = await migrateTechnicalServices(connection, data.technicalServices);
    }
    
    // Migrate premium content
    if (data.premiumContent && data.premiumContent.length > 0) {
      results.premiumContent = await migratePremiumContent(connection, data.premiumContent);
    }
    
    // Migration completed
    log('🎉 Data migration completed successfully!', 'green');
    log('📋 Migration Summary:', 'bright');
    log(`   ✅ Users migrated: ${results.users}`, 'green');
    log(`   ✅ System services migrated: ${results.systemServices}`, 'green');
    log(`   ✅ Technical services migrated: ${results.technicalServices}`, 'green');
    log(`   ✅ Premium content migrated: ${results.premiumContent}`, 'green');
    
    const totalMigrated = Object.values(results).reduce((sum, count) => sum + count, 0);
    log(`   🎯 Total records migrated: ${totalMigrated}`, 'bright');
    
    log('🔧 Next steps:', 'yellow');
    log('   1. Run: npm run dev:full (to start development servers)', 'yellow');
    log('   2. Visit: http://localhost:5173 (frontend)', 'yellow');
    log('   3. Login with: <EMAIL> / admin123', 'yellow');
    log('   4. Test the migrated data in the admin panel', 'yellow');
    
    return results;
    
  } catch (error) {
    log(`💥 Migration failed: ${error.message}`, 'red');
    log('🔧 Troubleshooting:', 'yellow');
    log('   1. Make sure database schema is set up (run: npm run setup:db)', 'yellow');
    log('   2. Check database connection settings in .env file', 'yellow');
    log('   3. Ensure MySQL/WampServer is running', 'yellow');
    
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      log('🔌 Database connection closed', 'blue');
    }
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');
  
  migrateData(null, dryRun).catch(error => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
}

module.exports = { migrateData, loadCompleteActualData };
