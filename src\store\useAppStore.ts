import { create } from 'zustand';
import { persist } from 'zustand/middleware';
// User interface defined locally to avoid database dependency
interface User {
  id: string;
  email: string;
  username: string;
  full_name: string;
  role: 'user' | 'admin';
  password_hash: string;
  created_at: string;
  updated_at: string;
}

// Notification interface
interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  timestamp: number;
  read: boolean;
  actionUrl?: string;
  actionLabel?: string;
}

// UI state interface
interface UIState {
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark';
  language: 'ar' | 'en';
  loading: boolean;
  mobileMenuOpen: boolean;
}

// App state interface
interface AppState {
  // User state
  user: User | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
  
  // UI state
  ui: UIState;
  
  // Notifications
  notifications: Notification[];
  unreadNotificationsCount: number;
  
  // Cache
  cache: Record<string, any>;
  
  // Actions
  setUser: (user: User | null) => void;
  logout: () => void;
  
  // UI actions
  setSidebarCollapsed: (collapsed: boolean) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  setLanguage: (language: 'ar' | 'en') => void;
  setLoading: (loading: boolean) => void;
  setMobileMenuOpen: (open: boolean) => void;
  
  // Notification actions
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  removeNotification: (id: string) => void;
  markNotificationAsRead: (id: string) => void;
  markAllNotificationsAsRead: () => void;
  clearNotifications: () => void;
  
  // Cache actions
  setCache: (key: string, value: any) => void;
  getCache: (key: string) => any;
  clearCache: (key?: string) => void;
  
  // Utility actions
  reset: () => void;
}

/**
 * Main application store using Zustand
 *
 * Features:
 * - Persistent storage for user preferences
 * - Notification management
 * - UI state management
 * - Cache management
 * - Type-safe actions and state
 * - Automatic persistence of important data
 */
const useAppStore = create<AppState>((set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isAdmin: false,
      
      ui: {
        sidebarCollapsed: false,
        theme: 'dark',
        language: 'ar',
        loading: false,
        mobileMenuOpen: false
      },
      
      notifications: [],
      unreadNotificationsCount: 0,
      
      cache: {},
      
      // User actions
      setUser: (user) => {
        set({
          user,
          isAuthenticated: !!user,
          isAdmin: user?.role === 'admin'
        });
      },
      
      logout: () => {
        set({
          user: null,
          isAuthenticated: false,
          isAdmin: false,
          notifications: [],
          unreadNotificationsCount: 0,
          cache: {}
        });
        
        // Clear localStorage items
        localStorage.removeItem('khanfashariya_current_user');
        localStorage.removeItem('khanfashariya_auth_token');
      },
      
      // UI actions
      setSidebarCollapsed: (collapsed) => {
        set((state) => ({
          ui: { ...state.ui, sidebarCollapsed: collapsed }
        }));
      },
      
      setTheme: (theme) => {
        set((state) => ({
          ui: { ...state.ui, theme }
        }));
        
        // Update document class for theme
        document.documentElement.classList.toggle('dark', theme === 'dark');
      },
      
      setLanguage: (language) => {
        set((state) => ({
          ui: { ...state.ui, language }
        }));
        
        // Update document direction
        document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
        document.documentElement.lang = language;
      },
      
      setLoading: (loading) => {
        set((state) => ({
          ui: { ...state.ui, loading }
        }));
      },
      
      setMobileMenuOpen: (open) => {
        set((state) => ({
          ui: { ...state.ui, mobileMenuOpen: open }
        }));
      },
      
      // Notification actions
      addNotification: (notification) => {
        const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
        const newNotification: Notification = {
          ...notification,
          id,
          timestamp: Date.now(),
          read: false
        };
        
        set((state) => ({
          notifications: [newNotification, ...state.notifications],
          unreadNotificationsCount: state.unreadNotificationsCount + 1
        }));
        
        // Auto-remove notification after duration
        if (notification.duration !== 0) {
          setTimeout(() => {
            get().removeNotification(id);
          }, notification.duration || 5000);
        }
      },
      
      removeNotification: (id) => {
        set((state) => {
          const notification = state.notifications.find(n => n.id === id);
          const wasUnread = notification && !notification.read;
          
          return {
            notifications: state.notifications.filter(n => n.id !== id),
            unreadNotificationsCount: wasUnread 
              ? Math.max(0, state.unreadNotificationsCount - 1)
              : state.unreadNotificationsCount
          };
        });
      },
      
      markNotificationAsRead: (id) => {
        set((state) => {
          const notifications = state.notifications.map(notification =>
            notification.id === id && !notification.read
              ? { ...notification, read: true }
              : notification
          );
          
          const unreadCount = notifications.filter(n => !n.read).length;
          
          return {
            notifications,
            unreadNotificationsCount: unreadCount
          };
        });
      },
      
      markAllNotificationsAsRead: () => {
        set((state) => ({
          notifications: state.notifications.map(notification => ({
            ...notification,
            read: true
          })),
          unreadNotificationsCount: 0
        }));
      },
      
      clearNotifications: () => {
        set({
          notifications: [],
          unreadNotificationsCount: 0
        });
      },
      
      // Cache actions
      setCache: (key, value) => {
        set((state) => ({
          cache: { ...state.cache, [key]: value }
        }));
      },
      
      getCache: (key) => {
        return get().cache[key];
      },
      
      clearCache: (key) => {
        if (key) {
          set((state) => {
            const newCache = { ...state.cache };
            delete newCache[key];
            return { cache: newCache };
          });
        } else {
          set({ cache: {} });
        }
      },
      
      // Utility actions
      reset: () => {
        set({
          user: null,
          isAuthenticated: false,
          isAdmin: false,
          ui: {
            sidebarCollapsed: false,
            theme: 'dark',
            language: 'ar',
            loading: false,
            mobileMenuOpen: false
          },
          notifications: [],
          unreadNotificationsCount: 0,
          cache: {}
        });
      }
    }));

// Simplified selector hooks
export const useUser = () => {
  const user = useAppStore((state) => state.user);
  const isAuthenticated = useAppStore((state) => state.isAuthenticated);
  const isAdmin = useAppStore((state) => state.isAdmin);
  const setUser = useAppStore((state) => state.setUser);
  const logout = useAppStore((state) => state.logout);

  return { user, isAuthenticated, isAdmin, setUser, logout };
};

export const useUI = () => {
  const ui = useAppStore((state) => state.ui);
  const setSidebarCollapsed = useAppStore((state) => state.setSidebarCollapsed);
  const setTheme = useAppStore((state) => state.setTheme);
  const setLanguage = useAppStore((state) => state.setLanguage);
  const setLoading = useAppStore((state) => state.setLoading);
  const setMobileMenuOpen = useAppStore((state) => state.setMobileMenuOpen);

  return { ui, setSidebarCollapsed, setTheme, setLanguage, setLoading, setMobileMenuOpen };
};

export const useNotifications = () => {
  const notifications = useAppStore((state) => state.notifications);
  const unreadNotificationsCount = useAppStore((state) => state.unreadNotificationsCount);
  const addNotification = useAppStore((state) => state.addNotification);
  const removeNotification = useAppStore((state) => state.removeNotification);
  const markNotificationAsRead = useAppStore((state) => state.markNotificationAsRead);
  const markAllNotificationsAsRead = useAppStore((state) => state.markAllNotificationsAsRead);
  const clearNotifications = useAppStore((state) => state.clearNotifications);

  return {
    notifications,
    unreadNotificationsCount,
    addNotification,
    removeNotification,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    clearNotifications
  };
};

export default useAppStore;
