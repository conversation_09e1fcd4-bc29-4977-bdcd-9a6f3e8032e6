<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البيانات - خان فشارية</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #fff; 
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .section { 
            margin: 30px 0; 
            padding: 25px; 
            border: 2px solid #333; 
            border-radius: 15px; 
            background: rgba(255,255,255,0.05);
            backdrop-filter: blur(5px);
        }
        .success { color: #4CAF50; font-weight: bold; }
        .error { color: #f44336; font-weight: bold; }
        .warning { color: #ff9800; font-weight: bold; }
        .info { color: #2196F3; font-weight: bold; }
        
        .system-card, .service-card {
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(156, 39, 176, 0.1));
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            transition: transform 0.3s ease;
        }
        .system-card:hover, .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(33, 150, 243, 0.3);
        }
        
        .price {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin: 10px 0;
        }
        
        .status {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status.active { background: #4CAF50; color: white; }
        .status.inactive { background: #f44336; color: white; }
        
        .loading {
            text-align: center;
            padding: 40px;
            font-size: 18px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(139, 195, 74, 0.2));
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid rgba(76, 175, 80, 0.3);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 10px;
        }
        
        .refresh-btn {
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .refresh-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار بيانات خان فشارية</h1>
            <p>اختبار مباشر لعرض البيانات من قاعدة البيانات MySQL</p>
            <button class="refresh-btn" onclick="loadAllData()">🔄 تحديث البيانات</button>
        </div>
        
        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-number" id="systems-count">-</div>
                <div>الأنظمة التقنية</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="services-count">-</div>
                <div>الخدمات التقنية</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="premium-count">-</div>
                <div>الخدمات المميزة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="total-value">-</div>
                <div>القيمة الإجمالية ($)</div>
            </div>
        </div>
        
        <div class="section">
            <h2>🖥️ الأنظمة التقنية</h2>
            <div id="systems-data" class="loading">جاري تحميل الأنظمة...</div>
        </div>
        
        <div class="section">
            <h2>🔧 الخدمات التقنية</h2>
            <div id="services-data" class="loading">جاري تحميل الخدمات...</div>
        </div>
        
        <div class="section">
            <h2>⭐ الخدمات المميزة</h2>
            <div id="premium-data" class="loading">جاري تحميل الخدمات المميزة...</div>
        </div>
        
        <div class="section">
            <h2>📊 معلومات الاتصال</h2>
            <div id="connection-info"></div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        
        async function fetchWithHeaders(url) {
            return fetch(url, {
                headers: {
                    'ngrok-skip-browser-warning': 'true',
                    'Content-Type': 'application/json'
                }
            });
        }
        
        async function loadSystems() {
            try {
                const response = await fetchWithHeaders(`${API_BASE}/systems`);
                const data = await response.json();
                
                const systemsDiv = document.getElementById('systems-data');
                const countDiv = document.getElementById('systems-count');
                
                if (data.success && data.data.systems) {
                    const systems = data.data.systems;
                    countDiv.textContent = systems.length;
                    
                    let html = '';
                    systems.forEach(system => {
                        html += `
                            <div class="system-card">
                                <h3>${system.name_ar}</h3>
                                <p><strong>English:</strong> ${system.name_en}</p>
                                <p>${system.description_ar}</p>
                                <div class="price">$${system.price}</div>
                                <span class="status ${system.status}">${system.status}</span>
                                <p><strong>الفئة:</strong> ${system.category}</p>
                                <p><strong>النوع:</strong> ${system.type}</p>
                                ${system.features_ar ? `<p><strong>الميزات:</strong> ${system.features_ar.join(', ')}</p>` : ''}
                            </div>
                        `;
                    });
                    systemsDiv.innerHTML = html;
                } else {
                    systemsDiv.innerHTML = '<div class="error">❌ فشل في تحميل الأنظمة</div>';
                    countDiv.textContent = '0';
                }
            } catch (error) {
                document.getElementById('systems-data').innerHTML = `<div class="error">❌ خطأ: ${error.message}</div>`;
                document.getElementById('systems-count').textContent = '0';
            }
        }
        
        async function loadServices() {
            try {
                const response = await fetchWithHeaders(`${API_BASE}/services/technical`);
                const data = await response.json();
                
                const servicesDiv = document.getElementById('services-data');
                const countDiv = document.getElementById('services-count');
                
                if (data.success && data.data) {
                    const services = Array.isArray(data.data) ? data.data : [];
                    countDiv.textContent = services.length;
                    
                    if (services.length > 0) {
                        let html = '';
                        services.forEach(service => {
                            html += `
                                <div class="service-card">
                                    <h3>${service.name_ar || service.title_ar}</h3>
                                    <p><strong>English:</strong> ${service.name_en || service.title_en}</p>
                                    <p>${service.description_ar}</p>
                                    <div class="price">$${service.price}</div>
                                    <span class="status ${service.status}">${service.status}</span>
                                </div>
                            `;
                        });
                        servicesDiv.innerHTML = html;
                    } else {
                        servicesDiv.innerHTML = '<div class="info">📝 لا توجد خدمات تقنية متاحة حالياً</div>';
                    }
                } else {
                    servicesDiv.innerHTML = '<div class="error">❌ فشل في تحميل الخدمات</div>';
                    countDiv.textContent = '0';
                }
            } catch (error) {
                document.getElementById('services-data').innerHTML = `<div class="error">❌ خطأ: ${error.message}</div>`;
                document.getElementById('services-count').textContent = '0';
            }
        }
        
        async function loadPremium() {
            try {
                const response = await fetchWithHeaders(`${API_BASE}/services/premium`);
                const data = await response.json();
                
                const premiumDiv = document.getElementById('premium-data');
                const countDiv = document.getElementById('premium-count');
                
                if (data.success && data.data) {
                    const premium = Array.isArray(data.data) ? data.data : [];
                    countDiv.textContent = premium.length;
                    
                    if (premium.length > 0) {
                        let html = '';
                        premium.forEach(item => {
                            html += `
                                <div class="service-card">
                                    <h3>${item.title_ar}</h3>
                                    <p><strong>English:</strong> ${item.title_en}</p>
                                    <p>${item.description_ar}</p>
                                    <div class="price">$${item.price}</div>
                                    <span class="status ${item.status}">${item.status}</span>
                                </div>
                            `;
                        });
                        premiumDiv.innerHTML = html;
                    } else {
                        premiumDiv.innerHTML = '<div class="info">📝 لا توجد خدمات مميزة متاحة حالياً</div>';
                    }
                } else {
                    premiumDiv.innerHTML = '<div class="error">❌ فشل في تحميل الخدمات المميزة</div>';
                    countDiv.textContent = '0';
                }
            } catch (error) {
                document.getElementById('premium-data').innerHTML = `<div class="error">❌ خطأ: ${error.message}</div>`;
                document.getElementById('premium-count').textContent = '0';
            }
        }
        
        async function updateConnectionInfo() {
            const infoDiv = document.getElementById('connection-info');
            
            try {
                const response = await fetchWithHeaders('/health');
                const data = await response.json();
                
                infoDiv.innerHTML = `
                    <div class="success">✅ الاتصال بقاعدة البيانات: ${data.database}</div>
                    <div class="info">🕐 وقت التشغيل: ${Math.round(data.uptime)} ثانية</div>
                    <div class="info">🌐 البيئة: ${data.environment}</div>
                    <div class="info">📅 الوقت: ${new Date(data.timestamp).toLocaleString('ar-SA')}</div>
                `;
            } catch (error) {
                infoDiv.innerHTML = `<div class="error">❌ خطأ في الاتصال: ${error.message}</div>`;
            }
        }
        
        async function calculateTotalValue() {
            const systemsCount = parseInt(document.getElementById('systems-count').textContent) || 0;
            const servicesCount = parseInt(document.getElementById('services-count').textContent) || 0;
            const premiumCount = parseInt(document.getElementById('premium-count').textContent) || 0;
            
            // Estimate total value (assuming average price)
            const totalValue = (systemsCount + servicesCount + premiumCount) * 299;
            document.getElementById('total-value').textContent = totalValue.toLocaleString();
        }
        
        async function loadAllData() {
            console.log('🔄 Loading all data...');
            
            await Promise.all([
                loadSystems(),
                loadServices(),
                loadPremium(),
                updateConnectionInfo()
            ]);
            
            // Calculate total value after all data is loaded
            setTimeout(calculateTotalValue, 1000);
            
            console.log('✅ All data loaded');
        }
        
        // Load data when page loads
        window.onload = loadAllData;
        
        // Auto-refresh every 30 seconds
        setInterval(loadAllData, 30000);
    </script>
</body>
</html>
