/**
 * MySQL Integration & Schema Alignment Test
 * Verifies WampServer connectivity and database schema alignment with code expectations
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khanfashariya_db'
};

let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

function logTest(name, status, details = '') {
  testResults.total++;
  const icon = status === 'PASS' ? '✅' : '❌';
  const message = `${icon} ${name}`;
  
  if (status === 'PASS') {
    testResults.passed++;
    console.log(`${message} ${details}`);
  } else {
    testResults.failed++;
    console.log(`${message} - ${details}`);
    testResults.errors.push({ test: name, error: details });
  }
}

async function testWampServerConnectivity() {
  console.log('\n🔍 Testing WampServer Connectivity...');
  
  try {
    // Test connection to WampServer
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456'
    });
    
    logTest('WampServer Connection', 'PASS', 'Connected to WampServer MySQL');
    
    // Test database existence
    const [databases] = await connection.execute('SHOW DATABASES');
    const dbNames = databases.map(row => row.Database);
    
    if (dbNames.includes('khanfashariya_db')) {
      logTest('Database Existence', 'PASS', 'khanfashariya_db database found');
    } else {
      logTest('Database Existence', 'FAIL', 'khanfashariya_db database not found');
    }
    
    // Test database access
    await connection.execute('USE khanfashariya_db');
    logTest('Database Access', 'PASS', 'Successfully accessed khanfashariya_db');
    
    // Test MySQL version compatibility
    const [version] = await connection.execute('SELECT VERSION() as version');
    const mysqlVersion = version[0].version;
    logTest('MySQL Version', 'PASS', `MySQL ${mysqlVersion}`);
    
    await connection.end();
  } catch (error) {
    logTest('WampServer Connectivity', 'FAIL', error.message);
  }
}

async function testSchemaStructure() {
  console.log('\n🔍 Testing Database Schema Structure...');
  
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // Define expected schema structure
    const expectedTables = {
      'users': {
        required: ['id', 'email', 'username', 'full_name', 'password_hash', 'role', 'status'],
        optional: ['phone', 'avatar_url', 'email_verified', 'last_login', 'login_count']
      },
      'system_services': {
        required: ['id', 'name_ar', 'name_en', 'description_ar', 'description_en', 'price', 'status'],
        optional: ['category', 'features_ar', 'features_en', 'video_url', 'image_url']
      },
      'technical_services': {
        required: ['id', 'name_ar', 'name_en', 'description_ar', 'description_en', 'price', 'status'],
        optional: ['category', 'service_type', 'delivery_time_ar', 'delivery_time_en']
      },
      'premium_content': {
        required: ['id', 'title_ar', 'title_en', 'description_ar', 'description_en', 'price', 'status'],
        optional: ['category', 'features_ar', 'features_en', 'video_url', 'image_url']
      },
      'orders': {
        required: ['id', 'user_id', 'order_type', 'item_id', 'status', 'payment_status', 'final_price'],
        optional: ['order_number', 'quantity', 'notes_ar', 'notes_en', 'admin_notes']
      }
    };
    
    // Test each table structure
    for (const [tableName, schema] of Object.entries(expectedTables)) {
      try {
        const [columns] = await connection.execute(`DESCRIBE ${tableName}`);
        const columnNames = columns.map(col => col.Field);
        
        logTest(`Table: ${tableName}`, 'PASS', `Table exists with ${columns.length} columns`);
        
        // Check required columns
        for (const requiredCol of schema.required) {
          if (columnNames.includes(requiredCol)) {
            logTest(`${tableName}.${requiredCol}`, 'PASS', 'Required column exists');
          } else {
            logTest(`${tableName}.${requiredCol}`, 'FAIL', 'Required column missing');
          }
        }
        
        // Check optional columns (don't fail if missing)
        let optionalCount = 0;
        for (const optionalCol of schema.optional) {
          if (columnNames.includes(optionalCol)) {
            optionalCount++;
          }
        }
        logTest(`${tableName} Optional Columns`, 'PASS', `${optionalCount}/${schema.optional.length} optional columns present`);
        
      } catch (error) {
        logTest(`Table: ${tableName}`, 'FAIL', `Table missing or inaccessible: ${error.message}`);
      }
    }
    
    await connection.end();
  } catch (error) {
    logTest('Schema Structure', 'FAIL', error.message);
  }
}

async function testDataIntegrity() {
  console.log('\n🔍 Testing Data Integrity...');
  
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // Test foreign key relationships
    const [orderUsers] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM orders o 
      LEFT JOIN users u ON o.user_id = u.id 
      WHERE u.id IS NULL
    `);
    
    if (orderUsers[0].count === 0) {
      logTest('Order-User Relationships', 'PASS', 'All orders have valid user references');
    } else {
      logTest('Order-User Relationships', 'FAIL', `${orderUsers[0].count} orders with invalid user references`);
    }
    
    // Test data consistency
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [orderCount] = await connection.execute('SELECT COUNT(*) as count FROM orders');
    const [systemCount] = await connection.execute('SELECT COUNT(*) as count FROM system_services');
    
    logTest('Data Population', 'PASS', `${userCount[0].count} users, ${orderCount[0].count} orders, ${systemCount[0].count} systems`);
    
    // Test bilingual data integrity
    const [bilingualSystems] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM system_services 
      WHERE name_ar IS NOT NULL AND name_en IS NOT NULL 
      AND description_ar IS NOT NULL AND description_en IS NOT NULL
    `);
    
    const [totalSystems] = await connection.execute('SELECT COUNT(*) as count FROM system_services');
    
    if (bilingualSystems[0].count === totalSystems[0].count) {
      logTest('Bilingual Data Integrity', 'PASS', 'All systems have complete bilingual content');
    } else {
      logTest('Bilingual Data Integrity', 'FAIL', `${totalSystems[0].count - bilingualSystems[0].count} systems missing bilingual content`);
    }
    
    await connection.end();
  } catch (error) {
    logTest('Data Integrity', 'FAIL', error.message);
  }
}

async function testIndexesAndPerformance() {
  console.log('\n🔍 Testing Indexes and Performance...');
  
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // Check for important indexes
    const [userIndexes] = await connection.execute('SHOW INDEX FROM users');
    const userIndexNames = userIndexes.map(idx => idx.Column_name);
    
    if (userIndexNames.includes('email')) {
      logTest('Users Email Index', 'PASS', 'Email column is indexed');
    } else {
      logTest('Users Email Index', 'FAIL', 'Email column not indexed');
    }
    
    // Test query performance
    const startTime = Date.now();
    await connection.execute('SELECT * FROM users WHERE email = ?', ['<EMAIL>']);
    const queryTime = Date.now() - startTime;
    
    if (queryTime < 50) {
      logTest('Query Performance', 'PASS', `User lookup: ${queryTime}ms`);
    } else {
      logTest('Query Performance', 'FAIL', `User lookup: ${queryTime}ms (too slow)`);
    }
    
    await connection.end();
  } catch (error) {
    logTest('Indexes and Performance', 'FAIL', error.message);
  }
}

async function testSchemaVersioning() {
  console.log('\n🔍 Testing Schema Versioning...');
  
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // Check if schema version table exists
    try {
      const [versionRows] = await connection.execute('SELECT * FROM schema_version ORDER BY version DESC LIMIT 1');
      if (versionRows.length > 0) {
        logTest('Schema Versioning', 'PASS', `Current schema version: ${versionRows[0].version}`);
      } else {
        logTest('Schema Versioning', 'PASS', 'Schema version table exists but empty');
      }
    } catch (error) {
      logTest('Schema Versioning', 'PASS', 'No schema versioning (acceptable for current setup)');
    }
    
    // Check table creation timestamps
    const [tableInfo] = await connection.execute(`
      SELECT TABLE_NAME, CREATE_TIME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = 'khanfashariya_db' 
      ORDER BY CREATE_TIME DESC
    `);
    
    if (tableInfo.length > 0) {
      logTest('Table Creation Info', 'PASS', `${tableInfo.length} tables with creation timestamps`);
    } else {
      logTest('Table Creation Info', 'FAIL', 'No table creation information available');
    }
    
    await connection.end();
  } catch (error) {
    logTest('Schema Versioning', 'FAIL', error.message);
  }
}

async function testBackupCompatibility() {
  console.log('\n🔍 Testing Backup Compatibility...');
  
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // Test if we can create a backup-like export
    const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [orders] = await connection.execute('SELECT COUNT(*) as count FROM orders');
    
    logTest('Backup Data Access', 'PASS', `Can access ${users[0].count} users and ${orders[0].count} orders for backup`);
    
    // Test data export format
    const [sampleUser] = await connection.execute('SELECT id, email, username, role FROM users LIMIT 1');
    if (sampleUser.length > 0) {
      logTest('Data Export Format', 'PASS', 'Data can be exported in standard format');
    } else {
      logTest('Data Export Format', 'FAIL', 'No data available for export testing');
    }
    
    await connection.end();
  } catch (error) {
    logTest('Backup Compatibility', 'FAIL', error.message);
  }
}

async function runMySQLSchemaTest() {
  console.log('🚀 Starting MySQL Integration & Schema Alignment Test');
  console.log('=' * 60);
  
  const startTime = Date.now();
  
  // Run all MySQL and schema tests
  await testWampServerConnectivity();
  await testSchemaStructure();
  await testDataIntegrity();
  await testIndexesAndPerformance();
  await testSchemaVersioning();
  await testBackupCompatibility();
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  // Print summary
  console.log('\n' + '=' * 60);
  console.log('📊 MYSQL INTEGRATION & SCHEMA ALIGNMENT SUMMARY');
  console.log('=' * 60);
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  console.log(`Duration: ${duration} seconds`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.test}: ${error.error}`);
    });
  }
  
  // MySQL integration assessment
  const successRate = (testResults.passed / testResults.total) * 100;
  console.log('\n🗄️ MYSQL INTEGRATION ASSESSMENT:');
  
  if (successRate >= 95) {
    console.log('🟢 EXCELLENT - MySQL integration and schema fully aligned');
  } else if (successRate >= 85) {
    console.log('🟡 GOOD - MySQL mostly integrated, minor schema issues to address');
  } else {
    console.log('🔴 NEEDS WORK - Critical MySQL integration or schema issues');
  }
  
  console.log('\n🎉 MySQL integration & schema alignment testing completed!');
}

// Run the test
runMySQLSchemaTest().catch(console.error);
