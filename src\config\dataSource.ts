/**
 * Data Source Configuration for Khanfashariya.com
 * 
 * This file centralizes the data source configuration and ensures
 * all components use the correct data adapter (API vs localStorage).
 */

// Force API usage - set to true to use API, false for localStorage
export const FORCE_API_USAGE = true;

// Environment-based configuration
export const USE_API = import.meta.env.VITE_USE_API === 'true' || FORCE_API_USAGE;

// API Configuration - use relative URLs in development for Vite proxy
const isDevelopment = import.meta.env.MODE === 'development';
export const API_CONFIG = {
  baseURL: isDevelopment
    ? '' // Use relative URLs for Vite proxy in development
    : (import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'),
  timeout: 10000,
  retries: 3
};

// Debug configuration
export const DEBUG_CONFIG = {
  logApiCalls: import.meta.env.NODE_ENV === 'development',
  logDataSource: true,
  showDataSourceIndicator: import.meta.env.NODE_ENV === 'development'
};

// Data source status
export function getDataSourceStatus() {
  return {
    useApi: USE_API,
    apiBaseURL: API_CONFIG.baseURL,
    environment: import.meta.env.NODE_ENV,
    forced: FORCE_API_USAGE
  };
}

// Log data source configuration
if (DEBUG_CONFIG.logDataSource) {
  console.log('🔧 Data Source Configuration:', getDataSourceStatus());
}
