import React, { useState } from 'react';
import { useNotifications, useLanguage } from '../store/simpleStore';
import Button from './ui/Button';
import Card from './ui/Card';
import Input from './ui/Input';
import Modal from './ui/Modal';
import BackButton from './ui/BackButton';
import { Save, Download, Trash2, Plus, Settings, Globe, ArrowLeft, Home } from 'lucide-react';

/**
 * Test page for new components
 */
interface TestPageProps {
  onBack?: () => void;
}

const TestPage: React.FC<TestPageProps> = ({ onBack }) => {
  const { addNotification } = useNotifications();
  const { language, setLanguage } = useLanguage();
  const [showModal, setShowModal] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);

  const showSuccessNotification = () => {
    addNotification({
      type: 'success',
      title: language === 'ar' ? 'تم بنجاح' : 'Success',
      message: language === 'ar' ? 'تم حفظ البيانات بنجاح' : 'Data saved successfully',
      duration: 5000
    });
  };

  const showErrorNotification = () => {
    addNotification({
      type: 'error',
      title: language === 'ar' ? 'خطأ' : 'Error',
      message: language === 'ar' ? 'حدث خطأ أثناء الحفظ' : 'An error occurred while saving',
      duration: 0 // Won't auto-close
    });
  };

  const showWarningNotification = () => {
    addNotification({
      type: 'warning',
      title: language === 'ar' ? 'تحذير' : 'Warning',
      message: language === 'ar' ? 'يرجى التحقق من البيانات' : 'Please check your data',
      duration: 7000
    });
  };

  const showInfoNotification = () => {
    addNotification({
      type: 'info',
      title: language === 'ar' ? 'معلومات' : 'Information',
      message: language === 'ar' ? 'تم تحديث النظام' : 'System has been updated',
      duration: 4000
    });
  };

  const handleLoadingTest = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      showSuccessNotification();
    }, 3000);
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-3xl font-bold text-white">
              {language === 'ar' ? 'صفحة اختبار المكونات' : 'Component Test Page'}
            </h1>

            {/* Back Button */}
            {onBack && (
              <BackButton onClick={onBack} variant="home" size="md" />
            )}
          </div>

          {/* Language Toggle */}
          <Button
            variant="outline"
            onClick={() => setLanguage(language === 'ar' ? 'en' : 'ar')}
            className="mb-6"
          >
            <Globe className="w-4 h-4 mr-2" />
            {language === 'ar' ? 'English' : 'العربية'}
          </Button>
        </div>

        {/* Button Tests */}
        <Card className="mb-8">
          <Card.Header>
            <h2 className="text-xl font-semibold text-white">
              {language === 'ar' ? 'اختبار الأزرار' : 'Button Tests'}
            </h2>
          </Card.Header>
          <Card.Body>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Button Variants */}
              <div className="space-y-4">
                <h3 className="text-lg text-secondary font-semibold">
                  {language === 'ar' ? 'الأنواع' : 'Variants'}
                </h3>
                <Button variant="primary">
                  <Save className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'أساسي' : 'Primary'}
                </Button>
                <Button variant="secondary">
                  <Download className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'ثانوي' : 'Secondary'}
                </Button>
                <Button variant="outline">
                  <Plus className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'محدد' : 'Outline'}
                </Button>
                <Button variant="ghost">
                  <Settings className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'شفاف' : 'Ghost'}
                </Button>
                <Button variant="danger">
                  <Trash2 className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'خطر' : 'Danger'}
                </Button>
              </div>

              {/* Button Sizes */}
              <div className="space-y-3">
                <h3 className="text-lg text-secondary font-semibold">
                  {language === 'ar' ? 'الأحجام' : 'Sizes'}
                </h3>
                <Button size="sm">
                  {language === 'ar' ? 'صغير' : 'Small'}
                </Button>
                <Button size="md">
                  {language === 'ar' ? 'متوسط' : 'Medium'}
                </Button>
                <Button size="lg">
                  {language === 'ar' ? 'كبير' : 'Large'}
                </Button>
              </div>

              {/* Button States */}
              <div className="space-y-3">
                <h3 className="text-lg text-secondary font-semibold">
                  {language === 'ar' ? 'الحالات' : 'States'}
                </h3>
                <Button loading={loading} onClick={handleLoadingTest}>
                  {loading 
                    ? (language === 'ar' ? 'جاري التحميل...' : 'Loading...') 
                    : (language === 'ar' ? 'اختبار التحميل' : 'Test Loading')
                  }
                </Button>
                <Button disabled>
                  {language === 'ar' ? 'معطل' : 'Disabled'}
                </Button>
              </div>
            </div>
          </Card.Body>
        </Card>

        {/* Input Tests */}
        <Card className="mb-8">
          <Card.Header>
            <h2 className="text-xl font-semibold text-white">
              {language === 'ar' ? 'اختبار حقول الإدخال' : 'Input Tests'}
            </h2>
          </Card.Header>
          <Card.Body>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Input
                  label={language === 'ar' ? 'الاسم' : 'Name'}
                  placeholder={language === 'ar' ? 'أدخل اسمك' : 'Enter your name'}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                />
                
                <Input
                  label={language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                  type="email"
                  placeholder={language === 'ar' ? 'أدخل بريدك الإلكتروني' : 'Enter your email'}
                  error={inputValue.length > 0 && !inputValue.includes('@') ? 
                    (language === 'ar' ? 'البريد الإلكتروني غير صحيح' : 'Invalid email') : 
                    undefined
                  }
                />
                
                <Input
                  label={language === 'ar' ? 'كلمة المرور' : 'Password'}
                  type="password"
                  showPasswordToggle
                  placeholder={language === 'ar' ? 'أدخل كلمة المرور' : 'Enter password'}
                />
              </div>
              
              <div className="space-y-4">
                <Input
                  label={language === 'ar' ? 'البحث' : 'Search'}
                  leftIcon={<Settings />}
                  placeholder={language === 'ar' ? 'ابحث...' : 'Search...'}
                />
                
                <Input
                  label={language === 'ar' ? 'مع تحميل' : 'With Loading'}
                  loading={loading}
                  placeholder={language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
                />
                
                <Input
                  label={language === 'ar' ? 'نجح' : 'Success'}
                  success={language === 'ar' ? 'البيانات صحيحة' : 'Data is valid'}
                  placeholder={language === 'ar' ? 'بيانات صحيحة' : 'Valid data'}
                />
              </div>
            </div>
          </Card.Body>
        </Card>

        {/* Notification Tests */}
        <Card className="mb-8">
          <Card.Header>
            <h2 className="text-xl font-semibold text-white">
              {language === 'ar' ? 'اختبار الإشعارات' : 'Notification Tests'}
            </h2>
          </Card.Header>
          <Card.Body>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button variant="outline" onClick={showSuccessNotification}>
                {language === 'ar' ? 'نجاح' : 'Success'}
              </Button>
              <Button variant="outline" onClick={showErrorNotification}>
                {language === 'ar' ? 'خطأ' : 'Error'}
              </Button>
              <Button variant="outline" onClick={showWarningNotification}>
                {language === 'ar' ? 'تحذير' : 'Warning'}
              </Button>
              <Button variant="outline" onClick={showInfoNotification}>
                {language === 'ar' ? 'معلومات' : 'Info'}
              </Button>
            </div>
          </Card.Body>
        </Card>

        {/* Modal Test */}
        <Card>
          <Card.Header>
            <h2 className="text-xl font-semibold text-white">
              {language === 'ar' ? 'اختبار النوافذ المنبثقة' : 'Modal Tests'}
            </h2>
          </Card.Header>
          <Card.Body>
            <Button onClick={() => setShowModal(true)}>
              {language === 'ar' ? 'فتح نافذة منبثقة' : 'Open Modal'}
            </Button>
          </Card.Body>
        </Card>

        {/* Modal */}
        {showModal && (
          <Modal
            isOpen={showModal}
            onClose={() => setShowModal(false)}
            title={language === 'ar' ? 'نافذة اختبار' : 'Test Modal'}
          >
            <Modal.Body>
              <p className="text-white">
                {language === 'ar' 
                  ? 'هذه نافذة منبثقة للاختبار. يمكنك إغلاقها بالنقر على زر الإغلاق أو الضغط على Escape.'
                  : 'This is a test modal. You can close it by clicking the close button or pressing Escape.'
                }
              </p>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="outline" onClick={() => setShowModal(false)}>
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button onClick={() => {
                setShowModal(false);
                showSuccessNotification();
              }}>
                {language === 'ar' ? 'موافق' : 'OK'}
              </Button>
            </Modal.Footer>
          </Modal>
        )}
      </div>
    </div>
  );
};

export default TestPage;
