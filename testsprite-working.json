{"name": "Khanfashariya API Tests - Working Tunnel", "baseUrl": "https://09e0719da445.ngrok-free.app", "headers": {"ngrok-skip-browser-warning": "true", "User-Agent": "TestSprite/1.0", "Content-Type": "application/json"}, "endpoints": [{"path": "/health", "method": "GET", "description": "Health check", "testCases": [{"name": "Health Check", "expectedStatus": 200}]}, {"path": "/api/auth/login", "method": "POST", "description": "Authentication", "testCases": [{"name": "<PERSON><PERSON>", "body": {"email": "<EMAIL>", "password": "admin123"}, "expectedStatus": 200}]}, {"path": "/api/systems", "method": "GET", "description": "Get systems", "testCases": [{"name": "Get Systems", "expectedStatus": 200}]}]}