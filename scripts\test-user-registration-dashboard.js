/**
 * User Registration & Dashboard Testing Script
 * Tests complete user journey from registration through dashboard functionality
 */

const axios = require('axios');
const mysql = require('mysql2/promise');

const API_BASE = 'http://localhost:3001/api';
const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khan<PERSON><PERSON><PERSON>_db'
};

// Generate unique test user
const generateTestUser = () => {
  const timestamp = Date.now().toString().slice(-6); // Use last 6 digits
  return {
    email: `test${timestamp}@example.com`,
    password: 'TestPassword123!',
    username: `test${timestamp}`,
    full_name: `Test User ${timestamp}`
  };
};

let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

function logTest(name, status, details = '') {
  testResults.total++;
  const icon = status === 'PASS' ? '✅' : '❌';
  const message = `${icon} ${name}`;
  
  if (status === 'PASS') {
    testResults.passed++;
    console.log(`${message} ${details}`);
  } else {
    testResults.failed++;
    console.log(`${message} - ${details}`);
    testResults.errors.push({ test: name, error: details });
  }
}

async function testUserRegistration() {
  console.log('\n🔍 Testing User Registration...');
  
  const testUser = generateTestUser();
  let registrationSuccess = false;
  
  try {
    // Test user registration
    const registerResponse = await axios.post(`${API_BASE}/auth/register`, testUser);
    
    if (registerResponse.data.success) {
      logTest('User Registration', 'PASS', `User ${testUser.email} registered successfully`);
      registrationSuccess = true;
      
      // Test immediate login after registration
      try {
        const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
          email: testUser.email,
          password: testUser.password
        });
        
        if (loginResponse.data.success && loginResponse.data.data.tokens) {
          logTest('Post-Registration Login', 'PASS', 'Login successful after registration');
          return {
            success: true,
            user: testUser,
            tokens: loginResponse.data.data.tokens,
            userProfile: loginResponse.data.data.user
          };
        } else {
          logTest('Post-Registration Login', 'FAIL', 'Login failed after registration');
        }
      } catch (loginError) {
        logTest('Post-Registration Login', 'FAIL', loginError.message);
      }
    } else {
      logTest('User Registration', 'FAIL', 'Registration response indicates failure');
    }
  } catch (error) {
    // Check if it's a duplicate user error (409) - this is expected behavior
    if (error.response?.status === 409) {
      logTest('User Registration', 'PASS', 'Duplicate user handling working correctly (409 conflict)');
      
      // Try to login with existing test user
      try {
        const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
          email: '<EMAIL>',
          password: '123456'
        });
        
        if (loginResponse.data.success) {
          logTest('Existing User Login', 'PASS', 'Login with existing test user successful');
          return {
            success: true,
            user: { email: '<EMAIL>' },
            tokens: loginResponse.data.data.tokens,
            userProfile: loginResponse.data.data.user
          };
        }
      } catch (loginError) {
        logTest('Existing User Login', 'FAIL', loginError.message);
      }
    } else {
      logTest('User Registration', 'FAIL', error.message);
    }
  }
  
  return { success: false };
}

async function testUserDashboard(authData) {
  console.log('\n🔍 Testing User Dashboard...');
  
  if (!authData.success) {
    logTest('Dashboard Access', 'FAIL', 'No valid authentication data');
    return;
  }
  
  const token = authData.tokens.accessToken;
  const headers = { Authorization: `Bearer ${token}` };
  
  try {
    // Test user profile access
    const profileResponse = await axios.get(`${API_BASE}/users/profile`, { headers });
    if (profileResponse.data.success) {
      logTest('User Profile Access', 'PASS', 'Profile data retrieved successfully');
    } else {
      logTest('User Profile Access', 'FAIL', 'Profile access failed');
    }
    
    // Test user orders access
    const ordersResponse = await axios.get(`${API_BASE}/orders`, { headers });
    if (ordersResponse.data.success) {
      const orders = ordersResponse.data.data.orders || [];
      logTest('User Orders Access', 'PASS', `Found ${orders.length} orders`);
    } else {
      logTest('User Orders Access', 'FAIL', 'Orders access failed');
    }
    
    // Test user messages/inbox
    const userId = authData.userProfile.id;
    const messagesResponse = await axios.get(`${API_BASE}/users/${userId}/messages`, { headers });
    if (messagesResponse.data.success) {
      const messages = messagesResponse.data.data.messages || [];
      logTest('User Messages Access', 'PASS', `Found ${messages.length} messages`);
    } else {
      logTest('User Messages Access', 'FAIL', 'Messages access failed');
    }

    // Test user subscriptions
    const subscriptionsResponse = await axios.get(`${API_BASE}/users/${userId}/subscriptions`, { headers });
    if (subscriptionsResponse.data.success) {
      const subscriptions = subscriptionsResponse.data.data.subscriptions || [];
      logTest('User Subscriptions Access', 'PASS', `Found ${subscriptions.length} subscriptions`);
    } else {
      logTest('User Subscriptions Access', 'FAIL', 'Subscriptions access failed');
    }
    
  } catch (error) {
    logTest('Dashboard Functionality', 'FAIL', error.message);
  }
}

async function testUserProfileUpdate(authData) {
  console.log('\n🔍 Testing User Profile Updates...');
  
  if (!authData.success) {
    logTest('Profile Update', 'FAIL', 'No valid authentication data');
    return;
  }
  
  const token = authData.tokens.accessToken;
  const headers = { Authorization: `Bearer ${token}` };
  
  try {
    // Test profile update
    const updateData = {
      full_name: 'Updated Test User Name',
      phone: '+1234567890',
      address: 'Test Address 123'
    };
    
    const updateResponse = await axios.put(`${API_BASE}/users/profile`, updateData, { headers });
    if (updateResponse.data.success) {
      logTest('Profile Update', 'PASS', 'Profile updated successfully');
      
      // Verify the update
      const verifyResponse = await axios.get(`${API_BASE}/users/profile`, { headers });
      if (verifyResponse.data.success && verifyResponse.data.data.user.full_name === updateData.full_name) {
        logTest('Profile Update Verification', 'PASS', 'Profile changes verified');
      } else {
        logTest('Profile Update Verification', 'FAIL', 'Profile changes not reflected');
        console.log('Expected:', updateData.full_name);
        console.log('Received:', verifyResponse.data.data.user?.full_name);
      }
    } else {
      logTest('Profile Update', 'FAIL', 'Profile update failed');
    }
  } catch (error) {
    logTest('Profile Update', 'FAIL', error.message);
  }
}

async function testDatabaseUserData() {
  console.log('\n🔍 Testing Database User Data...');
  
  try {
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // Check user count
    const [userRows] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const userCount = userRows[0].count;
    logTest('Database User Count', 'PASS', `${userCount} users in database`);
    
    // Check user roles
    const [roleRows] = await connection.execute('SELECT role, COUNT(*) as count FROM users GROUP BY role');
    roleRows.forEach(row => {
      logTest(`User Role: ${row.role}`, 'PASS', `${row.count} users`);
    });
    
    // Check user activity
    const [activityRows] = await connection.execute('SELECT COUNT(*) as count FROM users WHERE last_login IS NOT NULL');
    const activeUsers = activityRows[0].count;
    logTest('Active Users', 'PASS', `${activeUsers} users have logged in`);
    
    await connection.end();
  } catch (error) {
    logTest('Database User Data', 'FAIL', error.message);
  }
}

async function runUserRegistrationDashboardTest() {
  console.log('🚀 Starting User Registration & Dashboard Testing');
  console.log('=' * 60);
  
  const startTime = Date.now();
  
  // Run all test suites
  const authData = await testUserRegistration();
  await testUserDashboard(authData);
  await testUserProfileUpdate(authData);
  await testDatabaseUserData();
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  // Print summary
  console.log('\n' + '=' * 60);
  console.log('📊 USER REGISTRATION & DASHBOARD TEST SUMMARY');
  console.log('=' * 60);
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  console.log(`Duration: ${duration} seconds`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.test}: ${error.error}`);
    });
  }
  
  console.log('\n🎉 User registration & dashboard testing completed!');
}

// Run the test
runUserRegistrationDashboardTest().catch(console.error);
