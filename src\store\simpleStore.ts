import { create } from 'zustand';

// Simple notification interface
interface SimpleNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
}

// Simple store interface
interface SimpleStore {
  // Notifications
  notifications: SimpleNotification[];
  addNotification: (notification: Omit<SimpleNotification, 'id'>) => void;
  removeNotification: (id: string) => void;
  
  // UI state
  sidebarCollapsed: boolean;
  setSidebarCollapsed: (collapsed: boolean) => void;
  
  // Language
  language: 'ar' | 'en';
  setLanguage: (language: 'ar' | 'en') => void;
}

/**
 * Simple Zustand store without persistence to avoid issues
 */
export const useSimpleStore = create<SimpleStore>((set, get) => ({
  // Initial state
  notifications: [],
  sidebarCollapsed: false,
  language: 'ar',
  
  // Notification actions
  addNotification: (notification) => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    const newNotification: SimpleNotification = {
      ...notification,
      id
    };
    
    set((state) => ({
      notifications: [newNotification, ...state.notifications]
    }));
    
    // Auto-remove notification after duration
    if (notification.duration !== 0) {
      setTimeout(() => {
        get().removeNotification(id);
      }, notification.duration || 5000);
    }
  },
  
  removeNotification: (id) => {
    set((state) => ({
      notifications: state.notifications.filter(n => n.id !== id)
    }));
  },
  
  // UI actions
  setSidebarCollapsed: (collapsed) => {
    set({ sidebarCollapsed: collapsed });
  },
  
  setLanguage: (language) => {
    set({ language });
    // Update document direction
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = language;
  }
}));

// Simple hooks
export const useNotifications = () => {
  const notifications = useSimpleStore((state) => state.notifications);
  const addNotification = useSimpleStore((state) => state.addNotification);
  const removeNotification = useSimpleStore((state) => state.removeNotification);
  
  return { notifications, addNotification, removeNotification };
};

export const useLanguage = () => {
  const language = useSimpleStore((state) => state.language);
  const setLanguage = useSimpleStore((state) => state.setLanguage);
  
  return { language, setLanguage };
};

export const useSidebar = () => {
  const sidebarCollapsed = useSimpleStore((state) => state.sidebarCollapsed);
  const setSidebarCollapsed = useSimpleStore((state) => state.setSidebarCollapsed);
  
  return { sidebarCollapsed, setSidebarCollapsed };
};
