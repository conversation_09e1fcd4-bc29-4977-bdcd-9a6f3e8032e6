const axios = require('axios');

async function testServicesEndpoint() {
  try {
    console.log('Testing services endpoint...');
    const response = await axios.get('http://localhost:3001/api/services/technical');
    
    console.log('Response status:', response.status);
    console.log('Response data structure:', {
      success: response.data.success,
      hasData: !!response.data.data,
      hasServices: !!response.data.data?.services,
      servicesCount: response.data.data?.services?.length || 0,
      directServicesCount: Array.isArray(response.data) ? response.data.length : 0
    });
    
    console.log('Full response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('Error:', error.response?.data || error.message);
  }
}

testServicesEndpoint();
