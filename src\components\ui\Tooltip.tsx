import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';

interface TooltipProps {
  content: string;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'follow';
  delay?: number;
  className?: string;
  disabled?: boolean;
  followMouse?: boolean;
}

/**
 * Enhanced Tooltip component with mouse following capability
 *
 * Features:
 * - Dark theme compatible (white text on dark background)
 * - RTL layout support
 * - Multiple positioning options including mouse following
 * - Customizable delay
 * - Proper z-index handling
 * - Responsive positioning
 * - Mouse cursor following for better UX
 */
const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  position = 'top',
  delay = 500,
  className = '',
  disabled = false,
  followMouse = false
}) => {
  const { language } = useTranslation();
  const [isVisible, setIsVisible] = useState(false);
  const [actualPosition, setActualPosition] = useState(position);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const timeoutRef = useRef<NodeJS.Timeout>();
  const tooltipRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  const isRTL = language === 'ar';
  const shouldFollowMouse = followMouse || position === 'follow';

  // Handle mouse movement for following tooltip
  const handleMouseMove = (e: React.MouseEvent) => {
    if (shouldFollowMouse) {
      const rect = triggerRef.current?.getBoundingClientRect();
      if (rect) {
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    }
  };

  // Show tooltip with delay
  const showTooltip = (e?: React.MouseEvent) => {
    if (disabled || !content) return;

    if (shouldFollowMouse && e) {
      handleMouseMove(e);
    }

    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      // Calculate optimal position after showing
      if (!shouldFollowMouse) {
        calculatePosition();
      }
    }, delay);
  };

  // Hide tooltip immediately
  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  // Calculate optimal position based on viewport
  const calculatePosition = () => {
    if (!tooltipRef.current || !triggerRef.current) return;

    const tooltip = tooltipRef.current;
    const trigger = triggerRef.current;
    const triggerRect = trigger.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    let newPosition = position;

    // Check if tooltip fits in the preferred position
    switch (position) {
      case 'top':
        if (triggerRect.top - tooltipRect.height < 10) {
          newPosition = 'bottom';
        }
        break;
      case 'bottom':
        if (triggerRect.bottom + tooltipRect.height > viewport.height - 10) {
          newPosition = 'top';
        }
        break;
      case 'left':
        if (triggerRect.left - tooltipRect.width < 10) {
          newPosition = 'right';
        }
        break;
      case 'right':
        if (triggerRect.right + tooltipRect.width > viewport.width - 10) {
          newPosition = 'left';
        }
        break;
    }

    setActualPosition(newPosition);
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Position classes
  const getPositionClasses = () => {
    const baseClasses = 'absolute z-50 pointer-events-none';

    if (shouldFollowMouse) {
      return `${baseClasses}`;
    }

    switch (actualPosition) {
      case 'top':
        return `${baseClasses} bottom-full left-1/2 -translate-x-1/2 mb-2`;
      case 'bottom':
        return `${baseClasses} top-full left-1/2 -translate-x-1/2 mt-2`;
      case 'left':
        return `${baseClasses} right-full top-1/2 -translate-y-1/2 ${isRTL ? 'ml-2' : 'mr-2'}`;
      case 'right':
        return `${baseClasses} left-full top-1/2 -translate-y-1/2 ${isRTL ? 'mr-2' : 'ml-2'}`;
      default:
        return `${baseClasses} bottom-full left-1/2 -translate-x-1/2 mb-2`;
    }
  };

  // Get dynamic style for mouse following
  const getFollowMouseStyle = () => {
    if (!shouldFollowMouse) return {};

    return {
      left: `${mousePosition.x + 15}px`,
      top: `${mousePosition.y - 40}px`,
      transform: 'none'
    };
  };

  // Arrow classes
  const getArrowClasses = () => {
    if (shouldFollowMouse) return 'hidden'; // Hide arrow for following tooltips

    const baseClasses = 'absolute w-2 h-2 bg-gray-800 rotate-45';

    switch (actualPosition) {
      case 'top':
        return `${baseClasses} top-full left-1/2 -translate-x-1/2 -mt-1`;
      case 'bottom':
        return `${baseClasses} bottom-full left-1/2 -translate-x-1/2 -mb-1`;
      case 'left':
        return `${baseClasses} left-full top-1/2 -translate-y-1/2 -ml-1`;
      case 'right':
        return `${baseClasses} right-full top-1/2 -translate-y-1/2 -mr-1`;
      default:
        return `${baseClasses} top-full left-1/2 -translate-x-1/2 -mt-1`;
    }
  };

  if (!content || disabled) {
    return <>{children}</>;
  }

  return (
    <div
      ref={triggerRef}
      className="relative inline-block"
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
      onMouseMove={shouldFollowMouse ? handleMouseMove : undefined}
      onFocus={showTooltip}
      onBlur={hideTooltip}
    >
      {children}

      {isVisible && (
        <div
          ref={tooltipRef}
          className={`${getPositionClasses()} ${className}`}
          style={getFollowMouseStyle()}
          role="tooltip"
          aria-hidden={!isVisible}
        >
          {/* Tooltip content */}
          <div className="bg-gray-800 text-white text-sm px-3 py-2 rounded-lg shadow-lg border border-gray-600 max-w-xs whitespace-nowrap">
            {content}
          </div>

          {/* Arrow */}
          <div className={getArrowClasses()} />
        </div>
      )}
    </div>
  );
};

export default Tooltip;
