import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Check, Search, X } from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';

interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
  icon?: React.ReactNode;
}

interface SelectProps {
  options: SelectOption[];
  value?: string;
  defaultValue?: string;
  placeholder?: string;
  label?: string;
  error?: string;
  hint?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
  disabled?: boolean;
  required?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  multiple?: boolean;
  loading?: boolean;
  className?: string;
  onChange?: (value: string | string[]) => void;
  onSearch?: (query: string) => void;
}

/**
 * Enhanced Select component with advanced features
 * 
 * Features:
 * - Single and multiple selection
 * - Searchable options
 * - Clearable selection
 * - Custom option rendering with icons
 * - Loading state
 * - Keyboard navigation
 * - RTL layout support
 * - Accessibility features
 * - Floating label
 */
const Select: React.FC<SelectProps> = ({
  options,
  value,
  defaultValue,
  placeholder,
  label,
  error,
  hint,
  size = 'md',
  variant = 'default',
  disabled = false,
  required = false,
  searchable = false,
  clearable = false,
  multiple = false,
  loading = false,
  className = '',
  onChange,
  onSearch
}) => {
  const { language, t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValues, setSelectedValues] = useState<string[]>(
    multiple 
      ? (Array.isArray(value) ? value : [])
      : (value ? [value] : defaultValue ? [defaultValue] : [])
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(-1);
  
  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const optionsRef = useRef<HTMLDivElement>(null);

  const isRTL = language === 'ar';

  // Filter options based on search query
  const filteredOptions = searchable && searchQuery
    ? options.filter(option => 
        option.label.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : options;

  // Get selected option labels
  const getSelectedLabels = () => {
    return selectedValues
      .map(val => options.find(opt => opt.value === val)?.label)
      .filter(Boolean);
  };

  // Size-specific classes
  const sizeClasses = {
    sm: {
      trigger: 'h-10 px-3 text-sm',
      option: 'px-3 py-2 text-sm',
      icon: 'w-4 h-4'
    },
    md: {
      trigger: 'h-11 px-4 text-base',
      option: 'px-4 py-3 text-base',
      icon: 'w-5 h-5'
    },
    lg: {
      trigger: 'h-12 px-5 text-lg',
      option: 'px-5 py-4 text-lg',
      icon: 'w-6 h-6'
    }
  };

  // Variant classes
  const variantClasses = {
    default: [
      'bg-primary-500/30',
      'border',
      'border-accent-500/30',
      'focus:border-accent-500'
    ].join(' '),

    filled: [
      'bg-gray-100',
      'border',
      'border-transparent',
      'focus:border-accent-500'
    ].join(' '),

    outlined: [
      'bg-transparent',
      'border-2',
      'border-accent-500/40',
      'focus:border-accent-500'
    ].join(' ')
  };

  // Handle option selection
  const handleOptionSelect = (optionValue: string) => {
    let newValues: string[];

    if (multiple) {
      if (selectedValues.includes(optionValue)) {
        newValues = selectedValues.filter(val => val !== optionValue);
      } else {
        newValues = [...selectedValues, optionValue];
      }
    } else {
      newValues = [optionValue];
      setIsOpen(false);
    }

    setSelectedValues(newValues);
    onChange?.(multiple ? newValues : newValues[0] || '');
    setSearchQuery('');
  };

  // Handle clear selection
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedValues([]);
    onChange?.(multiple ? [] : '');
    setSearchQuery('');
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;

    switch (e.key) {
      case 'Enter':
      case ' ':
        if (!isOpen) {
          setIsOpen(true);
        } else if (focusedIndex >= 0) {
          handleOptionSelect(filteredOptions[focusedIndex].value);
        }
        e.preventDefault();
        break;

      case 'Escape':
        setIsOpen(false);
        setFocusedIndex(-1);
        break;

      case 'ArrowDown':
        if (!isOpen) {
          setIsOpen(true);
        } else {
          setFocusedIndex(prev => 
            prev < filteredOptions.length - 1 ? prev + 1 : 0
          );
        }
        e.preventDefault();
        break;

      case 'ArrowUp':
        if (isOpen) {
          setFocusedIndex(prev => 
            prev > 0 ? prev - 1 : filteredOptions.length - 1
          );
        }
        e.preventDefault();
        break;
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, searchable]);

  const triggerClasses = [
    'relative',
    'w-full',
    'flex',
    'items-center',
    'justify-between',
    'rounded-lg',
    'transition-all',
    'duration-200',
    'cursor-pointer',
    sizeClasses[size].trigger,
    variantClasses[variant],
    error ? 'border-error-500' : '',
    disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-accent-500/60',
    isOpen ? 'ring-2 ring-accent-500/20' : ''
  ].join(' ');

  const selectedLabels = getSelectedLabels();
  const displayText = selectedLabels.length > 0 
    ? (multiple && selectedLabels.length > 1 
        ? `${selectedLabels.length} ${t('common.selected', 'selected')}`
        : selectedLabels[0])
    : placeholder || t('common.selectOption', 'Select an option');

  return (
    <div className={`relative w-full ${className}`} ref={selectRef}>
      {/* Label */}
      {label && (
        <label className={`block text-sm font-medium text-text-secondary mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
          {label}
          {required && <span className="text-error-500 ml-1">*</span>}
        </label>
      )}

      {/* Trigger */}
      <div
        className={triggerClasses}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        tabIndex={disabled ? -1 : 0}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-label={label}
      >
        <span className={`flex-1 truncate ${selectedLabels.length === 0 ? 'text-text-tertiary' : 'text-text-primary'} ${isRTL ? 'text-right' : 'text-left'}`}>
          {displayText}
        </span>

        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          {/* Clear button */}
          {clearable && selectedValues.length > 0 && !disabled && (
            <button
              onClick={handleClear}
              className="text-gray-400 hover:text-gray-300 transition-colors"
              aria-label={t('common.clear', 'Clear')}
            >
              <X className={sizeClasses[size].icon} />
            </button>
          )}

          {/* Loading spinner */}
          {loading && (
            <div className={`animate-spin text-accent-500 ${sizeClasses[size].icon}`}>
              <div className="w-full h-full border-2 border-current border-t-transparent rounded-full" />
            </div>
          )}

          {/* Dropdown arrow */}
          <ChevronDown 
            className={`transition-transform duration-200 text-gray-400 ${sizeClasses[size].icon} ${isOpen ? 'rotate-180' : ''}`}
          />
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className={`absolute top-full ${isRTL ? 'right-0' : 'left-0'} w-full mt-1 bg-primary-500/95 backdrop-blur-sm border border-accent-500/30 rounded-lg shadow-xl z-50 max-h-60 overflow-hidden`}>
          {/* Search input */}
          {searchable && (
            <div className="p-3 border-b border-accent-500/20">
              <div className="relative">
                <Search className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4`} />
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    onSearch?.(e.target.value);
                  }}
                  placeholder={t('common.search', 'Search...')}
                  className={`w-full bg-transparent border border-accent-500/30 rounded-lg px-10 py-2 text-sm text-text-primary placeholder-text-tertiary focus:outline-none focus:border-accent-500 ${isRTL ? 'text-right' : 'text-left'}`}
                />
              </div>
            </div>
          )}

          {/* Options */}
          <div className="overflow-y-auto max-h-48" ref={optionsRef}>
            {filteredOptions.length === 0 ? (
              <div className={`${sizeClasses[size].option} text-text-tertiary text-center`}>
                {searchQuery ? t('common.noResults', 'No results found') : t('common.noOptions', 'No options available')}
              </div>
            ) : (
              filteredOptions.map((option, index) => {
                const isSelected = selectedValues.includes(option.value);
                const isFocused = index === focusedIndex;

                return (
                  <div
                    key={option.value}
                    className={`
                      ${sizeClasses[size].option}
                      flex items-center justify-between cursor-pointer transition-colors
                      ${isFocused ? 'bg-accent-500/20' : 'hover:bg-accent-500/10'}
                      ${isSelected ? 'bg-secondary-500/20 text-secondary-400' : 'text-white'}
                      ${option.disabled ? 'opacity-50 cursor-not-allowed' : ''}
                    `}
                    onClick={() => !option.disabled && handleOptionSelect(option.value)}
                    role="option"
                    aria-selected={isSelected}
                  >
                    <div className="flex items-center space-x-3 rtl:space-x-reverse flex-1">
                      {option.icon && (
                        <span className={sizeClasses[size].icon}>
                          {option.icon}
                        </span>
                      )}
                      <span className="truncate">{option.label}</span>
                    </div>

                    {isSelected && (
                      <Check className={`${sizeClasses[size].icon} text-secondary-400 flex-shrink-0`} />
                    )}
                  </div>
                );
              })
            )}
          </div>
        </div>
      )}

      {/* Helper text */}
      {(error || hint) && (
        <div className="mt-2">
          {error && (
            <p className="text-sm text-error-500">{error}</p>
          )}
          {hint && !error && (
            <p className="text-sm text-text-tertiary">{hint}</p>
          )}
        </div>
      )}
    </div>
  );
};

export default Select;
