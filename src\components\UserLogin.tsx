import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useNotification } from '../hooks/useNotification';
import { signIn, signUp } from '../lib/apiServices';
import {
  Lock,
  User,
  Eye,
  EyeOff,
  Shield,
  Zap,
  Mail,
  UserPlus,
  LogIn,
  Sword,
  Target,
  Star,
  Settings
} from 'lucide-react';

interface UserLoginProps {
  onClose: () => void;
}

const UserLogin: React.FC<UserLoginProps> = ({ onClose }) => {
  const { t, language } = useTranslation();
  const { showNotification } = useNotification();
  const [isSignUp, setIsSignUp] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    username: '',
    full_name: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (isSignUp) {
        const { error } = await signUp(formData.email, formData.password, {
          username: formData.username,
          full_name: formData.full_name
        });
        
        if (error) {
          showNotification({
            type: 'error',
            title: t('common.registrationError'),
            message: error.message
          });
        } else {
          showNotification({
            type: 'success',
            title: t('common.success'),
            message: t('notifications.accountCreateSuccess')
          });
          onClose();
          // Refresh the page to update auth state
          window.location.reload();
        }
      } else {
        const { error } = await signIn(formData.email, formData.password);
        
        if (error) {
          showNotification({
            type: 'error',
            title: t('common.loginError'),
            message: t('common.invalidCredentials')
          });
        } else {
          showNotification({
            type: 'success',
            title: t('common.success'),
            message: t('common.loginSuccess')
          });
          onClose();
          // Refresh the page to update auth state
          window.location.reload();
        }
      }
    } catch (error: any) {
      showNotification({
        type: 'error',
        title: t('common.unexpectedError'),
        message: error.message || t('common.unexpectedErrorOccurred')
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };



  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-primary to-background border border-accent/30 rounded-2xl w-full max-w-5xl relative overflow-hidden">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 text-gray-400 hover:text-white transition-colors duration-200 bg-black/20 rounded-full p-2"
        >
          ✕
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-2">
          {/* Left Side - Royal Security System */}
          <div className="relative h-64 lg:h-auto bg-gradient-to-br from-yellow-600/20 to-yellow-800/20 flex items-center justify-center overflow-hidden group">
            {/* Royal Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-600/10 to-amber-800/10"></div>

            {/* Animated Elements */}
            <div className="absolute top-10 left-10 w-2 h-2 bg-yellow-500 rounded-full animate-ping group-hover:scale-150 transition-transform duration-500"></div>
            <div className="absolute bottom-20 right-15 w-1 h-1 bg-amber-400 rounded-full animate-pulse group-hover:bg-yellow-500 transition-colors duration-500"></div>
            <div className="absolute top-1/3 right-10 w-1 h-1 bg-yellow-500 rounded-full animate-ping delay-1000 group-hover:w-2 group-hover:h-2 transition-all duration-500"></div>

            {/* Royal Security Character */}
            <div className="relative z-10 text-center group-hover:scale-105 transition-transform duration-500">
              {/* Main Security Circle */}
              <div className="relative w-40 h-40 mx-auto mb-6">
                {/* Outer Ring - Royal Gold */}
                <div className="absolute inset-0 bg-gradient-to-br from-yellow-500 to-amber-600 rounded-full shadow-2xl group-hover:shadow-yellow-500/50 transition-all duration-500 animate-pulse"></div>

                {/* Inner Security Core */}
                <div className="absolute inset-2 bg-gradient-to-br from-gray-900 to-black rounded-full flex items-center justify-center border-2 border-yellow-500/50 group-hover:border-yellow-400/70 transition-all duration-500">
                  <Shield className="w-16 h-16 text-yellow-500 group-hover:rotate-12 group-hover:scale-110 transition-transform duration-500" />
                </div>

                {/* Floating Security Orbs */}
                <div className="absolute -top-2 -left-2 w-6 h-6 bg-yellow-500/40 rounded-full animate-pulse group-hover:bg-yellow-500/60 transition-colors duration-500"></div>
                <div className="absolute -bottom-2 -right-2 w-4 h-4 bg-amber-400/40 rounded-full animate-pulse delay-500 group-hover:bg-amber-400/60 transition-colors duration-500"></div>
                <div className="absolute top-1/2 -right-4 w-3 h-3 bg-yellow-500/30 rounded-full animate-ping group-hover:bg-yellow-500/50 transition-colors duration-500"></div>
                <div className="absolute top-1/4 -left-4 w-2 h-2 bg-amber-400/30 rounded-full animate-ping delay-700 group-hover:bg-amber-400/50 transition-colors duration-500"></div>
              </div>

              {/* Security Stats */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse group-hover:scale-105 transition-transform duration-300">
                  <Shield className="w-5 h-5 text-yellow-500 group-hover:text-amber-400 transition-colors duration-300" />
                  <span className="text-yellow-100 font-semibold">{language === 'ar' ? 'مستوى الحماية: 100' : 'Protection Level: 100'}</span>
                </div>
                <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse group-hover:scale-105 transition-transform duration-300 delay-100">
                  <Target className="w-5 h-5 text-amber-400 group-hover:text-yellow-500 transition-colors duration-300" />
                  <span className="text-yellow-100 font-semibold">{language === 'ar' ? 'دقة الهجوم: 95%' : 'Attack Accuracy: 95%'}</span>
                </div>
                <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse group-hover:scale-105 transition-transform duration-300 delay-200">
                  <Settings className="w-5 h-5 text-yellow-500 group-hover:text-amber-400 transition-colors duration-300" />
                  <span className="text-yellow-100 font-semibold">{language === 'ar' ? 'التقييم: ⭐⭐⭐⭐⭐' : 'Rating: ⭐⭐⭐⭐⭐'}</span>
                </div>
              </div>

              <h2 className="text-3xl font-bold text-yellow-100 mb-2 group-hover:text-yellow-300 transition-colors duration-300">
                {t('site.name')}
              </h2>
              <p className="text-amber-300 group-hover:text-yellow-200 transition-colors duration-300 mb-4">
                {t('site.tagline')}
              </p>

              {/* Interactive Elements */}
              <div className="flex justify-center items-center space-x-4 rtl:space-x-reverse">
                <div className="h-px bg-gradient-to-r from-transparent via-yellow-500 to-transparent w-16 group-hover:w-24 group-hover:via-amber-400 transition-all duration-500"></div>
                <div className="relative">
                  <Settings className="w-8 h-8 text-yellow-500 group-hover:scale-125 group-hover:rotate-180 transition-transform duration-500" />
                  <div className="absolute inset-0 bg-yellow-500/20 rounded-full animate-ping group-hover:bg-amber-400/20 transition-colors duration-500"></div>
                </div>
                <div className="h-px bg-gradient-to-r from-transparent via-yellow-500 to-transparent w-16 group-hover:w-24 group-hover:via-amber-400 transition-all duration-500"></div>
              </div>
            </div>

            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10 group-hover:opacity-20 transition-opacity duration-500">
              <div className="absolute inset-0 bg-grid-pattern"></div>
            </div>

            {/* Floating Particles */}
            <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-secondary rounded-full animate-ping delay-300 group-hover:w-2 group-hover:h-2 transition-all duration-500"></div>
            <div className="absolute bottom-1/3 right-1/3 w-1 h-1 bg-accent rounded-full animate-pulse delay-700 group-hover:w-2 group-hover:h-2 transition-all duration-500"></div>
          </div>

          {/* Right Side - Form */}
          <div className="p-8">
            {/* Header */}
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-gradient-to-br from-secondary to-accent rounded-full flex items-center justify-center mx-auto mb-4">
                {isSignUp ? <UserPlus className="w-8 h-8 text-primary" /> : <LogIn className="w-8 h-8 text-primary" />}
              </div>
              <h2 className="text-2xl font-bold text-white mb-2">
                {isSignUp
                  ? t('auth.createAccount', 'Create New Account')
                  : t('nav.login')
                }
              </h2>
              <p className="text-gray-400">
                {isSignUp
                  ? t('auth.joinDescription', 'Join Khanfashariya and get the most powerful systems')
                  : t('auth.loginDescription', 'Access your account to view your services')
                }
              </p>
            </div>



            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-6">
              {isSignUp && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {t('auth.fullName')}
                    </label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        name="full_name"
                        value={formData.full_name}
                        onChange={handleChange}
                        required={isSignUp}
                        className="w-full pl-10 pr-4 py-3 bg-primary/50 border border-accent/30 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200 text-white placeholder-gray-400"
                        placeholder={t('auth.enterFullName')}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {t('auth.username')}
                    </label>
                    <div className="relative">
                      <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        name="username"
                        value={formData.username}
                        onChange={handleChange}
                        required={isSignUp}
                        className="w-full pl-10 pr-4 py-3 bg-primary/50 border border-accent/30 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200 text-white placeholder-gray-400"
                        placeholder={t('auth.enterUsername')}
                      />
                    </div>
                  </div>
                </>
              )}

              <div>
                <label className="block text-sm font-medium text-secondary mb-2">
                  {t('contact.email')}
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full pl-10 pr-4 py-3 bg-primary/50 border border-accent/30 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200 text-white placeholder-gray-400"
                    placeholder={t('auth.enterEmail')}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-secondary mb-2">
                  {t('auth.password')}
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    required
                    className="w-full pl-10 pr-12 py-3 bg-primary/50 border border-accent/30 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-200 text-white placeholder-gray-400"
                    placeholder={t('auth.enterPassword')}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-yellow-600 to-amber-700 hover:from-amber-600 hover:to-yellow-700 text-black font-bold py-3 px-6 rounded-lg transition-all duration-300 flex items-center justify-center space-x-2 rtl:space-x-reverse shadow-lg hover:shadow-yellow-500/25 group border-2 border-transparent hover:border-yellow-400/30 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                    <span>{t('common.loading')}</span>
                  </>
                ) : (
                  <>
                    <Zap className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
                    <span className="group-hover:scale-105 transition-transform duration-300">
                      {isSignUp
                        ? t('auth.createAccountButton')
                        : t('auth.loginButton')
                      }
                    </span>
                  </>
                )}
              </button>
            </form>

            {/* Toggle Form */}
            <div className="mt-6 text-center">
              <button
                onClick={() => setIsSignUp(!isSignUp)}
                className="text-accent hover:text-secondary transition-colors duration-200"
              >
                {isSignUp
                  ? t('auth.haveAccount')
                  : t('auth.noAccount')
                }
              </button>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default UserLogin;