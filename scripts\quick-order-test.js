/**
 * Quick Order Test
 * Simple test to verify order data display fixes
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

async function testOrderData() {
  try {
    console.log('🔍 Testing Order Data...');
    
    // Test admin login
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (!loginResponse.data.success) {
      console.log('❌ Admin login failed');
      return;
    }
    
    console.log('✅ Admin login successful');
    
    const authHeaders = {
      headers: { Authorization: `Bearer ${loginResponse.data.data.token}` }
    };
    
    // Test orders fetch
    const ordersResponse = await axios.get(`${API_BASE}/admin/orders`, authHeaders);
    
    if (!ordersResponse.data.success) {
      console.log('❌ Failed to fetch orders');
      return;
    }
    
    const orders = ordersResponse.data.data.orders || [];
    console.log(`✅ Found ${orders.length} orders`);
    
    if (orders.length > 0) {
      const testOrder = orders[0];
      console.log('\n📋 Sample Order Data:');
      console.log(`   ID: ${testOrder.id}`);
      console.log(`   Order Number: ${testOrder.order_number || 'N/A'}`);
      console.log(`   Type: ${testOrder.order_type}`);
      console.log(`   Category: ${testOrder.order_category || 'N/A'}`);
      console.log(`   Status: ${testOrder.status}`);
      console.log(`   Item Name AR: ${testOrder.item_name_ar || 'N/A'}`);
      console.log(`   Item Name EN: ${testOrder.item_name_en || 'N/A'}`);
      console.log(`   Username: ${testOrder.username || 'N/A'}`);
      console.log(`   Email: ${testOrder.email || 'N/A'}`);
      console.log(`   Created: ${testOrder.created_at}`);
      console.log(`   Updated: ${testOrder.updated_at}`);
      
      console.log('\n💰 Pricing Data:');
      console.log(`   Final Price: $${testOrder.final_price || 0}`);
      console.log(`   Total Price: $${testOrder.total_price || 0}`);
      console.log(`   Base Price: $${testOrder.base_price || 0}`);
      console.log(`   Add-ons Price: $${testOrder.addons_price || 0}`);
      console.log(`   Unit Price: $${testOrder.unit_price || 0}`);
      
      // Check for zero price issues
      const hasValidPrice = (testOrder.final_price > 0) || (testOrder.total_price > 0) || (testOrder.base_price > 0) || (testOrder.unit_price > 0);
      console.log(`   Price Status: ${hasValidPrice ? '✅ Valid' : '❌ Zero Price Issue'}`);
      
      // Check for date issues
      const hasValidDate = testOrder.created_at && testOrder.created_at !== 'Invalid Date';
      console.log(`   Date Status: ${hasValidDate ? '✅ Valid' : '❌ Invalid Date'}`);
      
      // Check for name issues
      const hasValidName = testOrder.item_name_ar || testOrder.item_name_en;
      console.log(`   Name Status: ${hasValidName ? '✅ Valid' : '❌ Missing Names'}`);
      
      // Check for user data
      const hasUserData = testOrder.username || testOrder.email;
      console.log(`   User Data: ${hasUserData ? '✅ Valid' : '❌ Missing User Data'}`);
      
      // Check premium orders
      if (testOrder.order_type === 'premium_package' || testOrder.order_type === 'premium_content') {
        console.log('\n👑 Premium Order Details:');
        console.log(`   Order Details: ${testOrder.order_details ? '✅ Present' : '❌ Missing'}`);
        console.log(`   Customer Requirements: ${testOrder.customer_requirements ? '✅ Present' : '❌ Missing'}`);
        console.log(`   Selected Add-ons: ${testOrder.selected_addons ? '✅ Present' : '❌ Missing'}`);
      }
    }
    
    console.log('\n🎉 Order data test completed!');
    
  } catch (error) {
    console.log('❌ Test failed:', error.response?.data?.error || error.message);
  }
}

testOrderData();
