import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotification } from '../../hooks/useNotification';
import Button from '../ui/Button';
import Card from '../ui/Card';
import Input from '../ui/Input';
import Modal from '../ui/Modal';
import BackButton from '../ui/BackButton';
import { 
  Save, 
  Download, 
  Upload,
  Trash2, 
  Plus, 
  Settings, 
  Globe, 
  ArrowLeft, 
  Home, 
  TestTube,
  Database,
  Mail,
  Shield,
  Palette,
  Code,
  Monitor,
  Server,
  Users,
  Bell,
  Lock,
  Eye,
  EyeOff,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Info,
  Zap,
  Cloud,
  HardDrive,
  Wifi,
  Activity
} from 'lucide-react';
import LanguageTest from '../LanguageTest';

interface ComprehensiveSystemSettingsProps {
  onBack?: () => void;
}

interface SystemConfig {
  general: {
    siteName: { ar: string; en: string };
    siteDescription: { ar: string; en: string };
    defaultLanguage: 'ar' | 'en';
    timezone: string;
    dateFormat: string;
    currency: string;
    maintenanceMode: boolean;
    debugMode: boolean;
  };
  database: {
    host: string;
    port: number;
    name: string;
    username: string;
    password: string;
    connectionPool: number;
    backupEnabled: boolean;
    backupFrequency: 'daily' | 'weekly' | 'monthly';
  };
  email: {
    provider: 'smtp' | 'sendgrid' | 'mailgun';
    host: string;
    port: number;
    username: string;
    password: string;
    encryption: 'tls' | 'ssl' | 'none';
    fromEmail: string;
    fromName: string;
  };
  security: {
    sessionTimeout: number;
    maxLoginAttempts: number;
    passwordMinLength: number;
    requireSpecialChars: boolean;
    twoFactorEnabled: boolean;
    ipWhitelist: string[];
    rateLimitEnabled: boolean;
    rateLimitRequests: number;
  };
  ui: {
    theme: 'dark' | 'light' | 'auto';
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
    logoUrl: string;
    faviconUrl: string;
    customCSS: string;
    showBranding: boolean;
  };
  notifications: {
    emailEnabled: boolean;
    smsEnabled: boolean;
    pushEnabled: boolean;
    adminNotifications: boolean;
    userNotifications: boolean;
    orderNotifications: boolean;
    systemAlerts: boolean;
  };
  performance: {
    cacheEnabled: boolean;
    cacheDriver: 'redis' | 'memcached' | 'file';
    cacheTimeout: number;
    compressionEnabled: boolean;
    minifyAssets: boolean;
    cdnEnabled: boolean;
    cdnUrl: string;
  };
  testing: {
    testModeEnabled: boolean;
    allowedTestUsers: string[];
    testDataReset: boolean;
    mockPayments: boolean;
    debugLogging: boolean;
  };
}

/**
 * Comprehensive System Settings Manager
 */
const ComprehensiveSystemSettings: React.FC<ComprehensiveSystemSettingsProps> = ({ onBack }) => {
  const { t, language } = useTranslation();
  const { showNotification } = useNotification();
  
  const [activeTab, setActiveTab] = useState<keyof SystemConfig>('general');
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState<'backup' | 'restore' | 'reset' | 'test'>('backup');
  const [showPassword, setShowPassword] = useState<Record<string, boolean>>({});
  const [showLanguageTest, setShowLanguageTest] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);

  const [config, setConfig] = useState<SystemConfig>({
    general: {
      siteName: { ar: 'خان فاشاريا', en: 'Khan Fashariya' },
      siteDescription: { ar: 'موقع خدمات ميتين 2', en: 'Metin2 Services Website' },
      defaultLanguage: 'ar',
      timezone: 'Asia/Riyadh',
      dateFormat: 'DD/MM/YYYY',
      currency: 'USD',
      maintenanceMode: false,
      debugMode: false
    },
    database: {
      host: 'localhost',
      port: 3306,
      name: 'khanfashariya',
      username: 'root',
      password: '',
      connectionPool: 10,
      backupEnabled: true,
      backupFrequency: 'daily'
    },
    email: {
      provider: 'smtp',
      host: 'smtp.gmail.com',
      port: 587,
      username: '',
      password: '',
      encryption: 'tls',
      fromEmail: '<EMAIL>',
      fromName: 'Khan Fashariya'
    },
    security: {
      sessionTimeout: 3600,
      maxLoginAttempts: 5,
      passwordMinLength: 8,
      requireSpecialChars: true,
      twoFactorEnabled: false,
      ipWhitelist: [],
      rateLimitEnabled: true,
      rateLimitRequests: 100
    },
    ui: {
      theme: 'dark',
      primaryColor: '#1a1a2e',
      secondaryColor: '#16213e',
      accentColor: '#0f3460',
      logoUrl: '',
      faviconUrl: '',
      customCSS: '',
      showBranding: true
    },
    notifications: {
      emailEnabled: true,
      smsEnabled: false,
      pushEnabled: true,
      adminNotifications: true,
      userNotifications: true,
      orderNotifications: true,
      systemAlerts: true
    },
    performance: {
      cacheEnabled: true,
      cacheDriver: 'redis',
      cacheTimeout: 3600,
      compressionEnabled: true,
      minifyAssets: true,
      cdnEnabled: false,
      cdnUrl: ''
    },
    testing: {
      testModeEnabled: false,
      allowedTestUsers: [],
      testDataReset: false,
      mockPayments: false,
      debugLogging: false
    }
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setLoading(true);
    try {
      // Load settings from backend
      // For now, we'll use the default config
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم تحميل الإعدادات بنجاح' : 'Settings loaded successfully'
      });
    } catch (error) {
      console.error('Error loading settings:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في تحميل الإعدادات' : 'Failed to load settings'
      });
    }
    setLoading(false);
  };

  const saveSettings = async () => {
    setLoading(true);
    try {
      // Save settings to backend
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      setUnsavedChanges(false);
      showNotification({
        type: 'success',
        message: language === 'ar' ? 'تم حفظ الإعدادات بنجاح' : 'Settings saved successfully'
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في حفظ الإعدادات' : 'Failed to save settings'
      });
    }
    setLoading(false);
  };

  const updateConfig = (section: keyof SystemConfig, field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
    setUnsavedChanges(true);
  };

  const togglePassword = (field: string) => {
    setShowPassword(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const tabs = [
    { id: 'general', label: language === 'ar' ? 'عام' : 'General', icon: Settings },
    { id: 'database', label: language === 'ar' ? 'قاعدة البيانات' : 'Database', icon: Database },
    { id: 'email', label: language === 'ar' ? 'البريد الإلكتروني' : 'Email', icon: Mail },
    { id: 'security', label: language === 'ar' ? 'الأمان' : 'Security', icon: Shield },
    { id: 'ui', label: language === 'ar' ? 'واجهة المستخدم' : 'User Interface', icon: Palette },
    { id: 'notifications', label: language === 'ar' ? 'الإشعارات' : 'Notifications', icon: Bell },
    { id: 'performance', label: language === 'ar' ? 'الأداء' : 'Performance', icon: Zap },
    { id: 'testing', label: language === 'ar' ? 'الاختبار' : 'Testing', icon: TestTube }
  ];

  if (loading && !config.general.siteName.ar) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-secondary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-6 space-y-4 lg:space-y-0">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          {onBack && (
            <BackButton onClick={onBack} variant="back" size="md" />
          )}
          <div>
            <h1 className="text-2xl font-bold text-white flex items-center">
              <Settings className="w-8 h-8 mr-3 text-accent" />
              {language === 'ar' ? 'إعدادات النظام الشاملة' : 'Comprehensive System Settings'}
            </h1>
            <p className="text-gray-400 text-sm mt-1">
              {language === 'ar' ? 'التحكم الكامل في جميع إعدادات الموقع والنظام' : 'Complete control over all website and system settings'}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          {unsavedChanges && (
            <div className="flex items-center space-x-2 rtl:space-x-reverse text-yellow-400">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-sm">{language === 'ar' ? 'تغييرات غير محفوظة' : 'Unsaved Changes'}</span>
            </div>
          )}
          <Button variant="outline" size="sm" onClick={() => setShowModal(true)}>
            <Download className="w-4 h-4 mr-2" />
            {language === 'ar' ? 'نسخ احتياطي' : 'Backup'}
          </Button>
          <Button variant="primary" onClick={saveSettings} disabled={loading}>
            {loading ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            {language === 'ar' ? 'حفظ الإعدادات' : 'Save Settings'}
          </Button>
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card className={`${config.general.maintenanceMode ? 'bg-red-500/10 border-red-500/20' : 'bg-green-500/10 border-green-500/20'}`}>
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${config.general.maintenanceMode ? 'text-red-300' : 'text-green-300'}`}>
                  {language === 'ar' ? 'حالة الموقع' : 'Site Status'}
                </p>
                <p className="text-lg font-bold text-white">
                  {config.general.maintenanceMode ?
                    (language === 'ar' ? 'صيانة' : 'Maintenance') :
                    (language === 'ar' ? 'نشط' : 'Active')
                  }
                </p>
              </div>
              {config.general.maintenanceMode ? (
                <AlertTriangle className="w-8 h-8 text-red-400" />
              ) : (
                <CheckCircle className="w-8 h-8 text-green-400" />
              )}
            </div>
          </Card.Body>
        </Card>

        <Card className="bg-blue-500/10 border-blue-500/20">
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-300 text-sm font-medium">{language === 'ar' ? 'قاعدة البيانات' : 'Database'}</p>
                <p className="text-lg font-bold text-white">
                  {language === 'ar' ? 'متصلة' : 'Connected'}
                </p>
              </div>
              <Database className="w-8 h-8 text-blue-400" />
            </div>
          </Card.Body>
        </Card>

        <Card className={`${config.performance.cacheEnabled ? 'bg-purple-500/10 border-purple-500/20' : 'bg-gray-500/10 border-gray-500/20'}`}>
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${config.performance.cacheEnabled ? 'text-purple-300' : 'text-gray-300'}`}>
                  {language === 'ar' ? 'التخزين المؤقت' : 'Cache'}
                </p>
                <p className="text-lg font-bold text-white">
                  {config.performance.cacheEnabled ?
                    (language === 'ar' ? 'مفعل' : 'Enabled') :
                    (language === 'ar' ? 'معطل' : 'Disabled')
                  }
                </p>
              </div>
              <Zap className={`w-8 h-8 ${config.performance.cacheEnabled ? 'text-purple-400' : 'text-gray-400'}`} />
            </div>
          </Card.Body>
        </Card>

        <Card className={`${config.security.twoFactorEnabled ? 'bg-green-500/10 border-green-500/20' : 'bg-yellow-500/10 border-yellow-500/20'}`}>
          <Card.Body>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${config.security.twoFactorEnabled ? 'text-green-300' : 'text-yellow-300'}`}>
                  {language === 'ar' ? 'الأمان' : 'Security'}
                </p>
                <p className="text-lg font-bold text-white">
                  {config.security.twoFactorEnabled ?
                    (language === 'ar' ? 'محمي' : 'Secured') :
                    (language === 'ar' ? 'أساسي' : 'Basic')
                  }
                </p>
              </div>
              <Shield className={`w-8 h-8 ${config.security.twoFactorEnabled ? 'text-green-400' : 'text-yellow-400'}`} />
            </div>
          </Card.Body>
        </Card>
      </div>

      {/* Settings Tabs */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Sidebar Navigation */}
        <div className="lg:w-64 flex-shrink-0">
          <Card>
            <Card.Body className="p-0">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as keyof SystemConfig)}
                      className={`w-full flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 text-left transition-colors ${
                        activeTab === tab.id
                          ? 'bg-secondary/20 text-secondary border-r-2 border-secondary'
                          : 'text-gray-300 hover:bg-accent/10 hover:text-white'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span className="font-medium">{tab.label}</span>
                    </button>
                  );
                })}
              </nav>
            </Card.Body>
          </Card>

          {/* Quick Actions */}
          <Card className="mt-4">
            <Card.Body>
              <h3 className="text-lg font-semibold text-white mb-4">
                {language === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}
              </h3>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => setShowLanguageTest(true)}
                >
                  <TestTube className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'اختبار اللغة' : 'Language Test'}
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Activity className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'مراقبة النظام' : 'System Monitor'}
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'إعادة تشغيل الخدمات' : 'Restart Services'}
                </Button>
              </div>
            </Card.Body>
          </Card>
        </div>

        {/* Main Content Area */}
        <div className="flex-1">
          <Card>
            <Card.Body>
              {/* General Settings */}
              {activeTab === 'general' && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-bold text-white mb-4">
                      {language === 'ar' ? 'الإعدادات العامة' : 'General Settings'}
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Input
                        label={`${language === 'ar' ? 'اسم الموقع' : 'Site Name'} (العربية)`}
                        value={config.general.siteName.ar}
                        onChange={(e) => updateConfig('general', 'siteName', { ...config.general.siteName, ar: e.target.value })}
                      />
                      <Input
                        label={`${language === 'ar' ? 'اسم الموقع' : 'Site Name'} (English)`}
                        value={config.general.siteName.en}
                        onChange={(e) => updateConfig('general', 'siteName', { ...config.general.siteName, en: e.target.value })}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      {language === 'ar' ? 'وصف الموقع' : 'Site Description'}
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <textarea
                        value={config.general.siteDescription.ar}
                        onChange={(e) => updateConfig('general', 'siteDescription', { ...config.general.siteDescription, ar: e.target.value })}
                        rows={3}
                        className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                        placeholder="وصف الموقع بالعربية..."
                      />
                      <textarea
                        value={config.general.siteDescription.en}
                        onChange={(e) => updateConfig('general', 'siteDescription', { ...config.general.siteDescription, en: e.target.value })}
                        rows={3}
                        className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white resize-none"
                        placeholder="Site description in English..."
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-secondary mb-2">
                        {language === 'ar' ? 'اللغة الافتراضية' : 'Default Language'}
                      </label>
                      <select
                        value={config.general.defaultLanguage}
                        onChange={(e) => updateConfig('general', 'defaultLanguage', e.target.value)}
                        className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                      >
                        <option value="ar">العربية</option>
                        <option value="en">English</option>
                      </select>
                    </div>
                    <Input
                      label={language === 'ar' ? 'المنطقة الزمنية' : 'Timezone'}
                      value={config.general.timezone}
                      onChange={(e) => updateConfig('general', 'timezone', e.target.value)}
                    />
                    <Input
                      label={language === 'ar' ? 'العملة' : 'Currency'}
                      value={config.general.currency}
                      onChange={(e) => updateConfig('general', 'currency', e.target.value)}
                    />
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <input
                        type="checkbox"
                        id="maintenanceMode"
                        checked={config.general.maintenanceMode}
                        onChange={(e) => updateConfig('general', 'maintenanceMode', e.target.checked)}
                        className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                      />
                      <label htmlFor="maintenanceMode" className="text-white">
                        {language === 'ar' ? 'وضع الصيانة' : 'Maintenance Mode'}
                      </label>
                    </div>
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <input
                        type="checkbox"
                        id="debugMode"
                        checked={config.general.debugMode}
                        onChange={(e) => updateConfig('general', 'debugMode', e.target.checked)}
                        className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                      />
                      <label htmlFor="debugMode" className="text-white">
                        {language === 'ar' ? 'وضع التطوير' : 'Debug Mode'}
                      </label>
                    </div>
                  </div>
                </div>
              )}

              {/* Database Settings */}
              {activeTab === 'database' && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-bold text-white mb-4">
                      {language === 'ar' ? 'إعدادات قاعدة البيانات' : 'Database Settings'}
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Input
                        label={language === 'ar' ? 'المضيف' : 'Host'}
                        value={config.database.host}
                        onChange={(e) => updateConfig('database', 'host', e.target.value)}
                        leftIcon={<Server />}
                      />
                      <Input
                        label={language === 'ar' ? 'المنفذ' : 'Port'}
                        type="number"
                        value={config.database.port}
                        onChange={(e) => updateConfig('database', 'port', Number(e.target.value))}
                      />
                      <Input
                        label={language === 'ar' ? 'اسم قاعدة البيانات' : 'Database Name'}
                        value={config.database.name}
                        onChange={(e) => updateConfig('database', 'name', e.target.value)}
                        leftIcon={<Database />}
                      />
                      <Input
                        label={language === 'ar' ? 'اسم المستخدم' : 'Username'}
                        value={config.database.username}
                        onChange={(e) => updateConfig('database', 'username', e.target.value)}
                        leftIcon={<Users />}
                      />
                      <div className="relative">
                        <Input
                          label={language === 'ar' ? 'كلمة المرور' : 'Password'}
                          type={showPassword.dbPassword ? 'text' : 'password'}
                          value={config.database.password}
                          onChange={(e) => updateConfig('database', 'password', e.target.value)}
                          leftIcon={<Lock />}
                        />
                        <button
                          type="button"
                          onClick={() => togglePassword('dbPassword')}
                          className="absolute right-3 top-9 text-gray-400 hover:text-white"
                        >
                          {showPassword.dbPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                      </div>
                      <Input
                        label={language === 'ar' ? 'مجموعة الاتصالات' : 'Connection Pool'}
                        type="number"
                        value={config.database.connectionPool}
                        onChange={(e) => updateConfig('database', 'connectionPool', Number(e.target.value))}
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <input
                        type="checkbox"
                        id="backupEnabled"
                        checked={config.database.backupEnabled}
                        onChange={(e) => updateConfig('database', 'backupEnabled', e.target.checked)}
                        className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                      />
                      <label htmlFor="backupEnabled" className="text-white">
                        {language === 'ar' ? 'تفعيل النسخ الاحتياطية' : 'Enable Automatic Backups'}
                      </label>
                    </div>
                    {config.database.backupEnabled && (
                      <div>
                        <label className="block text-sm font-medium text-secondary mb-2">
                          {language === 'ar' ? 'تكرار النسخ الاحتياطية' : 'Backup Frequency'}
                        </label>
                        <select
                          value={config.database.backupFrequency}
                          onChange={(e) => updateConfig('database', 'backupFrequency', e.target.value)}
                          className="w-full md:w-48 px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                        >
                          <option value="daily">{language === 'ar' ? 'يومي' : 'Daily'}</option>
                          <option value="weekly">{language === 'ar' ? 'أسبوعي' : 'Weekly'}</option>
                          <option value="monthly">{language === 'ar' ? 'شهري' : 'Monthly'}</option>
                        </select>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Testing Settings */}
              {activeTab === 'testing' && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-bold text-white mb-4 flex items-center">
                      <TestTube className="w-6 h-6 mr-3 text-accent" />
                      {language === 'ar' ? 'إعدادات الاختبار' : 'Testing Settings'}
                    </h2>
                    <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 mb-6">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <AlertTriangle className="w-5 h-5 text-yellow-400" />
                        <p className="text-yellow-300 font-medium">
                          {language === 'ar' ? 'تحذير: هذه الإعدادات للاختبار فقط' : 'Warning: These settings are for testing purposes only'}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <input
                        type="checkbox"
                        id="testModeEnabled"
                        checked={config.testing.testModeEnabled}
                        onChange={(e) => updateConfig('testing', 'testModeEnabled', e.target.checked)}
                        className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                      />
                      <label htmlFor="testModeEnabled" className="text-white">
                        {language === 'ar' ? 'تفعيل وضع الاختبار' : 'Enable Test Mode'}
                      </label>
                    </div>
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <input
                        type="checkbox"
                        id="mockPayments"
                        checked={config.testing.mockPayments}
                        onChange={(e) => updateConfig('testing', 'mockPayments', e.target.checked)}
                        className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                      />
                      <label htmlFor="mockPayments" className="text-white">
                        {language === 'ar' ? 'محاكاة المدفوعات' : 'Mock Payments'}
                      </label>
                    </div>
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <input
                        type="checkbox"
                        id="debugLogging"
                        checked={config.testing.debugLogging}
                        onChange={(e) => updateConfig('testing', 'debugLogging', e.target.checked)}
                        className="w-4 h-4 text-secondary bg-primary border-accent rounded focus:ring-secondary"
                      />
                      <label htmlFor="debugLogging" className="text-white">
                        {language === 'ar' ? 'تسجيل التطوير' : 'Debug Logging'}
                      </label>
                    </div>
                  </div>

                  {/* Test Components Section */}
                  <div className="border-t border-accent/20 pt-6">
                    <h3 className="text-lg font-semibold text-white mb-4">
                      {language === 'ar' ? 'اختبار المكونات' : 'Component Testing'}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Button
                        variant="outline"
                        onClick={() => setShowLanguageTest(true)}
                        className="justify-start"
                      >
                        <Globe className="w-4 h-4 mr-2" />
                        {language === 'ar' ? 'اختبار اللغة والترجمة' : 'Language & Translation Test'}
                      </Button>
                      <Button variant="outline" className="justify-start">
                        <Bell className="w-4 h-4 mr-2" />
                        {language === 'ar' ? 'اختبار الإشعارات' : 'Notification Test'}
                      </Button>
                      <Button variant="outline" className="justify-start">
                        <Database className="w-4 h-4 mr-2" />
                        {language === 'ar' ? 'اختبار قاعدة البيانات' : 'Database Test'}
                      </Button>
                      <Button variant="outline" className="justify-start">
                        <Mail className="w-4 h-4 mr-2" />
                        {language === 'ar' ? 'اختبار البريد الإلكتروني' : 'Email Test'}
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </Card.Body>
          </Card>
        </div>
      </div>

      {/* Language Test Modal */}
      {showLanguageTest && (
        <Modal
          isOpen={showLanguageTest}
          onClose={() => setShowLanguageTest(false)}
          title={language === 'ar' ? 'اختبار اللغة والترجمة' : 'Language & Translation Test'}
          size="lg"
        >
          <Modal.Body>
            <LanguageTest />
          </Modal.Body>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowLanguageTest(false)}>
              {language === 'ar' ? 'إغلاق' : 'Close'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}
    </div>
  );
};

export default ComprehensiveSystemSettings;
