/**
 * Disable Rate Limiting Completely
 * 
 * This script completely disables rate limiting to fix the issue
 */

const fs = require('fs');
const path = require('path');

console.log('🚫 DISABLING RATE LIMITING COMPLETELY');
console.log('=' .repeat(50));

try {
  const serverPath = path.join(__dirname, '..', 'server', 'server.js');
  let content = fs.readFileSync(serverPath, 'utf8');
  
  console.log('📝 Original rate limiting code found');
  
  // Comment out the rate limiting middleware
  const rateLimitRegex = /app\.use\('\/api\/', limiter\);/g;
  content = content.replace(rateLimitRegex, '// app.use(\'/api/\', limiter); // DISABLED TO FIX INFINITE REQUESTS');
  
  // Also comment out auth rate limiting
  const authLimitRegex = /app\.use\('\/api\/auth', authLimiter, authRoutes\);/g;
  content = content.replace(authLimitRegex, 'app.use(\'/api/auth\', authRoutes); // authLimiter DISABLED');
  
  fs.writeFileSync(serverPath, content);
  
  console.log('✅ Rate limiting completely disabled!');
  console.log('\n📋 What was changed:');
  console.log('• Commented out: app.use(\'/api/\', limiter)');
  console.log('• Removed auth rate limiting');
  console.log('• All API endpoints now unlimited');
  
  console.log('\n🔄 Next steps:');
  console.log('1. Stop current server (Ctrl+C)');
  console.log('2. Start server: npm run dev:server');
  console.log('3. Clear browser cache');
  console.log('4. Refresh website');
  console.log('5. No more 429 errors!');
  
  console.log('\n⚠️  Note: This is for development only!');
  console.log('Re-enable rate limiting for production.');
  
} catch (error) {
  console.error('❌ Failed to disable rate limiting:', error.message);
  console.log('\n🔧 Manual fix:');
  console.log('1. Open server/server.js');
  console.log('2. Find: app.use(\'/api/\', limiter);');
  console.log('3. Comment it out: // app.use(\'/api/\', limiter);');
  console.log('4. Save and restart server');
}