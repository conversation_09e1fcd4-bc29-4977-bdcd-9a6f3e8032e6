/**
 * Performance Optimized Image Component
 * 
 * Advanced image component with comprehensive optimization features
 */

import React, { useState, useRef, useEffect } from 'react'
import { Skeleton } from './LoadingStates'

interface PerformanceOptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  sizes?: string
  priority?: boolean
  quality?: number
  placeholder?: 'blur' | 'empty' | 'skeleton'
  blurDataURL?: string
  onLoad?: () => void
  onError?: () => void
  fallbackSrc?: string
  webpSrc?: string
  avifSrc?: string
}

export const PerformanceOptimizedImage: React.FC<PerformanceOptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  sizes,
  priority = false,
  quality = 75,
  placeholder = 'skeleton',
  blurDataURL,
  onLoad,
  onError,
  fallbackSrc,
  webpSrc,
  avifSrc
}) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isError, setIsError] = useState(false)
  const [currentSrc, setCurrentSrc] = useState(src)
  const imgRef = useRef<HTMLImageElement>(null)
  const [isInView, setIsInView] = useState(priority)

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || !imgRef.current) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      {
        rootMargin: '50px'
      }
    )

    observer.observe(imgRef.current)
    return () => observer.disconnect()
  }, [priority])

  // Handle image load
  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }

  // Handle image error with fallback
  const handleError = () => {
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc)
      return
    }
    setIsError(true)
    onError?.()
  }

  // Generate responsive srcSet
  const generateSrcSet = (baseSrc: string) => {
    if (!width) return undefined

    const breakpoints = [0.5, 1, 1.5, 2]
    return breakpoints
      .map(multiplier => {
        const scaledWidth = Math.round(width * multiplier)
        return `${baseSrc}?w=${scaledWidth}&q=${quality} ${multiplier}x`
      })
      .join(', ')
  }

  // Render loading state
  if (!isInView || (!isLoaded && !isError)) {
    return (
      <div 
        ref={imgRef}
        className={`relative overflow-hidden ${className}`}
        style={{ width, height }}
      >
        {placeholder === 'blur' && blurDataURL ? (
          <img
            src={blurDataURL}
            alt=""
            className="absolute inset-0 w-full h-full object-cover filter blur-sm scale-110"
            aria-hidden="true"
          />
        ) : placeholder === 'skeleton' ? (
          <Skeleton 
            width="100%" 
            height="100%" 
            className="absolute inset-0"
          />
        ) : null}
        
        {isInView && (
          <picture>
            {avifSrc && <source srcSet={avifSrc} type="image/avif" />}
            {webpSrc && <source srcSet={webpSrc} type="image/webp" />}
            <img
              src={currentSrc}
              srcSet={generateSrcSet(currentSrc)}
              sizes={sizes}
              alt={alt}
              width={width}
              height={height}
              className="absolute inset-0 w-full h-full object-cover opacity-0 transition-opacity duration-300"
              onLoad={handleLoad}
              onError={handleError}
              loading={priority ? 'eager' : 'lazy'}
            />
          </picture>
        )}
      </div>
    )
  }

  // Render error state
  if (isError) {
    return (
      <div 
        className={`relative bg-background-tertiary flex items-center justify-center ${className}`}
        style={{ width, height }}
      >
        <div className="text-center text-text-tertiary">
          <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <p className="text-xs">فشل تحميل الصورة</p>
        </div>
      </div>
    )
  }

  // Render loaded image
  return (
    <picture className={className}>
      {avifSrc && <source srcSet={avifSrc} type="image/avif" />}
      {webpSrc && <source srcSet={webpSrc} type="image/webp" />}
      <img
        ref={imgRef}
        src={currentSrc}
        srcSet={generateSrcSet(currentSrc)}
        sizes={sizes}
        alt={alt}
        width={width}
        height={height}
        className={`transition-opacity duration-300 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
        onLoad={handleLoad}
        onError={handleError}
        loading={priority ? 'eager' : 'lazy'}
      />
    </picture>
  )
}

export default PerformanceOptimizedImage
