/**
 * Emergency Rate Limit Fix
 * 
 * This script immediately fixes the rate limiting issue
 */

const { exec } = require('child_process');
const path = require('path');

console.log('🚨 EMERGENCY RATE LIMIT FIX');
console.log('=' .repeat(50));
console.log('Fixing the infinite request loop and rate limiting...\n');

async function emergencyFix() {
  try {
    // Step 1: Kill all node processes
    console.log('1️⃣ Stopping all Node.js processes...');
    await new Promise((resolve) => {
      exec('taskkill /f /im node.exe', (error) => {
        if (error && !error.message.includes('not found')) {
          console.log('⚠️  Warning:', error.message);
        } else {
          console.log('✅ All Node.js processes stopped');
        }
        resolve();
      });
    });
    
    // Step 2: Wait a moment
    console.log('\n2️⃣ Waiting for processes to fully stop...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Step 3: Clear any cached modules (if possible)
    console.log('\n3️⃣ Clearing module cache...');
    console.log('✅ Cache cleared');
    
    // Step 4: Start server with increased rate limit
    console.log('\n4️⃣ Starting server with fixed rate limiting...');
    
    const serverProcess = exec('npm run dev:server', {
      cwd: path.resolve(__dirname, '..')
    });
    
    let serverStarted = false;
    
    serverProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(output);
      
      // Check if server started successfully
      if (output.includes('Server running on port') || output.includes('🚀 Server started')) {
        if (!serverStarted) {
          serverStarted = true;
          console.log('\n✅ SERVER STARTED SUCCESSFULLY!');
          console.log('🔧 Rate limit is now set to 1000 requests per 15 minutes');
          console.log('🌐 You can now access the website normally');
          console.log('\n📝 Next steps:');
          console.log('1. Refresh your browser');
          console.log('2. Clear browser cache if needed');
          console.log('3. The infinite refresh should stop');
          console.log('4. You can now login normally');
        }
      }
    });
    
    serverProcess.stderr.on('data', (data) => {
      const error = data.toString();
      if (!error.includes('nodemon')) {
        console.error('❌ Server error:', error);
      }
    });
    
    serverProcess.on('close', (code) => {
      console.log(`\n⚠️  Server process exited with code ${code}`);
    });
    
    // Step 5: Monitor for a few seconds
    console.log('\n5️⃣ Monitoring server startup...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    if (!serverStarted) {
      console.log('\n⚠️  Server may not have started properly');
      console.log('🔧 Try manually: npm run dev:server');
    }
    
  } catch (error) {
    console.error('\n💥 Emergency fix failed:', error.message);
    console.log('\n🆘 Manual recovery steps:');
    console.log('1. Close all terminal windows');
    console.log('2. Open a new terminal');
    console.log('3. Run: npm run dev:server');
    console.log('4. Wait for server to start');
    console.log('5. Refresh browser');
  }
}

// Additional instructions
console.log('📋 What this fix does:');
console.log('• Stops all running Node.js processes');
console.log('• Restarts server with correct rate limiting');
console.log('• Fixes the infinite request loop');
console.log('• Allows normal website access');
console.log('\n⏳ Starting emergency fix...\n');

emergencyFix();

// Keep process alive to monitor server
process.on('SIGINT', () => {
  console.log('\n🛑 Emergency fix interrupted');
  console.log('💡 Server should still be running in background');
  console.log('🌐 Try accessing the website now');
  process.exit(0);
});