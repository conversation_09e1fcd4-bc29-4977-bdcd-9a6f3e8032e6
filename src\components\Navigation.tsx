import React, { useState, useEffect, Suspense } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuth } from '../hooks/useAuth';
import { useNotification } from '../hooks/useNotification';
import { Menu, X, Globe, Sword, Shield, Wrench, Phone, Home, Settings, Zap, LogIn, LogOut, Crown, Users, ShoppingCart, User } from 'lucide-react';
import { LoadingSpinner } from './ui/LoadingStates';
import { TouchButton, MobileNav } from './ui/MobileOptimized';
import {
  UserLogin,
  UserDashboard,
  SimpleUserDashboard,
  AdminDashboard,
  UserManagement,
  OrderManagement,
  AdvancedOrderManagement,
  PremiumEdition,
  PremiumContentManager,
  CustomOrderForm,
  preloadCriticalComponents,
  preloadAdminComponents
} from './LazyComponents';

const Navigation: React.FC = () => {
  const { language, changeLanguage, t } = useTranslation();
  const { isAuthenticated, userProfile, isAdmin, logout } = useAuth();
  const { showNotification } = useNotification();
  const [isOpen, setIsOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState('home');
  const [showUserLogin, setShowUserLogin] = useState(false);
  const [showDashboard, setShowDashboard] = useState(false);
  const [showAdminDashboard, setShowAdminDashboard] = useState(false);
  const [showUserManagement, setShowUserManagement] = useState(false);
  const [showOrderManagement, setShowOrderManagement] = useState(false);
  const [showPremiumEdition, setShowPremiumEdition] = useState(false);
  const [showPremiumContentManager, setShowPremiumContentManager] = useState(false);
  const [showCustomOrderForm, setShowCustomOrderForm] = useState(false);


  // Enhanced mobile menu management with accessibility
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (isOpen && !target.closest('.mobile-nav-container') && !target.closest('.mobile-menu-button')) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Preload components based on user state
  useEffect(() => {
    preloadCriticalComponents();

    if (isAdmin) {
      preloadAdminComponents();
    }
  }, [isAdmin]);

  // The "Premium" item has been removed from this array to avoid duplication
  const navigationItems = [
    { id: 'home', label: t('nav.home'), icon: Home },
    { id: 'systems', label: t('nav.systems'), icon: Shield },
    { id: 'services', label: t('nav.services'), icon: Wrench },
    { id: 'customOrder', label: t('nav.customOrder'), icon: Settings },
    { id: 'contact', label: t('nav.contact'), icon: Phone },
  ];

  const handleLanguageToggle = () => {
    changeLanguage(language === 'ar' ? 'en' : 'ar');
  };

  const handlePageChange = (pageId: string) => {
    setCurrentPage(pageId);
    if (pageId === 'customOrder') {
      setShowCustomOrderForm(true);
    }
    setIsOpen(false);

    const element = document.getElementById(pageId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Enhanced navigation handler for mobile
  const handleMobileNavigation = (pageId: string) => {
    handlePageChange(pageId);
    // Additional mobile-specific logic can be added here
  };

  const handleUserAction = () => {
    if (isAuthenticated) {
      if (isAdmin) {
        setShowAdminDashboard(true);
      } else {
        setShowDashboard(true);
      }
    } else {
      setShowUserLogin(true);
    }
  };

  const handleLogout = () => {
    showNotification({
      type: 'confirm',
      message: t('notifications.logoutConfirm'),
      onConfirm: () => {
        logout();
        showNotification({
          type: 'success',
          message: language === 'ar' ? 'تم تسجيل الخروج بنجاح' : 'Logged out successfully'
        });
      }
    });
  };



  // Show admin dashboard if requested
  if (showAdminDashboard && isAuthenticated && isAdmin) {
    return (
      <Suspense fallback={<LoadingSpinner fullScreen message={language === 'ar' ? 'جاري تحميل لوحة الإدارة...' : 'Loading admin dashboard...'} />}>
        <AdminDashboard onClose={() => setShowAdminDashboard(false)} />
      </Suspense>
    );
  }

  // Show user dashboard if requested
  if (showDashboard && isAuthenticated) {
    return (
      <Suspense fallback={<LoadingSpinner fullScreen message={language === 'ar' ? 'جاري تحميل لوحة التحكم...' : 'Loading dashboard...'} />}>
        <SimpleUserDashboard onClose={() => setShowDashboard(false)} />
      </Suspense>
    );
  }

  return (
    <>
      {/* Luxury Professional Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-gray-900/98 via-black/98 to-gray-900/98 backdrop-blur-2xl border-b border-yellow-500/20 shadow-2xl">
        {/* Luxury top accent line */}
        <div className="h-1 bg-gradient-to-r from-transparent via-yellow-500 to-transparent"></div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            {/* Premium Logo and Brand - Fixed container */}
            <div className="header-brand-container">
              <div className="relative group flex-shrink-0">
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/30 to-amber-600/30 rounded-2xl blur-xl opacity-50 group-hover:opacity-80 transition-all duration-500"></div>
                <div className="relative bg-gradient-to-br from-yellow-600 via-yellow-500 to-amber-600 p-4 rounded-2xl shadow-2xl group-hover:shadow-yellow-500/40 transition-all duration-500 group-hover:scale-110 border border-yellow-400/30">
                  <div className="absolute inset-1 bg-gradient-to-br from-black/20 to-transparent rounded-xl"></div>
                  <Shield className="w-8 h-8 text-black relative z-10 group-hover:rotate-12 transition-transform duration-500" />
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-yellow-300 to-amber-400 rounded-full animate-pulse"></div>
                </div>
              </div>
              <div className="header-brand-text hidden sm:block">
                <h1 className="site-name font-black bg-gradient-to-r from-yellow-400 via-yellow-300 to-amber-400 bg-clip-text text-transparent hover:from-yellow-300 hover:via-amber-300 hover:to-yellow-400 transition-all duration-700 cursor-default tracking-tight">
                  {t('site.name')}
                </h1>
                <p className="site-tagline text-yellow-200/80 font-medium tracking-widest uppercase">
                  {t('site.tagline')}
                </p>
              </div>
            </div>

            {/* Luxury Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-2 rtl:space-x-reverse">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.id}
                    onClick={() => handlePageChange(item.id)}
                    className={`nav-button group relative px-4 py-3 rounded-xl transition-all duration-500 border ${
                      currentPage === item.id
                        ? 'bg-gradient-to-r from-yellow-500/20 to-amber-600/20 border-yellow-500/50 text-yellow-300 shadow-lg shadow-yellow-500/20'
                        : 'text-gray-300 hover:text-yellow-300 border-transparent hover:border-yellow-500/30 hover:bg-gradient-to-r hover:from-yellow-500/10 hover:to-amber-600/10 hover:shadow-lg hover:shadow-yellow-500/10'
                    }`}
                  >
                    <div className="flex items-center space-x-2 rtl:space-x-reverse relative z-10">
                      <Icon className="w-5 h-5 nav-icon group-hover:scale-110 transition-transform duration-300" />
                      <span className="nav-button-text text-sm font-semibold tracking-wide">{item.label}</span>
                    </div>
                    {currentPage === item.id && (
                      <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-12 h-1 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full"></div>
                    )}
                    {/* Luxury hover effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/0 via-yellow-500/5 to-yellow-500/0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </button>
                );
              })}
            </div>

            {/* User Login, Language Toggle & Mobile Menu */}
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              



              {/* Luxury User Login/Dashboard Button */}
              <button
                onClick={handleUserAction}
                className={`nav-button rounded-xl transition-all duration-500 border shadow-lg ${
                  isAdmin
                    ? 'bg-gradient-to-r from-red-600/20 to-red-700/20 hover:from-red-600/30 hover:to-red-700/30 border-red-500/40 hover:border-red-400/60 text-red-300 hover:text-red-200 shadow-red-500/20'
                    : 'bg-gradient-to-r from-yellow-600/20 to-amber-700/20 hover:from-yellow-600/30 hover:to-amber-700/30 border-yellow-500/40 hover:border-yellow-400/60 text-yellow-300 hover:text-yellow-200 shadow-yellow-500/20'
                }`}
              >
                {isAuthenticated ? (isAdmin ? <Shield className="w-5 h-5 nav-icon" /> : <User className="w-5 h-5 nav-icon" />) : <LogIn className="w-5 h-5 nav-icon" />}
                <span className="nav-button-text text-sm font-medium hidden sm:block">
                  {isAuthenticated
                    ? (isAdmin
                        ? t('admin.dashboard.title')
                        : (userProfile?.username || t('nav.dashboard'))
                      )
                    : t('nav.login')
                  }
                </span>
              </button>

              {/* Luxury Logout Button - Only visible when logged in */}
              {isAuthenticated && (
                <button
                  onClick={handleLogout}
                  className="nav-button rounded-xl transition-all duration-500 bg-gradient-to-r from-red-600/20 to-red-700/20 hover:from-red-600/30 hover:to-red-700/30 border border-red-500/40 hover:border-red-400/60 text-red-300 hover:text-red-200 shadow-lg shadow-red-500/20"
                  title={language === 'ar' ? 'تسجيل الخروج' : 'Logout'}
                >
                  <LogOut className="w-4 h-4 nav-icon" />
                  <span className="nav-button-text text-sm font-semibold hidden lg:block">
                    {t('nav.logout')}
                  </span>
                </button>
              )}

              {/* Luxury Language Toggle */}
              <button
                onClick={handleLanguageToggle}
                className="nav-button rounded-xl bg-gradient-to-r from-blue-600/20 to-cyan-600/20 hover:from-blue-600/30 hover:to-cyan-600/30 border border-blue-500/40 hover:border-blue-400/60 transition-all duration-500 text-blue-300 hover:text-blue-200 shadow-lg shadow-blue-500/20"
              >
                <Globe className="w-5 h-5 nav-icon" />
                <span className="nav-button-text text-sm font-bold">{language === 'ar' ? t('common.english') : t('common.arabic')}</span>
              </button>

              {/* Luxury Mobile Menu Button */}
              <button
                onClick={() => setIsOpen(!isOpen)}
                className="mobile-menu-button md:hidden p-3 rounded-xl bg-gradient-to-r from-yellow-600/20 to-amber-700/20 hover:from-yellow-600/30 hover:to-amber-700/30 border border-yellow-500/40 hover:border-yellow-400/60 transition-all duration-500 text-yellow-300 hover:text-yellow-200 shadow-lg shadow-yellow-500/20 touch-manipulation min-h-[44px] min-w-[44px] flex items-center justify-center"
                aria-label={isOpen ? t('nav.closeMenu', 'Close menu') : t('nav.openMenu', 'Open menu')}
                aria-expanded={isOpen}
                aria-controls="mobile-navigation"
              >
                {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>

          {/* Enhanced Mobile Navigation */}
          {isOpen && (
            <>
              {/* Mobile overlay */}
              <div
                className="md:hidden fixed inset-0 bg-black/70 backdrop-blur-sm z-40"
                onClick={() => setIsOpen(false)}
                aria-hidden="true"
              />

              {/* Mobile menu */}
              <div
                id="mobile-navigation"
                className={`mobile-nav-container md:hidden fixed top-0 h-full w-80 max-w-[85vw] bg-gradient-to-b from-primary to-background border-accent/20 z-50 transition-transform duration-300 ease-in-out ${
                  language === 'ar' ? 'left-0 border-r' : 'right-0 border-l'
                }`}
                role="navigation"
                aria-label={t('nav.mobileMenu', 'Mobile navigation menu')}
              >
                {/* Mobile menu header */}
                <div className="p-6 border-b border-accent/20">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <div className="w-10 h-10 bg-gradient-to-br from-secondary to-accent rounded-lg flex items-center justify-center">
                        <Zap className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <h2 className="text-lg font-bold text-white">{t('site.name')}</h2>
                        <p className="text-xs text-accent">{t('site.tagline')}</p>
                      </div>
                    </div>
                    <button
                      onClick={() => setIsOpen(false)}
                      className="p-2 rounded-lg hover:bg-accent/10 transition-colors text-gray-400 hover:text-white touch-manipulation min-h-[44px] min-w-[44px] flex items-center justify-center"
                      aria-label={t('nav.closeMenu', 'Close menu')}
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>
                </div>

                {/* Mobile navigation items */}
                <div className="p-4 space-y-2">
                  {navigationItems.map((item) => {
                    const Icon = item.icon;
                    return (
                      <button
                        key={item.id}
                        onClick={() => handleMobileNavigation(item.id)}
                        className={`w-full flex items-center space-x-4 rtl:space-x-reverse px-4 py-4 rounded-lg transition-all duration-200 hover:bg-accent/10 border border-transparent hover:border-accent/30 touch-manipulation min-h-[52px] ${
                          currentPage === item.id
                            ? 'text-secondary bg-secondary/10 border-secondary/30'
                            : 'text-gray-300 hover:text-white'
                        }`}
                      >
                        <Icon className="w-6 h-6 flex-shrink-0" />
                        <span className="font-medium text-left rtl:text-right">{item.label}</span>
                      </button>
                    );
                  })}
                </div>

                {/* Mobile menu footer with language toggle and logout */}
                <div className="mt-auto p-4 border-t border-accent/20 space-y-3">
                  {/* Language toggle */}
                  <button
                    onClick={() => {
                      changeLanguage(language === 'ar' ? 'en' : 'ar');
                      setIsOpen(false);
                    }}
                    className="w-full flex items-center justify-center space-x-3 rtl:space-x-reverse px-4 py-4 rounded-lg bg-gradient-to-r from-accent/15 to-blue-500/15 hover:from-accent/25 hover:to-blue-500/25 border border-accent/40 hover:border-accent/60 transition-all duration-300 text-accent hover:text-white touch-manipulation min-h-[52px]"
                  >
                    <Globe className="w-5 h-5" />
                    <span className="font-bold">
                      {language === 'ar' ? t('common.english') : t('common.arabic')}
                    </span>
                  </button>

                  {/* Logout button for mobile - Only visible when logged in */}
                  {isAuthenticated && (
                    <button
                      onClick={() => {
                        handleLogout();
                        setIsOpen(false);
                      }}
                      className="w-full flex items-center justify-center space-x-3 rtl:space-x-reverse px-4 py-4 rounded-lg bg-gradient-to-r from-red-500/15 to-red-600/15 hover:from-red-500/25 hover:to-red-600/25 border border-red-500/40 hover:border-red-400/60 transition-all duration-300 text-red-400 hover:text-red-300 touch-manipulation min-h-[52px]"
                    >
                      <LogOut className="w-5 h-5" />
                      <span className="font-medium">{t('nav.logout')}</span>
                    </button>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </nav>

      {/* User Login Modal */}
      {showUserLogin && (
        <Suspense fallback={<LoadingSpinner fullScreen />}>
          <UserLogin onClose={() => setShowUserLogin(false)} />
        </Suspense>
      )}

      {/* User Management Modal */}
      {showUserManagement && (
        <Suspense fallback={<LoadingSpinner fullScreen />}>
          <UserManagement onClose={() => setShowUserManagement(false)} />
        </Suspense>
      )}

      {/* Advanced Order Management Modal */}
      {showOrderManagement && (
        <Suspense fallback={<LoadingSpinner fullScreen />}>
          <AdvancedOrderManagement onClose={() => setShowOrderManagement(false)} />
        </Suspense>
      )}

      {/* Premium Edition Modal */}
      {showPremiumEdition && (
        <Suspense fallback={<LoadingSpinner fullScreen />}>
          <PremiumEdition onClose={() => setShowPremiumEdition(false)} />
        </Suspense>
      )}

      {/* Premium Content Manager Modal */}
      {showPremiumContentManager && (
        <Suspense fallback={<LoadingSpinner fullScreen />}>
          <PremiumContentManager onClose={() => setShowPremiumContentManager(false)} />
        </Suspense>
      )}

      {/* Custom Order Form Modal */}
      {showCustomOrderForm && (
        <Suspense fallback={<LoadingSpinner fullScreen />}>
          <CustomOrderForm onClose={() => setShowCustomOrderForm(false)} />
        </Suspense>
      )}


    </>
  );
};

export default Navigation;