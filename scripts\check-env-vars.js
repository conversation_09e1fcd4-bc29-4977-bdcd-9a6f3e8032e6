#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function checkEnvVars() {
  console.log('🔍 Checking Environment Variables...\n');
  
  // Check .env files
  const envFiles = ['.env', '.env.local', '.env.production', '.env.development'];
  
  envFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ Found: ${file}`);
      const content = fs.readFileSync(filePath, 'utf8');
      console.log(`   Content:\n${content}\n`);
    } else {
      console.log(`❌ Missing: ${file}`);
    }
  });
  
  // Check vite.config.ts
  console.log('📋 Checking vite.config.ts...');
  const viteConfigPath = path.join(process.cwd(), 'vite.config.ts');
  if (fs.existsSync(viteConfigPath)) {
    const viteConfig = fs.readFileSync(viteConfigPath, 'utf8');
    
    if (viteConfig.includes('process.env.VITE_API_BASE_URL')) {
      console.log('✅ Vite config uses environment variables');
    } else {
      console.log('⚠️ Vite config might not be using environment variables correctly');
    }
    
    // Check proxy configuration
    if (viteConfig.includes('proxy')) {
      console.log('✅ Proxy configuration found');
      const proxyMatch = viteConfig.match(/target:\s*['"`]([^'"`]+)['"`]/);
      if (proxyMatch) {
        console.log(`   Proxy target: ${proxyMatch[1]}`);
      }
    }
  }
  
  // Check package.json scripts
  console.log('\n📋 Checking package.json scripts...');
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const scripts = packageJson.scripts || {};
    
    console.log('Available scripts:');
    Object.keys(scripts).forEach(script => {
      if (script.includes('dev') || script.includes('build') || script.includes('start')) {
        console.log(`   ${script}: ${scripts[script]}`);
      }
    });
  }
  
  console.log('\n💡 Recommendations:');
  console.log('1. Make sure .env.local exists with VITE_API_BASE_URL');
  console.log('2. Restart Vite dev server to load new environment variables');
  console.log('3. Check browser console for API base URL logs');
  console.log('4. Verify Vite proxy configuration');
}

checkEnvVars();
