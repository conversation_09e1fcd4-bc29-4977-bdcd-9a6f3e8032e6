#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class LocalAPITester {
  constructor(configPath) {
    this.config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  async makeRequest(endpoint, testCase) {
    const url = `${this.config.baseUrl}${endpoint.path}`;
    const options = {
      method: endpoint.method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'LocalAPITester/1.0'
      }
    };

    if (testCase.body && Object.keys(testCase.body).length > 0) {
      // Replace dynamic variables
      const bodyStr = JSON.stringify(testCase.body);
      const processedBody = bodyStr.replace(/\{\{timestamp\}\}/g, Date.now());
      options.body = processedBody;
    }

    try {
      const response = await fetch(url, options);
      const responseText = await response.text();
      
      let responseData;
      try {
        responseData = JSON.parse(responseText);
      } catch {
        responseData = responseText;
      }

      return {
        status: response.status,
        data: responseData,
        headers: Object.fromEntries(response.headers.entries())
      };
    } catch (error) {
      throw new Error(`Request failed: ${error.message}`);
    }
  }

  async runTest(endpoint, testCase) {
    this.results.total++;
    const testName = `${endpoint.method} ${endpoint.path} - ${testCase.name}`;
    
    try {
      this.log(`\n🧪 Running: ${testName}`, 'cyan');
      
      const response = await this.makeRequest(endpoint, testCase);
      
      // Check status code
      const expectedStatuses = Array.isArray(testCase.expectedStatus)
        ? testCase.expectedStatus
        : [testCase.expectedStatus];
      const statusMatch = expectedStatuses.includes(response.status);
      if (!statusMatch) {
        throw new Error(`Expected status ${expectedStatuses.join(' or ')}, got ${response.status}`);
      }

      // Check expected fields if specified
      if (testCase.expectedFields && Array.isArray(testCase.expectedFields)) {
        if (typeof response.data === 'object' && response.data !== null) {
          for (const field of testCase.expectedFields) {
            if (!(field in response.data)) {
              throw new Error(`Expected field '${field}' not found in response`);
            }
          }
        }
      }

      this.results.passed++;
      this.log(`✅ PASSED: ${testName}`, 'green');
      this.log(`   Status: ${response.status}`, 'green');
      
      this.results.tests.push({
        name: testName,
        status: 'PASSED',
        response: {
          status: response.status,
          data: response.data
        }
      });

    } catch (error) {
      this.results.failed++;
      this.log(`❌ FAILED: ${testName}`, 'red');
      this.log(`   Error: ${error.message}`, 'red');
      
      this.results.tests.push({
        name: testName,
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async runAllTests() {
    this.log(`\n🚀 Starting API Tests for: ${this.config.name}`, 'bright');
    this.log(`📍 Base URL: ${this.config.baseUrl}`, 'blue');
    this.log(`📊 Total Endpoints: ${this.config.endpoints.length}`, 'blue');

    for (const endpoint of this.config.endpoints) {
      this.log(`\n📂 Testing endpoint: ${endpoint.method} ${endpoint.path}`, 'magenta');
      this.log(`   Description: ${endpoint.description}`, 'yellow');
      
      for (const testCase of endpoint.testCases) {
        await this.runTest(endpoint, testCase);
      }
    }

    this.printSummary();
  }

  printSummary() {
    this.log('\n' + '='.repeat(60), 'bright');
    this.log('📋 TEST SUMMARY', 'bright');
    this.log('='.repeat(60), 'bright');
    
    this.log(`Total Tests: ${this.results.total}`, 'blue');
    this.log(`✅ Passed: ${this.results.passed}`, 'green');
    this.log(`❌ Failed: ${this.results.failed}`, 'red');
    
    const successRate = ((this.results.passed / this.results.total) * 100).toFixed(1);
    this.log(`📈 Success Rate: ${successRate}%`, successRate >= 80 ? 'green' : 'red');

    if (this.results.failed > 0) {
      this.log('\n🔍 Failed Tests:', 'red');
      this.results.tests
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          this.log(`   • ${test.name}: ${test.error}`, 'red');
        });
    }

    // Save detailed results
    const resultsFile = path.join(__dirname, '..', 'test-results.json');
    fs.writeFileSync(resultsFile, JSON.stringify(this.results, null, 2));
    this.log(`\n💾 Detailed results saved to: ${resultsFile}`, 'blue');
  }
}

// Main execution
async function main() {
  const configPath = path.join(__dirname, '..', 'testsprite-local.json');
  
  if (!fs.existsSync(configPath)) {
    console.error(`❌ Config file not found: ${configPath}`);
    process.exit(1);
  }

  const tester = new LocalAPITester(configPath);
  await tester.runAllTests();
  
  // Exit with error code if tests failed
  process.exit(tester.results.failed > 0 ? 1 : 0);
}

if (require.main === module) {
  main().catch(error => {
    console.error(`❌ Fatal error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = LocalAPITester;
