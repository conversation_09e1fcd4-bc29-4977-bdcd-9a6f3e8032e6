const axios = require('axios');

async function debugRegistration() {
  const API_BASE = 'http://localhost:3001/api';
  
  // Test data that should work
  const testUser = {
    email: `debug_test_${Date.now()}@example.com`,
    password: 'TestPassword123!',
    username: `debuguser_${Date.now()}`,
    full_name: 'Debug Test User'
  };
  
  console.log('🔍 Testing registration with data:', testUser);
  
  try {
    const response = await axios.post(`${API_BASE}/auth/register`, testUser);
    console.log('✅ Registration successful:', response.data);
  } catch (error) {
    console.log('❌ Registration failed');
    console.log('Status:', error.response?.status);
    console.log('Error data:', error.response?.data);
    console.log('Error message:', error.message);
    
    if (error.response?.data?.details) {
      console.log('Validation details:', error.response.data.details);
    }
  }
  
  // Test with existing user (should get 409)
  console.log('\n🔍 Testing with existing user...');
  try {
    const existingUser = {
      email: '<EMAIL>',
      password: '123456',
      username: 'testuser',
      full_name: 'Test User'
    };
    
    const response = await axios.post(`${API_BASE}/auth/register`, existingUser);
    console.log('❌ Unexpected success with existing user:', response.data);
  } catch (error) {
    if (error.response?.status === 409) {
      console.log('✅ Correctly rejected existing user with 409 conflict');
    } else {
      console.log('❌ Unexpected error:', error.response?.status, error.response?.data);
    }
  }
}

debugRegistration().catch(console.error);
