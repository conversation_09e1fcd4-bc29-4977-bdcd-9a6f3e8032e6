#!/usr/bin/env node

/**
 * Recreate clean data without JSON parsing issues
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function recreateCleanData() {
  let connection;
  
  try {
    console.log('🔧 Recreating clean data...');
    
    // Connect to database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '123456',
      database: 'khanfashariya_db',
      charset: 'utf8mb4'
    });
    
    console.log('✅ Connected to database');
    
    // Clear and recreate technical services
    await connection.execute('DELETE FROM technical_services');
    console.log('🗑️ Cleared technical services');
    
    const technicalServices = [
      {
        id: 'tech-service-1',
        name_ar: 'تطوير نظام مخصص',
        name_en: 'Custom System Development',
        description_ar: 'تطوير أنظمة مخصصة حسب متطلباتك الخاصة مع ضمان الجودة والأداء',
        description_en: 'Custom system development according to your specific requirements with quality and performance guarantee',
        price: 499.99,
        category: 'Development',
        service_type: 'development',
        delivery_time_ar: '7-14 يوم عمل',
        delivery_time_en: '7-14 working days',
        status: 'active',
        featured: true
      },
      {
        id: 'tech-service-2',
        name_ar: 'استشارة تقنية متخصصة',
        name_en: 'Specialized Technical Consultation',
        description_ar: 'استشارة تقنية متخصصة لحل مشاكل الخادم وتحسين الأداء',
        description_en: 'Specialized technical consultation for server problem solving and performance optimization',
        price: 99.99,
        category: 'Consultation',
        service_type: 'consultation',
        delivery_time_ar: '1-3 أيام عمل',
        delivery_time_en: '1-3 working days',
        status: 'active',
        featured: false
      }
    ];
    
    for (const service of technicalServices) {
      await connection.execute(`
        INSERT INTO technical_services (
          id, name_ar, name_en, description_ar, description_en, price, category,
          service_type, delivery_time_ar, delivery_time_en, status, featured,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        service.id, service.name_ar, service.name_en,
        service.description_ar, service.description_en,
        service.price, service.category, service.service_type,
        service.delivery_time_ar, service.delivery_time_en,
        service.status, service.featured
      ]);
    }
    console.log(`✅ Created ${technicalServices.length} technical services`);
    
    // Clear and recreate premium content
    await connection.execute('DELETE FROM premium_content');
    console.log('🗑️ Cleared premium content');
    
    const premiumContent = [
      {
        id: 'premium-gold-1',
        title_ar: 'الحزمة الذهبية الشاملة',
        title_en: 'Complete Gold Package',
        description_ar: 'حزمة شاملة تتضمن جميع الأنظمة والخدمات المتاحة مع دعم مدى الحياة',
        description_en: 'Complete package including all available systems and services with lifetime support',
        price: 1999.99,
        category: 'Premium',
        status: 'active',
        featured: true
      },
      {
        id: 'premium-dev-1',
        title_ar: 'حزمة المطور المحترف',
        title_en: 'Professional Developer Package',
        description_ar: 'حزمة خاصة للمطورين مع أدوات متقدمة ومكتبات البرمجة',
        description_en: 'Special package for developers with advanced tools and programming libraries',
        price: 999.99,
        category: 'Developer',
        status: 'active',
        featured: false
      }
    ];
    
    for (const content of premiumContent) {
      await connection.execute(`
        INSERT INTO premium_content (
          id, title_ar, title_en, description_ar, description_en, price, category,
          status, featured, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        content.id, content.title_ar, content.title_en,
        content.description_ar, content.description_en,
        content.price, content.category, content.status, content.featured
      ]);
    }
    console.log(`✅ Created ${premiumContent.length} premium content items`);
    
    // Clear and recreate system services
    await connection.execute('DELETE FROM system_services');
    console.log('🗑️ Cleared system services');
    
    const systemServices = [
      {
        id: 'system-shop-1',
        name_ar: 'نظام المتجر المتقدم',
        name_en: 'Advanced Shop System',
        description_ar: 'نظام متجر متقدم مع واجهة حديثة وميزات متطورة لإدارة المبيعات',
        description_en: 'Advanced shop system with modern interface and advanced features for sales management',
        price: 299.99,
        category: 'Shop Systems',
        type: 'regular',
        version: '2.1.0',
        file_size: '15.2 MB',
        status: 'active',
        featured: true
      },
      {
        id: 'system-guild-1',
        name_ar: 'نظام الجيلد المطور',
        name_en: 'Enhanced Guild System',
        description_ar: 'نظام جيلد محسن مع ميزات إضافية ونظام رانكات متقدم',
        description_en: 'Enhanced guild system with additional features and advanced ranking system',
        price: 199.99,
        category: 'Guild Systems',
        type: 'regular',
        version: '1.8.5',
        file_size: '8.7 MB',
        status: 'active',
        featured: false
      }
    ];
    
    for (const system of systemServices) {
      await connection.execute(`
        INSERT INTO system_services (
          id, name_ar, name_en, description_ar, description_en, price, category,
          type, version, file_size, status, featured, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        system.id, system.name_ar, system.name_en,
        system.description_ar, system.description_en,
        system.price, system.category, system.type,
        system.version, system.file_size, system.status, system.featured
      ]);
    }
    console.log(`✅ Created ${systemServices.length} system services`);
    
    console.log('🎉 Clean data recreation completed!');
    
  } catch (error) {
    console.error('❌ Recreation failed:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run recreation
recreateCleanData().catch(error => {
  console.error('Recreation script failed:', error);
  process.exit(1);
});
