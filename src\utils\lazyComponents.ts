/**
 * Lazy Component Loading
 * 
 * Implements code splitting for better performance
 */

import { lazy } from 'react'

// Lazy load admin components for better initial load performance
export const AdminDashboard = lazy(() => import('../components/AdminDashboard'))
export const UserManagement = lazy(() => import('../components/UserManagement'))
export const TechnicalSystemsManager = lazy(() => import('../components/admin/TechnicalSystemsManager'))
export const TechnicalServicesManager = lazy(() => import('../components/admin/TechnicalServicesManager'))
export const EnhancedPremiumManager = lazy(() => import('../components/admin/EnhancedPremiumManager'))
export const OrderManagement = lazy(() => import('../components/admin/OrderManagement'))
export const SystemSettings = lazy(() => import('../components/admin/SystemSettings'))

// Lazy load user dashboard components
export const UserDashboard = lazy(() => import('../components/UserDashboard'))
export const SimpleUserDashboard = lazy(() => import('../components/SimpleUserDashboard'))
export const UserOrderManager = lazy(() => import('../components/orders/UserOrderManager'))

// Lazy load cart and checkout components
export const ShoppingCartManager = lazy(() => import('../components/cart/ShoppingCartManager'))

// Lazy load modal and UI components
export const Modal = lazy(() => import('../components/ui/Modal'))
export const OptimizedImage = lazy(() => import('../components/ui/OptimizedImage'))
export const PerformanceOptimizedImage = lazy(() => import('../components/ui/PerformanceOptimizedImage'))

// Preload critical components for better UX
export const preloadCriticalComponents = () => {
  // Preload components that are likely to be used soon
  const criticalComponents = [
    () => import('../components/AdminDashboard'),
    () => import('../components/UserDashboard'),
    () => import('../components/cart/ShoppingCartManager')
  ]

  // Preload on idle
  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      criticalComponents.forEach(importFn => {
        importFn().catch(() => {
          // Silently fail - component will be loaded when needed
        })
      })
    })
  } else {
    // Fallback for browsers without requestIdleCallback
    setTimeout(() => {
      criticalComponents.forEach(importFn => {
        importFn().catch(() => {
          // Silently fail - component will be loaded when needed
        })
      })
    }, 2000)
  }
}

// Component loading utilities
export const createLazyComponent = (importFn: () => Promise<any>, fallback?: React.ComponentType) => {
  const LazyComponent = lazy(importFn)
  
  return {
    Component: LazyComponent,
    preload: () => importFn().catch(() => {}),
    fallback
  }
}

// Route-based code splitting - using existing components
export const RouteComponents = {
  Home: lazy(() => import('../components/Hero')),
  Services: lazy(() => import('../components/Services')),
  Systems: lazy(() => import('../components/SystemsGrid')),
  Premium: lazy(() => import('../components/PremiumSection')),
  Contact: lazy(() => import('../components/Contact')),
  Dashboard: lazy(() => import('../components/UserDashboard')),
  Admin: lazy(() => import('../components/AdminDashboard'))
}

// Preload routes based on user behavior
export const preloadRoute = (routeName: keyof typeof RouteComponents) => {
  const component = RouteComponents[routeName]
  if (component) {
    // Trigger the lazy loading - suppress Vite warning for dynamic imports
    component._payload?._result || import(/* @vite-ignore */ `../components/${routeName}`)
  }
}

// Smart preloading based on user interactions
export const setupSmartPreloading = () => {
  // Preload on hover for better perceived performance
  const setupHoverPreloading = () => {
    document.addEventListener('mouseover', (event) => {
      const target = event.target as HTMLElement
      const link = target.closest('a[href]') as HTMLAnchorElement
      
      if (link) {
        const href = link.getAttribute('href')
        if (href?.startsWith('/')) {
          const routeName = href.slice(1) || 'Home'
          if (routeName in RouteComponents) {
            preloadRoute(routeName as keyof typeof RouteComponents)
          }
        }
      }
    }, { passive: true })
  }

  // Preload on focus for keyboard navigation
  const setupFocusPreloading = () => {
    document.addEventListener('focusin', (event) => {
      const target = event.target as HTMLElement
      const link = target.closest('a[href]') as HTMLAnchorElement
      
      if (link) {
        const href = link.getAttribute('href')
        if (href?.startsWith('/')) {
          const routeName = href.slice(1) || 'Home'
          if (routeName in RouteComponents) {
            preloadRoute(routeName as keyof typeof RouteComponents)
          }
        }
      }
    }, { passive: true })
  }

  // Setup preloading strategies
  setupHoverPreloading()
  setupFocusPreloading()
}

// Bundle analysis utilities
export const getBundleInfo = () => {
  if (process.env.NODE_ENV === 'development') {
    return {
      totalChunks: Object.keys(RouteComponents).length,
      loadedChunks: Object.values(RouteComponents).filter(
        component => component._payload?._result
      ).length,
      preloadedComponents: 'Available in production build'
    }
  }
  return null
}

// Performance monitoring
export const monitorComponentLoading = () => {
  if (process.env.NODE_ENV === 'development') {
    const originalLazy = lazy
    
    // Override lazy to add performance monitoring
    const monitoredLazy = (importFn: () => Promise<any>) => {
      return originalLazy(() => {
        const start = performance.now()
        return importFn().then(module => {
          const end = performance.now()
          console.log(`Component loaded in ${(end - start).toFixed(2)}ms`)
          return module
        })
      })
    }
    
    return monitoredLazy
  }
  return lazy
}
