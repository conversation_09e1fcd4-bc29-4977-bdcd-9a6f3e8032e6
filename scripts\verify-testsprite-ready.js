#!/usr/bin/env node

const { exec } = require('child_process');
const fs = require('fs');

async function verifyTestSpriteReady() {
  console.log('🔍 Verifying TestSprite readiness...\n');
  
  try {
    // Get tunnel info
    const tunnelInfo = JSON.parse(fs.readFileSync('working-tunnel.json', 'utf8'));
    const baseUrl = tunnelInfo.baseUrl;
    
    console.log(`🔗 Testing Base URL: ${baseUrl}`);
    
    const endpoints = [
      { path: '/health', method: 'GET', name: 'Health Check' },
      { path: '/api/auth/login', method: 'POST', name: 'Login Endpoint', 
        body: '{"email":"<EMAIL>","password":"admin123"}' },
      { path: '/api/systems', method: 'GET', name: 'Systems API' },
      { path: '/api/services/technical', method: 'GET', name: 'Technical Services' },
      { path: '/api/services/premium', method: 'GET', name: 'Premium Services' }
    ];

    const results = [];
    
    for (const endpoint of endpoints) {
      console.log(`\n📡 Testing ${endpoint.name}...`);
      
      let curlCommand = `curl -s -H "ngrok-skip-browser-warning: true" -H "User-Agent: TestSprite/1.0"`;
      
      if (endpoint.method === 'POST') {
        curlCommand += ` -H "Content-Type: application/json" -X POST`;
        if (endpoint.body) {
          curlCommand += ` -d '${endpoint.body}'`;
        }
      }
      
      curlCommand += ` -w "\\n%{http_code}" "${baseUrl}${endpoint.path}"`;
      
      const result = await new Promise((resolve) => {
        exec(curlCommand, (error, stdout, stderr) => {
          if (error) {
            resolve({ success: false, error: error.message, endpoint: endpoint.name });
            return;
          }
          
          const lines = stdout.trim().split('\n');
          const statusCode = parseInt(lines[lines.length - 1]);
          const response = lines.slice(0, -1).join('\n');
          
          resolve({
            success: true,
            endpoint: endpoint.name,
            path: endpoint.path,
            method: endpoint.method,
            statusCode: statusCode,
            response: response.substring(0, 200) + (response.length > 200 ? '...' : ''),
            working: statusCode >= 200 && statusCode < 500
          });
        });
      });
      
      results.push(result);
      
      if (result.success) {
        const status = result.working ? '✅' : '⚠️';
        console.log(`${status} ${endpoint.name}: ${result.statusCode}`);
        if (result.response && result.response.length > 0) {
          console.log(`   Response: ${result.response.substring(0, 100)}...`);
        }
      } else {
        console.log(`❌ ${endpoint.name}: ${result.error}`);
      }
    }

    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📋 TESTSPRITE READINESS SUMMARY');
    console.log('='.repeat(60));
    
    const workingEndpoints = results.filter(r => r.success && r.working).length;
    const totalEndpoints = results.length;
    const readinessPercent = Math.round((workingEndpoints / totalEndpoints) * 100);
    
    console.log(`✅ Working Endpoints: ${workingEndpoints}/${totalEndpoints}`);
    console.log(`📊 Readiness Score: ${readinessPercent}%`);
    
    if (readinessPercent >= 80) {
      console.log('🎉 TestSprite is READY to use!');
    } else {
      console.log('⚠️ Some endpoints need attention');
    }

    // Create final TestSprite config
    const finalConfig = {
      name: "Khanfashariya API - Verified Ready",
      baseUrl: baseUrl,
      timestamp: new Date().toISOString(),
      readinessScore: readinessPercent,
      headers: {
        "ngrok-skip-browser-warning": "true",
        "User-Agent": "TestSprite/1.0",
        "Content-Type": "application/json",
        "Accept": "application/json"
      },
      endpoints: [
        {
          path: "/health",
          method: "GET",
          description: "Health check endpoint",
          testCases: [
            {
              name: "Health Check",
              expectedStatus: 200,
              description: "Verify server is running"
            }
          ]
        },
        {
          path: "/api/auth/login",
          method: "POST",
          description: "User authentication",
          testCases: [
            {
              name: "Valid Admin Login",
              body: {
                email: "<EMAIL>",
                password: "admin123"
              },
              expectedStatus: 200,
              description: "Login with valid admin credentials"
            },
            {
              name: "Invalid Credentials",
              body: {
                email: "<EMAIL>",
                password: "wrongpass"
              },
              expectedStatus: 401,
              description: "Test error handling for invalid credentials"
            }
          ]
        },
        {
          path: "/api/systems",
          method: "GET",
          description: "Technical systems endpoint",
          testCases: [
            {
              name: "Get All Systems",
              expectedStatus: 200,
              description: "Retrieve all technical systems"
            }
          ]
        },
        {
          path: "/api/services/technical",
          method: "GET",
          description: "Technical services endpoint",
          testCases: [
            {
              name: "Get Technical Services",
              expectedStatus: 200,
              description: "Retrieve all technical services"
            }
          ]
        },
        {
          path: "/api/services/premium",
          method: "GET",
          description: "Premium services endpoint",
          testCases: [
            {
              name: "Get Premium Services",
              expectedStatus: 200,
              description: "Retrieve all premium services"
            }
          ]
        }
      ],
      testResults: results
    };

    fs.writeFileSync('testsprite-final.json', JSON.stringify(finalConfig, null, 2));
    console.log('\n💾 Final TestSprite config saved to: testsprite-final.json');

    console.log('\n🎯 TestSprite Instructions:');
    console.log('1. Open TestSprite in your browser');
    console.log('2. Create a new test project');
    console.log(`3. Set Base URL: ${baseUrl}`);
    console.log('4. Add these headers:');
    console.log('   - ngrok-skip-browser-warning: true');
    console.log('   - User-Agent: TestSprite/1.0');
    console.log('5. Import endpoints from testsprite-final.json');
    console.log('6. Run comprehensive tests!');

    console.log('\n🔗 Quick Test URLs:');
    console.log(`Health: ${baseUrl}/health`);
    console.log(`Login: ${baseUrl}/api/auth/login`);
    console.log(`Systems: ${baseUrl}/api/systems`);

    return readinessPercent >= 80;

  } catch (error) {
    console.log(`❌ Verification failed: ${error.message}`);
    return false;
  }
}

verifyTestSpriteReady().then(ready => {
  if (ready) {
    console.log('\n✅ TestSprite verification completed successfully!');
    process.exit(0);
  } else {
    console.log('\n❌ TestSprite verification failed!');
    process.exit(1);
  }
});
