import React from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { <PERSON>Right, ArrowLeft, Zap, Shield, Code, Swords, Crown, Gem, Star, Sparkles } from 'lucide-react';
import { TouchButton } from './ui/MobileOptimized';

const Hero: React.FC = () => {
  const { language, t } = useTranslation();

  const handleCTAClick = () => {
    const premiumElement = document.getElementById('premium');
    if (premiumElement) {
      premiumElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 z-0">
        <img
          src="/bk.jpg"
          alt="Background"
          className="w-full h-full object-cover"
        />
        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-background-primary/85 via-background-secondary/80 to-background-primary/85"></div>
        {/* Theme-appropriate overlay with golden accents */}
        <div className="absolute inset-0 bg-gradient-to-t from-primary/60 via-transparent to-primary/40"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-secondary/10 via-transparent to-accent/10"></div>
      </div>

      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden z-10">
        {/* Premium floating elements */}
        <div className="absolute top-20 left-10 w-3 h-3 bg-gradient-to-r from-secondary to-accent rounded-full animate-ping"></div>
        <div className="absolute top-40 right-20 w-2 h-2 bg-gradient-to-r from-accent to-secondary rounded-full animate-pulse"></div>
        <div className="absolute bottom-32 left-1/4 w-2 h-2 bg-gradient-to-r from-secondary to-accent rounded-full animate-ping delay-1000"></div>

        {/* Luxury gradient orbs */}
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-secondary/20 to-accent/20 blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 rounded-full bg-gradient-to-br from-accent/20 to-secondary/20 blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full bg-gradient-to-br from-secondary/5 to-accent/5 blur-3xl animate-pulse delay-500"></div>

        {/* Premium sparkles */}
        <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-secondary rounded-full animate-ping delay-300"></div>
        <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-accent rounded-full animate-ping delay-700"></div>
        <div className="absolute top-1/2 right-1/3 w-1 h-1 bg-secondary rounded-full animate-ping delay-1200"></div>

        {/* Grid pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-20">
        <div className="text-center">
          {/* Luxury Main title */}
          <div className="mb-8">
            {/* Premium crown decoration - Unified positioning */}
            <div className="hero-icons-container mb-6">
              <div className="hero-icon-item">
                <Crown className="w-12 h-12 text-secondary animate-pulse" />
                <Sparkles className="w-6 h-6 text-accent absolute -top-2 -right-2 animate-ping" />
              </div>
              <div className="hero-icon-item">
                <Gem className="w-10 h-10 text-accent animate-pulse delay-300" />
                <Star className="w-5 h-5 text-secondary absolute -top-1 -left-1 animate-ping delay-500" />
              </div>
              <div className="hero-icon-item">
                <Crown className="w-12 h-12 text-accent animate-pulse delay-600" />
                <Sparkles className="w-6 h-6 text-secondary absolute -top-2 -left-2 animate-ping delay-800" />
              </div>
            </div>

            <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-bold text-white mb-6 leading-tight">
              <span className="block bg-gradient-to-r from-secondary via-accent to-secondary bg-clip-text text-transparent animate-gradient">
                {t('hero.title')}
              </span>
            </h1>

            {/* Luxury divider */}
            <div className="flex justify-center items-center space-x-6 rtl:space-x-reverse mb-6">
              <div className="h-px bg-gradient-to-r from-transparent via-secondary to-accent flex-1 max-w-40"></div>
              <div className="relative">
                <Swords className="w-10 h-10 text-secondary animate-pulse" />
                <div className="absolute inset-0 bg-secondary/20 rounded-full blur-lg animate-pulse"></div>
              </div>
              <div className="h-px bg-gradient-to-r from-accent via-secondary to-transparent flex-1 max-w-40"></div>
            </div>
          </div>

          {/* Premium Subtitle */}
          <div className="relative mb-8">
            <p className="text-xl sm:text-2xl md:text-3xl text-gray-200 mb-6 max-w-5xl mx-auto leading-relaxed font-light">
              {t('hero.subtitle')}
            </p>

            {/* Luxury Description */}
            <div className="relative bg-gradient-to-r from-secondary/10 via-accent/10 to-secondary/10 backdrop-blur-sm rounded-2xl border border-secondary/20 p-6 max-w-3xl mx-auto">
              <div className="absolute inset-0 bg-gradient-to-r from-secondary/5 to-accent/5 rounded-2xl blur-xl"></div>
              <p className="text-lg text-accent mb-0 relative z-10 font-medium">
                {t('hero.description')}
              </p>
              {/* Decorative corners */}
              <div className="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-secondary/50"></div>
              <div className="absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-accent/50"></div>
              <div className="absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-accent/50"></div>
              <div className="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-secondary/50"></div>
            </div>
          </div>

          {/* Premium Feature highlights */}
          <div className="flex flex-wrap justify-center gap-8 mb-16 flex-stable">
            {[
              { icon: Shield, text: t('hero.features.enhanced'), color: 'from-secondary to-accent', bgColor: 'from-secondary/20 to-accent/20' },
              { icon: Zap, text: t('hero.features.advanced'), color: 'from-accent to-secondary', bgColor: 'from-accent/20 to-secondary/20' },
              { icon: Code, text: t('hero.features.code'), color: 'from-secondary to-accent', bgColor: 'from-secondary/20 to-accent/20' },
            ].map((feature, index) => (
              <div key={index} className={`hero-feature-item group relative bg-gradient-to-r ${feature.bgColor} backdrop-blur-sm rounded-2xl border border-secondary/30 hover:border-accent/50 transition-all duration-500 hover:scale-105 hover:shadow-lg hover:shadow-secondary/20`}>
                <div className="absolute inset-0 bg-gradient-to-r from-secondary/5 to-accent/5 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10 flex items-center flex-stable">
                  <div className={`feature-icon p-2 rounded-xl bg-gradient-to-r ${feature.color} group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-6 h-6 text-primary" />
                  </div>
                  <span className="feature-text text-base font-semibold text-white group-hover:text-accent transition-colors duration-300">{feature.text}</span>
                </div>
                {/* Decorative sparkle */}
                <Sparkles className="absolute -top-1 -right-1 w-4 h-4 text-accent opacity-0 group-hover:opacity-100 animate-ping transition-opacity duration-300" />
              </div>
            ))}
          </div>

          {/* Premium CTA Button */}
          <div className="relative">
            <button
              onClick={handleCTAClick}
              className="group relative inline-flex items-center space-x-4 rtl:space-x-reverse bg-gradient-to-r from-secondary via-accent to-secondary text-primary font-bold px-12 py-6 rounded-2xl transition-all duration-500 hover:shadow-2xl hover:shadow-secondary/40 border-2 border-secondary/30 hover:border-accent/50 overflow-hidden"
            >
              {/* Animated background */}
              <div className="absolute inset-0 bg-gradient-to-r from-accent via-secondary to-accent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              {/* Button content */}
              <div className="relative z-10 flex items-center space-x-4 rtl:space-x-reverse flex-stable">
                <Crown className="w-6 h-6 group-hover:animate-pulse icon-stable" />
                <span className="text-xl group-hover:scale-105 transition-transform duration-300">{t('hero.cta')}</span>
                {language === 'ar' ? (
                  <ArrowLeft className="w-6 h-6 group-hover:translate-x-2 transition-transform duration-300 icon-stable" />
                ) : (
                  <ArrowRight className="w-6 h-6 group-hover:translate-x-2 transition-transform duration-300 icon-stable" />
                )}
              </div>

              {/* Sparkle effects */}
              <Sparkles className="absolute top-2 right-2 w-4 h-4 text-accent opacity-0 group-hover:opacity-100 animate-ping transition-opacity duration-300" />
              <Star className="absolute bottom-2 left-2 w-4 h-4 text-secondary opacity-0 group-hover:opacity-100 animate-ping delay-200 transition-opacity duration-300" />
            </button>

            {/* Glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-secondary/20 to-accent/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>
          </div>

          {/* Scroll indicator - Always visible with dark color */}
          <div className="absolute bottom-8 left-1/2 -translate-x-1/2 animate-bounce">
            <div className="w-6 h-10 border-2 border-gray-800/80 rounded-full flex justify-center bg-white/10 backdrop-blur-sm">
              <div className="w-1 h-3 bg-gray-800/80 rounded-full mt-2 animate-pulse"></div>
            </div>
            {/* Scroll hint text */}
            <div className="absolute top-12 left-1/2 -translate-x-1/2 text-gray-800/70 text-sm font-medium whitespace-nowrap">
              {language === 'ar' ? 'مرر للأسفل' : 'Scroll Down'}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;