{"openapi": "3.0.0", "info": {"title": "Khanfashariya API", "version": "1.0.0", "description": "Backend API for Khanfashariya Metin2 Services Platform"}, "servers": [{"url": "https://7b93f343ea56.ngrok-free.app", "description": "Production Server (ngrok)"}, {"url": "http://localhost:3001", "description": "Development server"}], "paths": {"/health": {"get": {"summary": "Health check", "responses": {"200": {"description": "Server is healthy"}}}}, "/api/status": {"get": {"summary": "API status", "responses": {"200": {"description": "API status information"}}}}, "/api/auth/login": {"post": {"summary": "User login", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "admin123"}}}}}}, "responses": {"200": {"description": "Login successful"}, "401": {"description": "Invalid credentials"}}}}, "/api/auth/register": {"post": {"summary": "User registration", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string", "example": "testuser"}, "email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "password123"}, "confirmPassword": {"type": "string", "example": "password123"}}}}}}, "responses": {"201": {"description": "User registered successfully"}, "400": {"description": "Registration failed"}}}}, "/api/users/profile": {"get": {"summary": "Get user profile", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "User profile data"}, "401": {"description": "Unauthorized"}}}}, "/api/systems": {"get": {"summary": "Get system services", "responses": {"200": {"description": "List of system services"}}}}, "/api/services": {"get": {"summary": "Get technical services", "responses": {"200": {"description": "List of technical services"}}}}, "/api/orders": {"get": {"summary": "Get orders", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of orders"}}}}, "/api/admin/dashboard": {"get": {"summary": "Admin dashboard", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Admin dashboard data"}, "403": {"description": "Admin access required"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}