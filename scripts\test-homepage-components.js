#!/usr/bin/env node

const axios = require('axios');

async function testHomepageComponents() {
  console.log('🏠 Testing Homepage Components Data Flow...\n');
  
  const BASE_URL = 'http://localhost:3001';
  const headers = {
    'ngrok-skip-browser-warning': 'true',
    'User-Agent': 'HomepageComponentTester/1.0'
  };

  try {
    // Test 1: Systems API (what SystemsGrid component uses)
    console.log('1️⃣ Testing Systems API (SystemsGrid component)...');
    const systemsResponse = await axios.get(`${BASE_URL}/api/systems`, { headers });
    
    console.log(`   Status: ${systemsResponse.status}`);
    console.log(`   Success: ${systemsResponse.data.success}`);
    
    if (systemsResponse.data.success) {
      const systemsData = systemsResponse.data.data;
      console.log(`   Data structure: ${JSON.stringify(Object.keys(systemsData))}`);
      
      if (systemsData.systems) {
        const systems = systemsData.systems;
        console.log(`   Total systems: ${systems.length}`);
        
        const activeSystems = systems.filter(s => s.status === 'active');
        console.log(`   Active systems: ${activeSystems.length}`);
        
        if (activeSystems.length > 0) {
          console.log('   Active systems details:');
          activeSystems.forEach((system, index) => {
            console.log(`     ${index + 1}. ${system.name_ar} - $${system.price} - ${system.status}`);
          });
        } else {
          console.log('   ⚠️ No active systems found!');
          console.log('   All systems:');
          systems.forEach((system, index) => {
            console.log(`     ${index + 1}. ${system.name_ar} - $${system.price} - ${system.status}`);
          });
        }
      }
    }

    // Test 2: Technical Services API (what Services component uses)
    console.log('\n2️⃣ Testing Technical Services API (Services component)...');
    const servicesResponse = await axios.get(`${BASE_URL}/api/services/technical`, { headers });
    
    console.log(`   Status: ${servicesResponse.status}`);
    console.log(`   Success: ${servicesResponse.data.success}`);
    
    if (servicesResponse.data.success) {
      const servicesData = servicesResponse.data.data;
      console.log(`   Data structure: ${JSON.stringify(Object.keys(servicesData))}`);
      
      if (servicesData.services) {
        const services = servicesData.services;
        console.log(`   Total services: ${services.length}`);
        
        const activeServices = services.filter(s => s.status === 'active');
        console.log(`   Active services: ${activeServices.length}`);
        
        if (activeServices.length > 0) {
          console.log('   Active services details:');
          activeServices.forEach((service, index) => {
            console.log(`     ${index + 1}. ${service.name_ar} - $${service.price} - ${service.status}`);
          });
        } else {
          console.log('   ⚠️ No active services found!');
          console.log('   All services:');
          services.forEach((service, index) => {
            console.log(`     ${index + 1}. ${service.name_ar} - $${service.price} - ${service.status}`);
          });
        }
      }
    }

    // Test 3: Premium Content API (what PremiumSection component uses)
    console.log('\n3️⃣ Testing Premium Content API (PremiumSection component)...');
    const premiumResponse = await axios.get(`${BASE_URL}/api/services/premium`, { headers });
    
    console.log(`   Status: ${premiumResponse.status}`);
    console.log(`   Success: ${premiumResponse.data.success}`);
    
    if (premiumResponse.data.success) {
      const premiumData = premiumResponse.data.data;
      console.log(`   Data structure: ${JSON.stringify(Object.keys(premiumData))}`);
      
      if (premiumData.premiumContent) {
        const premium = premiumData.premiumContent;
        console.log(`   Total premium items: ${premium.length}`);
        
        const activePremium = premium.filter(p => p.status === 'active');
        console.log(`   Active premium items: ${activePremium.length}`);
        
        if (activePremium.length > 0) {
          console.log('   Active premium items details:');
          activePremium.forEach((item, index) => {
            console.log(`     ${index + 1}. ${item.title_ar} - $${item.price} - ${item.status}`);
          });
        } else {
          console.log('   ⚠️ No active premium items found!');
          console.log('   All premium items:');
          premium.forEach((item, index) => {
            console.log(`     ${index + 1}. ${item.title_ar} - $${item.price} - ${item.status}`);
          });
        }
      }
    }

    // Test 4: Check database status directly
    console.log('\n4️⃣ Checking database status...');
    
    // Check systems table
    const systemsCountResponse = await axios.get(`${BASE_URL}/api/admin/systems`, { 
      headers: {
        ...headers,
        'Authorization': 'Bearer test-token' // This will fail but we can see the error
      }
    });
    
    console.log('   Systems table check: (This might fail due to auth, but that\'s expected)');

    // Summary and recommendations
    console.log('\n📊 Summary and Analysis:');
    
    const systemsCount = systemsResponse.data.success ? 
      (systemsResponse.data.data.systems?.filter(s => s.status === 'active').length || 0) : 0;
    const servicesCount = servicesResponse.data.success ? 
      (servicesResponse.data.data.services?.filter(s => s.status === 'active').length || 0) : 0;
    const premiumCount = premiumResponse.data.success ? 
      (premiumResponse.data.data.premiumContent?.filter(p => p.status === 'active').length || 0) : 0;
    
    console.log(`   Active Systems: ${systemsCount}`);
    console.log(`   Active Services: ${servicesCount}`);
    console.log(`   Active Premium: ${premiumCount}`);
    
    if (systemsCount === 0 && servicesCount === 0 && premiumCount === 0) {
      console.log('\n❌ PROBLEM IDENTIFIED: No active content found!');
      console.log('   Possible causes:');
      console.log('   1. All items in database have status = "inactive"');
      console.log('   2. Database is empty');
      console.log('   3. API filtering is too strict');
      console.log('\n🔧 Recommended fixes:');
      console.log('   1. Update database records to set status = "active"');
      console.log('   2. Check API filtering logic');
      console.log('   3. Verify frontend components handle empty data correctly');
    } else if (systemsCount > 0 || servicesCount > 0 || premiumCount > 0) {
      console.log('\n✅ CONTENT AVAILABLE: Some active content found');
      console.log('   If homepage is still empty, check:');
      console.log('   1. Frontend component rendering logic');
      console.log('   2. API client configuration');
      console.log('   3. Browser console for JavaScript errors');
    }

  } catch (error) {
    console.error('❌ Error during testing:', error.message);
    
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testHomepageComponents();
