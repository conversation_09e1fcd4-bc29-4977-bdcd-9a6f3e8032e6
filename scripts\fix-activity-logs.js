#!/usr/bin/env node

/**
 * Fix activity_logs table by adding missing columns
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixActivityLogs() {
  let connection;
  
  try {
    console.log('🔧 Fixing activity_logs table...');
    
    // Connect to database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '123456',
      database: 'khanfashariya_db',
      charset: 'utf8mb4'
    });
    
    console.log('✅ Connected to database');
    
    // Check if columns exist
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'khanfashariya_db' 
      AND TABLE_NAME = 'activity_logs'
    `);
    
    const columnNames = columns.map(col => col.COLUMN_NAME);
    
    // Add ip_address column if missing
    if (!columnNames.includes('ip_address')) {
      await connection.execute(`ALTER TABLE activity_logs ADD COLUMN ip_address VARCHAR(45)`);
      console.log('✅ Added ip_address column');
    } else {
      console.log('⚠️  ip_address column already exists');
    }
    
    // Add user_agent column if missing
    if (!columnNames.includes('user_agent')) {
      await connection.execute(`ALTER TABLE activity_logs ADD COLUMN user_agent TEXT`);
      console.log('✅ Added user_agent column');
    } else {
      console.log('⚠️  user_agent column already exists');
    }
    
    console.log('🎉 activity_logs table fixed successfully!');
    
  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run fix
fixActivityLogs().catch(error => {
  console.error('Fix failed:', error);
  process.exit(1);
});
