import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { X, Edit, Eye, Plus, Save } from 'lucide-react';
import Modal from './ui/Modal';
import Button from './ui/Button';
import Input from './ui/Input';

interface ModalTestProps {
  onClose: () => void;
}

/**
 * Modal Test Component - Test all modal functionalities
 */
const ModalTest: React.FC<ModalTestProps> = ({ onClose }) => {
  const { language } = useTranslation();
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [testData, setTestData] = useState({
    name: { ar: 'نظام اختبار', en: 'Test System' },
    description: { ar: 'وصف النظام', en: 'System description' },
    price: 100
  });

  const handleSave = () => {
    alert('Data saved successfully!');
    setShowEditModal(false);
    setShowCreateModal(false);
  };

  return (
    <>
      {/* Main Test Modal */}
      <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-40 flex items-center justify-center p-4">
        <div className="bg-primary rounded-xl max-w-2xl w-full border border-accent/30 relative">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-accent/20">
            <h2 className="text-xl font-bold text-white">
              {language === 'ar' ? 'اختبار النوافذ المنبثقة' : 'Modal Test'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Body */}
          <div className="p-6 space-y-6">
            <div className="bg-background/50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-4">
                {language === 'ar' ? 'اختبار النوافذ' : 'Test Modals'}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="flex items-center justify-center space-x-2 rtl:space-x-reverse px-4 py-3 bg-green-500/20 text-green-400 border border-green-500/30 rounded-lg hover:bg-green-500/30 transition-colors"
                >
                  <Plus className="w-5 h-5" />
                  <span>{language === 'ar' ? 'إضافة' : 'Create'}</span>
                </button>

                <button
                  onClick={() => setShowEditModal(true)}
                  className="flex items-center justify-center space-x-2 rtl:space-x-reverse px-4 py-3 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded-lg hover:bg-blue-500/30 transition-colors"
                >
                  <Edit className="w-5 h-5" />
                  <span>{language === 'ar' ? 'تعديل' : 'Edit'}</span>
                </button>

                <button
                  onClick={() => setShowPreviewModal(true)}
                  className="flex items-center justify-center space-x-2 rtl:space-x-reverse px-4 py-3 bg-purple-500/20 text-purple-400 border border-purple-500/30 rounded-lg hover:bg-purple-500/30 transition-colors"
                >
                  <Eye className="w-5 h-5" />
                  <span>{language === 'ar' ? 'معاينة' : 'Preview'}</span>
                </button>
              </div>
            </div>

            <div className="bg-background/50 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-white mb-2">
                {language === 'ar' ? 'البيانات الحالية' : 'Current Data'}
              </h4>
              <div className="space-y-2 text-sm">
                <p className="text-gray-300">
                  <span className="font-medium">{language === 'ar' ? 'الاسم (عربي):' : 'Name (Arabic):'}</span> {testData.name.ar}
                </p>
                <p className="text-gray-300">
                  <span className="font-medium">{language === 'ar' ? 'الاسم (إنجليزي):' : 'Name (English):'}</span> {testData.name.en}
                </p>
                <p className="text-gray-300">
                  <span className="font-medium">{language === 'ar' ? 'الوصف (عربي):' : 'Description (Arabic):'}</span> {testData.description.ar}
                </p>
                <p className="text-gray-300">
                  <span className="font-medium">{language === 'ar' ? 'الوصف (إنجليزي):' : 'Description (English):'}</span> {testData.description.en}
                </p>
                <p className="text-gray-300">
                  <span className="font-medium">{language === 'ar' ? 'السعر:' : 'Price:'}</span> ${testData.price}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Create Modal */}
      {showCreateModal && (
        <Modal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title={language === 'ar' ? 'إضافة نظام جديد' : 'Add New System'}
          size="lg"
          className="z-50"
        >
          <Modal.Body>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label={language === 'ar' ? 'الاسم (عربي)' : 'Name (Arabic)'}
                  value={testData.name.ar}
                  onChange={(e) => setTestData(prev => ({
                    ...prev,
                    name: { ...prev.name, ar: e.target.value }
                  }))}
                />
                <Input
                  label={language === 'ar' ? 'الاسم (إنجليزي)' : 'Name (English)'}
                  value={testData.name.en}
                  onChange={(e) => setTestData(prev => ({
                    ...prev,
                    name: { ...prev.name, en: e.target.value }
                  }))}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label={language === 'ar' ? 'الوصف (عربي)' : 'Description (Arabic)'}
                  value={testData.description.ar}
                  onChange={(e) => setTestData(prev => ({
                    ...prev,
                    description: { ...prev.description, ar: e.target.value }
                  }))}
                />
                <Input
                  label={language === 'ar' ? 'الوصف (إنجليزي)' : 'Description (English)'}
                  value={testData.description.en}
                  onChange={(e) => setTestData(prev => ({
                    ...prev,
                    description: { ...prev.description, en: e.target.value }
                  }))}
                />
              </div>

              <Input
                label={language === 'ar' ? 'السعر' : 'Price'}
                type="number"
                value={testData.price}
                onChange={(e) => setTestData(prev => ({
                  ...prev,
                  price: Number(e.target.value)
                }))}
              />
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button variant="primary" onClick={handleSave}>
              <Save className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'حفظ' : 'Save'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}

      {/* Edit Modal */}
      {showEditModal && (
        <Modal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          title={language === 'ar' ? 'تعديل النظام' : 'Edit System'}
          size="lg"
          className="z-50"
        >
          <Modal.Body>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label={language === 'ar' ? 'الاسم (عربي)' : 'Name (Arabic)'}
                  value={testData.name.ar}
                  onChange={(e) => setTestData(prev => ({
                    ...prev,
                    name: { ...prev.name, ar: e.target.value }
                  }))}
                />
                <Input
                  label={language === 'ar' ? 'الاسم (إنجليزي)' : 'Name (English)'}
                  value={testData.name.en}
                  onChange={(e) => setTestData(prev => ({
                    ...prev,
                    name: { ...prev.name, en: e.target.value }
                  }))}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label={language === 'ar' ? 'الوصف (عربي)' : 'Description (Arabic)'}
                  value={testData.description.ar}
                  onChange={(e) => setTestData(prev => ({
                    ...prev,
                    description: { ...prev.description, ar: e.target.value }
                  }))}
                />
                <Input
                  label={language === 'ar' ? 'الوصف (إنجليزي)' : 'Description (English)'}
                  value={testData.description.en}
                  onChange={(e) => setTestData(prev => ({
                    ...prev,
                    description: { ...prev.description, en: e.target.value }
                  }))}
                />
              </div>

              <Input
                label={language === 'ar' ? 'السعر' : 'Price'}
                type="number"
                value={testData.price}
                onChange={(e) => setTestData(prev => ({
                  ...prev,
                  price: Number(e.target.value)
                }))}
              />
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowEditModal(false)}>
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button variant="primary" onClick={handleSave}>
              <Save className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'حفظ التغييرات' : 'Save Changes'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}

      {/* Preview Modal */}
      {showPreviewModal && (
        <Modal
          isOpen={showPreviewModal}
          onClose={() => setShowPreviewModal(false)}
          title={language === 'ar' ? 'معاينة النظام' : 'System Preview'}
          size="xl"
          className="z-50"
        >
          <Modal.Body>
            <div className="space-y-6">
              <div className="bg-gradient-to-br from-secondary/10 to-accent/10 rounded-lg p-6 border border-secondary/20">
                <h3 className="text-2xl font-bold text-white mb-4">
                  {language === 'ar' ? testData.name_ar : testData.name_en}
                </h3>
                <p className="text-gray-300 mb-6">
                  {language === 'ar' ? testData.description_ar : testData.description_en}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400 text-lg">
                    {language === 'ar' ? 'السعر:' : 'Price:'}
                  </span>
                  <span className="text-3xl font-bold text-secondary">
                    ${testData.price}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-lg font-semibold text-white mb-3">
                    {language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'}
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-400">{language === 'ar' ? 'الاسم:' : 'Name:'}</span>
                      <span className="text-white">{language === 'ar' ? testData.name_ar : testData.name_en}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">{language === 'ar' ? 'السعر:' : 'Price:'}</span>
                      <span className="text-white">${testData.price}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-white mb-3">
                    {language === 'ar' ? 'الوصف' : 'Description'}
                  </h4>
                  <p className="text-gray-300">
                    {language === 'ar' ? testData.description_ar : testData.description_en}
                  </p>
                </div>
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="outline" onClick={() => setShowPreviewModal(false)}>
              {language === 'ar' ? 'إغلاق' : 'Close'}
            </Button>
            <Button variant="primary" onClick={() => {
              setShowPreviewModal(false);
              setShowEditModal(true);
            }}>
              <Edit className="w-4 h-4 mr-2" />
              {language === 'ar' ? 'تعديل' : 'Edit'}
            </Button>
          </Modal.Footer>
        </Modal>
      )}
    </>
  );
};

export default ModalTest;
