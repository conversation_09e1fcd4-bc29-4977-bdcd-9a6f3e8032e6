#!/usr/bin/env node

/**
 * Comprehensive Database Fix Script
 * 
 * Fixes all database issues including JSON fields and missing translations
 */

const mysql = require('mysql2/promise');

const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khan<PERSON><PERSON><PERSON>_db',
  charset: 'utf8mb4'
};

async function fixDatabase() {
  console.log('🔧 Comprehensive Database Fix');
  console.log('=============================\n');
  
  let connection;
  
  try {
    connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ Connected to MySQL database\n');
    
    // Fix system_services table
    console.log('🔧 Fixing system_services table...');
    
    // Get all systems
    const [systems] = await connection.execute('SELECT * FROM system_services');
    
    for (const system of systems) {
      const updates = [];
      const params = [];
      
      // Fix features_ar
      if (!system.features_ar || typeof system.features_ar === 'string') {
        try {
          JSON.parse(system.features_ar || '[]');
        } catch (e) {
          updates.push('features_ar = ?');
          params.push(JSON.stringify(['ميزة متقدمة', 'تقنية حديثة', 'أداء عالي']));
        }
      }
      
      // Fix features_en
      if (!system.features_en || typeof system.features_en === 'string') {
        try {
          JSON.parse(system.features_en || '[]');
        } catch (e) {
          updates.push('features_en = ?');
          params.push(JSON.stringify(['Advanced Feature', 'Modern Technology', 'High Performance']));
        }
      }
      
      // Fix tech_specs_ar
      if (!system.tech_specs_ar || typeof system.tech_specs_ar === 'string') {
        try {
          JSON.parse(system.tech_specs_ar || '[]');
        } catch (e) {
          updates.push('tech_specs_ar = ?');
          params.push(JSON.stringify(['MySQL 8.0+', 'PHP 8.0+', 'Apache/Nginx']));
        }
      }
      
      // Fix tech_specs_en
      if (!system.tech_specs_en || typeof system.tech_specs_en === 'string') {
        try {
          JSON.parse(system.tech_specs_en || '[]');
        } catch (e) {
          updates.push('tech_specs_en = ?');
          params.push(JSON.stringify(['MySQL 8.0+', 'PHP 8.0+', 'Apache/Nginx']));
        }
      }
      
      // Fix gallery_images
      if (!system.gallery_images || typeof system.gallery_images === 'string') {
        try {
          JSON.parse(system.gallery_images || '[]');
        } catch (e) {
          updates.push('gallery_images = ?');
          params.push(JSON.stringify([]));
        }
      }
      
      if (updates.length > 0) {
        params.push(system.id);
        await connection.execute(
          `UPDATE system_services SET ${updates.join(', ')} WHERE id = ?`,
          params
        );
        console.log(`   ✅ Fixed system: ${system.id}`);
      }
    }
    
    // Fix technical_services table
    console.log('🔧 Fixing technical_services table...');
    
    const [services] = await connection.execute('SELECT * FROM technical_services');
    
    for (const service of services) {
      const updates = [];
      const params = [];
      
      // Fix features_ar
      if (!service.features_ar || typeof service.features_ar === 'string') {
        try {
          JSON.parse(service.features_ar || '[]');
        } catch (e) {
          updates.push('features_ar = ?');
          params.push(JSON.stringify(['خدمة احترافية', 'دعم فني متميز', 'ضمان الجودة']));
        }
      }
      
      // Fix features_en
      if (!service.features_en || typeof service.features_en === 'string') {
        try {
          JSON.parse(service.features_en || '[]');
        } catch (e) {
          updates.push('features_en = ?');
          params.push(JSON.stringify(['Professional Service', 'Premium Support', 'Quality Guarantee']));
        }
      }
      
      if (updates.length > 0) {
        params.push(service.id);
        await connection.execute(
          `UPDATE technical_services SET ${updates.join(', ')} WHERE id = ?`,
          params
        );
        console.log(`   ✅ Fixed service: ${service.id}`);
      }
    }
    
    // Fix premium_content table
    console.log('🔧 Fixing premium_content table...');
    
    const [premiumContent] = await connection.execute('SELECT * FROM premium_content');
    
    for (const content of premiumContent) {
      const updates = [];
      const params = [];
      
      // Fix missing title fields (premium_content uses title_ar/title_en instead of name_ar/name_en)
      if (!content.title_ar) {
        updates.push('title_ar = ?');
        params.push('الحزمة المميزة الشاملة');
      }

      if (!content.title_en) {
        updates.push('title_en = ?');
        params.push('Complete Premium Package');
      }
      
      // Fix missing description fields
      if (!content.description_ar) {
        updates.push('description_ar = ?');
        params.push('حزمة شاملة تتضمن جميع الأنظمة والخدمات مع دعم مدى الحياة');
      }
      
      if (!content.description_en) {
        updates.push('description_en = ?');
        params.push('Complete package including all systems and services with lifetime support');
      }
      
      // Fix features_ar
      if (!content.features_ar || typeof content.features_ar === 'string') {
        try {
          JSON.parse(content.features_ar || '[]');
        } catch (e) {
          updates.push('features_ar = ?');
          params.push(JSON.stringify(['جميع الأنظمة', 'دعم مدى الحياة', 'تحديثات مجانية', 'أولوية في الدعم']));
        }
      }
      
      // Fix features_en
      if (!content.features_en || typeof content.features_en === 'string') {
        try {
          JSON.parse(content.features_en || '[]');
        } catch (e) {
          updates.push('features_en = ?');
          params.push(JSON.stringify(['All Systems', 'Lifetime Support', 'Free Updates', 'Priority Support']));
        }
      }
      
      // Fix included_systems
      if (!content.included_systems || typeof content.included_systems === 'string') {
        try {
          JSON.parse(content.included_systems || '[]');
        } catch (e) {
          updates.push('included_systems = ?');
          params.push(JSON.stringify([]));
        }
      }
      
      // Fix included_services
      if (!content.included_services || typeof content.included_services === 'string') {
        try {
          JSON.parse(content.included_services || '[]');
        } catch (e) {
          updates.push('included_services = ?');
          params.push(JSON.stringify([]));
        }
      }
      
      // Fix gallery_images
      if (!content.gallery_images || typeof content.gallery_images === 'string') {
        try {
          JSON.parse(content.gallery_images || '[]');
        } catch (e) {
          updates.push('gallery_images = ?');
          params.push(JSON.stringify([]));
        }
      }
      
      if (updates.length > 0) {
        params.push(content.id);
        await connection.execute(
          `UPDATE premium_content SET ${updates.join(', ')} WHERE id = ?`,
          params
        );
        console.log(`   ✅ Fixed premium content: ${content.id}`);
      }
    }
    
    console.log('\n🎉 Database fix completed successfully!');
    console.log('✅ All JSON fields are now properly formatted');
    console.log('✅ All missing translations have been added');
    console.log('✅ Database is ready for use');
    
    return true;
    
  } catch (error) {
    console.error('❌ Database fix failed:', error.message);
    return false;
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run if called directly
if (require.main === module) {
  fixDatabase().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Fix error:', error);
    process.exit(1);
  });
}

module.exports = { fixDatabase };
