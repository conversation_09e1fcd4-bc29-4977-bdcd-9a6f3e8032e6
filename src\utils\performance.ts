/**
 * Performance Optimization Utilities
 *
 * Collection of utilities for monitoring and optimizing performance
 */

import React from 'react'

// Web Vitals monitoring
export interface WebVitalsMetrics {
  FCP?: number  // First Contentful Paint
  LCP?: number  // Largest Contentful Paint
  FID?: number  // First Input Delay
  CLS?: number  // Cumulative Layout Shift
  TTFB?: number // Time to First Byte
}

class PerformanceMonitor {
  private metrics: WebVitalsMetrics = {}
  private observers: PerformanceObserver[] = []

  constructor() {
    this.initializeObservers()
  }

  private initializeObservers() {
    // Observe paint metrics
    if ('PerformanceObserver' in window) {
      try {
        // First Contentful Paint
        const paintObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name === 'first-contentful-paint') {
              this.metrics.FCP = entry.startTime
              this.reportMetric('FCP', entry.startTime)
            }
          }
        })
        paintObserver.observe({ entryTypes: ['paint'] })
        this.observers.push(paintObserver)

        // Largest Contentful Paint
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          this.metrics.LCP = lastEntry.startTime
          this.reportMetric('LCP', lastEntry.startTime)
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.push(lcpObserver)

        // First Input Delay
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.metrics.FID = (entry as any).processingStart - entry.startTime
            this.reportMetric('FID', this.metrics.FID)
          }
        })
        fidObserver.observe({ entryTypes: ['first-input'] })
        this.observers.push(fidObserver)

        // Cumulative Layout Shift
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value
            }
          }
          this.metrics.CLS = clsValue
          this.reportMetric('CLS', clsValue)
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
        this.observers.push(clsObserver)

      } catch (error) {
        console.warn('Performance monitoring not fully supported:', error)
      }
    }

    // Time to First Byte
    if ('navigation' in performance) {
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigationEntry) {
        this.metrics.TTFB = navigationEntry.responseStart - navigationEntry.requestStart
        this.reportMetric('TTFB', this.metrics.TTFB)
      }
    }
  }

  private reportMetric(name: string, value: number) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 ${name}: ${value.toFixed(2)}ms`)
    }

    // Send to analytics service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToAnalytics(name, value)
    }
  }

  private sendToAnalytics(metric: string, value: number) {
    // Integration with analytics services
    try {
      // Google Analytics 4 example
      if (typeof gtag !== 'undefined') {
        gtag('event', 'web_vitals', {
          metric_name: metric,
          metric_value: Math.round(value),
          custom_parameter: 'khanfashariya_performance'
        })
      }

      // Custom analytics endpoint
      fetch('/api/analytics/performance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          metric,
          value,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          url: window.location.href
        })
      }).catch(() => {
        // Silently fail - don't block user experience
      })
    } catch (error) {
      // Silently fail - analytics shouldn't break the app
    }
  }

  getMetrics(): WebVitalsMetrics {
    return { ...this.metrics }
  }

  disconnect() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// Resource loading optimization
export class ResourceOptimizer {
  private static instance: ResourceOptimizer
  private preloadedResources = new Set<string>()

  static getInstance(): ResourceOptimizer {
    if (!ResourceOptimizer.instance) {
      ResourceOptimizer.instance = new ResourceOptimizer()
    }
    return ResourceOptimizer.instance
  }

  // Preload critical resources
  preloadResource(href: string, as: string, crossorigin?: string) {
    if (this.preloadedResources.has(href)) return

    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = href
    link.as = as
    if (crossorigin) link.crossOrigin = crossorigin

    link.onload = () => this.preloadedResources.add(href)
    link.onerror = () => console.warn(`Failed to preload resource: ${href}`)

    document.head.appendChild(link)
  }

  // Prefetch resources for future navigation
  prefetchResource(href: string) {
    if (this.preloadedResources.has(href)) return

    const link = document.createElement('link')
    link.rel = 'prefetch'
    link.href = href

    link.onload = () => this.preloadedResources.add(href)
    document.head.appendChild(link)
  }

  // Optimize images with lazy loading
  optimizeImages() {
    const images = document.querySelectorAll('img[data-src]')
    
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            img.src = img.dataset.src!
            img.removeAttribute('data-src')
            imageObserver.unobserve(img)
          }
        })
      })

      images.forEach(img => imageObserver.observe(img))
    } else {
      // Fallback for browsers without IntersectionObserver
      images.forEach(img => {
        const image = img as HTMLImageElement
        image.src = image.dataset.src!
        image.removeAttribute('data-src')
      })
    }
  }

  // Critical CSS inlining
  inlineCriticalCSS(css: string) {
    const style = document.createElement('style')
    style.textContent = css
    document.head.appendChild(style)
  }
}

// Memory optimization
export class MemoryOptimizer {
  private static cleanupTasks: (() => void)[] = []

  static addCleanupTask(task: () => void) {
    this.cleanupTasks.push(task)
  }

  static cleanup() {
    this.cleanupTasks.forEach(task => {
      try {
        task()
      } catch (error) {
        console.warn('Cleanup task failed:', error)
      }
    })
    this.cleanupTasks = []
  }

  // Debounce function for performance
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout
    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func(...args), wait)
    }
  }

  // Throttle function for performance
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }
}

// Bundle size analyzer
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV === 'development') {
    const scripts = Array.from(document.querySelectorAll('script[src]'))
    const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))
    
    console.group('📦 Bundle Analysis')
    console.log('Scripts:', scripts.length)
    console.log('Stylesheets:', styles.length)
    
    // Estimate total size (rough calculation)
    let totalEstimatedSize = 0
    scripts.forEach(script => {
      const src = (script as HTMLScriptElement).src
      if (src.includes('chunk')) {
        totalEstimatedSize += 50 // Rough estimate in KB
      }
    })
    
    console.log(`Estimated total size: ~${totalEstimatedSize}KB`)
    console.groupEnd()
  }
}

// Performance hooks
export const usePerformanceMonitoring = () => {
  const [metrics, setMetrics] = React.useState<WebVitalsMetrics>({})
  
  React.useEffect(() => {
    const monitor = new PerformanceMonitor()
    
    const updateMetrics = () => {
      setMetrics(monitor.getMetrics())
    }
    
    const interval = setInterval(updateMetrics, 1000)
    
    return () => {
      clearInterval(interval)
      monitor.disconnect()
    }
  }, [])
  
  return metrics
}

// Initialize performance monitoring
export const initializePerformanceMonitoring = () => {
  // Start monitoring
  const monitor = new PerformanceMonitor()
  
  // Setup resource optimization
  const optimizer = ResourceOptimizer.getInstance()
  
  // Preload critical resources
  optimizer.preloadResource('/api/systems', 'fetch')
  optimizer.preloadResource('/api/services/technical', 'fetch')
  
  // Optimize images on load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      optimizer.optimizeImages()
    })
  } else {
    optimizer.optimizeImages()
  }
  
  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    monitor.disconnect()
    MemoryOptimizer.cleanup()
  })
  
  // Analyze bundle in development
  if (process.env.NODE_ENV === 'development') {
    setTimeout(analyzeBundleSize, 2000)
  }
  
  return { monitor, optimizer }
}

// Export singleton instances
export const performanceMonitor = new PerformanceMonitor()
export const resourceOptimizer = ResourceOptimizer.getInstance()
