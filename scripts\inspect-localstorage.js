#!/usr/bin/env node

/**
 * LocalStorage Data Inspector
 * 
 * This script inspects all data currently stored in localStorage
 * to ensure complete migration to MySQL before removal.
 */

const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

async function inspectLocalStorageData() {
  console.log('🔍 Inspecting localStorage data...\n');
  
  let browser;
  try {
    // Launch browser
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Navigate to the local development server
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });
    
    // Extract all localStorage data
    const localStorageData = await page.evaluate(() => {
      const data = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          try {
            const value = localStorage.getItem(key);
            // Try to parse as JSON, if it fails keep as string
            try {
              data[key] = JSON.parse(value);
            } catch {
              data[key] = value;
            }
          } catch (error) {
            data[key] = `Error reading: ${error.message}`;
          }
        }
      }
      return data;
    });
    
    // Analyze the data
    await analyzeLocalStorageData(localStorageData);
    
    // Save data to file for backup
    await saveDataBackup(localStorageData);
    
    return localStorageData;
    
  } catch (error) {
    console.error('Error inspecting localStorage:', error.message);
    
    // Fallback: try to read from database.ts directly
    console.log('\n⚠️ Falling back to direct database inspection...');
    return await inspectDatabaseFile();
    
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

async function analyzeLocalStorageData(data) {
  console.log('📊 LocalStorage Data Analysis');
  console.log('============================\n');
  
  const keys = Object.keys(data);
  
  if (keys.length === 0) {
    console.log('✅ No localStorage data found\n');
    return;
  }
  
  console.log(`📈 Total keys: ${keys.length}\n`);
  
  // Categorize data
  const categories = {
    'khanfashariya_db': 'Main database',
    'khanfashariya_current_user': 'Current user session',
    'khanfashariya_access_token': 'Authentication token',
    'khanfashariya_refresh_token': 'Refresh token',
    'khanfashariya_language': 'Language preference',
    'khanfashariya_theme': 'Theme preference',
    'khanfashariya_settings': 'User settings'
  };
  
  keys.forEach(key => {
    const category = categories[key] || 'Unknown';
    const dataSize = JSON.stringify(data[key]).length;
    
    console.log(`🔑 ${key}`);
    console.log(`   Category: ${category}`);
    console.log(`   Size: ${dataSize} characters`);
    
    if (key === 'khanfashariya_db' && typeof data[key] === 'object') {
      const dbData = data[key];
      console.log('   📋 Database contents:');
      
      Object.keys(dbData).forEach(table => {
        if (Array.isArray(dbData[table])) {
          console.log(`      • ${table}: ${dbData[table].length} records`);
        } else if (dbData[table] !== null) {
          console.log(`      • ${table}: ${typeof dbData[table]}`);
        }
      });
    }
    
    console.log('');
  });
}

async function saveDataBackup(data) {
  try {
    const backupDir = path.join(process.cwd(), 'backups');
    await fs.mkdir(backupDir, { recursive: true });
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(backupDir, `localStorage-backup-${timestamp}.json`);
    
    await fs.writeFile(backupFile, JSON.stringify(data, null, 2));
    console.log(`💾 Data backup saved to: ${backupFile}\n`);
    
  } catch (error) {
    console.warn('⚠️ Could not save backup:', error.message);
  }
}

async function inspectDatabaseFile() {
  console.log('📁 Inspecting database.ts file...\n');
  
  try {
    const databasePath = path.join(process.cwd(), 'src/lib/database.ts');
    const content = await fs.readFile(databasePath, 'utf8');
    
    // Extract sample data creation
    const sampleDataMatch = content.match(/createSampleData\(\)\s*{([\s\S]*?)}/);
    
    if (sampleDataMatch) {
      console.log('📋 Found sample data creation in database.ts');
      console.log('   This data needs to be migrated to MySQL\n');
      
      // Look for data structures
      const dataStructures = [
        'systemServices',
        'technicalServices', 
        'users',
        'orders',
        'premiumContent',
        'premiumPackages',
        'subscriptions',
        'contactMessages',
        'inboxMessages'
      ];
      
      dataStructures.forEach(structure => {
        if (content.includes(structure)) {
          console.log(`   ✓ Found ${structure} structure`);
        }
      });
      
      console.log('');
    }
    
    return { database_file_analyzed: true };
    
  } catch (error) {
    console.error('Error reading database.ts:', error.message);
    return {};
  }
}

async function checkMySQLData() {
  console.log('🗄️ Checking MySQL data...\n');
  
  try {
    const mysql = require('mysql2/promise');
    
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '123456',
      database: 'khanfashariya_db'
    });
    
    const tables = [
      'users',
      'system_services',
      'technical_services',
      'orders',
      'premium_content',
      'premium_packages',
      'subscriptions',
      'contact_messages',
      'inbox_messages'
    ];
    
    console.log('📊 MySQL Table Status:');
    console.log('─'.repeat(22));
    
    for (const table of tables) {
      try {
        const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
        const count = rows[0].count;
        console.log(`   ${table.padEnd(20)} ${count} records`);
      } catch (error) {
        console.log(`   ${table.padEnd(20)} ❌ Error: ${error.message}`);
      }
    }
    
    await connection.end();
    console.log('');
    
  } catch (error) {
    console.warn('⚠️ Could not check MySQL data:', error.message);
  }
}

async function generateMigrationPlan(localStorageData) {
  console.log('📋 Migration Plan');
  console.log('================\n');
  
  const migrationTasks = [];
  
  // Check what needs to be migrated
  if (localStorageData.khanfashariya_db) {
    const dbData = localStorageData.khanfashariya_db;
    
    Object.keys(dbData).forEach(table => {
      if (Array.isArray(dbData[table]) && dbData[table].length > 0) {
        migrationTasks.push({
          table,
          records: dbData[table].length,
          priority: getPriority(table)
        });
      }
    });
  }
  
  // Sort by priority
  migrationTasks.sort((a, b) => a.priority - b.priority);
  
  if (migrationTasks.length === 0) {
    console.log('✅ No data migration needed\n');
    return;
  }
  
  console.log('📝 Required migrations:');
  migrationTasks.forEach((task, index) => {
    console.log(`   ${index + 1}. ${task.table}: ${task.records} records`);
  });
  
  console.log('\n🔧 Recommended steps:');
  console.log('   1. Run migration script for each table');
  console.log('   2. Verify data integrity in MySQL');
  console.log('   3. Test API endpoints');
  console.log('   4. Remove localStorage usage');
  console.log('');
}

function getPriority(table) {
  const priorities = {
    'users': 1,
    'systemServices': 2,
    'technicalServices': 3,
    'orders': 4,
    'premiumContent': 5,
    'premiumPackages': 6,
    'subscriptions': 7,
    'contactMessages': 8,
    'inboxMessages': 9
  };
  return priorities[table] || 10;
}

async function main() {
  console.log('🔍 LocalStorage Data Inspection');
  console.log('===============================\n');
  
  // Inspect localStorage data
  const localStorageData = await inspectLocalStorageData();
  
  // Check MySQL data
  await checkMySQLData();
  
  // Generate migration plan
  await generateMigrationPlan(localStorageData);
  
  console.log('✅ Inspection complete!');
  console.log('📋 Review the migration plan above before proceeding');
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Error during inspection:', error);
    process.exit(1);
  });
}

module.exports = { inspectLocalStorageData, checkMySQLData };
