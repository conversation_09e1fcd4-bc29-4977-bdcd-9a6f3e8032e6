#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'https://7b93f343ea56.ngrok-free.app';

async function testFrontendDataWithNgrok() {
  console.log('🌐 Testing Frontend Data with ngrok URLs...\n');
  
  const headers = {
    'ngrok-skip-browser-warning': 'true',
    'User-Agent': 'TestScript/1.0',
    'Content-Type': 'application/json'
  };

  try {
    // Test 1: Public Systems Data
    console.log('1️⃣ Testing Systems Data...');
    const systemsResponse = await axios.get(`${BASE_URL}/api/systems`, { headers });
    
    if (systemsResponse.data.success && systemsResponse.data.data.systems) {
      const systems = systemsResponse.data.data.systems;
      console.log(`✅ Found ${systems.length} systems in database`);
      
      systems.forEach((system, index) => {
        console.log(`   ${index + 1}. ${system.name_ar} (${system.name_en}) - $${system.price}`);
      });
    } else {
      console.log('❌ No systems data found');
    }

    // Test 2: Login and get token
    console.log('\n2️⃣ Testing Admin Login...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    }, { headers });

    let authToken = null;
    if (loginResponse.data.success && loginResponse.data.data.accessToken) {
      authToken = loginResponse.data.data.accessToken;
      console.log('✅ Admin login successful, token received');
    } else {
      console.log('❌ Admin login failed');
      return;
    }

    // Test 3: Get user profile with token
    console.log('\n3️⃣ Testing User Profile...');
    const authHeaders = {
      ...headers,
      'Authorization': `Bearer ${authToken}`
    };

    const profileResponse = await axios.get(`${BASE_URL}/api/users/profile`, { headers: authHeaders });
    
    if (profileResponse.data.success) {
      const user = profileResponse.data.data;
      console.log(`✅ User profile: ${user.full_name} (${user.email}) - Role: ${user.role}`);
    } else {
      console.log('❌ Failed to get user profile');
    }

    // Test 4: Get orders with token
    console.log('\n4️⃣ Testing Orders Data...');
    const ordersResponse = await axios.get(`${BASE_URL}/api/orders`, { headers: authHeaders });
    
    if (ordersResponse.data.success) {
      const orders = ordersResponse.data.data;
      console.log(`✅ Found ${orders.length} orders in database`);
      
      if (orders.length > 0) {
        orders.slice(0, 3).forEach((order, index) => {
          console.log(`   ${index + 1}. Order #${order.id} - ${order.status} - $${order.total_amount}`);
        });
      }
    } else {
      console.log('❌ Failed to get orders data');
    }

    // Test 5: Technical Services
    console.log('\n5️⃣ Testing Technical Services...');
    const servicesResponse = await axios.get(`${BASE_URL}/api/services/technical`, { headers });
    
    if (servicesResponse.data.success) {
      const services = servicesResponse.data.data;
      console.log(`✅ Found ${services.length} technical services`);
      
      services.slice(0, 3).forEach((service, index) => {
        console.log(`   ${index + 1}. ${service.name_ar} - $${service.price}`);
      });
    } else {
      console.log('❌ No technical services found');
    }

    // Test 6: Premium Services
    console.log('\n6️⃣ Testing Premium Services...');
    const premiumResponse = await axios.get(`${BASE_URL}/api/services/premium`, { headers });
    
    if (premiumResponse.data.success) {
      const premium = premiumResponse.data.data;
      console.log(`✅ Found ${premium.length} premium services`);
      
      premium.slice(0, 3).forEach((service, index) => {
        console.log(`   ${index + 1}. ${service.title_ar} - $${service.price}`);
      });
    } else {
      console.log('❌ No premium services found');
    }

    // Test 7: Frontend API Proxy
    console.log('\n7️⃣ Testing Frontend API Proxy...');
    try {
      const proxyResponse = await axios.get('https://70f354611634.ngrok-free.app/api/systems', { 
        headers: {
          'ngrok-skip-browser-warning': 'true'
        }
      });
      
      if (proxyResponse.data.success) {
        console.log('✅ Frontend proxy working - data flows correctly');
      } else {
        console.log('❌ Frontend proxy not working properly');
      }
    } catch (error) {
      console.log('❌ Frontend proxy error:', error.message);
    }

    console.log('\n📊 Summary:');
    console.log('✅ Backend API: Working with database');
    console.log('✅ Authentication: Working');
    console.log('✅ Systems Data: Available');
    console.log('✅ User Management: Working');
    console.log('✅ Orders System: Working');
    console.log('✅ Services: Available');
    
    console.log('\n🎯 Next Steps:');
    console.log('1. Check if Frontend environment variables are loaded');
    console.log('2. Verify Frontend components are using correct API URLs');
    console.log('3. Check browser console for any CORS or network errors');
    console.log('4. Ensure Frontend proxy is configured correctly');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testFrontendDataWithNgrok();
