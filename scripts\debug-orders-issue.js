/**
 * Debug Orders Issue
 * Investigates why orders are not showing up in user and admin dashboards
 */

const axios = require('axios');
const mysql = require('mysql2/promise');

const API_BASE = 'http://localhost:3001/api';
const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khanfashariya_db'
};

const TEST_USER_CREDS = { email: '<EMAIL>', password: '123456' };
const ADMIN_CREDS = { email: '<EMAIL>', password: 'admin123' };

async function debugOrdersIssue() {
  console.log('🔍 Debugging Orders Issue...');
  
  try {
    // 1. Check database directly
    console.log('\n📊 Checking Database Directly...');
    const connection = await mysql.createConnection(DB_CONFIG);
    
    const [allOrders] = await connection.execute('SELECT * FROM orders ORDER BY created_at DESC LIMIT 10');
    console.log(`Database orders count: ${allOrders.length}`);
    
    if (allOrders.length > 0) {
      console.log('Recent orders in database:');
      allOrders.forEach((order, index) => {
        console.log(`${index + 1}. Order ${order.order_number} - User: ${order.user_id} - Status: ${order.status} - Type: ${order.order_type}`);
      });
    }
    
    // 2. Test user authentication and orders API
    console.log('\n👤 Testing User Orders API...');
    const userLogin = await axios.post(`${API_BASE}/auth/login`, TEST_USER_CREDS);
    
    if (userLogin.data.success) {
      const userAuth = {
        token: userLogin.data.data.tokens.accessToken,
        user: userLogin.data.data.user,
        headers: { Authorization: `Bearer ${userLogin.data.data.tokens.accessToken}` }
      };
      
      console.log(`User logged in: ${userAuth.user.email} (ID: ${userAuth.user.id})`);
      
      // Check user's orders in database
      const [userOrders] = await connection.execute('SELECT * FROM orders WHERE user_id = ?', [userAuth.user.id]);
      console.log(`User's orders in database: ${userOrders.length}`);
      
      // Test API endpoint
      const ordersResponse = await axios.get(`${API_BASE}/orders`, { headers: userAuth.headers });
      console.log(`User orders API response:`, {
        success: ordersResponse.data.success,
        hasData: !!ordersResponse.data.data,
        hasOrders: !!ordersResponse.data.data?.orders,
        ordersCount: ordersResponse.data.data?.orders?.length || 0
      });
      
      if (ordersResponse.data.data?.orders) {
        console.log('Orders from API:');
        ordersResponse.data.data.orders.forEach((order, index) => {
          console.log(`${index + 1}. ${order.order_number} - ${order.status}`);
        });
      }
    }
    
    // 3. Test admin orders API
    console.log('\n🛠️ Testing Admin Orders API...');
    const adminLogin = await axios.post(`${API_BASE}/auth/login`, ADMIN_CREDS);
    
    if (adminLogin.data.success) {
      const adminAuth = {
        token: adminLogin.data.data.tokens.accessToken,
        headers: { Authorization: `Bearer ${adminLogin.data.data.tokens.accessToken}` }
      };
      
      console.log('Admin logged in successfully');
      
      const adminOrdersResponse = await axios.get(`${API_BASE}/admin/orders`, { headers: adminAuth.headers });
      console.log(`Admin orders API response:`, {
        success: adminOrdersResponse.data.success,
        hasData: !!adminOrdersResponse.data.data,
        hasOrders: !!adminOrdersResponse.data.data?.orders,
        ordersCount: adminOrdersResponse.data.data?.orders?.length || 0
      });
      
      if (adminOrdersResponse.data.data?.orders) {
        console.log('Orders from Admin API:');
        adminOrdersResponse.data.data.orders.forEach((order, index) => {
          console.log(`${index + 1}. ${order.order_number} - User: ${order.user_id} - ${order.status}`);
        });
      }
    }
    
    // 4. Test creating a new order
    console.log('\n📦 Testing Order Creation...');
    const userAuth = {
      token: userLogin.data.data.tokens.accessToken,
      headers: { Authorization: `Bearer ${userLogin.data.data.tokens.accessToken}` }
    };
    
    const newOrderData = {
      order_type: 'system_service',
      item_id: 'test-debug-' + Date.now(),
      quantity: 1,
      notes_ar: 'طلب تجريبي لتشخيص المشكلة',
      notes_en: 'Debug test order'
    };
    
    const createOrderResponse = await axios.post(`${API_BASE}/orders`, newOrderData, { headers: userAuth.headers });
    console.log(`Order creation response:`, {
      success: createOrderResponse.data.success,
      hasOrder: !!createOrderResponse.data.data?.order,
      orderNumber: createOrderResponse.data.data?.order?.order_number
    });
    
    if (createOrderResponse.data.success) {
      // Check if it appears in database
      const [newOrderCheck] = await connection.execute(
        'SELECT * FROM orders WHERE order_number = ?', 
        [createOrderResponse.data.data.order.order_number]
      );
      console.log(`New order in database: ${newOrderCheck.length > 0 ? 'YES' : 'NO'}`);
      
      // Check if it appears in API immediately
      const ordersAfterCreate = await axios.get(`${API_BASE}/orders`, { headers: userAuth.headers });
      console.log(`Orders count after creation: ${ordersAfterCreate.data.data?.orders?.length || 0}`);
    }
    
    await connection.end();
    
  } catch (error) {
    console.log('❌ Error during debugging:', error.response?.data || error.message);
  }
}

debugOrdersIssue();
