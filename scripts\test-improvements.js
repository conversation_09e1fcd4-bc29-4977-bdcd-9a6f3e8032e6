#!/usr/bin/env node

/**
 * Test Script for New Improvements
 * 
 * Tests all the improvements implemented:
 * 1. Enhanced Dark Theme
 * 2. Error Boundary
 * 3. Mobile Optimized Components
 * 4. Loading States
 * 5. Enhanced Notification System
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Khanfashariya Improvements...\n');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

function runTest(testName, testFunction) {
  testResults.total++;
  try {
    const result = testFunction();
    if (result) {
      console.log(`✅ PASSED: ${testName}`);
      testResults.passed++;
      testResults.details.push({ name: testName, status: 'PASSED', error: null });
    } else {
      console.log(`❌ FAILED: ${testName}`);
      testResults.failed++;
      testResults.details.push({ name: testName, status: 'FAILED', error: 'Test returned false' });
    }
  } catch (error) {
    console.log(`❌ FAILED: ${testName} - ${error.message}`);
    testResults.failed++;
    testResults.details.push({ name: testName, status: 'FAILED', error: error.message });
  }
}

// Test 1: Enhanced Theme System
console.log('🎨 Testing Enhanced Theme System...');

runTest('Theme TypeScript File Exists', () => {
  return fs.existsSync(path.join(__dirname, '../src/styles/theme.ts'));
});

runTest('Theme Exports Correct Interfaces', () => {
  const themeContent = fs.readFileSync(path.join(__dirname, '../src/styles/theme.ts'), 'utf8');
  return themeContent.includes('ThemeColors') && 
         themeContent.includes('darkTheme') && 
         themeContent.includes('lightTheme') &&
         themeContent.includes('generateCSSVariables');
});

runTest('Design Tokens Updated', () => {
  const tokensContent = fs.readFileSync(path.join(__dirname, '../src/styles/designTokens.ts'), 'utf8');
  return tokensContent.includes('text-primary: \'#F8FAFC\'') &&
         tokensContent.includes('text-secondary: \'#CBD5E1\'');
});

runTest('CSS Variables Added', () => {
  const cssContent = fs.readFileSync(path.join(__dirname, '../src/index.css'), 'utf8');
  return cssContent.includes('--bg-primary: #0F172A') &&
         cssContent.includes('--text-primary: #F8FAFC') &&
         cssContent.includes('.bg-background-primary');
});

// Test 2: Error Boundary
console.log('\n🛡️ Testing Error Boundary...');

runTest('Error Boundary Component Exists', () => {
  return fs.existsSync(path.join(__dirname, '../src/components/ErrorBoundary.tsx'));
});

runTest('Error Boundary Has Required Methods', () => {
  const errorBoundaryContent = fs.readFileSync(path.join(__dirname, '../src/components/ErrorBoundary.tsx'), 'utf8');
  return errorBoundaryContent.includes('getDerivedStateFromError') &&
         errorBoundaryContent.includes('componentDidCatch') &&
         errorBoundaryContent.includes('withErrorBoundary') &&
         errorBoundaryContent.includes('useErrorHandler');
});

runTest('Error Boundary Integrated in App', () => {
  const appContent = fs.readFileSync(path.join(__dirname, '../src/App.tsx'), 'utf8');
  return appContent.includes('ErrorBoundary') &&
         appContent.includes('showDetails={process.env.NODE_ENV === \'development\'}');
});

// Test 3: Mobile Optimized Components
console.log('\n📱 Testing Mobile Optimized Components...');

runTest('Mobile Components File Exists', () => {
  return fs.existsSync(path.join(__dirname, '../src/components/ui/MobileOptimized.tsx'));
});

runTest('Mobile Components Export Correctly', () => {
  const mobileContent = fs.readFileSync(path.join(__dirname, '../src/components/ui/MobileOptimized.tsx'), 'utf8');
  return mobileContent.includes('TouchButton') &&
         mobileContent.includes('MobileNav') &&
         mobileContent.includes('MobileCard') &&
         mobileContent.includes('MobileInput') &&
         mobileContent.includes('useSwipeGesture');
});

runTest('Touch Targets Meet Accessibility Standards', () => {
  const mobileContent = fs.readFileSync(path.join(__dirname, '../src/components/ui/MobileOptimized.tsx'), 'utf8');
  return mobileContent.includes('min-h-[44px]') &&
         mobileContent.includes('min-h-[48px]') &&
         mobileContent.includes('min-w-[44px]');
});

// Test 4: Loading States
console.log('\n⏳ Testing Loading States...');

runTest('Loading States File Exists', () => {
  return fs.existsSync(path.join(__dirname, '../src/components/ui/LoadingStates.tsx'));
});

runTest('Loading Components Export Correctly', () => {
  const loadingContent = fs.readFileSync(path.join(__dirname, '../src/components/ui/LoadingStates.tsx'), 'utf8');
  return loadingContent.includes('LoadingSpinner') &&
         loadingContent.includes('LoadingWithText') &&
         loadingContent.includes('ProgressLoading') &&
         loadingContent.includes('Skeleton') &&
         loadingContent.includes('FullPageLoading') &&
         loadingContent.includes('useLoadingState');
});

runTest('Loading States Support Internationalization', () => {
  const loadingContent = fs.readFileSync(path.join(__dirname, '../src/components/ui/LoadingStates.tsx'), 'utf8');
  return loadingContent.includes('textEn') &&
         loadingContent.includes('جاري التحميل') &&
         loadingContent.includes('Loading...');
});

// Test 5: Enhanced Notification System
console.log('\n🔔 Testing Enhanced Notification System...');

runTest('Notification Hook Enhanced', () => {
  const notificationContent = fs.readFileSync(path.join(__dirname, '../src/hooks/useNotification.tsx'), 'utf8');
  return notificationContent.includes('NotificationAction') &&
         notificationContent.includes('addNotification') &&
         notificationContent.includes('removeNotification') &&
         notificationContent.includes('markAsRead') &&
         notificationContent.includes('unreadCount');
});

runTest('Notification Types Support', () => {
  const notificationContent = fs.readFileSync(path.join(__dirname, '../src/hooks/useNotification.tsx'), 'utf8');
  return notificationContent.includes('success') &&
         notificationContent.includes('error') &&
         notificationContent.includes('warning') &&
         notificationContent.includes('info');
});

runTest('Notification Persistence Features', () => {
  const notificationContent = fs.readFileSync(path.join(__dirname, '../src/hooks/useNotification.tsx'), 'utf8');
  return notificationContent.includes('persistent') &&
         notificationContent.includes('duration') &&
         notificationContent.includes('timestamp') &&
         notificationContent.includes('read');
});

// Test 6: App Integration
console.log('\n🔗 Testing App Integration...');

runTest('App Uses Enhanced Loading', () => {
  const appContent = fs.readFileSync(path.join(__dirname, '../src/App.tsx'), 'utf8');
  return appContent.includes('FullPageLoading') &&
         appContent.includes('جاري تحميل خان فشارية');
});

runTest('App Applies Theme', () => {
  const appContent = fs.readFileSync(path.join(__dirname, '../src/App.tsx'), 'utf8');
  return appContent.includes('applyTheme') &&
         appContent.includes('defaultTheme');
});

// Test 7: Build System
console.log('\n🏗️ Testing Build System...');

runTest('TypeScript Compilation', () => {
  // Check if there are any TypeScript errors in the new files
  const files = [
    '../src/styles/theme.ts',
    '../src/components/ErrorBoundary.tsx',
    '../src/components/ui/MobileOptimized.tsx',
    '../src/components/ui/LoadingStates.tsx'
  ];
  
  for (const file of files) {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      // Basic check for TypeScript syntax
      if (!content.includes('interface') && !content.includes('type') && !content.includes('React.FC')) {
        return false;
      }
    }
  }
  return true;
});

runTest('CSS Classes Available', () => {
  const cssContent = fs.readFileSync(path.join(__dirname, '../src/index.css'), 'utf8');
  return cssContent.includes('.touch-target') &&
         cssContent.includes('.focus-enhanced') &&
         cssContent.includes('.btn-enhanced');
});

// Test 8: Documentation
console.log('\n📚 Testing Documentation...');

runTest('Improvement Report Exists', () => {
  return fs.existsSync(path.join(__dirname, '../IMMEDIATE_IMPROVEMENTS_REPORT.md'));
});

runTest('Technical Analysis Report Exists', () => {
  return fs.existsSync(path.join(__dirname, '../TECHNICAL_ANALYSIS_REPORT.md'));
});

runTest('Comprehensive Review Exists', () => {
  return fs.existsSync(path.join(__dirname, '../COMPREHENSIVE_WEBSITE_REVIEW.md'));
});

// Final Results
console.log('\n📊 Test Results Summary:');
console.log(`✅ Passed: ${testResults.passed}`);
console.log(`❌ Failed: ${testResults.failed}`);
console.log(`📊 Total: ${testResults.total}`);
console.log(`🎯 Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);

if (testResults.failed > 0) {
  console.log('\n❌ Failed Tests:');
  testResults.details
    .filter(test => test.status === 'FAILED')
    .forEach(test => {
      console.log(`   • ${test.name}: ${test.error}`);
    });
}

console.log('\n🎉 Improvement Testing Completed!');

// Exit with appropriate code
process.exit(testResults.failed > 0 ? 1 : 0);
