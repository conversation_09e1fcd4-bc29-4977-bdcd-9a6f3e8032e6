#!/usr/bin/env node

const mysql = require('mysql2/promise');

async function checkTechnicalServicesSchema() {
  console.log('🔍 Checking technical_services table schema...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '123456',
      database: 'khan<PERSON><PERSON><PERSON>_db'
    });

    console.log('✅ Connected to MySQL database\n');

    // Check table structure
    console.log('1️⃣ Checking table structure...');
    const [columns] = await connection.execute('DESCRIBE technical_services');
    
    console.log('Current columns:');
    columns.forEach(col => {
      console.log(`   ${col.Field} - ${col.Type} - ${col.Null} - ${col.Key} - ${col.Default}`);
    });

    // Check if 'type' column exists
    const hasTypeColumn = columns.some(col => col.Field === 'type');
    console.log(`\n'type' column exists: ${hasTypeColumn ? '✅ Yes' : '❌ No'}`);

    if (!hasTypeColumn) {
      console.log('\n2️⃣ Adding missing type column...');
      await connection.execute(`
        ALTER TABLE technical_services 
        ADD COLUMN type VARCHAR(50) DEFAULT 'service' AFTER category
      `);
      console.log('✅ Added type column');

      // Update existing records
      await connection.execute(`
        UPDATE technical_services 
        SET type = 'service' 
        WHERE type IS NULL OR type = ''
      `);
      console.log('✅ Updated existing records with default type');
    }

    // Check system_services table for comparison
    console.log('\n3️⃣ Checking system_services table for comparison...');
    const [systemColumns] = await connection.execute('DESCRIBE system_services');
    
    console.log('system_services columns:');
    systemColumns.forEach(col => {
      console.log(`   ${col.Field} - ${col.Type}`);
    });

    const systemHasType = systemColumns.some(col => col.Field === 'type');
    console.log(`\nsystem_services has 'type' column: ${systemHasType ? '✅ Yes' : '❌ No'}`);

    if (!systemHasType) {
      console.log('\n4️⃣ Adding type column to system_services...');
      await connection.execute(`
        ALTER TABLE system_services 
        ADD COLUMN type VARCHAR(50) DEFAULT 'system' AFTER category
      `);
      console.log('✅ Added type column to system_services');

      await connection.execute(`
        UPDATE system_services 
        SET type = 'system' 
        WHERE type IS NULL OR type = ''
      `);
      console.log('✅ Updated system_services records');
    }

    // Test the fix
    console.log('\n5️⃣ Testing the fix...');
    const [testServices] = await connection.execute(`
      SELECT id, name_ar, category, type, status 
      FROM technical_services 
      LIMIT 3
    `);
    
    console.log('Technical services test:');
    testServices.forEach(service => {
      console.log(`   ${service.id}: ${service.name_ar} - ${service.category} - ${service.type} - ${service.status}`);
    });

    const [testSystems] = await connection.execute(`
      SELECT id, name_ar, category, type, status 
      FROM system_services 
      LIMIT 3
    `);
    
    console.log('\nSystem services test:');
    testSystems.forEach(system => {
      console.log(`   ${system.id}: ${system.name_ar} - ${system.category} - ${system.type} - ${system.status}`);
    });

    await connection.end();

    console.log('\n✅ Schema check and fixes completed!');
    console.log('\n🔄 Next steps:');
    console.log('1. Test admin API endpoints');
    console.log('2. Verify frontend data display');
    console.log('3. Check all CRUD operations');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkTechnicalServicesSchema();
