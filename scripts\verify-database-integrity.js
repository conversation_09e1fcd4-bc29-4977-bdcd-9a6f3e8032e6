#!/usr/bin/env node

/**
 * Database Integrity Verification Script
 * 
 * Verifies that all database records have proper Arabic and English fields
 */

const mysql = require('mysql2/promise');

const DB_CONFIG = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'khan<PERSON><PERSON>riya_db',
  charset: 'utf8mb4'
};

async function verifyDatabaseIntegrity() {
  console.log('🔍 Database Integrity Verification');
  console.log('==================================\n');
  
  let connection;
  
  try {
    connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ Connected to MySQL database\n');
    
    // Check system_services table
    console.log('📊 Checking system_services table...');
    const [systems] = await connection.execute('SELECT * FROM system_services LIMIT 5');
    
    let systemsValid = true;
    systems.forEach((system, index) => {
      const issues = [];
      
      if (!system.name_ar) issues.push('Missing name_ar');
      if (!system.name_en) issues.push('Missing name_en');
      if (!system.description_ar) issues.push('Missing description_ar');
      if (!system.description_en) issues.push('Missing description_en');
      
      // Check JSON fields
      try {
        if (system.features_ar) {
          const features = JSON.parse(system.features_ar);
          if (!Array.isArray(features)) issues.push('features_ar is not an array');
        } else {
          issues.push('Missing features_ar');
        }
      } catch (e) {
        issues.push('Invalid features_ar JSON');
      }
      
      try {
        if (system.features_en) {
          const features = JSON.parse(system.features_en);
          if (!Array.isArray(features)) issues.push('features_en is not an array');
        } else {
          issues.push('Missing features_en');
        }
      } catch (e) {
        issues.push('Invalid features_en JSON');
      }
      
      if (issues.length > 0) {
        console.log(`   ❌ System ${index + 1} (${system.id}): ${issues.join(', ')}`);
        systemsValid = false;
      } else {
        console.log(`   ✅ System ${index + 1} (${system.id}): Valid`);
      }
    });
    
    if (systemsValid) {
      console.log('✅ All systems have valid structure\n');
    } else {
      console.log('❌ Some systems have issues\n');
    }
    
    // Check technical_services table
    console.log('📊 Checking technical_services table...');
    const [services] = await connection.execute('SELECT * FROM technical_services LIMIT 5');
    
    let servicesValid = true;
    services.forEach((service, index) => {
      const issues = [];
      
      if (!service.name_ar) issues.push('Missing name_ar');
      if (!service.name_en) issues.push('Missing name_en');
      if (!service.description_ar) issues.push('Missing description_ar');
      if (!service.description_en) issues.push('Missing description_en');
      
      // Check JSON fields
      try {
        if (service.features_ar) {
          const features = JSON.parse(service.features_ar);
          if (!Array.isArray(features)) issues.push('features_ar is not an array');
        }
      } catch (e) {
        issues.push('Invalid features_ar JSON');
      }
      
      try {
        if (service.features_en) {
          const features = JSON.parse(service.features_en);
          if (!Array.isArray(features)) issues.push('features_en is not an array');
        }
      } catch (e) {
        issues.push('Invalid features_en JSON');
      }
      
      if (issues.length > 0) {
        console.log(`   ❌ Service ${index + 1} (${service.id}): ${issues.join(', ')}`);
        servicesValid = false;
      } else {
        console.log(`   ✅ Service ${index + 1} (${service.id}): Valid`);
      }
    });
    
    if (servicesValid) {
      console.log('✅ All services have valid structure\n');
    } else {
      console.log('❌ Some services have issues\n');
    }
    
    // Check premium_content table
    console.log('📊 Checking premium_content table...');
    const [premiumContent] = await connection.execute('SELECT * FROM premium_content LIMIT 3');
    
    let premiumValid = true;
    premiumContent.forEach((content, index) => {
      const issues = [];
      
      if (!content.name_ar) issues.push('Missing name_ar');
      if (!content.name_en) issues.push('Missing name_en');
      if (!content.description_ar) issues.push('Missing description_ar');
      if (!content.description_en) issues.push('Missing description_en');
      
      if (issues.length > 0) {
        console.log(`   ❌ Premium ${index + 1} (${content.id}): ${issues.join(', ')}`);
        premiumValid = false;
      } else {
        console.log(`   ✅ Premium ${index + 1} (${content.id}): Valid`);
      }
    });
    
    if (premiumValid) {
      console.log('✅ All premium content has valid structure\n');
    } else {
      console.log('❌ Some premium content has issues\n');
    }
    
    // Summary
    console.log('📋 Summary');
    console.log('=========');
    console.log(`Systems Table: ${systemsValid ? '✅ Valid' : '❌ Has Issues'}`);
    console.log(`Services Table: ${servicesValid ? '✅ Valid' : '❌ Has Issues'}`);
    console.log(`Premium Table: ${premiumValid ? '✅ Valid' : '❌ Has Issues'}`);
    
    const allValid = systemsValid && servicesValid && premiumValid;
    console.log(`\n🎯 Overall Status: ${allValid ? '✅ Database is properly structured' : '❌ Database needs fixes'}`);
    
    if (allValid) {
      console.log('\n🎉 Database integrity verified successfully!');
      console.log('✅ All tables have proper Arabic/English fields');
      console.log('✅ All JSON fields are properly formatted');
      console.log('✅ Ready for production use');
    } else {
      console.log('\n⚠️ Database integrity issues found!');
      console.log('🔧 Run the fix-json-data.js script to resolve issues');
    }
    
    return allValid;
    
  } catch (error) {
    console.error('❌ Database verification failed:', error.message);
    return false;
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run if called directly
if (require.main === module) {
  verifyDatabaseIntegrity().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Verification error:', error);
    process.exit(1);
  });
}

module.exports = { verifyDatabaseIntegrity };
