import React from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { Zap, Facebook, Twitter, Instagram, Github, Mail, Shield } from 'lucide-react';

const Footer: React.FC = () => {
  const { t } = useTranslation();

  const socialLinks = [
    { icon: Facebook, href: '#', label: 'Facebook' },
    { icon: Twitter, href: '#', label: 'Twitter' },
    { icon: Instagram, href: '#', label: 'Instagram' },
    { icon: Github, href: '#', label: 'GitHub' },
    { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email' }
  ];

  return (
    <footer className="bg-gradient-to-br from-primary to-background border-t border-accent/20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-3 rtl:space-x-reverse mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-secondary to-accent rounded-lg flex items-center justify-center shadow-lg">
                <Zap className="w-7 h-7 text-primary" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">{t('site.name')}</h3>
                <p className="text-sm text-accent">{t('site.tagline')}</p>
              </div>
            </div>
            <p className="text-gray-300 mb-6 max-w-md leading-relaxed">
              {t('hero.subtitle')}
            </p>
            
            {/* Social Links - Fixed positioning for RTL */}
            <div className="social-icons-container">
              {socialLinks.map((social, index) => {
                const Icon = social.icon;
                return (
                  <a
                    key={index}
                    href={social.href}
                    className="social-icon bg-primary/50 hover:bg-secondary/20 border border-accent/20 hover:border-secondary/40 rounded-lg transition-all duration-300 hover:scale-110 group"
                    aria-label={social.label}
                  >
                    <Icon className="w-5 h-5 text-gray-400 group-hover:text-secondary transition-colors duration-300" />
                  </a>
                );
              })}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4 text-white flex items-center space-x-2 rtl:space-x-reverse">
              <Shield className="w-5 h-5 text-secondary" />
              <span>{t('language') === 'ar' ? 'روابط سريعة' : 'Quick Links'}</span>
            </h4>
            <ul className="space-y-2">
              {[
                { key: 'nav.home', href: '#home' },
                { key: 'nav.systems', href: '#systems' },
                { key: 'nav.services', href: '#services' },
                { key: 'nav.contact', href: '#contact' }
              ].map((link) => (
                <li key={link.key}>
                  <a
                    href={link.href}
                    className="text-gray-400 hover:text-accent transition-colors duration-200 flex items-center space-x-2 rtl:space-x-reverse group"
                  >
                    <div className="w-1 h-1 bg-accent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                    <span>{t(link.key)}</span>
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h4 className="text-lg font-semibold mb-4 text-white flex items-center space-x-2 rtl:space-x-reverse">
              <Zap className="w-5 h-5 text-accent" />
              <span>{t('language') === 'ar' ? 'قانوني' : 'Legal'}</span>
            </h4>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-gray-400 hover:text-accent transition-colors duration-200 flex items-center space-x-2 rtl:space-x-reverse group">
                  <div className="w-1 h-1 bg-accent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                  <span>{t('footer.privacy')}</span>
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-accent transition-colors duration-200 flex items-center space-x-2 rtl:space-x-reverse group">
                  <div className="w-1 h-1 bg-accent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                  <span>{t('footer.terms')}</span>
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-accent/20 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © 2024 {t('site.name')}. {t('footer.rights')}.
          </p>
          <p className="text-gray-400 text-sm mt-2 md:mt-0 flex items-center space-x-2 rtl:space-x-reverse">
            <span>{t('footer.madeWith')}</span>
            <Zap className="w-4 h-4 text-secondary" />
            <span>{t('footer.forDevelopers')}</span>
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;