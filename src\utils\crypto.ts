/**
 * Browser-safe crypto utilities
 * Replaces bcryptjs for client-side use
 */

/**
 * Simple hash function for client-side password hashing
 * Note: This is NOT secure for production use - only for demo purposes
 * In production, password hashing should be done server-side
 */
export const hashPassword = async (password: string): Promise<string> => {
  // Use Web Crypto API if available
  if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
    try {
      const encoder = new TextEncoder();
      const data = encoder.encode(password + 'khanfashariya_salt'); // Add salt
      const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      return hashHex;
    } catch (error) {
      console.warn('Web Crypto API failed, using fallback hash');
    }
  }
  
  // Fallback simple hash (NOT secure - for demo only)
  let hash = 0;
  const str = password + 'khanfashariya_salt';
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(16);
};

/**
 * Compare password with hash
 * Note: This is NOT secure for production use - only for demo purposes
 */
export const comparePassword = async (password: string, hash: string): Promise<boolean> => {
  const passwordHash = await hashPassword(password);
  return passwordHash === hash;
};

/**
 * Generate a simple random ID
 */
export const generateId = (): string => {
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    const array = new Uint32Array(1);
    window.crypto.getRandomValues(array);
    return array[0].toString(36);
  }
  
  // Fallback
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

/**
 * Generate a random salt
 */
export const generateSalt = (): string => {
  return generateId() + generateId();
};
