# 🚀 التشغيل السريع لـ TestSprite

## الطريقة الأسرع (تلقائية):

### 1. تشغيل السكريبت التلقائي:
```bash
npm run testsprite:auto
```

هذا السكريبت سيقوم بـ:
- ✅ فحص وتثبيت ngrok
- ✅ تشغيل المشروع (باك اند + فرونت اند)
- ✅ إنشاء أنفاق ngrok
- ✅ إعطاؤك البيانات الجاهزة لـ TestSprite

### 2. انسخ البيانات من الشاشة مباشرة إلى TestSprite

---

## الطريقة اليدوية:

### 1. تثبيت ngrok:
```bash
npm install -g ngrok
```

### 2. تشغيل المشروع:
```bash
npm run dev:full
```

### 3. في terminals منفصلة:
```bash
# Terminal 1
ngrok http 3001

# Terminal 2  
ngrok http 5173
```

### 4. انسخ الروابط إلى TestSprite

---

## 📋 البيانات المطلوبة لـ TestSprite:

### للباك اند API:
- **API Name:** Khanfashariya Backend API
- **API Endpoint:** [رابط ngrok للمنفذ 3001]
- **Authentication:** None

### للفرونت اند:
- **Website Name:** Khanfashariya Frontend  
- **Website URL:** [رابط ngrok للمنفذ 5173]

### بيانات الاختبار:
- **Email:** <EMAIL>
- **Password:** admin123

---

## 🧪 ما سيختبره TestSprite:

### الباك اند:
- ✅ API endpoints
- ✅ المصادقة والتسجيل
- ✅ قاعدة البيانات
- ✅ الأمان والأداء
- ✅ معالجة الأخطاء

### الفرونت اند:
- ✅ واجهة المستخدم
- ✅ التنقل والروابط
- ✅ النماذج والتفاعل
- ✅ الاستجابة للشاشات
- ✅ الأداء والتحميل

### التكامل:
- ✅ التواصل بين الباك اند والفرونت اند
- ✅ تدفق البيانات
- ✅ المصادقة الشاملة
- ✅ تجربة المستخدم الكاملة

---

## ⚠️ ملاحظات مهمة:

1. **لا تغلق terminals أثناء الاختبار**
2. **تأكد من عمل قاعدة البيانات (WAMP/XAMPP)**
3. **الروابط تتغير كل مرة تعيد تشغيل ngrok**
4. **استخدم بيانات الاختبار المذكورة**
5. **راقب logs للتأكد من عمل كل شيء**