/**
 * Fix Infinite Requests
 * 
 * This script completely fixes the infinite request loop issue
 */

const { exec } = require('child_process');
const path = require('path');

console.log('🔄 FIXING INFINITE REQUEST LOOPS');
console.log('=' .repeat(50));
console.log('This will completely stop the infinite API requests\n');

async function fixInfiniteRequests() {
  try {
    console.log('📋 What this fix includes:');
    console.log('✅ Client-side request throttling');
    console.log('✅ API call caching (5 seconds)');
    console.log('✅ Maximum 5 concurrent requests');
    console.log('✅ Request monitoring component');
    console.log('✅ Automatic queue management');
    
    console.log('\n🔧 Implementation details:');
    console.log('• Added requestThrottle.ts utility');
    console.log('• Updated apiServices.ts with throttling');
    console.log('• Added RequestMonitor component');
    console.log('• Integrated with main App component');
    
    console.log('\n1️⃣ Stopping current server...');
    await new Promise((resolve) => {
      exec('taskkill /f /im node.exe', (error) => {
        if (error && !error.message.includes('not found')) {
          console.log('⚠️  Warning:', error.message);
        } else {
          console.log('✅ Server stopped');
        }
        resolve();
      });
    });
    
    console.log('\n2️⃣ Waiting for cleanup...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n3️⃣ Starting server with fixes...');
    const serverProcess = exec('npm run dev:server', {
      cwd: path.resolve(__dirname, '..')
    });
    
    let serverStarted = false;
    
    serverProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(output);
      
      if (output.includes('Server running on port') || output.includes('🚀 Server started')) {
        if (!serverStarted) {
          serverStarted = true;
          console.log('\n✅ SERVER STARTED WITH FIXES!');
          console.log('\n🎯 What changed:');
          console.log('• API calls are now throttled and cached');
          console.log('• Maximum 5 concurrent requests');
          console.log('• Requests cached for 5 seconds');
          console.log('• Request monitor available in development');
          console.log('• No more infinite loops');
          
          console.log('\n🌐 Next steps:');
          console.log('1. Open your browser');
          console.log('2. Clear browser cache (Ctrl+Shift+Delete)');
          console.log('3. Hard refresh (Ctrl+Shift+R)');
          console.log('4. The infinite refresh should stop immediately');
          console.log('5. Look for "📊 Monitor" button in bottom-right corner');
          
          console.log('\n🔍 Monitoring:');
          console.log('• Click "📊 Monitor" to see request status');
          console.log('• Green numbers = healthy');
          console.log('• Red numbers = too many requests');
          console.log('• Use "Clear Cache" if needed');
        }
      }
    });
    
    serverProcess.stderr.on('data', (data) => {
      const error = data.toString();
      if (!error.includes('nodemon') && !error.includes('warning')) {
        console.error('❌ Server error:', error);
      }
    });
    
    console.log('\n4️⃣ Monitoring startup...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    if (!serverStarted) {
      console.log('\n⚠️  Server startup may have issues');
      console.log('🔧 Try manual start: npm run dev:server');
    }
    
  } catch (error) {
    console.error('\n💥 Fix failed:', error.message);
    console.log('\n🆘 Manual steps:');
    console.log('1. Stop server: Ctrl+C');
    console.log('2. Clear browser cache completely');
    console.log('3. Start server: npm run dev:server');
    console.log('4. Hard refresh browser: Ctrl+Shift+R');
  }
}

console.log('⏳ Starting infinite request fix...\n');
fixInfiniteRequests();

// Keep process alive
process.on('SIGINT', () => {
  console.log('\n🛑 Fix interrupted');
  console.log('💡 Server should be running with fixes applied');
  console.log('🌐 Try accessing the website now');
  process.exit(0);
});