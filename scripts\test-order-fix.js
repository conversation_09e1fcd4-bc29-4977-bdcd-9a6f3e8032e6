/**
 * Test Order Creation Fix
 * Tests the fixed order creation functionality
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';
const TEST_USER_CREDS = { email: '<EMAIL>', password: '123456' };

async function testOrderCreation() {
  console.log('🔧 Testing Order Creation Fix...');
  
  try {
    // Login first
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, TEST_USER_CREDS);
    
    if (!loginResponse.data.success) {
      console.log('❌ Login failed');
      return;
    }
    
    const token = loginResponse.data.data.tokens.accessToken;
    const headers = { Authorization: `Bearer ${token}` };
    
    // Get available systems
    const systemsResponse = await axios.get(`${API_BASE}/systems`);
    
    if (!systemsResponse.data.success || !systemsResponse.data.data.systems.length) {
      console.log('❌ No systems available');
      return;
    }
    
    const testSystem = systemsResponse.data.data.systems[0];
    console.log(`📦 Testing with system: ${testSystem.name_en} (ID: ${testSystem.id})`);
    
    // Test regular order creation
    const orderData = {
      order_type: 'system_service',
      item_id: testSystem.id,
      quantity: 1,
      notes_ar: 'طلب تجريبي للاختبار',
      notes_en: 'Test order for testing'
    };
    
    console.log('📤 Sending order data:', orderData);
    
    const orderResponse = await axios.post(`${API_BASE}/orders`, orderData, { headers });
    
    if (orderResponse.data.success) {
      console.log('✅ Regular order created successfully!');
      console.log(`📋 Order Number: ${orderResponse.data.data.order.order_number}`);
      console.log(`💰 Price: $${orderResponse.data.data.order.final_price}`);
    } else {
      console.log('❌ Regular order creation failed:', orderResponse.data);
    }
    
    // Test custom order creation
    const customOrderData = {
      order_type: 'system_service',
      item_id: 'custom-order-' + Date.now(),
      quantity: 1,
      notes_ar: 'طلب نظام مخصص للاختبار',
      notes_en: 'Custom system request for testing'
    };
    
    console.log('\n📤 Sending custom order data:', customOrderData);
    
    const customOrderResponse = await axios.post(`${API_BASE}/orders`, customOrderData, { headers });
    
    if (customOrderResponse.data.success) {
      console.log('✅ Custom order created successfully!');
      console.log(`📋 Order Number: ${customOrderResponse.data.data.order.order_number}`);
      console.log(`💰 Price: $${customOrderResponse.data.data.order.final_price}`);
    } else {
      console.log('❌ Custom order creation failed:', customOrderResponse.data);
    }
    
    // Test technical service order
    const servicesResponse = await axios.get(`${API_BASE}/services/technical`);
    
    if (servicesResponse.data.success && servicesResponse.data.data.length > 0) {
      const testService = servicesResponse.data.data[0];
      
      const serviceOrderData = {
        order_type: 'technical_service',
        item_id: testService.id,
        quantity: 1,
        notes_ar: 'طلب خدمة تقنية للاختبار',
        notes_en: 'Technical service order for testing'
      };
      
      console.log('\n📤 Sending service order data:', serviceOrderData);
      
      const serviceOrderResponse = await axios.post(`${API_BASE}/orders`, serviceOrderData, { headers });
      
      if (serviceOrderResponse.data.success) {
        console.log('✅ Service order created successfully!');
        console.log(`📋 Order Number: ${serviceOrderResponse.data.data.order.order_number}`);
        console.log(`💰 Price: $${serviceOrderResponse.data.data.order.final_price}`);
      } else {
        console.log('❌ Service order creation failed:', serviceOrderResponse.data);
      }
    }
    
    console.log('\n🎉 Order creation testing completed!');
    
  } catch (error) {
    console.log('❌ Error during testing:', error.response?.data || error.message);
  }
}

testOrderCreation();
