/**
 * Admin Management Routes
 * 
 * Handles admin operations:
 * - Dashboard statistics
 * - User management
 * - Content management
 * - System monitoring
 * - Reports and analytics
 */

const express = require('express');
const { executeQuery, generateUUID, getDatabaseStats, checkDatabaseHealth } = require('../config/database');
const { verifyToken, requireAdmin } = require('../middleware/auth');
const { 
  asyncHandler, 
  validationError, 
  notFoundError 
} = require('../middleware/errorHandler');
const { logUserAction, cleanupOldLogs } = require('../middleware/logger');

const router = express.Router();

// Safe JSON parsing utility
function safeJsonParse(jsonData, defaultValue = null) {
  // If it's already an object/array, return it directly
  if (typeof jsonData === 'object' && jsonData !== null) {
    return jsonData;
  }

  // If it's not a string, return default value
  if (!jsonData || typeof jsonData !== 'string') {
    return defaultValue;
  }

  try {
    return JSON.parse(jsonData);
  } catch (error) {
    console.warn('JSON parse error:', error.message, 'for string:', jsonData.substring(0, 100));
    return defaultValue;
  }
}

// Apply admin authentication to all routes
router.use(verifyToken);
router.use(requireAdmin);

/**
 * @route   GET /api/admin/dashboard
 * @desc    Get admin dashboard statistics
 * @access  Private (admin only)
 */
router.get('/dashboard', asyncHandler(async (req, res) => {
  // Get comprehensive dashboard statistics
  const [
    { rows: userStats },
    { rows: orderStats },
    { rows: serviceStats },
    { rows: revenueStats },
    { rows: recentOrders },
    { rows: recentUsers },
    { rows: systemHealth }
  ] = await Promise.all([
    // User statistics
    executeQuery(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
        COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_30d,
        COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_users_7d
      FROM users
    `),
    
    // Order statistics
    executeQuery(`
      SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as orders_30d,
        AVG(final_price) as avg_order_value
      FROM orders
    `),
    
    // Service statistics
    executeQuery(`
      SELECT
        (SELECT COUNT(*) FROM system_services WHERE status = 'active') as active_systems,
        (SELECT COUNT(*) FROM technical_services WHERE status = 'active') as active_technical,
        (SELECT COUNT(*) FROM premium_content WHERE status = 'active') as active_premium,
        (SELECT COUNT(*) FROM premium_content WHERE status = 'active' AND featured = 1) as active_packages
    `),
    
    // Revenue statistics
    executeQuery(`
      SELECT 
        COALESCE(SUM(final_price), 0) as total_revenue,
        COALESCE(SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN final_price END), 0) as revenue_30d,
        COALESCE(SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN final_price END), 0) as revenue_7d,
        COALESCE(SUM(CASE WHEN payment_status = 'paid' THEN final_price END), 0) as paid_revenue
      FROM orders
      WHERE status != 'cancelled'
    `),
    
    // Recent orders
    executeQuery(`
      SELECT o.*, u.username, u.email
      FROM orders o
      JOIN users u ON o.user_id = u.id
      ORDER BY o.created_at DESC
      LIMIT 10
    `),
    
    // Recent users
    executeQuery(`
      SELECT id, username, email, full_name, role, status, created_at, last_login
      FROM users
      ORDER BY created_at DESC
      LIMIT 10
    `),
    
    // System health check
    executeQuery(`
      SELECT 
        (SELECT COUNT(*) FROM activity_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)) as recent_activities,
        (SELECT COUNT(*) FROM user_sessions WHERE is_active = true) as active_sessions,
        (SELECT COUNT(*) FROM inbox_messages WHERE is_read = false) as unread_messages
    `)
  ]);
  
  // Get database statistics
  const dbStats = await getDatabaseStats();
  const dbHealth = await checkDatabaseHealth();
  
  res.json({
    success: true,
    data: {
      stats: {
        users: userStats[0],
        orders: orderStats[0],
        services: serviceStats[0],
        revenue: revenueStats[0],
        system: systemHealth[0]
      },
      recent: {
        orders: recentOrders,
        users: recentUsers
      },
      database: {
        stats: dbStats,
        health: dbHealth
      }
    }
  });
}));

/**
 * @route   GET /api/admin/users
 * @desc    Get all users with filtering and pagination
 * @access  Private (admin only)
 */
router.get('/users', asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    role, 
    status, 
    search,
    sort = 'created_at',
    order = 'DESC'
  } = req.query;
  
  const offset = (page - 1) * limit;
  
  // Build query conditions
  let whereConditions = [];
  let queryParams = [];
  
  if (role) {
    whereConditions.push('role = ?');
    queryParams.push(role);
  }
  
  if (status) {
    whereConditions.push('status = ?');
    queryParams.push(status);
  }
  
  if (search) {
    whereConditions.push('(username LIKE ? OR email LIKE ? OR full_name LIKE ?)');
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm, searchTerm);
  }
  
  const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
  
  // Validate sort field
  const allowedSortFields = ['created_at', 'updated_at', 'username', 'email', 'full_name', 'last_login', 'login_count'];
  const sortField = allowedSortFields.includes(sort) ? sort : 'created_at';
  const sortOrder = order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
  
  // Get users
  const { rows: users } = await executeQuery(`
    SELECT 
      id, email, username, full_name, role, status, phone, avatar_url,
      email_verified, last_login, login_count, created_at, updated_at
    FROM users
    ${whereClause}
    ORDER BY ${sortField} ${sortOrder}
    LIMIT ? OFFSET ?
  `, [...queryParams, parseInt(limit), offset]);
  
  // Get total count
  const { rows: countResult } = await executeQuery(`
    SELECT COUNT(*) as total
    FROM users
    ${whereClause}
  `, queryParams);
  
  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.json({
    success: true,
    data: {
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @route   PUT /api/admin/users/:id/status
 * @desc    Update user status
 * @access  Private (admin only)
 */
router.put('/users/:id/status', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;
  
  // Validation
  const validStatuses = ['active', 'inactive', 'suspended'];
  if (!validStatuses.includes(status)) {
    throw validationError('Invalid status');
  }
  
  // Check if user exists
  const { rows: users } = await executeQuery(
    'SELECT id, username, email, status FROM users WHERE id = ?',
    [id]
  );
  
  if (users.length === 0) {
    throw notFoundError('User not found');
  }
  
  const user = users[0];
  const oldStatus = user.status;
  
  // Update user status
  await executeQuery(
    'UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?',
    [status, id]
  );
  
  // Log admin action
  await logUserAction('user_status_updated', 'user', id, {
    targetUser: user.username,
    oldStatus,
    newStatus: status
  }, req);
  
  res.json({
    success: true,
    message: 'User status updated successfully',
    data: {
      userId: id,
      oldStatus,
      newStatus: status
    }
  });
}));

/**
 * @route   GET /api/admin/orders
 * @desc    Get all orders with filtering and pagination
 * @access  Private (admin only)
 */
router.get('/orders', verifyToken, requireAdmin, asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    status, 
    order_type,
    payment_status,
    search,
    sort = 'created_at',
    order = 'DESC'
  } = req.query;
  
  const offset = (page - 1) * limit;
  
  // Build query conditions
  let whereConditions = [];
  let queryParams = [];
  
  if (status) {
    whereConditions.push('o.status = ?');
    queryParams.push(status);
  }
  
  if (order_type) {
    whereConditions.push('o.order_type = ?');
    queryParams.push(order_type);
  }
  
  if (payment_status) {
    whereConditions.push('o.payment_status = ?');
    queryParams.push(payment_status);
  }
  
  if (search) {
    whereConditions.push('(o.order_number LIKE ? OR u.username LIKE ? OR u.email LIKE ?)');
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm, searchTerm);
  }
  
  const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
  
  // Validate sort field
  const allowedSortFields = ['created_at', 'updated_at', 'order_number', 'final_price', 'status'];
  const sortField = allowedSortFields.includes(sort) ? `o.${sort}` : 'o.created_at';
  const sortOrder = order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
  
  // Get orders
  const { rows: orders } = await executeQuery(`
    SELECT 
      o.*, u.username, u.email, u.full_name
    FROM orders o
    JOIN users u ON o.user_id = u.id
    ${whereClause}
    ORDER BY ${sortField} ${sortOrder}
    LIMIT ? OFFSET ?
  `, [...queryParams, parseInt(limit), offset]);
  
  // Get total count
  const { rows: countResult } = await executeQuery(`
    SELECT COUNT(*) as total
    FROM orders o
    JOIN users u ON o.user_id = u.id
    ${whereClause}
  `, queryParams);
  
  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.json({
    success: true,
    data: {
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @route   GET /api/admin/analytics
 * @desc    Get detailed analytics data
 * @access  Private (admin only)
 */
router.get('/analytics', asyncHandler(async (req, res) => {
  const { period = '30d' } = req.query;
  
  // Determine date range
  let dateCondition;
  switch (period) {
    case '7d':
      dateCondition = 'DATE_SUB(NOW(), INTERVAL 7 DAY)';
      break;
    case '30d':
      dateCondition = 'DATE_SUB(NOW(), INTERVAL 30 DAY)';
      break;
    case '90d':
      dateCondition = 'DATE_SUB(NOW(), INTERVAL 90 DAY)';
      break;
    case '1y':
      dateCondition = 'DATE_SUB(NOW(), INTERVAL 1 YEAR)';
      break;
    default:
      dateCondition = 'DATE_SUB(NOW(), INTERVAL 30 DAY)';
  }
  
  // Get analytics data
  const [
    { rows: orderTrends },
    { rows: revenueTrends },
    { rows: topServices },
    { rows: userGrowth },
    { rows: categoryStats }
  ] = await Promise.all([
    // Order trends
    executeQuery(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as orders,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders
      FROM orders
      WHERE created_at >= ${dateCondition}
      GROUP BY DATE(created_at)
      ORDER BY date
    `),
    
    // Revenue trends
    executeQuery(`
      SELECT 
        DATE(created_at) as date,
        COALESCE(SUM(final_price), 0) as revenue,
        COALESCE(SUM(CASE WHEN payment_status = 'paid' THEN final_price END), 0) as paid_revenue
      FROM orders
      WHERE created_at >= ${dateCondition} AND status != 'cancelled'
      GROUP BY DATE(created_at)
      ORDER BY date
    `),
    
    // Top services
    executeQuery(`
      SELECT 
        item_name_en as service_name,
        order_type,
        COUNT(*) as order_count,
        SUM(final_price) as total_revenue
      FROM orders
      WHERE created_at >= ${dateCondition} AND status = 'completed'
      GROUP BY item_id, order_type, item_name_en
      ORDER BY order_count DESC
      LIMIT 10
    `),
    
    // User growth
    executeQuery(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as new_users
      FROM users
      WHERE created_at >= ${dateCondition}
      GROUP BY DATE(created_at)
      ORDER BY date
    `),
    
    // Category statistics
    executeQuery(`
      SELECT 
        'system_service' as type,
        category,
        COUNT(*) as total_items,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_items
      FROM system_services
      GROUP BY category
      UNION ALL
      SELECT 
        'technical_service' as type,
        category,
        COUNT(*) as total_items,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_items
      FROM technical_services
      GROUP BY category
    `)
  ]);
  
  res.json({
    success: true,
    data: {
      period,
      trends: {
        orders: orderTrends,
        revenue: revenueTrends,
        userGrowth
      },
      topServices,
      categoryStats
    }
  });
}));

/**
 * @route   GET /api/admin/systems
 * @desc    Get all system services for admin management
 * @access  Private (admin only)
 */
router.get('/systems', asyncHandler(async (req, res) => {
  const { rows: systems } = await executeQuery(`
    SELECT 
      id, name_ar, name_en, description_ar, description_en, price, category, type,
      features_ar, features_en, tech_specs_ar, tech_specs_en,
      video_url, image_url, gallery_images, status, featured,
      download_count, rating, rating_count, created_at, updated_at
    FROM system_services
    ORDER BY created_at DESC
  `);

  // Parse JSON fields for each system safely
  const parsedSystems = systems.map(system => ({
    ...system,
    features_ar: safeJsonParse(system.features_ar, []),
    features_en: safeJsonParse(system.features_en, []),
    tech_specs_ar: safeJsonParse(system.tech_specs_ar, []),
    tech_specs_en: safeJsonParse(system.tech_specs_en, []),
    gallery_images: safeJsonParse(system.gallery_images, [])
  }));

  res.json({
    success: true,
    data: parsedSystems
  });
}));

/**
 * @route   POST /api/admin/systems
 * @desc    Create new system service
 * @access  Private (admin only)
 */
router.post('/systems', asyncHandler(async (req, res) => {
  const {
    name_ar, name_en, description_ar, description_en, price, category, type = 'regular',
    features_ar = [], features_en = [], tech_specs_ar = [], tech_specs_en = [],
    video_url = '', image_url = '', gallery_images = [], status = 'active',
    is_premium_addon = false
  } = req.body;

  // Validation
  if (!name_ar || !name_en || !description_ar || !description_en || !price) {
    throw validationError('Missing required fields');
  }

  const systemId = generateUUID();

  await executeQuery(`
    INSERT INTO system_services (
      id, name_ar, name_en, description_ar, description_en, price, category, type,
      features_ar, features_en, tech_specs_ar, tech_specs_en,
      video_url, image_url, gallery_images, status, is_premium_addon, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
  `, [
    systemId, name_ar, name_en, description_ar, description_en, price, category, type,
    JSON.stringify(features_ar), JSON.stringify(features_en),
    JSON.stringify(tech_specs_ar), JSON.stringify(tech_specs_en),
    video_url, image_url, JSON.stringify(gallery_images), status, is_premium_addon
  ]);

  // Get the created system to return complete data
  const { rows: createdSystems } = await executeQuery(`
    SELECT
      id, name_ar, name_en, description_ar, description_en, price, category, type,
      features_ar, features_en, tech_specs_ar, tech_specs_en,
      video_url, image_url, gallery_images, status, featured, is_premium_addon,
      download_count, rating, rating_count, created_at, updated_at
    FROM system_services
    WHERE id = ?
  `, [systemId]);

  const createdSystem = createdSystems[0];

  // Parse JSON fields safely
  createdSystem.features_ar = safeJsonParse(createdSystem.features_ar, []);
  createdSystem.features_en = safeJsonParse(createdSystem.features_en, []);
  createdSystem.tech_specs_ar = safeJsonParse(createdSystem.tech_specs_ar, []);
  createdSystem.tech_specs_en = safeJsonParse(createdSystem.tech_specs_en, []);
  createdSystem.gallery_images = safeJsonParse(createdSystem.gallery_images, []);

  // Log admin action
  await logUserAction('system_created', 'system_service', systemId, {
    systemName: name_en,
    category,
    price
  }, req);

  res.json({
    success: true,
    message: 'System service created successfully',
    data: createdSystem
  });
}));

/**
 * @route   PUT /api/admin/systems/:id
 * @desc    Update system service
 * @access  Private (admin only)
 */
router.put('/systems/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    name_ar, name_en, description_ar, description_en, price, category, type,
    features_ar, features_en, tech_specs_ar, tech_specs_en,
    video_url, image_url, gallery_images, status
  } = req.body;

  // Check if system exists
  const { rows: existingSystems } = await executeQuery(
    'SELECT id, name_en FROM system_services WHERE id = ?',
    [id]
  );

  if (existingSystems.length === 0) {
    throw notFoundError('System service not found');
  }

  // Build update query dynamically
  const updateFields = [];
  const updateValues = [];

  // Handle all possible fields with proper validation
  if (name_ar !== undefined && name_ar !== null) { updateFields.push('name_ar = ?'); updateValues.push(name_ar); }
  if (name_en !== undefined && name_en !== null) { updateFields.push('name_en = ?'); updateValues.push(name_en); }
  if (description_ar !== undefined && description_ar !== null) { updateFields.push('description_ar = ?'); updateValues.push(description_ar); }
  if (description_en !== undefined && description_en !== null) { updateFields.push('description_en = ?'); updateValues.push(description_en); }
  if (price !== undefined && price !== null) { updateFields.push('price = ?'); updateValues.push(price); }
  if (category !== undefined && category !== null) { updateFields.push('category = ?'); updateValues.push(category); }
  if (type !== undefined && type !== null) { updateFields.push('type = ?'); updateValues.push(type); }

  // Handle boolean fields properly
  if (req.body.hasOwnProperty('is_premium_addon')) {
    updateFields.push('is_premium_addon = ?');
    updateValues.push(Boolean(req.body.is_premium_addon) ? 1 : 0);
  }

  // Handle JSON fields with proper validation
  if (features_ar !== undefined) {
    updateFields.push('features_ar = ?');
    updateValues.push(JSON.stringify(Array.isArray(features_ar) ? features_ar : []));
  }
  if (features_en !== undefined) {
    updateFields.push('features_en = ?');
    updateValues.push(JSON.stringify(Array.isArray(features_en) ? features_en : []));
  }
  if (tech_specs_ar !== undefined) {
    updateFields.push('tech_specs_ar = ?');
    updateValues.push(JSON.stringify(Array.isArray(tech_specs_ar) ? tech_specs_ar : []));
  }
  if (tech_specs_en !== undefined) {
    updateFields.push('tech_specs_en = ?');
    updateValues.push(JSON.stringify(Array.isArray(tech_specs_en) ? tech_specs_en : []));
  }
  if (gallery_images !== undefined) {
    updateFields.push('gallery_images = ?');
    updateValues.push(JSON.stringify(Array.isArray(gallery_images) ? gallery_images : []));
  }

  // Handle optional fields
  if (video_url !== undefined) { updateFields.push('video_url = ?'); updateValues.push(video_url || ''); }
  if (image_url !== undefined) { updateFields.push('image_url = ?'); updateValues.push(image_url || ''); }
  if (status !== undefined && status !== null) { updateFields.push('status = ?'); updateValues.push(status); }

  if (updateFields.length === 0) {
    throw validationError('No fields to update');
  }

  updateFields.push('updated_at = NOW()');
  updateValues.push(id);

  await executeQuery(`
    UPDATE system_services
    SET ${updateFields.join(', ')}
    WHERE id = ?
  `, updateValues);

  // Get the updated system to return complete data
  const { rows: updatedSystems } = await executeQuery(`
    SELECT
      id, name_ar, name_en, description_ar, description_en, price, category, type,
      features_ar, features_en, tech_specs_ar, tech_specs_en,
      video_url, image_url, gallery_images, status, featured,
      is_premium_addon, download_count, rating, rating_count, created_at, updated_at
    FROM system_services
    WHERE id = ?
  `, [id]);

  const updatedSystem = updatedSystems[0];

  // Parse JSON fields safely
  updatedSystem.features_ar = safeJsonParse(updatedSystem.features_ar, []);
  updatedSystem.features_en = safeJsonParse(updatedSystem.features_en, []);
  updatedSystem.tech_specs_ar = safeJsonParse(updatedSystem.tech_specs_ar, []);
  updatedSystem.tech_specs_en = safeJsonParse(updatedSystem.tech_specs_en, []);
  updatedSystem.gallery_images = safeJsonParse(updatedSystem.gallery_images, []);

  // Log admin action
  await logUserAction('system_updated', 'system_service', id, {
    systemName: name_en || existingSystems[0].name_en,
    updatedFields: updateFields.length - 1
  }, req);

  res.json({
    success: true,
    message: 'System service updated successfully',
    data: updatedSystem
  });
}));

/**
 * @route   DELETE /api/admin/systems/:id
 * @desc    Delete system service
 * @access  Private (admin only)
 */
router.delete('/systems/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Check if system exists
  const { rows: existingSystems } = await executeQuery(
    'SELECT id, name_en FROM system_services WHERE id = ?',
    [id]
  );

  if (existingSystems.length === 0) {
    throw notFoundError('System service not found');
  }

  // Check if system is being used in orders
  const { rows: systemOrders } = await executeQuery(
    'SELECT COUNT(*) as count FROM orders WHERE item_id = ? AND order_type = ?',
    [id, 'system_service']
  );

  if (systemOrders[0].count > 0) {
    // Don't delete, just deactivate
    await executeQuery(
      'UPDATE system_services SET status = ?, updated_at = NOW() WHERE id = ?',
      ['inactive', id]
    );

    await logUserAction('system_deactivated', 'system_service', id, {
      systemName: existingSystems[0].name_en,
      reason: 'Has existing orders'
    }, req);

    res.json({
      success: true,
      message: 'System service deactivated (has existing orders)'
    });
  } else {
    // Safe to delete
    await executeQuery('DELETE FROM system_services WHERE id = ?', [id]);

    await logUserAction('system_deleted', 'system_service', id, {
      systemName: existingSystems[0].name_en
    }, req);

    res.json({
      success: true,
      message: 'System service deleted successfully'
    });
  }
}));

/**
 * @route   GET /api/admin/technical-services
 * @desc    Get all technical services for admin management
 * @access  Private (admin only)
 */
router.get('/technical-services', asyncHandler(async (req, res) => {
  const { rows: services } = await executeQuery(`
    SELECT 
      id, name_ar, name_en, description_ar, description_en, price, category, type,
      features_ar, features_en, tech_specs_ar, tech_specs_en,
      video_url, image_url, gallery_images, status, featured,
      created_at, updated_at
    FROM technical_services
    ORDER BY created_at DESC
  `);

  // Parse JSON fields for each service safely
  const parsedServices = services.map(service => ({
    ...service,
    features_ar: safeJsonParse(service.features_ar, []),
    features_en: safeJsonParse(service.features_en, []),
    tech_specs_ar: safeJsonParse(service.tech_specs_ar, []),
    tech_specs_en: safeJsonParse(service.tech_specs_en, []),
    gallery_images: safeJsonParse(service.gallery_images, [])
  }));

  res.json({
    success: true,
    data: parsedServices
  });
}));

/**
 * @route   POST /api/admin/technical-services
 * @desc    Create new technical service
 * @access  Private (admin only)
 */
router.post('/technical-services', asyncHandler(async (req, res) => {
  const {
    name_ar, name_en, description_ar, description_en, price, category,
    service_type = 'development', // Use service_type instead of type
    features_ar = [], features_en = [], tech_specs_ar = [], tech_specs_en = [],
    is_premium_addon = false, premium_price = 0, subscription_type = 'none',
    delivery_time_ar = '', delivery_time_en = '',
    video_url = '', image_url = '', gallery_images = [], status = 'active'
  } = req.body;

  // Validation
  if (!name_ar || !name_en || !description_ar || !description_en || !price) {
    throw validationError('Missing required fields');
  }

  const serviceId = generateUUID();

  await executeQuery(`
    INSERT INTO technical_services (
      id, name_ar, name_en, description_ar, description_en, price, category, service_type,
      features_ar, features_en, tech_specs_ar, tech_specs_en,
      is_premium_addon, premium_price, subscription_type,
      delivery_time_ar, delivery_time_en,
      video_url, image_url, gallery_images, status, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
  `, [
    serviceId, name_ar, name_en, description_ar, description_en, price, category, service_type,
    JSON.stringify(features_ar), JSON.stringify(features_en),
    JSON.stringify(tech_specs_ar), JSON.stringify(tech_specs_en),
    is_premium_addon, premium_price, subscription_type,
    delivery_time_ar, delivery_time_en,
    video_url, image_url, JSON.stringify(gallery_images), status
  ]);

  // Get the created service to return complete data
  const { rows: createdServices } = await executeQuery(`
    SELECT
      id, name_ar, name_en, description_ar, description_en, price, category, service_type,
      features_ar, features_en, tech_specs_ar, tech_specs_en,
      is_premium_addon, premium_price, subscription_type,
      delivery_time_ar, delivery_time_en,
      video_url, image_url, gallery_images, status, featured,
      order_count, rating, rating_count, created_at, updated_at
    FROM technical_services
    WHERE id = ?
  `, [serviceId]);

  const createdService = createdServices[0];

  // Parse JSON fields safely
  createdService.features_ar = safeJsonParse(createdService.features_ar, []);
  createdService.features_en = safeJsonParse(createdService.features_en, []);
  createdService.tech_specs_ar = safeJsonParse(createdService.tech_specs_ar, []);
  createdService.tech_specs_en = safeJsonParse(createdService.tech_specs_en, []);
  createdService.gallery_images = safeJsonParse(createdService.gallery_images, []);

  // Log admin action
  await logUserAction('technical_service_created', 'technical_service', serviceId, {
    serviceName: name_en,
    category,
    price
  }, req);

  res.json({
    success: true,
    message: 'Technical service created successfully',
    data: createdService
  });
}));

/**
 * @route   PUT /api/admin/technical-services/:id
 * @desc    Update technical service
 * @access  Private (admin only)
 */
router.put('/technical-services/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    name_ar, name_en, description_ar, description_en, price, category, type,
    features_ar, features_en, tech_specs_ar, tech_specs_en,
    video_url, image_url, gallery_images, status
  } = req.body;

  // Check if service exists
  const { rows: existingServices } = await executeQuery(
    'SELECT id, name_en FROM technical_services WHERE id = ?',
    [id]
  );

  if (existingServices.length === 0) {
    throw notFoundError('Technical service not found');
  }

  // Build update query dynamically
  const updateFields = [];
  const updateValues = [];

  if (name_ar !== undefined) { updateFields.push('name_ar = ?'); updateValues.push(name_ar); }
  if (name_en !== undefined) { updateFields.push('name_en = ?'); updateValues.push(name_en); }
  if (description_ar !== undefined) { updateFields.push('description_ar = ?'); updateValues.push(description_ar); }
  if (description_en !== undefined) { updateFields.push('description_en = ?'); updateValues.push(description_en); }
  if (price !== undefined) { updateFields.push('price = ?'); updateValues.push(price); }
  if (category !== undefined) { updateFields.push('category = ?'); updateValues.push(category); }
  if (type !== undefined) { updateFields.push('type = ?'); updateValues.push(type); }
  if (features_ar !== undefined) { updateFields.push('features_ar = ?'); updateValues.push(JSON.stringify(features_ar)); }
  if (features_en !== undefined) { updateFields.push('features_en = ?'); updateValues.push(JSON.stringify(features_en)); }
  if (tech_specs_ar !== undefined) { updateFields.push('tech_specs_ar = ?'); updateValues.push(JSON.stringify(tech_specs_ar)); }
  if (tech_specs_en !== undefined) { updateFields.push('tech_specs_en = ?'); updateValues.push(JSON.stringify(tech_specs_en)); }
  if (video_url !== undefined) { updateFields.push('video_url = ?'); updateValues.push(video_url); }
  if (image_url !== undefined) { updateFields.push('image_url = ?'); updateValues.push(image_url); }
  if (gallery_images !== undefined) { updateFields.push('gallery_images = ?'); updateValues.push(JSON.stringify(gallery_images)); }
  if (status !== undefined) { updateFields.push('status = ?'); updateValues.push(status); }

  if (updateFields.length === 0) {
    throw validationError('No fields to update');
  }

  updateFields.push('updated_at = NOW()');
  updateValues.push(id);

  await executeQuery(`
    UPDATE technical_services 
    SET ${updateFields.join(', ')}
    WHERE id = ?
  `, updateValues);

  // Log admin action
  await logUserAction('technical_service_updated', 'technical_service', id, {
    serviceName: name_en || existingServices[0].name_en,
    updatedFields: updateFields.length - 1
  }, req);

  res.json({
    success: true,
    message: 'Technical service updated successfully'
  });
}));

/**
 * @route   DELETE /api/admin/technical-services/:id
 * @desc    Delete technical service
 * @access  Private (admin only)
 */
router.delete('/technical-services/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Check if service exists
  const { rows: existingServices } = await executeQuery(
    'SELECT id, name_en FROM technical_services WHERE id = ?',
    [id]
  );

  if (existingServices.length === 0) {
    throw notFoundError('Technical service not found');
  }

  // Check if service is being used in orders
  const { rows: serviceOrders } = await executeQuery(
    'SELECT COUNT(*) as count FROM orders WHERE item_id = ? AND order_type = ?',
    [id, 'technical_service']
  );

  if (serviceOrders[0].count > 0) {
    // Don't delete, just deactivate
    await executeQuery(
      'UPDATE technical_services SET status = ?, updated_at = NOW() WHERE id = ?',
      ['inactive', id]
    );

    await logUserAction('technical_service_deactivated', 'technical_service', id, {
      serviceName: existingServices[0].name_en,
      reason: 'Has existing orders'
    }, req);

    res.json({
      success: true,
      message: 'Technical service deactivated (has existing orders)'
    });
  } else {
    // Safe to delete
    await executeQuery('DELETE FROM technical_services WHERE id = ?', [id]);

    await logUserAction('technical_service_deleted', 'technical_service', id, {
      serviceName: existingServices[0].name_en
    }, req);

    res.json({
      success: true,
      message: 'Technical service deleted successfully'
    });
  }
}));

/**
 * @route   POST /api/admin/maintenance
 * @desc    Perform system maintenance tasks
 * @access  Private (admin only)
 */
router.post('/maintenance', asyncHandler(async (req, res) => {
  const { action } = req.body;
  
  let result = {};
  
  switch (action) {
    case 'cleanup_logs':
      const deletedLogs = await cleanupOldLogs(30); // Keep 30 days
      result = { action: 'cleanup_logs', deletedRecords: deletedLogs };
      break;
      
    case 'cleanup_sessions':
      const { rows: sessionResult } = await executeQuery(
        'DELETE FROM user_sessions WHERE expires_at < NOW() OR is_active = false'
      );
      result = { action: 'cleanup_sessions', deletedRecords: sessionResult.affectedRows || 0 };
      break;
      
    case 'database_stats':
      const dbStats = await getDatabaseStats();
      result = { action: 'database_stats', stats: dbStats };
      break;
      
    default:
      throw validationError('Invalid maintenance action');
  }
  
  // Log maintenance action
  await logUserAction('maintenance_performed', 'system', 'maintenance', {
    action,
    result
  }, req);
  
  res.json({
    success: true,
    message: 'Maintenance task completed successfully',
    data: result
  });
}));

// Send message to user
router.post('/users/:userId/messages', verifyToken, requireAdmin, async (req, res) => {
  try {
    console.log('Sending message to user:', req.params.userId);
    console.log('Message data:', req.body);

    const { userId } = req.params;
    const { subject_ar, subject_en, message_ar, message_en, message_type = 'admin_message', priority = 'normal' } = req.body;

    // Validate required fields
    if (!subject_ar || !subject_en || !message_ar || !message_en) {
      return res.status(400).json({
        success: false,
        error: 'All message fields are required (Arabic and English)'
      });
    }

    // Check if user exists
    const { rows: users } = await executeQuery('SELECT id FROM users WHERE id = ?', [userId]);
    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Create message
    const messageId = generateUUID();
    const messageData = {
      id: messageId,
      user_id: userId,
      sender_type: 'admin',
      sender_id: req.user?.id || 'admin',
      subject_ar,
      subject_en,
      message_ar,
      message_en,
      message_type,
      priority,
      is_read: false,
      created_at: new Date(),
      updated_at: new Date()
    };

    await executeQuery(`
      INSERT INTO inbox_messages (
        id, user_id, sender_type, sender_id, subject_ar, subject_en,
        message_ar, message_en, message_type, priority, is_read, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      messageData.id, messageData.user_id, messageData.sender_type, messageData.sender_id,
      messageData.subject_ar, messageData.subject_en, messageData.message_ar, messageData.message_en,
      messageData.message_type, messageData.priority, messageData.is_read,
      messageData.created_at, messageData.updated_at
    ]);

    res.json({
      success: true,
      message: 'Message sent successfully',
      data: { message: messageData }
    });

  } catch (error) {
    console.error('Error sending message to user:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send message'
    });
  }
});

// Update user role/status
router.put('/users/:userId', verifyToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { role, status, full_name, email } = req.body;

    // Check if user exists
    const { rows: users } = await executeQuery('SELECT * FROM users WHERE id = ?', [userId]);
    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    const currentUser = users[0];

    // Prepare update data
    const updateFields = [];
    const updateValues = [];

    if (role && role !== currentUser.role) {
      updateFields.push('role = ?');
      updateValues.push(role);
    }

    if (status && status !== currentUser.status) {
      updateFields.push('status = ?');
      updateValues.push(status);
    }

    if (full_name && full_name !== currentUser.full_name) {
      updateFields.push('full_name = ?');
      updateValues.push(full_name);
    }

    if (email && email !== currentUser.email) {
      updateFields.push('email = ?');
      updateValues.push(email);
    }

    if (updateFields.length === 0) {
      return res.json({
        success: true,
        message: 'No changes to update',
        data: { user: currentUser }
      });
    }

    updateFields.push('updated_at = ?');
    updateValues.push(new Date());
    updateValues.push(userId);

    // Update user
    await executeQuery(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // Get updated user
    const { rows: updatedUsers } = await executeQuery('SELECT * FROM users WHERE id = ?', [userId]);
    const updatedUser = updatedUsers[0];

    // Remove sensitive data
    delete updatedUser.password_hash;

    res.json({
      success: true,
      message: 'User updated successfully',
      data: { user: updatedUser }
    });

  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user'
    });
  }
});

// Update order status
router.put('/orders/:orderId', verifyToken, requireAdmin, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status, payment_status, admin_notes, priority } = req.body;

    // Check if order exists
    const { rows: orders } = await executeQuery('SELECT * FROM orders WHERE id = ?', [orderId]);
    if (orders.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }

    const currentOrder = orders[0];

    // Prepare update data
    const updateFields = [];
    const updateValues = [];

    if (status && status !== currentOrder.status) {
      updateFields.push('status = ?');
      updateValues.push(status);
    }

    if (payment_status && payment_status !== currentOrder.payment_status) {
      updateFields.push('payment_status = ?');
      updateValues.push(payment_status);
    }

    if (admin_notes !== undefined) {
      updateFields.push('admin_notes = ?');
      updateValues.push(admin_notes);
    }

    if (priority && priority !== currentOrder.priority) {
      updateFields.push('priority = ?');
      updateValues.push(priority);
    }

    // Add support for new fields
    if (req.body.estimated_completion !== undefined) {
      updateFields.push('estimated_completion = ?');
      updateValues.push(req.body.estimated_completion);
    }

    if (req.body.support_level !== undefined) {
      updateFields.push('support_level = ?');
      updateValues.push(req.body.support_level);
    }

    if (req.body.maintenance_included !== undefined) {
      updateFields.push('maintenance_included = ?');
      updateValues.push(req.body.maintenance_included ? 1 : 0);
    }

    if (req.body.installation_included !== undefined) {
      updateFields.push('installation_included = ?');
      updateValues.push(req.body.installation_included ? 1 : 0);
    }

    if (updateFields.length === 0) {
      return res.json({
        success: true,
        message: 'No changes to update',
        data: { order: currentOrder }
      });
    }

    updateFields.push('updated_at = ?');
    updateValues.push(new Date());
    updateValues.push(orderId);

    // Update order
    await executeQuery(
      `UPDATE orders SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // Get updated order
    const { rows: updatedOrders } = await executeQuery('SELECT * FROM orders WHERE id = ?', [orderId]);
    const updatedOrder = updatedOrders[0];

    // Send notification to user if status changed
    if (status && status !== currentOrder.status) {
      const messageId = generateUUID();
      await executeQuery(`
        INSERT INTO inbox_messages (
          id, user_id, sender_type, sender_id, subject_ar, subject_en,
          message_ar, message_en, message_type, priority, is_read, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        messageId, currentOrder.user_id, 'admin', req.user?.id || 'admin',
        `تحديث حالة الطلب ${currentOrder.order_number}`,
        `Order ${currentOrder.order_number} Status Update`,
        `تم تحديث حالة طلبك ${currentOrder.order_number} إلى: ${status}`,
        `Your order ${currentOrder.order_number} status has been updated to: ${status}`,
        'order_update', 'normal', false, new Date(), new Date()
      ]);
    }

    res.json({
      success: true,
      message: 'Order updated successfully',
      data: { order: updatedOrder }
    });

  } catch (error) {
    console.error('Error updating order:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update order'
    });
  }
});

// Add admin note to order
router.post('/orders/:orderId/notes', verifyToken, requireAdmin, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { note_ar, note_en } = req.body;

    if (!note_ar && !note_en) {
      return res.status(400).json({
        success: false,
        error: 'At least one note (Arabic or English) is required'
      });
    }

    // Check if order exists
    const { rows: orders } = await executeQuery('SELECT * FROM orders WHERE id = ?', [orderId]);
    if (orders.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }

    const order = orders[0];
    const currentNotes = order.admin_notes ? JSON.parse(order.admin_notes) : [];

    // Add new note
    const newNote = {
      id: generateUUID(),
      admin_id: req.user?.id || 'admin',
      admin_name: req.user?.username || 'Admin',
      note_ar: note_ar || '',
      note_en: note_en || '',
      timestamp: new Date().toISOString()
    };

    currentNotes.push(newNote);

    // Update order with new notes
    await executeQuery(
      'UPDATE orders SET admin_notes = ?, updated_at = ? WHERE id = ?',
      [JSON.stringify(currentNotes), new Date(), orderId]
    );

    res.json({
      success: true,
      message: 'Note added successfully',
      data: { note: newNote }
    });

  } catch (error) {
    console.error('Error adding note:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to add note'
    });
  }
});

// Delete order (admin only)
router.delete('/orders/:orderId', verifyToken, requireAdmin, async (req, res) => {
  try {
    const { orderId } = req.params;

    // Check if order exists
    const { rows: orders } = await executeQuery('SELECT * FROM orders WHERE id = ?', [orderId]);
    if (orders.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }

    const order = orders[0];

    // Instead of actually deleting, we'll cancel the order
    await executeQuery(
      'UPDATE orders SET status = ?, updated_at = ? WHERE id = ?',
      ['cancelled', new Date(), orderId]
    );

    // Send notification to user
    const messageId = generateUUID();
    await executeQuery(`
      INSERT INTO inbox_messages (
        id, user_id, sender_type, sender_id, subject_ar, subject_en,
        message_ar, message_en, message_type, priority, is_read, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      messageId, order.user_id, 'admin', req.user?.id || 'admin',
      `إلغاء الطلب ${order.order_number}`,
      `Order ${order.order_number} Cancelled`,
      `تم إلغاء طلبك ${order.order_number}. إذا كان لديك أي استفسارات، يرجى التواصل معنا.`,
      `Your order ${order.order_number} has been cancelled. If you have any questions, please contact us.`,
      'order_cancellation', 'normal', false, new Date(), new Date()
    ]);

    res.json({
      success: true,
      message: 'Order cancelled successfully',
      data: { orderId }
    });

  } catch (error) {
    console.error('Error cancelling order:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel order'
    });
  }
});

module.exports = router;
