/**
 * Enhanced Theme System for Khanfashariya
 * 
 * Provides comprehensive theming with improved contrast ratios
 * and accessibility compliance (WCAG 2.1 AA)
 */

export interface ThemeColors {
  // Background colors
  background: {
    primary: string
    secondary: string
    tertiary: string
    card: string
    hover: string
    active: string
    border: string
    input: string
    disabled: string
  }
  
  // Text colors
  text: {
    primary: string
    secondary: string
    tertiary: string
    inverse: string
    accent: string
    success: string
    warning: string
    error: string
    info: string
    disabled: string
  }
  
  // Interactive colors
  interactive: {
    primary: string
    primaryHover: string
    primaryActive: string
    secondary: string
    secondaryHover: string
    secondaryActive: string
    accent: string
    accentHover: string
    accentActive: string
  }
  
  // Status colors
  status: {
    success: string
    successBg: string
    warning: string
    warningBg: string
    error: string
    errorBg: string
    info: string
    infoBg: string
  }
}

// Dark Theme - Enhanced for better contrast
export const darkTheme: ThemeColors = {
  background: {
    primary: '#0F172A',      // slate-900 - Main background
    secondary: '#1E293B',    // slate-800 - Secondary background
    tertiary: '#334155',     // slate-700 - Tertiary background
    card: '#1E293B',         // Card background
    hover: '#334155',        // Hover state
    active: '#475569',       // Active state
    border: '#475569',       // Border color
    input: '#1E293B',        // Input background
    disabled: '#64748B'      // Disabled state
  },
  
  text: {
    primary: '#F8FAFC',      // slate-50 - High contrast primary text
    secondary: '#CBD5E1',    // slate-300 - Good contrast secondary text
    tertiary: '#94A3B8',     // slate-400 - Muted text
    inverse: '#0F172A',      // Dark text for light backgrounds
    accent: '#38BDF8',       // sky-400 - Accent text
    success: '#34D399',      // emerald-400 - Success text
    warning: '#FBBF24',      // amber-400 - Warning text
    error: '#F87171',        // red-400 - Error text
    info: '#60A5FA',         // blue-400 - Info text
    disabled: '#64748B'      // Disabled text
  },
  
  interactive: {
    primary: '#3B82F6',      // blue-500 - Primary buttons
    primaryHover: '#2563EB', // blue-600 - Primary hover
    primaryActive: '#1D4ED8', // blue-700 - Primary active
    secondary: '#6B7280',    // gray-500 - Secondary buttons
    secondaryHover: '#4B5563', // gray-600 - Secondary hover
    secondaryActive: '#374151', // gray-700 - Secondary active
    accent: '#38BDF8',       // sky-400 - Accent elements
    accentHover: '#0EA5E9',  // sky-500 - Accent hover
    accentActive: '#0284C7'  // sky-600 - Accent active
  },
  
  status: {
    success: '#34D399',      // emerald-400
    successBg: '#064E3B',    // emerald-900
    warning: '#FBBF24',      // amber-400
    warningBg: '#78350F',    // amber-900
    error: '#F87171',        // red-400
    errorBg: '#7F1D1D',      // red-900
    info: '#60A5FA',         // blue-400
    infoBg: '#1E3A8A'        // blue-900
  }
}

// Light Theme - For future implementation
export const lightTheme: ThemeColors = {
  background: {
    primary: '#FFFFFF',      // white - Main background
    secondary: '#F8FAFC',    // slate-50 - Secondary background
    tertiary: '#F1F5F9',     // slate-100 - Tertiary background
    card: '#FFFFFF',         // Card background
    hover: '#F1F5F9',        // Hover state
    active: '#E2E8F0',       // Active state
    border: '#E2E8F0',       // Border color
    input: '#FFFFFF',        // Input background
    disabled: '#F1F5F9'      // Disabled state
  },
  
  text: {
    primary: '#0F172A',      // slate-900 - Primary text
    secondary: '#334155',    // slate-700 - Secondary text
    tertiary: '#64748B',     // slate-500 - Muted text
    inverse: '#FFFFFF',      // Light text for dark backgrounds
    accent: '#0EA5E9',       // sky-500 - Accent text
    success: '#059669',      // emerald-600 - Success text
    warning: '#D97706',      // amber-600 - Warning text
    error: '#DC2626',        // red-600 - Error text
    info: '#2563EB',         // blue-600 - Info text
    disabled: '#94A3B8'      // Disabled text
  },
  
  interactive: {
    primary: '#3B82F6',      // blue-500 - Primary buttons
    primaryHover: '#2563EB', // blue-600 - Primary hover
    primaryActive: '#1D4ED8', // blue-700 - Primary active
    secondary: '#E5E7EB',    // gray-200 - Secondary buttons
    secondaryHover: '#D1D5DB', // gray-300 - Secondary hover
    secondaryActive: '#9CA3AF', // gray-400 - Secondary active
    accent: '#0EA5E9',       // sky-500 - Accent elements
    accentHover: '#0284C7',  // sky-600 - Accent hover
    accentActive: '#0369A1'  // sky-700 - Accent active
  },
  
  status: {
    success: '#059669',      // emerald-600
    successBg: '#D1FAE5',    // emerald-100
    warning: '#D97706',      // amber-600
    warningBg: '#FEF3C7',    // amber-100
    error: '#DC2626',        // red-600
    errorBg: '#FEE2E2',      // red-100
    info: '#2563EB',         // blue-600
    infoBg: '#DBEAFE'        // blue-100
  }
}

// Theme context type
export interface ThemeContextType {
  theme: 'dark' | 'light'
  colors: ThemeColors
  toggleTheme: () => void
  setTheme: (theme: 'dark' | 'light') => void
}

// CSS Custom Properties Generator
export const generateCSSVariables = (colors: ThemeColors): Record<string, string> => {
  return {
    // Background variables
    '--bg-primary': colors.background.primary,
    '--bg-secondary': colors.background.secondary,
    '--bg-tertiary': colors.background.tertiary,
    '--bg-card': colors.background.card,
    '--bg-hover': colors.background.hover,
    '--bg-active': colors.background.active,
    '--bg-border': colors.background.border,
    '--bg-input': colors.background.input,
    '--bg-disabled': colors.background.disabled,
    
    // Text variables
    '--text-primary': colors.text.primary,
    '--text-secondary': colors.text.secondary,
    '--text-tertiary': colors.text.tertiary,
    '--text-inverse': colors.text.inverse,
    '--text-accent': colors.text.accent,
    '--text-success': colors.text.success,
    '--text-warning': colors.text.warning,
    '--text-error': colors.text.error,
    '--text-info': colors.text.info,
    '--text-disabled': colors.text.disabled,
    
    // Interactive variables
    '--interactive-primary': colors.interactive.primary,
    '--interactive-primary-hover': colors.interactive.primaryHover,
    '--interactive-primary-active': colors.interactive.primaryActive,
    '--interactive-secondary': colors.interactive.secondary,
    '--interactive-secondary-hover': colors.interactive.secondaryHover,
    '--interactive-secondary-active': colors.interactive.secondaryActive,
    '--interactive-accent': colors.interactive.accent,
    '--interactive-accent-hover': colors.interactive.accentHover,
    '--interactive-accent-active': colors.interactive.accentActive,
    
    // Status variables
    '--status-success': colors.status.success,
    '--status-success-bg': colors.status.successBg,
    '--status-warning': colors.status.warning,
    '--status-warning-bg': colors.status.warningBg,
    '--status-error': colors.status.error,
    '--status-error-bg': colors.status.errorBg,
    '--status-info': colors.status.info,
    '--status-info-bg': colors.status.infoBg
  }
}

// Utility function to apply theme
export const applyTheme = (colors: ThemeColors) => {
  const variables = generateCSSVariables(colors)
  const root = document.documentElement
  
  Object.entries(variables).forEach(([property, value]) => {
    root.style.setProperty(property, value)
  })
}

// Export default theme
export const defaultTheme = darkTheme
