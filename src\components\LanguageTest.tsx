import React from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { Globe, CheckCircle, XCircle } from 'lucide-react';

/**
 * Language Test Component
 * This component tests language switching and translation coverage
 */
const LanguageTest: React.FC = () => {
  const { language, changeLanguage, t } = useTranslation();

  const testKeys = [
    'nav.home',
    'nav.systems', 
    'nav.services',
    'nav.contact',
    'nav.login',
    'hero.title',
    'hero.subtitle',
    'common.loading',
    'common.success',
    'common.error',
    'common.confirm',
    'common.cancel',
    'auth.createAccount',
    'auth.loginButton',
    'auth.fullName',
    'auth.password',
    'contact.email',
    'notifications.deleteSuccess',
    'admin.dashboard.title'
  ];

  const handleLanguageSwitch = () => {
    changeLanguage(language === 'ar' ? 'en' : 'ar');
  };

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-primary to-background border border-accent/30 rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white flex items-center">
              <Globe className="w-6 h-6 mr-2 text-secondary" />
              Language Test - {language === 'ar' ? 'اختبار اللغة' : 'Language Test'}
            </h2>
            <button
              onClick={handleLanguageSwitch}
              className="px-4 py-2 bg-secondary text-primary rounded-lg hover:bg-accent transition-colors"
            >
              Switch to {language === 'ar' ? 'English' : 'العربية'}
            </button>
          </div>

          {/* Current Language Info */}
          <div className="mb-6 p-4 bg-primary/30 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">
              {language === 'ar' ? 'اللغة الحالية' : 'Current Language'}
            </h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Language Code:</span>
                <span className="text-white ml-2">{language}</span>
              </div>
              <div>
                <span className="text-gray-400">Direction:</span>
                <span className="text-white ml-2">{language === 'ar' ? 'RTL' : 'LTR'}</span>
              </div>
              <div>
                <span className="text-gray-400">Document Lang:</span>
                <span className="text-white ml-2">{document.documentElement.lang}</span>
              </div>
              <div>
                <span className="text-gray-400">Document Dir:</span>
                <span className="text-white ml-2">{document.documentElement.dir}</span>
              </div>
            </div>
          </div>

          {/* Translation Tests */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">
              {language === 'ar' ? 'اختبار الترجمات' : 'Translation Tests'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {testKeys.map((key) => {
                const translation = t(key);
                const isTranslated = translation !== key;
                
                return (
                  <div 
                    key={key}
                    className={`p-3 rounded-lg border ${
                      isTranslated 
                        ? 'bg-green-500/10 border-green-500/30' 
                        : 'bg-red-500/10 border-red-500/30'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs text-gray-400 font-mono">{key}</span>
                      {isTranslated ? (
                        <CheckCircle className="w-4 h-4 text-green-400" />
                      ) : (
                        <XCircle className="w-4 h-4 text-red-400" />
                      )}
                    </div>
                    <div className="text-white text-sm">
                      {translation}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Sample UI Elements */}
          <div className="mt-6 space-y-4">
            <h3 className="text-lg font-semibold text-white">
              {language === 'ar' ? 'عناصر الواجهة' : 'UI Elements Sample'}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Navigation Sample */}
              <div className="p-4 bg-primary/30 rounded-lg">
                <h4 className="text-white font-medium mb-3">{t('nav.home')}</h4>
                <div className="space-y-2 text-sm">
                  <div className="text-gray-300">{t('nav.systems')}</div>
                  <div className="text-gray-300">{t('nav.services')}</div>
                  <div className="text-gray-300">{t('nav.contact')}</div>
                </div>
              </div>

              {/* Auth Sample */}
              <div className="p-4 bg-primary/30 rounded-lg">
                <h4 className="text-white font-medium mb-3">{t('auth.createAccount')}</h4>
                <div className="space-y-2 text-sm">
                  <div className="text-gray-300">{t('auth.fullName')}</div>
                  <div className="text-gray-300">{t('contact.email')}</div>
                  <div className="text-gray-300">{t('auth.password')}</div>
                  <button className="px-3 py-1 bg-secondary text-primary rounded text-xs">
                    {t('auth.loginButton')}
                  </button>
                </div>
              </div>

              {/* Common Elements */}
              <div className="p-4 bg-primary/30 rounded-lg">
                <h4 className="text-white font-medium mb-3">{t('common.success')}</h4>
                <div className="space-y-2 text-sm">
                  <div className="text-gray-300">{t('common.loading')}</div>
                  <div className="text-gray-300">{t('common.error')}</div>
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <button className="px-2 py-1 bg-green-600 text-white rounded text-xs">
                      {t('common.confirm')}
                    </button>
                    <button className="px-2 py-1 bg-gray-600 text-white rounded text-xs">
                      {t('common.cancel')}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
            <h4 className="text-blue-400 font-medium mb-2">
              {language === 'ar' ? 'تعليمات الاختبار' : 'Test Instructions'}
            </h4>
            <div className="text-sm text-gray-300 space-y-1">
              <div>
                {language === 'ar' 
                  ? '1. انقر على "Switch to English/العربية" لتبديل اللغة'
                  : '1. Click "Switch to English/العربية" to change language'
                }
              </div>
              <div>
                {language === 'ar'
                  ? '2. تحقق من أن جميع النصوص تتغير فوراً'
                  : '2. Verify that all text changes immediately'
                }
              </div>
              <div>
                {language === 'ar'
                  ? '3. تأكد من عدم ظهور نصوص مختلطة (عربي + إنجليزي)'
                  : '3. Ensure no mixed content appears (Arabic + English)'
                }
              </div>
              <div>
                {language === 'ar'
                  ? '4. تحقق من اتجاه النص (RTL للعربية، LTR للإنجليزية)'
                  : '4. Check text direction (RTL for Arabic, LTR for English)'
                }
              </div>
            </div>
          </div>

          {/* Close Button */}
          <div className="mt-6 text-center">
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-2 bg-accent text-primary rounded-lg hover:bg-secondary transition-colors"
            >
              {language === 'ar' ? 'إغلاق الاختبار' : 'Close Test'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LanguageTest;
