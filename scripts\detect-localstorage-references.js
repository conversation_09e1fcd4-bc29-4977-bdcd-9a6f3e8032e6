const fs = require('fs').promises;
const path = require('path');

/**
 * LocalStorage Reference Detection Script
 * 
 * Scans the entire codebase for:
 * - localStorage references
 * - sessionStorage references
 * - Local data storage patterns
 * - Hardcoded data arrays
 * - Non-API data fetching
 */

class LocalStorageDetector {
  constructor() {
    this.results = {
      localStorage: [],
      sessionStorage: [],
      hardcodedData: [],
      nonApiCalls: [],
      suspiciousPatterns: []
    };
    this.excludePatterns = [
      'node_modules',
      '.git',
      'dist',
      'build',
      '.next',
      'coverage',
      'scripts/detect-localstorage-references.js' // Exclude this file
    ];
  }

  async scanDirectory(dirPath, basePath = '') {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        const relativePath = path.join(basePath, entry.name);
        
        // Skip excluded directories
        if (this.shouldExclude(relativePath)) {
          continue;
        }
        
        if (entry.isDirectory()) {
          await this.scanDirectory(fullPath, relativePath);
        } else if (this.isRelevantFile(entry.name)) {
          await this.scanFile(fullPath, relativePath);
        }
      }
    } catch (error) {
      console.error(`Error scanning directory ${dirPath}:`, error.message);
    }
  }

  shouldExclude(relativePath) {
    return this.excludePatterns.some(pattern => 
      relativePath.includes(pattern) || relativePath.startsWith(pattern)
    );
  }

  isRelevantFile(filename) {
    const extensions = ['.js', '.jsx', '.ts', '.tsx', '.vue', '.svelte'];
    return extensions.some(ext => filename.endsWith(ext));
  }

  async scanFile(filePath, relativePath) {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const lines = content.split('\n');
      
      lines.forEach((line, index) => {
        const lineNumber = index + 1;
        const trimmedLine = line.trim();
        
        // Check for localStorage
        if (this.containsLocalStorage(trimmedLine)) {
          this.results.localStorage.push({
            file: relativePath,
            line: lineNumber,
            content: trimmedLine,
            context: this.getContext(lines, index)
          });
        }
        
        // Check for sessionStorage
        if (this.containsSessionStorage(trimmedLine)) {
          this.results.sessionStorage.push({
            file: relativePath,
            line: lineNumber,
            content: trimmedLine,
            context: this.getContext(lines, index)
          });
        }
        
        // Check for hardcoded data arrays
        if (this.containsHardcodedData(trimmedLine)) {
          this.results.hardcodedData.push({
            file: relativePath,
            line: lineNumber,
            content: trimmedLine,
            context: this.getContext(lines, index)
          });
        }
        
        // Check for non-API data fetching patterns
        if (this.containsNonApiPattern(trimmedLine)) {
          this.results.nonApiCalls.push({
            file: relativePath,
            line: lineNumber,
            content: trimmedLine,
            context: this.getContext(lines, index)
          });
        }
        
        // Check for suspicious patterns
        if (this.containsSuspiciousPattern(trimmedLine)) {
          this.results.suspiciousPatterns.push({
            file: relativePath,
            line: lineNumber,
            content: trimmedLine,
            context: this.getContext(lines, index)
          });
        }
      });
      
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error.message);
    }
  }

  containsLocalStorage(line) {
    return /localStorage\.(getItem|setItem|removeItem|clear)/i.test(line) ||
           /window\.localStorage/i.test(line);
  }

  containsSessionStorage(line) {
    return /sessionStorage\.(getItem|setItem|removeItem|clear)/i.test(line) ||
           /window\.sessionStorage/i.test(line);
  }

  containsHardcodedData(line) {
    // Look for large hardcoded arrays that might be data
    const patterns = [
      /const\s+\w+\s*=\s*\[\s*{.*name.*:.*,.*}/i, // const data = [{ name: ... }]
      /const\s+\w+Systems\s*=\s*\[/i, // const someSystems = [
      /const\s+\w+Services\s*=\s*\[/i, // const someServices = [
      /const\s+\w+Data\s*=\s*\[/i, // const someData = [
      /export\s+const\s+\w+\s*=\s*\[\s*{/i // export const data = [{
    ];
    
    return patterns.some(pattern => pattern.test(line));
  }

  containsNonApiPattern(line) {
    // Look for data fetching that doesn't use API
    const patterns = [
      /\.filter\(.*=>.*\.id\s*===\s*id\)/i, // Direct array filtering by ID
      /\.find\(.*=>.*\.id\s*===\s*id\)/i, // Direct array finding by ID
      /useState\(\[.*{.*name.*:.*}\]/i, // useState with hardcoded objects
      /const\s+\w+\s*=\s*\w+\.filter\(/i // Direct filtering without API
    ];
    
    return patterns.some(pattern => pattern.test(line));
  }

  containsSuspiciousPattern(line) {
    // Look for patterns that might indicate local data storage
    const patterns = [
      /\/\*.*localStorage.*\*\//i, // Comments about localStorage
      /\/\/.*localStorage/i, // Comments about localStorage
      /TODO.*localStorage/i, // TODO comments about localStorage
      /FIXME.*localStorage/i, // FIXME comments about localStorage
      /mock.*data/i, // Mock data references
      /dummy.*data/i, // Dummy data references
      /test.*data/i, // Test data references (in non-test files)
      /sample.*data/i // Sample data references
    ];
    
    return patterns.some(pattern => pattern.test(line)) && 
           !relativePath.includes('test') && 
           !relativePath.includes('spec');
  }

  getContext(lines, currentIndex) {
    const start = Math.max(0, currentIndex - 2);
    const end = Math.min(lines.length, currentIndex + 3);
    return lines.slice(start, end).map((line, index) => ({
      line: start + index + 1,
      content: line.trim(),
      current: start + index === currentIndex
    }));
  }

  generateReport() {
    const totalIssues = Object.values(this.results).reduce((sum, arr) => sum + arr.length, 0);
    
    console.log('🔍 LocalStorage Reference Detection Report');
    console.log('=' .repeat(60));
    console.log(`📊 Total Issues Found: ${totalIssues}\n`);
    
    // localStorage references
    if (this.results.localStorage.length > 0) {
      console.log(`❌ localStorage References: ${this.results.localStorage.length}`);
      this.results.localStorage.forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.file}:${item.line}`);
        console.log(`      ${item.content}`);
      });
      console.log();
    } else {
      console.log('✅ No localStorage references found\n');
    }
    
    // sessionStorage references
    if (this.results.sessionStorage.length > 0) {
      console.log(`❌ sessionStorage References: ${this.results.sessionStorage.length}`);
      this.results.sessionStorage.forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.file}:${item.line}`);
        console.log(`      ${item.content}`);
      });
      console.log();
    } else {
      console.log('✅ No sessionStorage references found\n');
    }
    
    // Hardcoded data
    if (this.results.hardcodedData.length > 0) {
      console.log(`⚠️ Potential Hardcoded Data: ${this.results.hardcodedData.length}`);
      this.results.hardcodedData.slice(0, 10).forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.file}:${item.line}`);
        console.log(`      ${item.content.substring(0, 80)}...`);
      });
      if (this.results.hardcodedData.length > 10) {
        console.log(`   ... and ${this.results.hardcodedData.length - 10} more`);
      }
      console.log();
    } else {
      console.log('✅ No suspicious hardcoded data found\n');
    }
    
    // Non-API patterns
    if (this.results.nonApiCalls.length > 0) {
      console.log(`⚠️ Non-API Data Patterns: ${this.results.nonApiCalls.length}`);
      this.results.nonApiCalls.slice(0, 5).forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.file}:${item.line}`);
        console.log(`      ${item.content}`);
      });
      if (this.results.nonApiCalls.length > 5) {
        console.log(`   ... and ${this.results.nonApiCalls.length - 5} more`);
      }
      console.log();
    } else {
      console.log('✅ No non-API data patterns found\n');
    }
    
    // Suspicious patterns
    if (this.results.suspiciousPatterns.length > 0) {
      console.log(`🔍 Suspicious Patterns: ${this.results.suspiciousPatterns.length}`);
      this.results.suspiciousPatterns.slice(0, 5).forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.file}:${item.line}`);
        console.log(`      ${item.content}`);
      });
      if (this.results.suspiciousPatterns.length > 5) {
        console.log(`   ... and ${this.results.suspiciousPatterns.length - 5} more`);
      }
      console.log();
    } else {
      console.log('✅ No suspicious patterns found\n');
    }
    
    // Summary
    console.log('📋 Summary:');
    console.log(`   🔴 Critical Issues (localStorage/sessionStorage): ${this.results.localStorage.length + this.results.sessionStorage.length}`);
    console.log(`   🟡 Potential Issues (hardcoded data): ${this.results.hardcodedData.length}`);
    console.log(`   🔵 Review Needed (patterns): ${this.results.nonApiCalls.length + this.results.suspiciousPatterns.length}`);
    
    const criticalIssues = this.results.localStorage.length + this.results.sessionStorage.length;
    if (criticalIssues === 0) {
      console.log('\n🎉 No critical localStorage/sessionStorage issues found!');
      console.log('✅ MySQL migration appears to be complete');
    } else {
      console.log(`\n⚠️ ${criticalIssues} critical issues need attention`);
      console.log('❌ localStorage references still exist in the codebase');
    }
    
    return this.results;
  }

  async saveDetailedReport() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportFile = path.join(__dirname, `localstorage-scan-${timestamp}.json`);
    
    await fs.writeFile(reportFile, JSON.stringify(this.results, null, 2));
    console.log(`\n💾 Detailed report saved: ${reportFile}`);
    
    return reportFile;
  }
}

async function main() {
  console.log('🚀 Starting LocalStorage Reference Detection...\n');
  
  const detector = new LocalStorageDetector();
  const projectRoot = path.resolve(__dirname, '..');
  
  await detector.scanDirectory(projectRoot);
  const results = detector.generateReport();
  await detector.saveDetailedReport();
  
  console.log('\n✅ Scan completed');
}

// Export for use in other scripts
module.exports = LocalStorageDetector;

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
