import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { X, Edit, Eye, Save, Plus } from 'lucide-react';

interface SimpleTestModalProps {
  onClose: () => void;
}

/**
 * Simple test modal to verify modal functionality
 */
const SimpleTestModal: React.FC<SimpleTestModalProps> = ({ onClose }) => {
  const { language } = useTranslation();
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [testData, setTestData] = useState({
    name: 'Test System',
    description: 'Test Description',
    price: 100
  });

  const handleEdit = () => {
    setShowEditModal(true);
  };

  const handlePreview = () => {
    setShowPreviewModal(true);
  };

  const handleSave = () => {
    console.log('Saving data:', testData);
    setShowEditModal(false);
    alert('Data saved successfully!');
  };

  return (
    <>
      {/* Main Test Modal */}
      <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-primary rounded-xl max-w-2xl w-full border border-accent/30">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-accent/20">
            <h2 className="text-xl font-bold text-white">
              {language === 'ar' ? 'اختبار الخصائص' : 'Feature Test'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Body */}
          <div className="p-6 space-y-6">
            <div className="bg-background/50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-4">
                {language === 'ar' ? 'بيانات الاختبار' : 'Test Data'}
              </h3>
              <div className="space-y-2">
                <p className="text-gray-300">
                  <span className="font-medium">{language === 'ar' ? 'الاسم:' : 'Name:'}</span> {testData.name}
                </p>
                <p className="text-gray-300">
                  <span className="font-medium">{language === 'ar' ? 'الوصف:' : 'Description:'}</span> {testData.description}
                </p>
                <p className="text-gray-300">
                  <span className="font-medium">{language === 'ar' ? 'السعر:' : 'Price:'}</span> ${testData.price}
                </p>
              </div>
            </div>

            <div className="flex space-x-4 rtl:space-x-reverse">
              <button
                onClick={handleEdit}
                className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded-lg hover:bg-blue-500/30 transition-colors"
              >
                <Edit className="w-4 h-4" />
                <span>{language === 'ar' ? 'تعديل' : 'Edit'}</span>
              </button>

              <button
                onClick={handlePreview}
                className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-green-500/20 text-green-400 border border-green-500/30 rounded-lg hover:bg-green-500/30 transition-colors"
              >
                <Eye className="w-4 h-4" />
                <span>{language === 'ar' ? 'معاينة' : 'Preview'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Edit Modal */}
      {showEditModal && (
        <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-60 flex items-center justify-center p-4">
          <div className="bg-primary rounded-xl max-w-lg w-full border border-accent/30">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-accent/20">
              <h3 className="text-lg font-bold text-white">
                {language === 'ar' ? 'تعديل البيانات' : 'Edit Data'}
              </h3>
              <button
                onClick={() => setShowEditModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Body */}
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'الاسم' : 'Name'}
                </label>
                <input
                  type="text"
                  value={testData.name}
                  onChange={(e) => setTestData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 bg-background border border-accent/30 rounded-lg text-white focus:border-accent focus:outline-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'الوصف' : 'Description'}
                </label>
                <textarea
                  value={testData.description}
                  onChange={(e) => setTestData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 bg-background border border-accent/30 rounded-lg text-white focus:border-accent focus:outline-none resize-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'السعر' : 'Price'}
                </label>
                <input
                  type="number"
                  value={testData.price}
                  onChange={(e) => setTestData(prev => ({ ...prev, price: Number(e.target.value) }))}
                  className="w-full px-3 py-2 bg-background border border-accent/30 rounded-lg text-white focus:border-accent focus:outline-none"
                />
              </div>
            </div>

            {/* Footer */}
            <div className="flex justify-end space-x-3 rtl:space-x-reverse p-6 border-t border-accent/20">
              <button
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </button>
              <button
                onClick={handleSave}
                className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-gradient-to-r from-secondary to-accent text-primary rounded-lg hover:from-secondary/90 hover:to-accent/90 transition-colors"
              >
                <Save className="w-4 h-4" />
                <span>{language === 'ar' ? 'حفظ' : 'Save'}</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {showPreviewModal && (
        <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-60 flex items-center justify-center p-4">
          <div className="bg-primary rounded-xl max-w-2xl w-full border border-accent/30">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-accent/20">
              <h3 className="text-lg font-bold text-white">
                {language === 'ar' ? 'معاينة البيانات' : 'Data Preview'}
              </h3>
              <button
                onClick={() => setShowPreviewModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Body */}
            <div className="p-6">
              <div className="bg-gradient-to-br from-secondary/10 to-accent/10 rounded-lg p-6 border border-secondary/20">
                <h4 className="text-2xl font-bold text-white mb-4">{testData.name}</h4>
                <p className="text-gray-300 mb-4">{testData.description}</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">{language === 'ar' ? 'السعر:' : 'Price:'}</span>
                  <span className="text-2xl font-bold text-secondary">${testData.price}</span>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex justify-end space-x-3 rtl:space-x-reverse p-6 border-t border-accent/20">
              <button
                onClick={() => setShowPreviewModal(false)}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                {language === 'ar' ? 'إغلاق' : 'Close'}
              </button>
              <button
                onClick={() => {
                  setShowPreviewModal(false);
                  setShowEditModal(true);
                }}
                className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-500/20 text-blue-400 border border-blue-500/30 rounded-lg hover:bg-blue-500/30 transition-colors"
              >
                <Edit className="w-4 h-4" />
                <span>{language === 'ar' ? 'تعديل' : 'Edit'}</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SimpleTestModal;
