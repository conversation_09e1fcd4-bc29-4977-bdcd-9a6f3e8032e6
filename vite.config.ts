import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  // Base path for deployment - use relative paths for WAMP compatibility
  base: process.env.NODE_ENV === 'production' ? './' : '/',
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  build: {
    // Enable code splitting
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom'],
          'ui-vendor': ['lucide-react'],

          // Feature chunks - using existing components
          'admin': [
            './src/components/AdminDashboard',
            './src/components/UserManagement',
            './src/components/admin/TechnicalSystemsManager',
            './src/components/admin/TechnicalServicesManager',
            './src/components/admin/EnhancedPremiumManager'
          ],
          'user': [
            './src/components/UserDashboard',
            './src/components/SimpleUserDashboard'
          ],
          'cart': [
            './src/components/cart/ShoppingCartManager'
          ]
        }
      }
    },
    // Optimize chunk size
    chunkSizeWarningLimit: 1000,
    // Enable source maps for production debugging
    sourcemap: process.env.NODE_ENV === 'development',
    // Minification options
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: process.env.NODE_ENV === 'production'
      }
    }
  },
  // Performance optimizations
  server: {
    // Enable HTTP/2
    https: false,
    // Allow external connections (for ngrok and TestSprite)
    host: '0.0.0.0',
    port: 5173,
    // Preload modules
    preTransformRequests: true,
    // Allow all hosts for ngrok compatibility
    allowedHosts: [
      'localhost',
      '127.0.0.1',
      '0.0.0.0',
      '.ngrok-free.app',
      '.ngrok.io',
      '70f354611634.ngrok-free.app'
    ],
    // Proxy API requests to backend server
    proxy: {
      '/api': {
        target: process.env.VITE_API_BASE_URL || 'http://localhost:3001',
        changeOrigin: true,
        secure: false,
        headers: {
          'ngrok-skip-browser-warning': 'true'
        }
      }
    }
  }
});
