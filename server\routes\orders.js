/**
 * Orders Management Routes
 * 
 * Handles order operations:
 * - Create new orders
 * - Get order details and history
 * - Update order status
 * - Process payments
 * - Manage subscriptions
 */

const express = require('express');
const { executeQuery, generateUUID, executeTransaction } = require('../config/database');
const { verifyToken, requireOwnerOrAdmin } = require('../middleware/auth');
const { 
  asyncHandler, 
  validationError, 
  notFoundError,
  forbiddenError 
} = require('../middleware/errorHandler');
const { logUserAction } = require('../middleware/logger');

const router = express.Router();

/**
 * Generate unique order number
 */
function generateOrderNumber() {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  
  return `ORD-${year}${month}${day}-${random}`;
}

/**
 * @route   POST /api/orders
 * @desc    Create a new order
 * @access  Private
 */
router.post('/', verifyToken, asyncHandler(async (req, res) => {
  const {
    order_type,
    order_category = 'standard',
    item_id,
    quantity = 1,
    notes_ar,
    notes_en,
    customer_requirements,
    selected_systems = [],
    selected_services = [],
    subscription_type = 'none',
    maintenance_included = false,
    installation_included = false,
    support_level = 'basic'
  } = req.body;
  const userId = req.user.id;
  
  // Validation
  if (!order_type || !item_id) {
    throw validationError('Order type and item ID are required');
  }
  
  const validOrderTypes = ['system_service', 'technical_service', 'premium_content', 'premium_package'];
  if (!validOrderTypes.includes(order_type)) {
    throw validationError('Invalid order type');
  }
  
  if (quantity < 1 || quantity > 10) {
    throw validationError('Quantity must be between 1 and 10');
  }
  
  // Enhanced order processing with complex pricing logic
  let item, basePrice, addonsPrice, subscriptionPrice, maintenancePrice, totalPrice, discountAmount, finalPrice;
  let orderDetails = {};

  if (item_id.startsWith('custom-order-') || item_id.startsWith('test-debug-')) {
    // Handle custom orders
    item = {
      id: item_id,
      name_ar: 'طلب نظام مخصص',
      name_en: 'Custom System Request',
      price: 500,
      status: 'active'
    };
    basePrice = 500;
    addonsPrice = 0;
    subscriptionPrice = 0;
    maintenancePrice = 0;
    totalPrice = basePrice * quantity;
    discountAmount = 0;
    finalPrice = totalPrice - discountAmount;
  } else {
    // Get base item details
    let itemQuery;
    switch (order_type) {
      case 'system_service':
        itemQuery = 'SELECT id, name_ar, name_en, price, status FROM system_services WHERE id = ?';
        break;
      case 'technical_service':
        itemQuery = 'SELECT id, name_ar, name_en, price, status FROM technical_services WHERE id = ?';
        break;
      case 'premium_content':
      case 'premium_package':
        itemQuery = 'SELECT id, title_ar as name_ar, title_en as name_en, price, status FROM premium_content WHERE id = ?';
        break;
      default:
        throw validationError('Invalid order type');
    }

    const { rows: items } = await executeQuery(itemQuery, [item_id]);
    if (items.length === 0) {
      throw notFoundError('Item not found');
    }

    item = items[0];
    if (item.status !== 'active') {
      throw validationError('Item is not available for purchase');
    }

    // Calculate base price
    basePrice = parseFloat(item.price) * quantity;

    // Calculate add-ons pricing for Premium Edition orders
    addonsPrice = 0;
    if ((order_type === 'premium_package' || order_category === 'premium_custom') &&
        (selected_systems.length > 0 || selected_services.length > 0)) {

      // Calculate systems pricing
      if (selected_systems.length > 0) {
        const systemIds = selected_systems.map(id => `'${id}'`).join(',');
        const { rows: systems } = await executeQuery(`
          SELECT s.id, s.price, COALESCE(p.premium_price, s.price) as premium_price
          FROM system_services s
          LEFT JOIN premium_system_pricing p ON s.id = p.system_id
          WHERE s.id IN (${systemIds}) AND s.status = 'active'
        `);

        addonsPrice += systems.reduce((sum, sys) => sum + parseFloat(sys.premium_price || sys.price), 0);
      }

      // Calculate services pricing
      if (selected_services.length > 0) {
        const serviceIds = selected_services.map(id => `'${id}'`).join(',');
        const { rows: services } = await executeQuery(`
          SELECT s.id, s.price, COALESCE(p.premium_price, s.price) as premium_price
          FROM technical_services s
          LEFT JOIN premium_service_pricing p ON s.id = p.service_id
          WHERE s.id IN (${serviceIds}) AND s.status = 'active'
        `);

        addonsPrice += services.reduce((sum, srv) => sum + parseFloat(srv.premium_price || srv.price), 0);
      }

      // Store detailed order information
      orderDetails = {
        is_premium_edition: true,
        base_item: item,
        selected_systems: selected_systems,
        selected_services: selected_services,
        pricing_breakdown: {
          base_price: basePrice,
          addons_price: addonsPrice,
          subscription_price: subscriptionPrice,
          maintenance_price: maintenancePrice
        }
      };
    }

    // Calculate subscription pricing (if applicable)
    subscriptionPrice = 0;
    if (subscription_type !== 'none') {
      // TODO: Implement subscription pricing logic
      subscriptionPrice = 0;
    }

    // Calculate maintenance pricing
    maintenancePrice = 0;
    if (maintenance_included) {
      maintenancePrice = basePrice * 0.1; // 10% of base price for maintenance
    }

    totalPrice = basePrice + addonsPrice + subscriptionPrice + maintenancePrice;
    discountAmount = 0; // TODO: Implement discount logic
    finalPrice = totalPrice - discountAmount;
  }
  
  // Create enhanced order with comprehensive data
  const orderId = generateUUID();
  const orderNumber = generateOrderNumber();

  const orderQueries = [
    {
      sql: `INSERT INTO orders (
        id, user_id, order_number, order_type, order_category, item_id,
        item_name_ar, item_name_en, quantity,
        unit_price, base_price, addons_price, subscription_price, maintenance_price,
        total_price, discount_amount, final_price,
        subscription_type, maintenance_included, installation_included, support_level,
        order_details, selected_addons, customer_requirements,
        notes_ar, notes_en, status, payment_status, priority,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', 'pending', 'medium', NOW(), NOW())`,
      params: [
        orderId,
        userId,
        orderNumber,
        order_type,
        order_category,
        item_id,
        item.name_ar || (order_type === 'premium_package' ? 'النسخة المميزة' : 'خدمة مخصصة'),
        item.name_en || (order_type === 'premium_package' ? 'Premium Edition' : 'Custom Service'),
        quantity,
        basePrice / quantity, // unit_price
        basePrice,
        addonsPrice,
        subscriptionPrice,
        maintenancePrice,
        totalPrice,
        discountAmount,
        finalPrice,
        subscription_type,
        maintenance_included ? 1 : 0,
        installation_included ? 1 : 0,
        support_level,
        JSON.stringify(orderDetails),
        JSON.stringify({ systems: selected_systems, services: selected_services }),
        customer_requirements || null,
        notes_ar || null,
        notes_en || null
      ]
    }
  ];
  
  const results = await executeTransaction(orderQueries);
  // orderId and orderNumber are already declared above
  
  // Get the created order
  const { rows: orders } = await executeQuery(
    'SELECT * FROM orders WHERE id = ?',
    [orderId]
  );
  
  const order = orders[0];
  
  // Send notification to admin
  await executeQuery(`
    INSERT INTO inbox_messages (
      id, user_id, sender_type, subject_ar, subject_en, 
      message_ar, message_en, message_type, priority, created_at
    ) VALUES (?, ?, 'system', ?, ?, ?, ?, 'order_update', 'normal', NOW())
  `, [
    generateUUID(),
    userId,
    'طلب جديد تم إنشاؤه',
    'New Order Created',
    `تم إنشاء طلب جديد برقم ${orderNumber}`,
    `A new order has been created with number ${orderNumber}`
  ]);
  
  // Log order creation
  await logUserAction('order_created', 'order', orderId, {
    orderNumber,
    orderType: order_type,
    itemName: item.name_en,
    finalPrice,
    quantity
  }, req);
  
  res.status(201).json({
    success: true,
    message: 'Order created successfully',
    data: {
      id: orderId,
      order_number: orderNumber,
      order
    }
  });
}));

/**
 * @route   GET /api/orders
 * @desc    Get user orders with pagination
 * @access  Private
 */
router.get('/', verifyToken, asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, status, order_type } = req.query;
  const userId = req.user.id;
  const offset = (page - 1) * limit;

  // Log for debugging if needed
  // console.log('📋 GET /api/orders - User:', req.user.email, 'ID:', userId);

  // Build query conditions
  let whereConditions = ['user_id = ?'];
  let queryParams = [userId];
  
  if (status) {
    whereConditions.push('status = ?');
    queryParams.push(status);
  }
  
  if (order_type) {
    whereConditions.push('order_type = ?');
    queryParams.push(order_type);
  }
  
  const whereClause = whereConditions.join(' AND ');
  
  // Get orders with enhanced data
  const { rows: orders } = await executeQuery(`
    SELECT
      o.*,
      u.username,
      u.email,
      u.full_name
    FROM orders o
    JOIN users u ON o.user_id = u.id
    WHERE ${whereClause}
    ORDER BY o.created_at DESC
    LIMIT ? OFFSET ?
  `, [...queryParams, parseInt(limit), offset]);

  // Get total count
  const { rows: countResult } = await executeQuery(`
    SELECT COUNT(*) as total
    FROM orders o
    JOIN users u ON o.user_id = u.id
    WHERE ${whereClause}
  `, queryParams);

  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);

  // Log for debugging if needed
  // console.log(`✅ Found ${orders.length} orders (${total} total) for user ${req.user.email}`);
  
  res.json({
    success: true,
    data: {
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @route   GET /api/orders/:id
 * @desc    Get order details
 * @access  Private (owner or admin)
 */
router.get('/:id', verifyToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // Get order details
  const { rows: orders } = await executeQuery(`
    SELECT o.*, u.username, u.email, u.full_name
    FROM orders o
    JOIN users u ON o.user_id = u.id
    WHERE o.id = ?
  `, [id]);
  
  if (orders.length === 0) {
    throw notFoundError('Order not found');
  }
  
  const order = orders[0];
  
  // Check if user can access this order
  if (req.user.role !== 'admin' && order.user_id !== req.user.id) {
    throw forbiddenError('Access denied');
  }
  
  // Get order activity logs
  const { rows: activities } = await executeQuery(`
    SELECT action, details, created_at
    FROM activity_logs
    WHERE entity_type = 'order' AND entity_id = ?
    ORDER BY created_at DESC
  `, [id]);
  
  res.json({
    success: true,
    data: {
      order,
      activities
    }
  });
}));

/**
 * @route   PUT /api/orders/:id/status
 * @desc    Update order status with workflow validation (admin only)
 * @access  Private (admin only)
 */
router.put('/:id/status', verifyToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { status, admin_notes, priority, estimated_completion } = req.body;

  // Only admin can update order status
  if (req.user.role !== 'admin') {
    throw forbiddenError('Admin access required');
  }

  // Enhanced validation with workflow support
  const validStatuses = ['pending', 'confirmed', 'in_progress', 'testing', 'completed', 'cancelled', 'refunded', 'on_hold', 'expired'];
  if (!validStatuses.includes(status)) {
    throw validationError('Invalid status');
  }

  const validPriorities = ['low', 'medium', 'high', 'urgent'];
  if (priority && !validPriorities.includes(priority)) {
    throw validationError('Invalid priority');
  }
  
  // Get current order with full details
  const { rows: orders } = await executeQuery(
    'SELECT * FROM orders WHERE id = ?',
    [id]
  );

  if (orders.length === 0) {
    throw notFoundError('Order not found');
  }

  const order = orders[0];
  const oldStatus = order.status;

  // Validate status transition using workflow rules
  const statusTransitions = {
    pending: ['confirmed', 'cancelled', 'expired'],
    confirmed: ['in_progress', 'on_hold', 'cancelled'],
    in_progress: ['testing', 'completed', 'on_hold', 'cancelled'],
    testing: ['completed', 'in_progress', 'on_hold'],
    completed: ['refunded'],
    cancelled: [],
    refunded: [],
    on_hold: ['confirmed', 'in_progress', 'cancelled'],
    expired: ['confirmed']
  };

  const allowedTransitions = statusTransitions[oldStatus] || [];
  if (status !== oldStatus && !allowedTransitions.includes(status)) {
    throw validationError(`Invalid status transition from ${oldStatus} to ${status}`);
  }

  // Business rule validations
  if (status === 'completed' && order.payment_status !== 'paid') {
    throw validationError('Cannot complete order without payment confirmation');
  }

  if (status === 'refunded' && order.payment_status !== 'paid') {
    throw validationError('Cannot refund unpaid orders');
  }
  
  // Calculate progress percentage based on status
  const progressMap = {
    pending: 0,
    confirmed: 10,
    in_progress: 50,
    testing: 80,
    completed: 100,
    cancelled: 0,
    refunded: 0,
    on_hold: 0,
    expired: 0
  };

  const progressPercentage = progressMap[status] || 0;

  // Prepare enhanced update query
  let updateSql = 'UPDATE orders SET status = ?, progress_percentage = ?, updated_at = NOW()';
  let updateParams = [status, progressPercentage];

  if (admin_notes) {
    updateSql += ', admin_notes = ?';
    updateParams.push(admin_notes);
  }

  if (priority) {
    updateSql += ', priority = ?';
    updateParams.push(priority);
  }

  if (estimated_completion) {
    updateSql += ', estimated_completion = ?';
    updateParams.push(estimated_completion);
  }

  // Set timestamp fields based on status
  if (status === 'confirmed' && oldStatus === 'pending') {
    updateSql += ', confirmed_at = NOW()';
  } else if (status === 'in_progress' && !order.started_at) {
    updateSql += ', started_at = NOW()';
  } else if (status === 'completed' && !order.completed_at) {
    updateSql += ', completed_at = NOW()';
  }

  updateSql += ' WHERE id = ?';
  updateParams.push(id);

  const updateQueries = [
    {
      sql: updateSql,
      params: updateParams
    }
  ];

  // Record status change in history (if table exists)
  try {
    updateQueries.push({
      sql: `INSERT INTO order_status_history (
        id, order_id, old_status, new_status, changed_by, change_reason, notes, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`,
      params: [
        generateUUID(),
        id,
        oldStatus,
        status,
        req.user.id,
        'Admin status update',
        admin_notes || null
      ]
    });
  } catch (error) {
    // History table might not exist yet, continue without it
    console.warn('Order status history table not available:', error.message);
  }
  
  // If order is completed, create user service record
  if (status === 'completed' && oldStatus !== 'completed') {
    updateQueries.push({
      sql: `INSERT INTO user_services (
        id, user_id, service_id, service_type, order_id, status, purchase_date, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW(), NOW())`,
      params: [
        generateUUID(),
        order.user_id,
        order.item_id,
        order.order_type,
        id
      ]
    });
    
    // Set completion date
    updateQueries.push({
      sql: 'UPDATE orders SET completion_date = NOW() WHERE id = ?',
      params: [id]
    });
  }
  
  await executeTransaction(updateQueries);
  
  // Send notification to user
  const statusMessages = {
    confirmed: { ar: 'تم تأكيد طلبك', en: 'Your order has been confirmed' },
    in_progress: { ar: 'طلبك قيد التنفيذ', en: 'Your order is in progress' },
    completed: { ar: 'تم إكمال طلبك', en: 'Your order has been completed' },
    cancelled: { ar: 'تم إلغاء طلبك', en: 'Your order has been cancelled' },
    refunded: { ar: 'تم استرداد طلبك', en: 'Your order has been refunded' }
  };
  
  if (statusMessages[status]) {
    await executeQuery(`
      INSERT INTO inbox_messages (
        id, user_id, sender_type, subject_ar, subject_en, 
        message_ar, message_en, message_type, priority, created_at
      ) VALUES (?, ?, 'admin', ?, ?, ?, ?, 'order_update', 'normal', NOW())
    `, [
      generateUUID(),
      order.user_id,
      statusMessages[status].ar,
      statusMessages[status].en,
      `${statusMessages[status].ar} - رقم الطلب: ${order.order_number}`,
      `${statusMessages[status].en} - Order Number: ${order.order_number}`
    ]);
  }
  
  // Log status change
  await logUserAction('order_status_updated', 'order', id, {
    oldStatus,
    newStatus: status,
    adminNotes: admin_notes,
    orderNumber: order.order_number
  }, req);
  
  res.json({
    success: true,
    message: 'Order status updated successfully',
    data: {
      orderId: id,
      oldStatus,
      newStatus: status
    }
  });
}));

/**
 * @route   POST /api/orders/:id/payment
 * @desc    Process order payment
 * @access  Private (owner or admin)
 */
router.post('/:id/payment', verifyToken, requireOwnerOrAdmin('userId'), asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { payment_method, payment_reference } = req.body;
  
  // Validation
  if (!payment_method) {
    throw validationError('Payment method is required');
  }
  
  // Get order
  const { rows: orders } = await executeQuery(
    'SELECT * FROM orders WHERE id = ?',
    [id]
  );
  
  if (orders.length === 0) {
    throw notFoundError('Order not found');
  }
  
  const order = orders[0];
  
  if (order.payment_status === 'paid') {
    throw validationError('Order is already paid');
  }
  
  // Update payment status
  await executeQuery(`
    UPDATE orders 
    SET payment_status = 'paid', payment_method = ?, payment_reference = ?, updated_at = NOW()
    WHERE id = ?
  `, [payment_method, payment_reference || null, id]);
  
  // Log payment
  await logUserAction('payment_processed', 'order', id, {
    paymentMethod: payment_method,
    paymentReference: payment_reference,
    amount: order.final_price,
    orderNumber: order.order_number
  }, req);
  
  res.json({
    success: true,
    message: 'Payment processed successfully',
    data: {
      orderId: id,
      paymentStatus: 'paid',
      paymentMethod: payment_method
    }
  });
}));

module.exports = router;
