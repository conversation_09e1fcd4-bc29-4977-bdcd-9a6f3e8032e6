/**
 * Test Admin API Endpoints
 * 
 * This script tests the admin endpoints to ensure they return data correctly
 */

const axios = require('axios');
require('dotenv').config();

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';

// Test credentials (you should have an admin user)
const TEST_ADMIN = {
  email: '<EMAIL>',
  password: 'admin123456'
};

async function testAdminEndpoints() {
  console.log('🧪 Testing Admin API Endpoints...\n');
  
  try {
    // Step 1: Login as admin
    console.log('1️⃣ Logging in as admin...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, TEST_ADMIN);
    
    if (!loginResponse.data.success) {
      throw new Error('Login failed: ' + loginResponse.data.error);
    }
    
    const token = loginResponse.data.data.token;
    console.log('✅ Admin login successful');
    
    // Step 2: Test systems admin endpoint
    console.log('\n2️⃣ Testing /api/systems/admin...');
    try {
      const systemsResponse = await axios.get(`${BASE_URL}/api/systems/admin`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log('✅ Systems admin endpoint working');
      console.log(`📊 Found ${systemsResponse.data.length} systems`);
      
      if (systemsResponse.data.length > 0) {
        console.log('📋 Sample system:', {
          id: systemsResponse.data[0].id,
          name_ar: systemsResponse.data[0].name_ar,
          name_en: systemsResponse.data[0].name_en
        });
      }
    } catch (error) {
      console.error('❌ Systems admin endpoint failed:', error.response?.data || error.message);
    }
    
    // Step 3: Test services admin endpoint
    console.log('\n3️⃣ Testing /api/services/admin/technical...');
    try {
      const servicesResponse = await axios.get(`${BASE_URL}/api/services/admin/technical`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log('✅ Services admin endpoint working');
      console.log(`📊 Found ${servicesResponse.data.length} services`);
      
      if (servicesResponse.data.length > 0) {
        console.log('📋 Sample service:', {
          id: servicesResponse.data[0].id,
          name_ar: servicesResponse.data[0].name_ar,
          name_en: servicesResponse.data[0].name_en
        });
      }
    } catch (error) {
      console.error('❌ Services admin endpoint failed:', error.response?.data || error.message);
    }
    
    // Step 4: Test public endpoints for comparison
    console.log('\n4️⃣ Testing public endpoints...');
    
    try {
      const publicSystemsResponse = await axios.get(`${BASE_URL}/api/systems`);
      console.log('✅ Public systems endpoint working');
      console.log(`📊 Found ${publicSystemsResponse.data.data?.systems?.length || 0} public systems`);
    } catch (error) {
      console.error('❌ Public systems endpoint failed:', error.response?.data || error.message);
    }
    
    try {
      const publicServicesResponse = await axios.get(`${BASE_URL}/api/services/technical`);
      console.log('✅ Public services endpoint working');
      console.log(`📊 Found ${publicServicesResponse.data.data?.services?.length || 0} public services`);
    } catch (error) {
      console.error('❌ Public services endpoint failed:', error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run tests
if (require.main === module) {
  testAdminEndpoints();
}

module.exports = { testAdminEndpoints };