/**
 * Comprehensive Layout Fixes Testing Script
 * Tests all the systematic fixes implemented for the Khanfashariya website
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class LayoutFixTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = {
      ctaButtonNavigation: false,
      starsPositioning: false,
      socialMediaPositioning: false,
      heroFeatureIcons: false,
      buttonTextAnimations: false,
      languageSwitching: false,
      responsiveDesign: false,
      overallScore: 0
    };
  }

  async initialize() {
    console.log('🚀 Initializing Layout Fix Testing...');
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    this.page = await this.browser.newPage();
    await this.page.goto('http://localhost:5173', { waitUntil: 'networkidle0' });
  }

  async testCTAButtonNavigation() {
    console.log('🔍 Testing CTA Button Navigation...');
    try {
      // Test if CTA button scrolls to premium section
      const ctaButton = await this.page.$('button:has-text("استكشف عالم التميز"), button:has-text("Explore Excellence")');
      if (ctaButton) {
        await ctaButton.click();
        await this.page.waitForTimeout(1000);
        
        // Check if premium section is in view
        const premiumSection = await this.page.$('#premium');
        if (premiumSection) {
          const isInView = await this.page.evaluate((element) => {
            const rect = element.getBoundingClientRect();
            return rect.top >= 0 && rect.top <= window.innerHeight;
          }, premiumSection);
          
          this.testResults.ctaButtonNavigation = isInView;
          console.log(`   ✅ CTA Button Navigation: ${isInView ? 'PASS' : 'FAIL'}`);
        }
      }
    } catch (error) {
      console.log(`   ❌ CTA Button Navigation: ERROR - ${error.message}`);
    }
  }

  async testStarsPositioning() {
    console.log('🔍 Testing Stars Positioning...');
    try {
      const starRatings = await this.page.$$('.star-rating');
      let allCentered = true;
      
      for (const rating of starRatings) {
        const styles = await this.page.evaluate((el) => {
          const computed = window.getComputedStyle(el);
          return {
            justifyContent: computed.justifyContent,
            display: computed.display
          };
        }, rating);
        
        if (styles.justifyContent !== 'center') {
          allCentered = false;
          break;
        }
      }
      
      this.testResults.starsPositioning = allCentered;
      console.log(`   ✅ Stars Positioning: ${allCentered ? 'PASS' : 'FAIL'}`);
    } catch (error) {
      console.log(`   ❌ Stars Positioning: ERROR - ${error.message}`);
    }
  }

  async testSocialMediaPositioning() {
    console.log('🔍 Testing Social Media Icons Positioning...');
    try {
      const socialContainers = await this.page.$$('.social-icons-container');
      let correctPositioning = true;
      
      for (const container of socialContainers) {
        const hasDirectionalClass = await this.page.evaluate((el) => {
          return el.classList.contains('social-icons-directional') || 
                 el.classList.contains('social-icons-center');
        }, container);
        
        if (!hasDirectionalClass) {
          correctPositioning = false;
          break;
        }
      }
      
      this.testResults.socialMediaPositioning = correctPositioning;
      console.log(`   ✅ Social Media Positioning: ${correctPositioning ? 'PASS' : 'FAIL'}`);
    } catch (error) {
      console.log(`   ❌ Social Media Positioning: ERROR - ${error.message}`);
    }
  }

  async testHeroFeatureIcons() {
    console.log('🔍 Testing Hero Feature Icons Layout...');
    try {
      const heroFeatures = await this.page.$$('.hero-feature-item');
      let allProperlyAligned = true;
      
      for (const feature of heroFeatures) {
        const styles = await this.page.evaluate((el) => {
          const computed = window.getComputedStyle(el);
          return {
            display: computed.display,
            alignItems: computed.alignItems,
            gap: computed.gap
          };
        }, feature);
        
        if (styles.display !== 'flex' || styles.alignItems !== 'center') {
          allProperlyAligned = false;
          break;
        }
      }
      
      this.testResults.heroFeatureIcons = allProperlyAligned;
      console.log(`   ✅ Hero Feature Icons: ${allProperlyAligned ? 'PASS' : 'FAIL'}`);
    } catch (error) {
      console.log(`   ❌ Hero Feature Icons: ERROR - ${error.message}`);
    }
  }

  async testButtonTextAnimations() {
    console.log('🔍 Testing Button Text Animations...');
    try {
      const navButtons = await this.page.$$('.nav-button');
      let animationsWorking = true;
      
      if (navButtons.length > 0) {
        const button = navButtons[0];
        
        // Hover over button and check for animation
        await button.hover();
        await this.page.waitForTimeout(500);
        
        const hasAnimation = await this.page.evaluate((el) => {
          const textElement = el.querySelector('.nav-button-text');
          if (textElement) {
            const computed = window.getComputedStyle(textElement);
            return computed.animationName !== 'none';
          }
          return false;
        }, button);
        
        animationsWorking = hasAnimation;
      }
      
      this.testResults.buttonTextAnimations = animationsWorking;
      console.log(`   ✅ Button Text Animations: ${animationsWorking ? 'PASS' : 'FAIL'}`);
    } catch (error) {
      console.log(`   ❌ Button Text Animations: ERROR - ${error.message}`);
    }
  }

  async testLanguageSwitching() {
    console.log('🔍 Testing Language Switching...');
    try {
      // Get current language
      const currentLang = await this.page.evaluate(() => document.documentElement.lang);
      
      // Find and click language toggle
      const langButton = await this.page.$('button:has-text("English"), button:has-text("العربية")');
      if (langButton) {
        await langButton.click();
        await this.page.waitForTimeout(1000);
        
        // Check if language changed
        const newLang = await this.page.evaluate(() => document.documentElement.lang);
        const langChanged = currentLang !== newLang;
        
        // Check if layout corruption class is properly handled
        const hasTransitionClass = await this.page.evaluate(() => 
          document.body.classList.contains('language-switching')
        );
        
        this.testResults.languageSwitching = langChanged && !hasTransitionClass;
        console.log(`   ✅ Language Switching: ${this.testResults.languageSwitching ? 'PASS' : 'FAIL'}`);
      }
    } catch (error) {
      console.log(`   ❌ Language Switching: ERROR - ${error.message}`);
    }
  }

  async testResponsiveDesign() {
    console.log('🔍 Testing Responsive Design...');
    try {
      const viewports = [
        { width: 375, height: 667 },  // Mobile
        { width: 768, height: 1024 }, // Tablet
        { width: 1920, height: 1080 } // Desktop
      ];
      
      let responsiveWorking = true;
      
      for (const viewport of viewports) {
        await this.page.setViewport(viewport);
        await this.page.waitForTimeout(500);
        
        // Check if hero features adapt properly
        const heroFeatures = await this.page.$$('.hero-feature-item');
        if (heroFeatures.length > 0) {
          const styles = await this.page.evaluate((el) => {
            const computed = window.getComputedStyle(el);
            return {
              padding: computed.padding,
              gap: computed.gap
            };
          }, heroFeatures[0]);
          
          // Basic check - ensure styles are applied
          if (!styles.padding || !styles.gap) {
            responsiveWorking = false;
            break;
          }
        }
      }
      
      this.testResults.responsiveDesign = responsiveWorking;
      console.log(`   ✅ Responsive Design: ${responsiveWorking ? 'PASS' : 'FAIL'}`);
    } catch (error) {
      console.log(`   ❌ Responsive Design: ERROR - ${error.message}`);
    }
  }

  calculateOverallScore() {
    const tests = Object.keys(this.testResults).filter(key => key !== 'overallScore');
    const passedTests = tests.filter(test => this.testResults[test]).length;
    this.testResults.overallScore = Math.round((passedTests / tests.length) * 100);
  }

  async generateReport() {
    this.calculateOverallScore();
    
    const report = {
      timestamp: new Date().toISOString(),
      testResults: this.testResults,
      summary: {
        totalTests: Object.keys(this.testResults).length - 1,
        passedTests: Object.values(this.testResults).filter(result => result === true).length,
        overallScore: this.testResults.overallScore
      }
    };
    
    const reportPath = path.join(__dirname, 'layout-fixes-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    Object.entries(this.testResults).forEach(([test, result]) => {
      if (test !== 'overallScore') {
        console.log(`${result ? '✅' : '❌'} ${test}: ${result ? 'PASS' : 'FAIL'}`);
      }
    });
    console.log(`\n🎯 Overall Score: ${this.testResults.overallScore}%`);
    console.log(`📄 Detailed report saved to: ${reportPath}`);
  }

  async runAllTests() {
    try {
      await this.initialize();
      
      await this.testCTAButtonNavigation();
      await this.testStarsPositioning();
      await this.testSocialMediaPositioning();
      await this.testHeroFeatureIcons();
      await this.testButtonTextAnimations();
      await this.testLanguageSwitching();
      await this.testResponsiveDesign();
      
      await this.generateReport();
    } catch (error) {
      console.error('❌ Testing failed:', error);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new LayoutFixTester();
  tester.runAllTests().catch(console.error);
}

module.exports = LayoutFixTester;
