import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuth } from '../hooks/useAuth';
import { getUserOrders } from '../lib/apiServices';
import { UserService } from '../lib/database';
import {
  Package,
  Clock,
  CheckCircle,
  XCircle,
  User,
  Settings,
  LogOut,
  Shield,
  Zap,
  Calendar,
  DollarSign,
  FileText,
  Download,
  Eye,
  Home,
  ArrowLeft,
  ShoppingCart
} from 'lucide-react';
import BackButton from './ui/BackButton';

interface UserDashboardProps {
  onLogout: () => void;
}

const UserDashboard: React.FC<UserDashboardProps> = ({ onLogout }) => {
  const { t, language } = useTranslation();
  const { userProfile, isAdmin } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [userServices, setUserServices] = useState<UserService[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (userProfile) {
      loadUserServices();
    }
  }, [userProfile]);

  const loadUserServices = async () => {
    if (!userProfile) return;
    
    try {
      const { data, error } = await getUserOrders(userProfile.id);
      if (error) {
        console.error('Error loading orders:', error);
      } else {
        // Convert orders to services format for compatibility
        const servicesFromOrders = (data || []).map(order => ({
          id: order.id,
          user_id: order.user_id,
          service_name: order.service_name || order.item_name_en,
          service_type: order.order_type,
          status: order.status,
          purchase_date: order.created_at,
          completion_date: order.updated_at,
          price: order.final_price,
          notes: order.notes
        }));
        setUserServices(servicesFromOrders);
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const { logout } = useAuth();
  
  const handleLogout = async () => {
    await logout();
    onLogout();
    window.location.reload();
  };

  const handleBackToSite = () => {
    onLogout();
  };

  const handleBrowseServices = () => {
    onLogout();
    setTimeout(() => {
      const servicesElement = document.getElementById('services');
      if (servicesElement) {
        servicesElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-400" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-blue-400" />;
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-400" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      active: language === 'ar' ? 'نشط' : 'Active',
      pending: language === 'ar' ? 'قيد الانتظار' : 'Pending',
      completed: language === 'ar' ? 'مكتمل' : 'Completed',
      cancelled: language === 'ar' ? 'ملغي' : 'Cancelled'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const tabs = [
    { id: 'overview', label: language === 'ar' ? 'نظرة عامة' : 'Overview', icon: User },
    { id: 'services', label: language === 'ar' ? 'خدماتي' : 'My Services', icon: Package },
    { id: 'profile', label: language === 'ar' ? 'الملف الشخصي' : 'Profile', icon: Settings }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            <div className="bg-gradient-to-r from-secondary/10 to-accent/10 rounded-xl p-6 border border-secondary/20">
              <h3 className="text-2xl font-bold text-white mb-4">
                {language === 'ar' ? `مرحباً، ${userProfile?.full_name || userProfile?.username}` : `Welcome, ${userProfile?.full_name || userProfile?.username}`}
              </h3>
              <p className="text-gray-300 mb-4">
                {language === 'ar' ? 'إليك نظرة سريعة على حسابك وخدماتك' : 'Here\'s a quick overview of your account and services'}
              </p>
              
              {/* Quick Actions */}
              <div className="flex flex-wrap gap-4">
                <button
                  onClick={handleBrowseServices}
                  className="bg-gradient-to-r from-secondary to-accent text-primary font-bold px-6 py-3 rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-secondary/25 flex items-center space-x-2 rtl:space-x-reverse"
                >
                  <ShoppingCart className="w-5 h-5" />
                  <span>{language === 'ar' ? 'تصفح الخدمات' : 'Browse Services'}</span>
                </button>
                <button
                  onClick={handleBackToSite}
                  className="bg-accent/20 hover:bg-accent/30 text-accent hover:text-white px-6 py-3 rounded-lg transition-all duration-300 flex items-center space-x-2 rtl:space-x-reverse border border-accent/30"
                >
                  <Home className="w-5 h-5" />
                  <span>{language === 'ar' ? 'العودة للموقع' : 'Back to Site'}</span>
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gradient-to-br from-primary/50 to-background/50 rounded-xl p-6 border border-accent/20">
                <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
                  <Package className="w-8 h-8 text-secondary" />
                  <h4 className="text-lg font-semibold text-white">
                    {language === 'ar' ? 'إجمالي الخدمات' : 'Total Services'}
                  </h4>
                </div>
                <p className="text-3xl font-bold text-secondary">{userServices.length}</p>
              </div>

              <div className="bg-gradient-to-br from-primary/50 to-background/50 rounded-xl p-6 border border-accent/20">
                <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
                  <CheckCircle className="w-8 h-8 text-green-400" />
                  <h4 className="text-lg font-semibold text-white">
                    {language === 'ar' ? 'الخدمات النشطة' : 'Active Services'}
                  </h4>
                </div>
                <p className="text-3xl font-bold text-green-400">
                  {userServices.filter(s => s.status === 'active').length}
                </p>
              </div>

              <div className="bg-gradient-to-br from-primary/50 to-background/50 rounded-xl p-6 border border-accent/20">
                <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
                  <DollarSign className="w-8 h-8 text-accent" />
                  <h4 className="text-lg font-semibold text-white">
                    {language === 'ar' ? 'إجمالي الإنفاق' : 'Total Spent'}
                  </h4>
                </div>
                <p className="text-3xl font-bold text-accent">
                  ${userServices.reduce((total, service) => total + service.price, 0)}
                </p>
              </div>
            </div>

            {/* Recent Services */}
            {userServices.length > 0 && (
              <div className="bg-gradient-to-br from-primary/50 to-background/50 rounded-xl p-6 border border-accent/20">
                <h4 className="text-xl font-bold text-white mb-4">
                  {language === 'ar' ? 'الخدمات الأخيرة' : 'Recent Services'}
                </h4>
                <div className="space-y-3">
                  {userServices.slice(0, 3).map((service) => (
                    <div key={service.id} className="flex items-center justify-between p-3 bg-primary/30 rounded-lg">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        {getStatusIcon(service.status)}
                        <div>
                          <p className="text-white font-medium">{service.service_name}</p>
                          <p className="text-gray-400 text-sm">{new Date(service.purchase_date).toLocaleDateString()}</p>
                        </div>
                      </div>
                      <span className="text-accent font-bold">${service.price}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case 'services':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-2xl font-bold text-white">
                {language === 'ar' ? 'خدماتي المشتراة' : 'My Purchased Services'}
              </h3>
              <button
                onClick={handleBrowseServices}
                className="bg-gradient-to-r from-secondary to-accent text-primary font-bold px-4 py-2 rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-secondary/25 flex items-center space-x-2 rtl:space-x-reverse text-sm"
              >
                <ShoppingCart className="w-4 h-4" />
                <span>{language === 'ar' ? 'تصفح المزيد' : 'Browse More'}</span>
              </button>
            </div>

            {loading ? (
              <div className="text-center py-8">
                <div className="w-8 h-8 border-2 border-secondary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-300">{language === 'ar' ? 'جاري التحميل...' : 'Loading...'}</p>
              </div>
            ) : userServices.length === 0 ? (
              <div className="text-center py-12 bg-gradient-to-br from-primary/50 to-background/50 rounded-xl border border-accent/20">
                <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h4 className="text-xl font-semibold text-white mb-2">
                  {language === 'ar' ? 'لا توجد خدمات' : 'No Services Yet'}
                </h4>
                <p className="text-gray-400 mb-6">
                  {language === 'ar' ? 'لم تقم بشراء أي خدمات بعد' : 'You haven\'t purchased any services yet'}
                </p>
                <button
                  onClick={handleBrowseServices}
                  className="bg-gradient-to-r from-secondary to-accent text-primary font-bold px-6 py-3 rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-secondary/25 flex items-center space-x-2 rtl:space-x-reverse mx-auto"
                >
                  <ShoppingCart className="w-5 h-5" />
                  <span>{language === 'ar' ? 'تصفح الخدمات' : 'Browse Services'}</span>
                </button>
              </div>
            ) : (
              <div className="grid gap-6">
                {userServices.map((service) => (
                  <div key={service.id} className="bg-gradient-to-br from-primary/50 to-background/50 rounded-xl p-6 border border-accent/20 hover:border-secondary/30 transition-all duration-300">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h4 className="text-xl font-semibold text-white mb-2">{service.service_name}</h4>
                        <p className="text-gray-400 text-sm">{service.service_type}</p>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        {getStatusIcon(service.status)}
                        <span className="text-sm font-medium text-white">{getStatusText(service.status)}</span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <DollarSign className="w-4 h-4 text-accent" />
                        <span className="text-sm text-gray-300">${service.price}</span>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Calendar className="w-4 h-4 text-secondary" />
                        <span className="text-sm text-gray-300">
                          {new Date(service.purchase_date).toLocaleDateString()}
                        </span>
                      </div>
                      {service.completion_date && (
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <span className="text-sm text-gray-300">
                            {new Date(service.completion_date).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>

                    {service.notes && (
                      <div className="bg-accent/10 rounded-lg p-3 mb-4">
                        <div className="flex items-start space-x-2 rtl:space-x-reverse">
                          <FileText className="w-4 h-4 text-accent mt-0.5" />
                          <p className="text-sm text-gray-300">{service.notes}</p>
                        </div>
                      </div>
                    )}

                    <div className="flex space-x-2 rtl:space-x-reverse">
                      <button className="flex items-center space-x-1 rtl:space-x-reverse px-3 py-2 bg-secondary/20 text-secondary rounded-lg hover:bg-secondary/30 transition-colors duration-200 text-sm">
                        <Eye className="w-4 h-4" />
                        <span>{language === 'ar' ? 'عرض التفاصيل' : 'View Details'}</span>
                      </button>
                      {service.status === 'completed' && (
                        <button className="flex items-center space-x-1 rtl:space-x-reverse px-3 py-2 bg-accent/20 text-accent rounded-lg hover:bg-accent/30 transition-colors duration-200 text-sm">
                          <Download className="w-4 h-4" />
                          <span>{language === 'ar' ? 'تحميل' : 'Download'}</span>
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );

      case 'profile':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white">
              {language === 'ar' ? 'الملف الشخصي' : 'Profile Settings'}
            </h3>

            <div className="bg-gradient-to-br from-primary/50 to-background/50 rounded-xl p-6 border border-accent/20">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-secondary mb-2">
                    {language === 'ar' ? 'الاسم الكامل' : 'Full Name'}
                  </label>
                  <input
                    type="text"
                    value={userProfile?.full_name || ''}
                    className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary mb-2">
                    {language === 'ar' ? 'اسم المستخدم' : 'Username'}
                  </label>
                  <input
                    type="text"
                    value={userProfile?.username || ''}
                    className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary mb-2">
                    {language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                  </label>
                  <input
                    type="email"
                    value={userProfile?.email || ''}
                    className="w-full px-4 py-3 bg-primary/50 border border-accent/30 rounded-lg text-white"
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-secondary mb-2">
                    {language === 'ar' ? 'الرتبة' : 'Role'}
                  </label>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    {userProfile?.role === 'admin' ? (
                      <Shield className="w-5 h-5 text-red-400" />
                    ) : (
                      <User className="w-5 h-5 text-accent" />
                    )}
                    <span className="text-white capitalize">{userProfile?.role}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="bg-gradient-to-br from-primary/50 to-background/50 rounded-xl p-6 border border-accent/20">
            <p className="text-gray-300">
              {language === 'ar' ? 'المحتوى قيد التطوير' : 'Content under development'}
            </p>
          </div>
        );
    }
  };

  if (!userProfile) {
    return (
      <div className="fixed inset-0 bg-background z-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-secondary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">{language === 'ar' ? 'جاري التحميل...' : 'Loading...'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-background z-50 overflow-auto">
      <div className="min-h-screen">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary to-background border-b border-accent/20 p-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="w-12 h-12 bg-gradient-to-br from-secondary to-accent rounded-lg flex items-center justify-center">
                <User className="w-6 h-6 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  {language === 'ar' ? 'لوحة التحكم' : 'Dashboard'}
                </h1>
                <p className="text-accent">
                  {userProfile.full_name || userProfile.username}
                  {isAdmin && (
                    <span className="ml-2 px-2 py-1 bg-red-500/20 text-red-400 rounded text-xs">
                      {language === 'ar' ? 'مدير' : 'Admin'}
                    </span>
                  )}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <BackButton onClick={handleBackToSite} variant="home" size="md" />
              
              <button
                onClick={handleLogout}
                className="bg-red-500/20 hover:bg-red-500/30 text-red-400 hover:text-red-300 px-4 py-2 rounded-lg flex items-center space-x-2 rtl:space-x-reverse transition-all duration-300 border border-red-500/30"
              >
                <LogOut className="w-4 h-4" />
                <span>{language === 'ar' ? 'تسجيل خروج' : 'Logout'}</span>
              </button>
            </div>
          </div>
        </div>

        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-gradient-to-b from-primary to-background border-r border-accent/20 min-h-screen p-6">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 rounded-lg transition-all duration-300 ${
                      activeTab === tab.id
                        ? 'bg-secondary/20 text-secondary border border-secondary/30'
                        : 'text-gray-300 hover:bg-accent/10 hover:text-accent border border-transparent hover:border-accent/30'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-8">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDashboard;