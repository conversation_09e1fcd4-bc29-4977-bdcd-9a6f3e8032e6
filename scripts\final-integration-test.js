const axios = require('axios');
const mysql = require('mysql2/promise');

async function finalIntegrationTest() {
  console.log('🎯 الاختبار النهائي لتكامل البيانات مع MySQL\n');
  console.log('=' .repeat(60));
  
  let connection;
  let testResults = {
    total: 0,
    passed: 0,
    failed: 0
  };
  
  function test(name, condition) {
    testResults.total++;
    if (condition) {
      console.log(`✅ ${name}`);
      testResults.passed++;
    } else {
      console.log(`❌ ${name}`);
      testResults.failed++;
    }
  }
  
  try {
    // 1. اتصال مباشر بقاعدة البيانات
    console.log('1️⃣ اختبار الاتصال المباشر بقاعدة البيانات...');
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'khan<PERSON><PERSON>riya_db'
    });
    
    test('اتصال قاعدة البيانات', true);
    
    // 2. فحص الجداول والبيانات
    console.log('\n2️⃣ فحص الجداول والبيانات...');
    
    const [systemsRows] = await connection.execute('SELECT COUNT(*) as count FROM system_services WHERE status = "active"');
    const [servicesRows] = await connection.execute('SELECT COUNT(*) as count FROM technical_services WHERE status = "active"');
    const [usersRows] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [ordersRows] = await connection.execute('SELECT COUNT(*) as count FROM orders');
    
    const systemsCount = systemsRows[0].count;
    const servicesCount = servicesRows[0].count;
    const usersCount = usersRows[0].count;
    const ordersCount = ordersRows[0].count;
    
    test('جدول الأنظمة يحتوي على بيانات', systemsCount > 0);
    test('جدول الخدمات يحتوي على بيانات', servicesCount > 0);
    test('جدول المستخدمين يحتوي على بيانات', usersCount > 0);
    
    console.log(`   📊 الأنظمة النشطة: ${systemsCount}`);
    console.log(`   📊 الخدمات النشطة: ${servicesCount}`);
    console.log(`   📊 المستخدمين: ${usersCount}`);
    console.log(`   📊 الطلبات: ${ordersCount}`);
    
    // 3. اختبار API مقابل قاعدة البيانات
    console.log('\n3️⃣ مقارنة API مع قاعدة البيانات...');
    
    const systemsApiResponse = await axios.get('http://localhost:3001/api/systems');
    const servicesApiResponse = await axios.get('http://localhost:3001/api/services/technical');
    
    const apiSystemsCount = systemsApiResponse.data.data.systems.length;
    const apiServicesCount = servicesApiResponse.data.data.services.length;
    
    test('عدد الأنظمة متطابق بين API وقاعدة البيانات', apiSystemsCount === systemsCount);
    test('عدد الخدمات متطابق بين API وقاعدة البيانات', apiServicesCount === servicesCount);
    
    // 4. اختبار تكامل البيانات
    console.log('\n4️⃣ اختبار تكامل البيانات...');
    
    // فحص النظام الأول من API
    if (apiSystemsCount > 0) {
      const firstApiSystem = systemsApiResponse.data.data.systems[0];
      const [dbSystemRows] = await connection.execute(
        'SELECT * FROM system_services WHERE id = ?', 
        [firstApiSystem.id]
      );
      
      if (dbSystemRows.length > 0) {
        const dbSystem = dbSystemRows[0];
        test('بيانات النظام متطابقة بين API وقاعدة البيانات', 
          firstApiSystem.name_ar === dbSystem.name_ar && 
          firstApiSystem.price == dbSystem.price
        );
        test('حقول JSON محللة بشكل صحيح', 
          Array.isArray(firstApiSystem.features_ar) && 
          Array.isArray(firstApiSystem.tech_specs_ar)
        );
      }
    }
    
    // فحص الخدمة الأولى من API
    if (apiServicesCount > 0) {
      const firstApiService = servicesApiResponse.data.data.services[0];
      const [dbServiceRows] = await connection.execute(
        'SELECT * FROM technical_services WHERE id = ?', 
        [firstApiService.id]
      );
      
      if (dbServiceRows.length > 0) {
        const dbService = dbServiceRows[0];
        test('بيانات الخدمة متطابقة بين API وقاعدة البيانات', 
          firstApiService.name_ar === dbService.name_ar && 
          firstApiService.price == dbService.price
        );
        test('حقول Premium محللة بشكل صحيح', 
          firstApiService.is_premium_addon !== undefined && 
          firstApiService.premium_price !== undefined
        );
      }
    }
    
    // 5. اختبار العمليات مع المصادقة
    console.log('\n5️⃣ اختبار العمليات مع المصادقة...');
    
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    test('تسجيل الدخول يعمل', loginResponse.status === 200);
    
    const token = loginResponse.data.data.tokens.accessToken;
    const headers = { Authorization: `Bearer ${token}` };
    
    // اختبار admin endpoints
    const adminSystemsResponse = await axios.get('http://localhost:3001/api/systems/admin', { headers });
    const adminServicesResponse = await axios.get('http://localhost:3001/api/services/admin/technical', { headers });
    
    test('Admin systems endpoint يعمل', adminSystemsResponse.status === 200);
    test('Admin services endpoint يعمل', adminServicesResponse.status === 200);
    test('Admin endpoints ترجع نفس عدد البيانات', 
      adminSystemsResponse.data.length === systemsCount && 
      adminServicesResponse.data.length === servicesCount
    );
    
    // 6. اختبار إنشاء وحذف بيانات
    console.log('\n6️⃣ اختبار دورة حياة البيانات (إنشاء/قراءة/حذف)...');
    
    // إنشاء نظام تجريبي
    const testSystemData = {
      name_ar: 'نظام اختبار تكامل',
      name_en: 'Integration Test System',
      description_ar: 'نظام للاختبار فقط',
      description_en: 'System for testing only',
      price: 1.00,
      category: 'test',
      type: 'regular',
      features_ar: ['اختبار'],
      features_en: ['test'],
      tech_specs_ar: ['مواصفة اختبار'],
      tech_specs_en: ['test spec'],
      status: 'active'
    };
    
    const createResponse = await axios.post('http://localhost:3001/api/admin/systems', testSystemData, { headers });
    test('إنشاء نظام جديد يعمل', createResponse.status === 200);
    
    const createdSystemId = createResponse.data.data.id;
    test('النظام المُنشأ له ID صحيح', createdSystemId && createdSystemId.length > 0);
    
    // التحقق من وجود النظام في قاعدة البيانات
    const [createdSystemRows] = await connection.execute(
      'SELECT * FROM system_services WHERE id = ?', 
      [createdSystemId]
    );
    test('النظام المُنشأ موجود في قاعدة البيانات', createdSystemRows.length === 1);
    
    // حذف النظام التجريبي
    if (createdSystemId) {
      await axios.delete(`http://localhost:3001/api/admin/systems/${createdSystemId}`, { headers });
      
      // التحقق من حذف النظام من قاعدة البيانات
      const [deletedSystemRows] = await connection.execute(
        'SELECT * FROM system_services WHERE id = ?', 
        [createdSystemId]
      );
      test('النظام المحذوف غير موجود في قاعدة البيانات', deletedSystemRows.length === 0);
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
    testResults.failed++;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
  
  // النتائج النهائية
  console.log('\n' + '='.repeat(60));
  console.log('📊 النتائج النهائية');
  console.log('='.repeat(60));
  
  const successRate = Math.round((testResults.passed / testResults.total) * 100);
  console.log(`✅ نجح: ${testResults.passed}/${testResults.total} اختبار`);
  console.log(`❌ فشل: ${testResults.failed}/${testResults.total} اختبار`);
  console.log(`📈 معدل النجاح: ${successRate}%`);
  
  if (successRate >= 95) {
    console.log('\n🎉 ممتاز! تكامل البيانات يعمل بشكل مثالي!');
  } else if (successRate >= 85) {
    console.log('\n✅ جيد! معظم الوظائف تعمل بشكل صحيح.');
  } else {
    console.log('\n⚠️ يحتاج إلى تحسين! هناك مشاكل تحتاج إلى إصلاح.');
  }
  
  console.log('\n🔗 روابط الوصول:');
  console.log('   🌐 الصفحة الرئيسية: http://localhost:5173');
  console.log('   👑 لوحة التحكم: http://localhost:5173/admin');
  console.log('   🔐 تسجيل الدخول: <EMAIL> / admin123');
}

finalIntegrationTest();
