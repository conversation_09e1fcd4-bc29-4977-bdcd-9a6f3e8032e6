import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { 
  BarChart3, Package, Wrench, FileText, Users, Crown, Settings,
  ChevronLeft, ChevronRight, Menu, X
} from 'lucide-react';

interface AdminSidebarProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

/**
 * Responsive admin sidebar with collapsible functionality and mobile support
 * 
 * Features:
 * - Collapsible sidebar for desktop
 * - Mobile overlay menu
 * - Touch-friendly navigation
 * - Accessibility support
 * - RTL layout support
 * - Smooth animations
 */
const AdminSidebar: React.FC<AdminSidebarProps> = ({
  activeTab,
  onTabChange,
  isCollapsed = false,
  onToggleCollapse
}) => {
  const { language, t } = useTranslation();
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  // Close mobile sidebar when tab changes
  useEffect(() => {
    setIsMobileOpen(false);
  }, [activeTab]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileOpen]);

  // Close mobile menu on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (isMobileOpen && !target.closest('.admin-sidebar-container') && !target.closest('.mobile-sidebar-button')) {
        setIsMobileOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobileOpen]);

  const tabs = [
    { 
      id: 'overview', 
      label: language === 'ar' ? 'نظرة عامة' : 'Overview', 
      icon: BarChart3,
      description: language === 'ar' ? 'إحصائيات عامة' : 'General statistics'
    },
    { 
      id: 'systems', 
      label: language === 'ar' ? 'إدارة الأنظمة التقنية' : 'Manage Systems', 
      icon: Package,
      description: language === 'ar' ? 'إدارة الأنظمة' : 'System management'
    },
    { 
      id: 'technical', 
      label: language === 'ar' ? 'إدارة الخدمات التقنية' : 'Technical Services', 
      icon: Wrench,
      description: language === 'ar' ? 'الخدمات التقنية' : 'Technical services'
    },
    { 
      id: 'orders', 
      label: language === 'ar' ? 'الطلبات' : 'Orders', 
      icon: FileText,
      description: language === 'ar' ? 'إدارة الطلبات' : 'Order management'
    },
    { 
      id: 'users', 
      label: language === 'ar' ? 'إدارة المستخدمين' : 'User Management', 
      icon: Users,
      description: language === 'ar' ? 'إدارة المستخدمين' : 'User management'
    },
    { 
      id: 'premium', 
      label: language === 'ar' ? 'المحتوى المميز' : 'Premium Content', 
      icon: Crown,
      description: language === 'ar' ? 'المحتوى المميز' : 'Premium content'
    },
    { 
      id: 'settings', 
      label: language === 'ar' ? 'إعدادات النظام' : 'System Settings', 
      icon: Settings,
      description: language === 'ar' ? 'إعدادات النظام' : 'System settings'
    }
  ];

  const handleTabChange = (tabId: string) => {
    onTabChange(tabId);
    setIsMobileOpen(false);
  };

  return (
    <>
      {/* Mobile menu button */}
      <button
        onClick={() => setIsMobileOpen(!isMobileOpen)}
        className="mobile-sidebar-button lg:hidden fixed top-4 left-4 z-50 p-3 bg-primary/90 backdrop-blur-sm rounded-lg border border-accent/30 text-white hover:bg-primary transition-colors touch-manipulation min-h-[44px] min-w-[44px] flex items-center justify-center"
        aria-label={isMobileOpen ? t('admin.closeSidebar', 'Close admin menu') : t('admin.openSidebar', 'Open admin menu')}
        aria-expanded={isMobileOpen}
      >
        {isMobileOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
      </button>

      {/* Mobile overlay */}
      {isMobileOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black/70 backdrop-blur-sm z-40"
          onClick={() => setIsMobileOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <div
        className={`
          admin-sidebar-container
          fixed lg:relative top-0 h-full bg-gradient-to-b from-primary to-background border-accent/20 z-50 transition-all duration-300
          ${isMobileOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          ${isCollapsed ? 'w-16' : 'w-64'}
          ${language === 'ar' ? 'right-0 left-auto border-l' : 'left-0 border-r'}
        `}
        role="navigation"
        aria-label={t('admin.sidebar', 'Admin navigation')}
      >
        {/* Collapse toggle for desktop */}
        {onToggleCollapse && (
          <button
            onClick={onToggleCollapse}
            className={`
              hidden lg:flex absolute top-8 w-6 h-6 bg-primary border border-accent/30 rounded-full items-center justify-center text-accent hover:text-white transition-colors
              ${language === 'ar' ? '-left-3' : '-right-3'}
            `}
            aria-label={isCollapsed ? t('admin.expandSidebar', 'Expand sidebar') : t('admin.collapseSidebar', 'Collapse sidebar')}
          >
            {isCollapsed ? 
              (language === 'ar' ? <ChevronLeft className="w-3 h-3" /> : <ChevronRight className="w-3 h-3" />) : 
              (language === 'ar' ? <ChevronRight className="w-3 h-3" /> : <ChevronLeft className="w-3 h-3" />)
            }
          </button>
        )}

        {/* Navigation */}
        <nav className="p-4 space-y-2 mt-16 lg:mt-4">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            
            return (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`
                  w-full flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 rounded-lg transition-all duration-300 touch-manipulation min-h-[52px]
                  ${isActive
                    ? 'bg-secondary/20 text-secondary border border-secondary/30'
                    : 'text-gray-300 hover:bg-accent/10 hover:text-accent border border-transparent hover:border-accent/30'
                  }
                  ${isCollapsed ? 'justify-center' : ''}
                `}
                title={isCollapsed ? tab.label : undefined}
                aria-label={isCollapsed ? tab.label : undefined}
                aria-current={isActive ? 'page' : undefined}
              >
                <Icon className="w-5 h-5 flex-shrink-0" />
                {!isCollapsed && (
                  <div className="flex-1 text-left rtl:text-right">
                    <div className="font-medium truncate">{tab.label}</div>
                    {!isMobileOpen && (
                      <div className="text-xs text-gray-400 truncate">{tab.description}</div>
                    )}
                  </div>
                )}
              </button>
            );
          })}
        </nav>
      </div>
    </>
  );
};

export default AdminSidebar;
