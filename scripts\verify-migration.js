#!/usr/bin/env node

/**
 * Migration Verification Script
 * 
 * This script verifies that all data has been successfully migrated
 * from localStorage to MySQL and that the API is working correctly.
 */

const axios = require('axios');
const mysql = require('mysql2/promise');
require('dotenv').config();

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';

async function verifyMySQLData() {
  console.log('🗄️ Verifying MySQL data...\n');
  
  let connection;
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '123456',
      database: 'khanfashariya_db'
    });
    
    const tables = [
      { name: 'users', required: true, minRecords: 1 },
      { name: 'system_services', required: true, minRecords: 5 },
      { name: 'technical_services', required: true, minRecords: 5 },
      { name: 'orders', required: false, minRecords: 0 },
      { name: 'premium_content', required: false, minRecords: 0 },
      { name: 'premium_packages', required: false, minRecords: 0 },
      { name: 'subscriptions', required: false, minRecords: 0 },
      { name: 'contact_messages', required: false, minRecords: 0 },
      { name: 'inbox_messages', required: false, minRecords: 0 }
    ];
    
    const results = {};
    let allPassed = true;
    
    console.log('📊 MySQL Table Verification:');
    console.log('─'.repeat(28));
    
    for (const table of tables) {
      try {
        const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table.name}`);
        const count = rows[0].count;
        
        const passed = count >= table.minRecords;
        const status = passed ? '✅' : (table.required ? '❌' : '⚠️');
        
        console.log(`   ${table.name.padEnd(20)} ${count.toString().padStart(3)} records ${status}`);
        
        results[table.name] = { count, passed, required: table.required };
        
        if (table.required && !passed) {
          allPassed = false;
        }
        
      } catch (error) {
        console.log(`   ${table.name.padEnd(20)} ❌ Error: ${error.message}`);
        results[table.name] = { count: 0, passed: false, error: error.message };
        if (table.required) {
          allPassed = false;
        }
      }
    }
    
    console.log('');
    return { results, allPassed };
    
  } catch (error) {
    console.error('❌ MySQL verification failed:', error.message);
    return { results: {}, allPassed: false };
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function verifyAPIEndpoints() {
  console.log('🔌 Verifying API endpoints...\n');
  
  const endpoints = [
    { path: '/health', method: 'GET', name: 'Health Check' },
    { path: '/api/systems', method: 'GET', name: 'System Services' },
    { path: '/api/services/technical', method: 'GET', name: 'Technical Services' },
    { path: '/api/users', method: 'GET', name: 'Users (requires auth)', requiresAuth: true },
    { path: '/api/orders', method: 'GET', name: 'Orders (requires auth)', requiresAuth: true }
  ];
  
  const results = {};
  let allPassed = true;
  
  console.log('🌐 API Endpoint Verification:');
  console.log('─'.repeat(29));
  
  for (const endpoint of endpoints) {
    try {
      const config = {
        method: endpoint.method,
        url: `${API_BASE_URL}${endpoint.path}`,
        timeout: 10000
      };
      
      // Skip auth-required endpoints for now
      if (endpoint.requiresAuth) {
        console.log(`   ${endpoint.name.padEnd(25)} ⚠️ Skipped (requires auth)`);
        results[endpoint.path] = { status: 'skipped', reason: 'requires auth' };
        continue;
      }
      
      const response = await axios(config);
      
      if (response.status === 200) {
        const dataCount = response.data?.data?.length || 0;
        console.log(`   ${endpoint.name.padEnd(25)} ✅ ${dataCount} records`);
        results[endpoint.path] = { status: 'success', count: dataCount };
      } else {
        console.log(`   ${endpoint.name.padEnd(25)} ⚠️ Status: ${response.status}`);
        results[endpoint.path] = { status: 'warning', httpStatus: response.status };
      }
      
    } catch (error) {
      console.log(`   ${endpoint.name.padEnd(25)} ❌ ${error.message}`);
      results[endpoint.path] = { status: 'error', error: error.message };
      if (!endpoint.requiresAuth) {
        allPassed = false;
      }
    }
  }
  
  console.log('');
  return { results, allPassed };
}

async function verifyDataConsistency() {
  console.log('🔍 Verifying data consistency...\n');
  
  try {
    // Get data from API
    const systemsResponse = await axios.get(`${API_BASE_URL}/api/systems`);
    const servicesResponse = await axios.get(`${API_BASE_URL}/api/services/technical`);
    
    const systems = Array.isArray(systemsResponse.data?.data) ? systemsResponse.data.data : [];
    const services = Array.isArray(servicesResponse.data?.data) ? servicesResponse.data.data : [];
    
    console.log('📋 Data Consistency Check:');
    console.log('─'.repeat(26));
    
    // Check systems data
    const activeSystems = systems.filter(s => s.status === 'active');
    console.log(`   Active Systems: ${activeSystems.length}/${systems.length} ✅`);
    
    // Check services data
    const activeServices = services.filter(s => s.status === 'active');
    console.log(`   Active Services: ${activeServices.length}/${services.length} ✅`);
    
    // Check data structure
    const systemsHaveRequiredFields = systems.every(s => 
      s.id && s.name_ar && s.name_en && s.description_ar && s.description_en
    );
    console.log(`   Systems Structure: ${systemsHaveRequiredFields ? '✅' : '❌'}`);
    
    const servicesHaveRequiredFields = services.every(s => 
      s.id && s.name_ar && s.name_en && s.description_ar && s.description_en
    );
    console.log(`   Services Structure: ${servicesHaveRequiredFields ? '✅' : '❌'}`);
    
    console.log('');
    
    return {
      systemsCount: systems.length,
      servicesCount: services.length,
      activeSystems: activeSystems.length,
      activeServices: activeServices.length,
      structureValid: systemsHaveRequiredFields && servicesHaveRequiredFields
    };
    
  } catch (error) {
    console.error('❌ Data consistency check failed:', error.message);
    return { structureValid: false, error: error.message };
  }
}

async function checkLocalStorageCleanup() {
  console.log('🧹 Checking localStorage cleanup status...\n');
  
  // This would require browser automation, so we'll check the code instead
  const fs = require('fs').promises;
  const path = require('path');
  
  try {
    // Check if forceApiUsage is enabled
    const mainPath = path.join(process.cwd(), 'src/main.tsx');
    const mainContent = await fs.readFile(mainPath, 'utf8');
    
    const hasForceApiUsage = mainContent.includes('initializeApiForcing');
    
    console.log('🔒 LocalStorage Protection:');
    console.log('─'.repeat(27));
    console.log(`   Force API Usage: ${hasForceApiUsage ? '✅ Enabled' : '❌ Disabled'}`);
    
    // Check for remaining localStorage usage in components
    const componentsDir = path.join(process.cwd(), 'src/components');
    const files = await fs.readdir(componentsDir, { recursive: true });
    
    let localStorageUsageFound = false;
    for (const file of files) {
      if (file.endsWith('.tsx') || file.endsWith('.ts')) {
        const filePath = path.join(componentsDir, file);
        const content = await fs.readFile(filePath, 'utf8');
        
        if (content.includes('localStorage.') && !file.includes('forceApiUsage')) {
          localStorageUsageFound = true;
          break;
        }
      }
    }
    
    console.log(`   Components Clean: ${!localStorageUsageFound ? '✅ No localStorage usage' : '⚠️ Usage found'}`);
    console.log('');
    
    return {
      forceApiEnabled: hasForceApiUsage,
      componentsClean: !localStorageUsageFound
    };
    
  } catch (error) {
    console.error('❌ LocalStorage cleanup check failed:', error.message);
    return { forceApiEnabled: false, componentsClean: false };
  }
}

async function generateMigrationReport(mysqlResults, apiResults, consistencyResults, cleanupResults) {
  console.log('📊 Migration Verification Report');
  console.log('================================\n');
  
  const overallStatus = 
    mysqlResults.allPassed && 
    apiResults.allPassed && 
    consistencyResults.structureValid && 
    cleanupResults.forceApiEnabled;
  
  console.log(`🎯 Overall Status: ${overallStatus ? '✅ PASSED' : '❌ FAILED'}\n`);
  
  console.log('📋 Summary:');
  console.log('─'.repeat(11));
  console.log(`   MySQL Database: ${mysqlResults.allPassed ? '✅ Ready' : '❌ Issues found'}`);
  console.log(`   API Endpoints: ${apiResults.allPassed ? '✅ Working' : '❌ Issues found'}`);
  console.log(`   Data Structure: ${consistencyResults.structureValid ? '✅ Valid' : '❌ Invalid'}`);
  console.log(`   LocalStorage: ${cleanupResults.forceApiEnabled ? '✅ Protected' : '⚠️ Not protected'}`);
  
  console.log('\n📈 Data Counts:');
  console.log('─'.repeat(15));
  console.log(`   Systems: ${consistencyResults.systemsCount || 0} (${consistencyResults.activeSystems || 0} active)`);
  console.log(`   Services: ${consistencyResults.servicesCount || 0} (${consistencyResults.activeServices || 0} active)`);
  console.log(`   Users: ${mysqlResults.results.users?.count || 0}`);
  console.log(`   Orders: ${mysqlResults.results.orders?.count || 0}`);
  
  if (overallStatus) {
    console.log('\n🎉 Migration Verification Complete!');
    console.log('✅ All systems are ready for production');
    console.log('✅ LocalStorage has been successfully replaced with MySQL');
  } else {
    console.log('\n⚠️ Migration Issues Found');
    console.log('🔧 Please address the issues above before proceeding');
  }
  
  return overallStatus;
}

async function main() {
  console.log('🔍 Migration Verification');
  console.log('=========================\n');
  
  // Verify MySQL data
  const mysqlResults = await verifyMySQLData();
  
  // Verify API endpoints
  const apiResults = await verifyAPIEndpoints();
  
  // Verify data consistency
  const consistencyResults = await verifyDataConsistency();
  
  // Check localStorage cleanup
  const cleanupResults = await checkLocalStorageCleanup();
  
  // Generate final report
  const success = await generateMigrationReport(
    mysqlResults, 
    apiResults, 
    consistencyResults, 
    cleanupResults
  );
  
  process.exit(success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Verification error:', error);
    process.exit(1);
  });
}

module.exports = { main };
