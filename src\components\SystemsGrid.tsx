import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuth } from '../hooks/useAuth';
import { useNotification } from '../hooks/useNotification';
import { useButtonActions } from '../utils/buttonActions';
import { getSystemServices, SystemService } from '../lib/apiServices';
import { createOrder } from '../lib/apiServices';
import PremiumEdition from './PremiumEdition';

import { ExternalLink, Shield, Zap, Sword, ShoppingCart, Book, Heart, Target, Star, ArrowRight, ArrowLeft, X, Play, Image as ImageIcon, Crown, Eye, ChevronLeft, ChevronRight, Grid, List, Info, CheckCircle } from 'lucide-react';

// YouTube URL parsing utility
const parseYouTubeURL = (url: string): string | null => {
  if (!url) return null;

  // Handle different YouTube URL formats
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
    /youtube\.com\/watch\?.*v=([^&\n?#]+)/
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return `https://www.youtube.com/embed/${match[1]}`;
    }
  }

  return null;
};

const SystemsGrid: React.FC = () => {
  const { t, language } = useTranslation();
  const { isAuthenticated, userProfile } = useAuth();
  const { showNotification } = useNotification();
  const buttonActions = useButtonActions();
  const [selectedSystem, setSelectedSystem] = useState<SystemService | null>(null);
  const [systems, setSystems] = useState<SystemService[]>([]);
  const [loading, setLoading] = useState(true);
  const [showPremiumContent, setShowPremiumContent] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [hoveredSystem, setHoveredSystem] = useState<string | null>(null);

  useEffect(() => {
    loadSystems();
  }, []);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (selectedSystem) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [selectedSystem]);

  const loadSystems = async () => {
    setLoading(true);
    try {
      const result = await getSystemServices();
      console.log('SystemsGrid - API result:', result); // Debug log

      // Check if data is in result.data.systems or result.data directly
      const systemsData = result.data?.systems || result.data;
      if (systemsData && Array.isArray(systemsData)) {
        const activeSystems = systemsData.filter(s => s.status === 'active');
        setSystems(activeSystems);
        console.log('SystemsGrid - Active systems loaded:', activeSystems.length);

        // Systems loaded successfully - no notification needed for normal operation
      } else if (result.error) {
        console.error('Error loading systems:', result.error);
        setSystems([]);
        showNotification({
          type: 'error',
          message: result.error.message || (language === 'ar' ? 'فشل في تحميل الأنظمة' : 'Failed to load systems')
        });
      } else {
        // No data and no error - this might be normal (empty database)
        console.warn('No systems data received');
        setSystems([]);
      }
    } catch (error) {
      console.error('Error loading systems:', error);
      setSystems([]);
      showNotification({
        type: 'error',
        message: language === 'ar' ? 'فشل في تحميل الأنظمة' : 'Failed to load systems'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSystemDetails = (system: SystemService) => {
    setSelectedSystem(system);
  };

  const handleOrderNow = async (system: SystemService) => {
    if (!isAuthenticated) {
      showNotification({ type: 'error', message: t('notifications.loginRequired') });
      return;
    }

    if (!userProfile) {
      showNotification({ type: 'error', message: t('notifications.profileError') });
      return;
    }

    try {
      const result = await createOrder({
        order_type: 'system_service',
        item_id: system.id,
        quantity: 1,
        notes_ar: `شراء نظام: ${system.name_ar}`,
        notes_en: `Purchased system: ${system.name_en}`
      });

      if (result.error) {
        showNotification({
          type: 'error',
          message: result.error.message || t('notifications.purchaseError')
        });
      } else {
        showNotification({ type: 'success', message: t('notifications.purchaseSuccess') });
        if(selectedSystem) setSelectedSystem(null);
      }
    } catch (error) {
      console.error('Error:', error);
      showNotification({ type: 'error', message: t('notifications.purchaseError') });
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'combat': return Sword;
      case 'events': return Target;
      case 'utility': return Book;
      case 'economy': return ShoppingCart;
      case 'gameplay': return Heart;
      default: return Shield;
    }
  };

  if (loading) {
    return (
      <section id="systems" className="py-20 bg-gradient-to-br from-background to-primary">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-8">
            <div className="w-8 h-8 border-2 border-secondary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-300">{language === 'ar' ? 'جاري التحميل...' : 'Loading...'}</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="systems" className="py-20 bg-gradient-to-br from-background to-primary">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            <span className="bg-gradient-to-r from-secondary to-accent bg-clip-text text-transparent">
              {t('systems.title')}
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            {t('systems.subtitle')}
          </p>

          {/* View Mode Toggle */}
          <div className="flex justify-center items-center space-x-4 rtl:space-x-reverse mb-8">
            <div className="bg-primary/50 backdrop-blur-sm rounded-xl border border-accent/20 p-1 flex">
              <button
                onClick={() => setViewMode('grid')}
                className={`flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 rounded-lg transition-all duration-300 ${
                  viewMode === 'grid'
                    ? 'bg-secondary text-primary shadow-lg'
                    : 'text-gray-300 hover:text-white hover:bg-accent/20'
                }`}
              >
                <Grid className="w-4 h-4" />
                <span className="text-sm font-medium">{language === 'ar' ? 'شبكة' : 'Grid'}</span>
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 rounded-lg transition-all duration-300 ${
                  viewMode === 'list'
                    ? 'bg-secondary text-primary shadow-lg'
                    : 'text-gray-300 hover:text-white hover:bg-accent/20'
                }`}
              >
                <List className="w-4 h-4" />
                <span className="text-sm font-medium">{language === 'ar' ? 'قائمة' : 'List'}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Systems Display */}
        <div className={viewMode === 'grid'
          ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          : "space-y-6"
        }>
          {systems.map((system) => {
            const CategoryIcon = getCategoryIcon(system.category);

            return (
              <div
                key={system.id}
                className={`group relative bg-gradient-to-br from-primary/80 to-background/80 backdrop-blur-sm rounded-2xl border border-accent/20 hover:border-secondary/50 transition-all duration-300 overflow-hidden hover:shadow-2xl hover:shadow-secondary/20 cursor-pointer ${
                  viewMode === 'grid'
                    ? 'hover:-translate-y-1 hover:scale-[1.02]'
                    : 'hover:scale-[1.01] flex items-center p-6'
                }`}
                onMouseEnter={() => setHoveredSystem(system.id)}
                onMouseLeave={() => setHoveredSystem(null)}
                title={`${language === 'ar' ? system.name_ar : system.name_en} - $${system.price} - ${system.status === 'active' ? (language === 'ar' ? 'متاح' : 'Available') : (language === 'ar' ? 'غير متاح' : 'Unavailable')}`}
              >


                {viewMode === 'grid' ? (
                  // Grid View Content
                  <div onClick={() => setSelectedSystem(system)} className="flex flex-col h-full">
                {/* Image Section */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={system.image_url || 'https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg'}
                    alt={language === 'ar' ? system.name_ar : system.name_en}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-primary/80 to-transparent"></div>
                  
                  <div className="absolute top-4 right-4 bg-secondary/90 backdrop-blur-sm rounded-full p-2 transition-all duration-300 group-hover:bg-secondary group-hover:scale-110">
                    <CategoryIcon className="w-5 h-5 text-primary" />
                  </div>
                  
                  <div className="absolute bottom-4 left-4 bg-accent/90 backdrop-blur-sm rounded-full px-3 py-1">
                    <span className="text-primary font-bold text-sm">${system.price}</span>
                  </div>

                  <div className="absolute bottom-4 right-4 flex space-x-2 rtl:space-x-reverse">
                    {system.video_url && (
                      <div className="bg-red-500/90 backdrop-blur-sm rounded-full p-1">
                        <Play className="w-4 h-4 text-white" />
                      </div>
                    )}
                    {system.image_url && (
                      <div className="bg-blue-500/90 backdrop-blur-sm rounded-full p-1">
                        <ImageIcon className="w-4 h-4 text-white" />
                      </div>
                    )}
                    {system.gallery_images && system.gallery_images.length > 0 && (
                      <div className="bg-green-500/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
                        <ImageIcon className="w-3 h-3 text-white" />
                        <span className="text-white text-xs font-medium">{system.gallery_images.length}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Content Section - Fixed layout */}
                <div className="card-content p-6">
                  <div className="card-header-container">
                    <h3 className="card-title">
                      {language === 'ar' ? system.name_ar : system.name_en}
                    </h3>
                    {/* Premium Badge */}
                    {system.isPremiumAddon && (
                      <span className="px-2 py-1 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-400 border border-yellow-500/30 rounded-full text-xs flex items-center icon-container flex-shrink-0">
                        <Crown className="w-3 h-3 icon-left" />
                        <span>{language === 'ar' ? 'مميز' : 'Premium'}</span>
                      </span>
                    )}
                  </div>
                  
                  <p className="text-gray-300 text-sm mb-4 leading-relaxed line-clamp-3">
                    {language === 'ar' ? system.description_ar : system.description_en}
                  </p>
                  
                  <div className="space-y-2 mb-6 flex-1">
                    {(language === 'ar' ? system.features_ar : system.features_en)?.slice(0, 3)?.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Star className="w-3 h-3 text-accent fill-current" />
                        <span className="text-xs text-gray-400">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <div className="button-container">
                    <button
                      onClick={() => handleSystemDetails(system)}
                      className="flex-1 px-4 py-2 text-accent hover:bg-accent/10 rounded-lg transition-all duration-300 flex items-center justify-center icon-container text-sm border border-accent/30 hover:border-accent/50 group/btn"
                    >
                      <ExternalLink className="w-4 h-4 group-hover/btn:scale-110 transition-transform duration-300 icon-left" />
                      <span>{t('systems.readMore')}</span>
                    </button>
                    <button
                      onClick={() => handleOrderNow(system)}
                      className="flex-1 px-4 py-2 bg-gradient-to-r from-secondary to-accent text-primary rounded-lg transition-all duration-300 text-sm font-medium hover:shadow-lg hover:shadow-secondary/30 flex items-center justify-center icon-container group/btn"
                    >
                      <span>{t('systems.orderNow')}</span>
                      {language === 'ar' ? (
                        <ArrowLeft className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform duration-300 icon-right" />
                      ) : (
                        <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform duration-300 icon-right" />
                      )}
                    </button>
                  </div>


                </div>
                  </div>
                ) : (
                  // List View Content
                  <div onClick={() => setSelectedSystem(system)} className="flex items-center space-x-6 rtl:space-x-reverse">
                    {/* System Icon */}
                    <div className="w-20 h-20 bg-gradient-to-br from-secondary/20 to-accent/20 rounded-xl flex items-center justify-center flex-shrink-0">
                      <CategoryIcon className="w-10 h-10 text-secondary" />
                    </div>

                    {/* System Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2 rtl:space-x-reverse flex-1">
                          <h3 className="text-xl font-bold text-white group-hover:text-secondary transition-colors duration-300">
                            {language === 'ar' ? system.name_ar : system.name_en}
                          </h3>
                          {/* Premium Badge */}
                          {system.isPremiumAddon && (
                            <span className="px-2 py-1 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-400 border border-yellow-500/30 rounded-full text-xs flex items-center space-x-1 rtl:space-x-reverse">
                              <Crown className="w-3 h-3" />
                              <span>{language === 'ar' ? 'مميز' : 'Premium'}</span>
                            </span>
                          )}
                        </div>
                        <span className="text-2xl font-bold text-accent">${system.price}</span>
                      </div>

                      <p className="text-gray-300 text-sm mb-3 line-clamp-2">
                        {language === 'ar' ? system.description_ar : system.description_en}
                      </p>

                      <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm">
                        <span className="text-gray-400">{language === 'ar' ? 'الفئة:' : 'Category:'}</span>
                        <span className="text-accent">{system.category}</span>
                        <span className="text-gray-400">•</span>
                        <span className={`flex items-center space-x-1 rtl:space-x-reverse ${
                          system.status === 'active' ? 'text-green-400' : 'text-red-400'
                        }`}>
                          <CheckCircle className="w-3 h-3" />
                          <span>{system.status === 'active' ? (language === 'ar' ? 'متاح' : 'Available') : (language === 'ar' ? 'غير متاح' : 'Unavailable')}</span>
                        </span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-3 rtl:space-x-reverse flex-shrink-0">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedSystem(system);
                        }}
                        className="px-4 py-2 bg-primary/50 border border-accent/30 text-white rounded-lg transition-all duration-300 text-sm font-medium hover:bg-accent/20 hover:border-accent/50 flex items-center space-x-2 rtl:space-x-reverse"
                      >
                        <Eye className="w-4 h-4" />
                        <span>{language === 'ar' ? 'تفاصيل' : 'Details'}</span>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleOrderNow(system);
                        }}
                        className="px-4 py-2 bg-gradient-to-r from-secondary to-accent text-primary rounded-lg transition-all duration-300 text-sm font-medium hover:shadow-lg hover:shadow-secondary/30 flex items-center space-x-2 rtl:space-x-reverse"
                      >
                        <ShoppingCart className="w-4 h-4" />
                        <span>{language === 'ar' ? 'اطلب الآن' : 'Order Now'}</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* System Details Modal */}
        {selectedSystem && (
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 overflow-y-auto">
            <div className="bg-gradient-to-br from-primary to-background border border-accent/30 rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto relative my-8">
              <button
                onClick={() => setSelectedSystem(null)}
                className="absolute top-4 right-4 z-10 text-gray-400 hover:text-white transition-colors duration-200 bg-black/20 rounded-full p-2"
              >
                <X className="w-6 h-6" />
              </button>

              <div className="p-8">
                <div className="flex items-center space-x-4 rtl:space-x-reverse mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-secondary to-accent rounded-xl flex items-center justify-center">
                    {React.createElement(getCategoryIcon(selectedSystem.category), { className: "w-8 h-8 text-primary" })}
                  </div>
                  <div>
                    <h2 className="text-3xl font-bold text-white">{language === 'ar' ? selectedSystem.name_ar : selectedSystem.name_en}</h2>
                    <p className="text-accent text-xl font-semibold">${selectedSystem.price}</p>
                  </div>
                </div>

                {/* Video Section - Show video if available, otherwise show image */}
                <div className="relative h-64 rounded-xl overflow-hidden mb-6">
                  {selectedSystem.video_url ? (
                    // Show video if available
                    <div className="w-full h-full">
                      <iframe
                        src={parseYouTubeURL(selectedSystem.video_url) || selectedSystem.video_url}
                        title={language === 'ar' ? selectedSystem.name_ar : selectedSystem.name_en}
                        className="w-full h-full rounded-xl"
                        frameBorder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                      />
                    </div>
                  ) : (
                    // Show image if no video
                    <>
                      <img
                        src={selectedSystem.image_url || 'https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg'}
                        alt={language === 'ar' ? selectedSystem.name_ar : selectedSystem.name_en}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-primary/60 to-transparent"></div>
                    </>
                  )}
                </div>

                <div className="mb-6">
                  <h3 className="text-xl font-bold text-secondary mb-3">
                    {language === 'ar' ? 'وصف النظام' : 'System Description'}
                  </h3>
                  <p className="text-gray-300 leading-relaxed">
                    {language === 'ar' ? selectedSystem.description_ar : selectedSystem.description_en}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <h3 className="text-xl font-bold text-secondary mb-3">
                      {language === 'ar' ? 'المميزات الرئيسية' : 'Key Features'}
                    </h3>
                    <div className="space-y-2">
                      {(() => {
                        const features = language === 'ar'
                          ? (Array.isArray(selectedSystem.features_ar) ? selectedSystem.features_ar : [])
                          : (Array.isArray(selectedSystem.features_en) ? selectedSystem.features_en : []);
                        return features.map((feature: string, index: number) => (
                          <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                            <Star className="w-4 h-4 text-accent fill-current" />
                            <span className="text-gray-300">{feature}</span>
                          </div>
                        ));
                      })()}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-xl font-bold text-secondary mb-3">
                      {language === 'ar' ? 'المواصفات التقنية' : 'Technical Specifications'}
                    </h3>
                    <div className="space-y-2">
                      {(() => {
                        const techSpecs = language === 'ar'
                          ? (selectedSystem.tech_specs_ar ? (Array.isArray(selectedSystem.tech_specs_ar) ? selectedSystem.tech_specs_ar : selectedSystem.tech_specs_ar.split('\n').filter(s => s.trim())) : [])
                          : (selectedSystem.tech_specs_en ? (Array.isArray(selectedSystem.tech_specs_en) ? selectedSystem.tech_specs_en : selectedSystem.tech_specs_en.split('\n').filter(s => s.trim())) : []);

                        if (techSpecs.length === 0) {
                          return (
                            <div className="flex items-center space-x-2 rtl:space-x-reverse">
                              <Shield className="w-4 h-4 text-secondary" />
                              <span className="text-gray-300">{language === 'ar' ? 'لا توجد مواصفات تقنية متاحة' : 'No technical specifications available'}</span>
                            </div>
                          );
                        }

                        return techSpecs.map((spec: string, index: number) => (
                          <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                            <Shield className="w-4 h-4 text-secondary" />
                            <span className="text-gray-300">{spec}</span>
                          </div>
                        ));
                      })()}
                    </div>
                  </div>
                </div>

                {/* Image Gallery */}
                {selectedSystem.gallery_images && selectedSystem.gallery_images.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-xl font-bold text-secondary mb-4">
                      {language === 'ar' ? 'معرض الصور' : 'Image Gallery'}
                    </h3>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                      {selectedSystem.gallery_images.slice(0, 10).map((imageUrl, index) => (
                        <div
                          key={index}
                          className="relative aspect-square rounded-lg overflow-hidden cursor-pointer group hover:scale-105 transition-all duration-300 hover:shadow-lg hover:shadow-secondary/30"
                          onClick={() => setSelectedImageIndex(index)}
                        >
                          <img
                            src={imageUrl}
                            alt={`${language === 'ar' ? selectedSystem.name_ar : selectedSystem.name_en} - ${index + 1}`}
                            className="w-full h-full object-cover group-hover:brightness-110 transition-all duration-300"
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
                            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                              <Eye className="w-4 h-4 text-white" />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex space-x-4 rtl:space-x-reverse">
                  <button
                    onClick={() => handleOrderNow(selectedSystem)}
                    className="flex-1 bg-gradient-to-r from-secondary to-accent text-primary font-bold py-3 px-6 rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-secondary/30 flex items-center justify-center space-x-2 rtl:space-x-reverse hover:scale-105"
                  >
                    <ShoppingCart className="w-5 h-5" />
                    <span>{t('systems.orderNow')}</span>
                  </button>
                  <button
                    onClick={() => setSelectedSystem(null)}
                    className="px-6 py-3 border border-accent/30 text-accent rounded-lg hover:bg-accent/10 transition-all duration-300"
                  >
                    {language === 'ar' ? 'إغلاق' : 'Close'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Image Lightbox Modal */}
        {selectedImageIndex !== null && selectedSystem?.gallery_images && (
          <div
            className="fixed inset-0 bg-black/90 backdrop-blur-sm z-[60] flex items-center justify-center p-4"
            onClick={() => setSelectedImageIndex(null)}
          >
            <div className="relative max-w-4xl max-h-[90vh] w-full h-full flex items-center justify-center">
              <button
                onClick={() => setSelectedImageIndex(null)}
                className="absolute top-4 right-4 z-10 text-white hover:text-gray-300 transition-colors duration-200 bg-black/50 rounded-full p-2"
              >
                <X className="w-6 h-6" />
              </button>

              <img
                src={selectedSystem.gallery_images[selectedImageIndex]}
                alt={`${language === 'ar' ? selectedSystem.name_ar : selectedSystem.name_en} - ${selectedImageIndex + 1}`}
                className="max-w-full max-h-full object-contain rounded-lg"
                onClick={(e) => e.stopPropagation()}
              />

              {/* Navigation arrows */}
              {selectedSystem.gallery_images.length > 1 && (
                <>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedImageIndex(selectedImageIndex > 0 ? selectedImageIndex - 1 : selectedSystem.gallery_images!.length - 1);
                    }}
                    className="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 transition-colors duration-200 bg-black/50 rounded-full p-3"
                  >
                    <ChevronLeft className="w-6 h-6" />
                  </button>

                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedImageIndex(selectedImageIndex < selectedSystem.gallery_images!.length - 1 ? selectedImageIndex + 1 : 0);
                    }}
                    className="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 transition-colors duration-200 bg-black/50 rounded-full p-3"
                  >
                    <ChevronRight className="w-6 h-6" />
                  </button>
                </>
              )}

              {/* Image counter */}
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                {selectedImageIndex + 1} / {selectedSystem.gallery_images.length}
              </div>
            </div>
          </div>
        )}

        {showPremiumContent && (
          <PremiumEdition onClose={() => setShowPremiumContent(false)} />
        )}
      </div>
    </section>
  );
};

export default SystemsGrid;