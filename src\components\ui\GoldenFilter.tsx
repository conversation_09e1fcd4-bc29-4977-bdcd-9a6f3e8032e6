import React from 'react';
import { useTranslation } from '../../hooks/useTranslation';

interface FilterOption {
  value: string;
  label: string;
  labelAr?: string;
}

interface GoldenFilterProps {
  label: string;
  labelAr?: string;
  value: string;
  onChange: (value: string) => void;
  options: FilterOption[];
  className?: string;
  placeholder?: string;
  placeholderAr?: string;
}

/**
 * Golden Ratio Filter Component
 * Unified filter dropdown with consistent styling
 */
const GoldenFilter: React.FC<GoldenFilterProps> = ({
  label,
  labelAr,
  value,
  onChange,
  options,
  className = '',
  placeholder,
  placeholderAr
}) => {
  const { language } = useTranslation();

  const displayLabel = language === 'ar' && labelAr ? labelAr : label;
  const displayPlaceholder = language === 'ar' && placeholderAr ? placeholderAr : placeholder;

  return (
    <div className={`golden-spacing-sm ${className}`}>
      <label className="block golden-text-sm font-semibold text-secondary mb-3 tracking-wide">
        {displayLabel}
      </label>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="golden-select w-full hover:shadow-md focus:shadow-lg transition-all duration-300"
      >
        {displayPlaceholder && (
          <option value="" disabled>
            {displayPlaceholder}
          </option>
        )}
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {language === 'ar' && option.labelAr ? option.labelAr : option.label}
          </option>
        ))}
      </select>
    </div>
  );
};

export default GoldenFilter;
